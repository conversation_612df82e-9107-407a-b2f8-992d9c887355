import React, {useState} from 'react';
import {Provider} from 'react-redux';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {persistor, store} from './src/store';
import {PersistGate} from 'redux-persist/integration/react';
import AppNavigator from './src/navigation/AppNavigator';
import {ErrorBoundary} from './src/components/common';
import {ToastProvider} from './src/components/common/Toast';
import SplashScreen from './src/screens/SplashScreen';

function App(): JSX.Element {
  const [isBootstrapComplete, setIsBootstrapComplete] = useState(false);

  const handleBootstrapComplete = () => {
    setIsBootstrapComplete(true);
  };

  if (!isBootstrapComplete) {
    return (
      <ErrorBoundary>
        <SplashScreen onBootstrapComplete={handleBootstrapComplete} />
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <ToastProvider config={{position: 'top', duration: 3000, maxToasts: 3}}>
              <AppNavigator />
            </ToastProvider>
          </PersistGate>
        </Provider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

export default App;
