# Sàn Bảo Hiểm - React Native App

## Tổng quan
Ứng dụng React Native cho hệ thống Bảo hiểm xã hội ESCS được phát triển với các tính năng cơ bản để quản lý thông tin bảo hiểm xã hội.

## Tính năng chính
- 🔐 **Đăng nhập/Đăng xuất** với validation form
- 🏠 **Trang chủ** với thao tác nhanh và hoạt động gần đây
- 👤 **<PERSON><PERSON> sơ cá nhân** với thông tin chi tiết
- 🎨 **UI/UX hiện đại** với theme system nhất quán
- 📱 **Responsive design** tương thích đa thiết bị

## Công nghệ sử dụng
- **React Native 0.72.x** - Framework chính
- **Redux Toolkit** - Quản lý state toàn cục
- **TypeScript** - Type safety
- **Custom Navigation** - Điều hướng đơn giản
- **Fetch API** - HTTP client tích hợp

## Cấu trúc dự án
```
src/
├── components/          # Reusable components
│   ├── common/         # Button, Input, Card, Loading
│   └── forms/          # Form components
├── screens/            # Screen components
│   ├── auth/          # LoginScreen
│   └── main/          # HomeScreen, ProfileScreen
├── navigation/         # Navigation setup
├── store/             # Redux store và slices
│   ├── slices/        # authSlice, userSlice
│   └── index.ts       # Store configuration
├── services/          # API services
│   ├── api.ts         # API client
│   └── endpoints/     # API endpoints
├── constants/         # Theme, config
├── types/             # TypeScript types
├── hooks/             # Custom hooks
└── assets/            # Images, fonts, icons
```

## Demo ứng dụng

### Màn hình đăng nhập
- Form validation với email và password
- Loading state khi đăng nhập
- Mock authentication thành công

### Màn hình chính
- Hiển thị thông tin user
- Thao tác nhanh (Tra cứu, Nộp hồ sơ, etc.)
- Hoạt động gần đây
- Tin tức và thông báo

### Màn hình hồ sơ
- Thông tin cá nhân và bảo hiểm
- Cài đặt ứng dụng
- Chức năng đăng xuất

## Tính năng đã triển khai

### ✅ Hoàn thành
- [x] Khởi tạo dự án React Native
- [x] Cấu trúc thư mục theo best practices
- [x] Redux Store với authSlice và userSlice
- [x] API Client với error handling
- [x] Navigation system đơn giản
- [x] UI Components: Button, Input, Card, Loading
- [x] Theme system với colors, typography, spacing
- [x] LoginScreen với validation
- [x] HomeScreen với dashboard
- [x] ProfileScreen với thông tin user
- [x] Responsive design

### 🔄 Cần cải thiện
- [ ] Cài đặt dependencies (React Native, Redux, etc.)
- [ ] Tích hợp API thực tế
- [ ] Thêm AsyncStorage cho persistence
- [ ] Push notifications
- [ ] Testing framework

---

**Phiên bản**: 1.0.0
**Cập nhật lần cuối**: 26/08/2025
# bao-hiem-xh-escs
