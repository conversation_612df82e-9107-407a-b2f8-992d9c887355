{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@components/*": ["src/components/*"], "@constants/*": ["src/constants/*"], "@navigation/*": ["src/navigation/*"], "@screens/*": ["src/screens/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@assets/*": ["src/assets/*"], "@utils/*": ["src/utils/*"], "@commons/*": ["src/commons/*"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "include": ["src/**/*.ts", "src/**/*.tsx", "src/declarations.d.ts"]}