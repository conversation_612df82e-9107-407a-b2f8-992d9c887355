# Danh Sách Công Việc Triển Khai React Native App

## Tổng Quan Dự Án

- **Framework**: React Native 0.72.x
- **State Management**: Redux Toolkit
- **HTTP Client**: Axios
- **Navigation**: React Navigation v6
- **Th<PERSON> mục dự án**: `/Users/<USER>/Documents/GitHub/bao-hiem-xa-hoi-escs`
- ** Sử dụng yarn để cài đặt
---

## 1. Khởi tạo dự án React Native

### Mô tả

Tạo dự án React Native mới với phiên bản 0.72.x và cấu hình cơ bản

### C<PERSON>c bước thực hiện

```bash
# Khởi tạo dự án React Native
npx react-native@latest init BaoHiemXaHoiESCS --version 0.72.15

# Di chuyển nội dung vào thư mục hiện tại
mv BaoHiemXaHoiESCS/* .
mv BaoHiemXaHoiESCS/.* . 2>/dev/null || true
rmdir BaoHiemXaHoiESCS

# Kiểm tra phiên bản
npx react-native --version
```

### Files được tạo

- `package.json`
- `App.tsx`
- `index.js`
- `android/` và `ios/` folders
- `metro.config.js`

---

## 2. Cài đặt và cấu hình dependencies chính

### Mô tả

Cài đặt Redux Toolkit, Axios, React Navigation v6 và các thư viện UI cơ bản

### Dependencies cần cài đặt

```bash
# Redux Toolkit và React Redux
npm install @reduxjs/toolkit react-redux

# Axios cho API calls
npm install axios

# React Navigation v6
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context

# Vector Icons
npm install react-native-vector-icons
npm install --save-dev @types/react-native-vector-icons

# Gesture Handler (cho navigation)
npm install react-native-gesture-handler

# Async Storage
npm install @react-native-async-storage/async-storage

# Additional UI libraries
npm install react-native-paper
npm install react-native-elements
```

### Cấu hình iOS (nếu cần)

```bash
cd ios && pod install && cd ..
```

---

## 3. Thiết lập cấu trúc thư mục dự án

### Mô tả

Tạo cấu trúc thư mục theo best practices cho React Native app

### Cấu trúc thư mục

```
src/
├── components/          # Reusable components
│   ├── common/         # Common UI components
│   └── forms/          # Form components
├── screens/            # Screen components
├── navigation/         # Navigation configuration
├── store/             # Redux store và slices
│   ├── slices/        # Redux slices
│   └── index.ts       # Store configuration
├── services/          # API services
│   ├── api.ts         # Axios configuration
│   └── endpoints/     # API endpoints
├── utils/             # Utility functions
├── constants/         # App constants
├── types/             # TypeScript types
├── hooks/             # Custom hooks
└── assets/            # Images, fonts, etc.
    ├── images/
    ├── icons/
    └── fonts/
```

### Commands để tạo thư mục

```bash
mkdir -p src/{components/{common,forms},screens,navigation,store/slices,services/endpoints,utils,constants,types,hooks,assets/{images,icons,fonts}}
```

---

## 4. Cấu hình Redux Store và Slices

### Mô tả

Thiết lập Redux Toolkit store, tạo các slice cơ bản và middleware

### File: `src/store/index.ts`

```typescript
import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { combineReducers } from "@reduxjs/toolkit";

// Import slices
import authSlice from "./slices/authSlice";
import userSlice from "./slices/userSlice";

const persistConfig = {
  key: "root",
  storage: AsyncStorage,
  whitelist: ["auth", "user"], // Chỉ persist những slice cần thiết
};

const rootReducer = combineReducers({
  auth: authSlice,
  user: userSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### File: `src/store/slices/authSlice.ts`

```typescript
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  user: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  token: null,
  user: null,
  loading: false,
  error: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (
      state,
      action: PayloadAction<{ token: string; user: any }>
    ) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.user = action.payload.user;
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.user = null;
    },
  },
});

export const { loginStart, loginSuccess, loginFailure, logout } =
  authSlice.actions;
export default authSlice.reducer;
```

---

## 5. Thiết lập Axios và API Client

### Mô tả

Cấu hình Axios interceptors, base URL, error handling và API client

### File: `src/services/api.ts`

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Base URL - thay đổi theo environment
const BASE_URL = __DEV__
  ? "http://localhost:3000/api"
  : "https://your-production-api.com/api";

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem("auth_token");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized - logout user
          await AsyncStorage.removeItem("auth_token");
          // Redirect to login screen
        }
        return Promise.reject(error);
      }
    );
  }

  // HTTP Methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config);
    return response.data;
  }

  async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }

  async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### File: `src/services/endpoints/authEndpoints.ts`

```typescript
import { apiClient } from "../api";

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

export const authEndpoints = {
  login: (data: LoginRequest): Promise<LoginResponse> =>
    apiClient.post("/auth/login", data),

  logout: (): Promise<void> => apiClient.post("/auth/logout"),

  refreshToken: (): Promise<{ token: string }> =>
    apiClient.post("/auth/refresh"),

  getProfile: (): Promise<any> => apiClient.get("/auth/profile"),
};
```

---

## 6. Cấu hình React Navigation

### Mô tả

Thiết lập navigation container, stack navigator và tab navigator

### File: `src/navigation/AppNavigator.tsx`

```typescript
import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useSelector } from "react-redux";
import { RootState } from "../store";

// Import screens
import LoginScreen from "../screens/auth/LoginScreen";
import HomeScreen from "../screens/main/HomeScreen";
import ProfileScreen from "../screens/main/ProfileScreen";

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Profile: undefined;
};

const RootStack = createStackNavigator<RootStackParamList>();
const AuthStack = createStackNavigator<AuthStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

// Auth Navigator
function AuthNavigator() {
  return (
    <AuthStack.Navigator screenOptions={{ headerShown: false }}>
      <AuthStack.Screen name="Login" component={LoginScreen} />
    </AuthStack.Navigator>
  );
}

// Main Tab Navigator
function MainNavigator() {
  return (
    <MainTab.Navigator>
      <MainTab.Screen name="Home" component={HomeScreen} />
      <MainTab.Screen name="Profile" component={ProfileScreen} />
    </MainTab.Navigator>
  );
}

// Root Navigator
export default function AppNavigator() {
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );

  return (
    <NavigationContainer>
      <RootStack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <RootStack.Screen name="Main" component={MainNavigator} />
        ) : (
          <RootStack.Screen name="Auth" component={AuthNavigator} />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
}
```

---

## 7. Tạo components cơ bản và UI Kit

### Mô tả

Tạo các component tái sử dụng như Button, Input, Card, Loading, etc.

### File: `src/components/common/Button.tsx`

```typescript
import React from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
} from "react-native";

interface ButtonProps {
  title: string;
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  variant?: "primary" | "secondary" | "outline";
  size?: "small" | "medium" | "large";
}

export default function Button({
  title,
  onPress,
  loading = false,
  disabled = false,
  variant = "primary",
  size = "medium",
}: ButtonProps) {
  return (
    <TouchableOpacity
      style={[
        styles.button,
        styles[variant],
        styles[size],
        (disabled || loading) && styles.disabled,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator color="#fff" />
      ) : (
        <Text style={[styles.text, styles[`${variant}Text`]]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  primary: {
    backgroundColor: "#007AFF",
  },
  secondary: {
    backgroundColor: "#6C757D",
  },
  outline: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "#007AFF",
  },
  small: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  medium: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  large: {
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  disabled: {
    opacity: 0.6,
  },
  text: {
    fontWeight: "600",
  },
  primaryText: {
    color: "#fff",
  },
  secondaryText: {
    color: "#fff",
  },
  outlineText: {
    color: "#007AFF",
  },
});
```

### File: `src/components/common/Input.tsx`

```typescript
import React from "react";
import { TextInput, View, Text, StyleSheet } from "react-native";

interface InputProps {
  label?: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  error?: string;
  multiline?: boolean;
  numberOfLines?: number;
}

export default function Input({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  error,
  multiline = false,
  numberOfLines = 1,
}: InputProps) {
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TextInput
        style={[
          styles.input,
          error && styles.inputError,
          multiline && styles.multiline,
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        secureTextEntry={secureTextEntry}
        multiline={multiline}
        numberOfLines={numberOfLines}
      />
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
    color: "#333",
  },
  input: {
    borderWidth: 1,
    borderColor: "#DDD",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  inputError: {
    borderColor: "#FF3B30",
  },
  multiline: {
    textAlignVertical: "top",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginTop: 4,
  },
});
```

### File: `src/components/common/Loading.tsx`

```typescript
import React from "react";
import { View, ActivityIndicator, Text, StyleSheet } from "react-native";

interface LoadingProps {
  message?: string;
  size?: "small" | "large";
}

export default function Loading({ message, size = "large" }: LoadingProps) {
  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color="#007AFF" />
      {message && <Text style={styles.message}>{message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  message: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});
```

---

## 8. Thiết lập styling và theme system

### Mô tả

Cấu hình theme provider, colors, typography và responsive design

### File: `src/constants/theme.ts`

```typescript
export const colors = {
  primary: "#007AFF",
  secondary: "#6C757D",
  success: "#28A745",
  danger: "#FF3B30",
  warning: "#FF9500",
  info: "#17A2B8",
  light: "#F8F9FA",
  dark: "#343A40",
  white: "#FFFFFF",
  black: "#000000",
  gray: {
    100: "#F8F9FA",
    200: "#E9ECEF",
    300: "#DEE2E6",
    400: "#CED4DA",
    500: "#ADB5BD",
    600: "#6C757D",
    700: "#495057",
    800: "#343A40",
    900: "#212529",
  },
};

export const typography = {
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    "2xl": 24,
    "3xl": 30,
    "4xl": 36,
  },
  fontWeight: {
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  "2xl": 48,
  "3xl": 64,
};

export const borderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};

export const shadows = {
  sm: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  base: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  lg: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
};
```

---

## 9. Cấu hình build và deployment

### Mô tả

Thiết lập scripts build, environment variables và cấu hình deployment

### File: `src/constants/config.ts`

```typescript
export const config = {
  API_BASE_URL: __DEV__
    ? "http://localhost:3000/api"
    : "https://your-production-api.com/api",

  APP_NAME: "Sàn Bảo Hiểm",
  APP_VERSION: "1.0.0",

  // Environment flags
  ENABLE_FLIPPER: __DEV__,
  ENABLE_LOGS: __DEV__,

  // API timeouts
  API_TIMEOUT: 10000,

  // Storage keys
  STORAGE_KEYS: {
    AUTH_TOKEN: "auth_token",
    USER_DATA: "user_data",
    APP_SETTINGS: "app_settings",
  },
};
```

### File: `package.json` scripts (thêm vào)

```json
{
  "scripts": {
    "android:dev": "react-native run-android --variant=debug",
    "android:release": "react-native run-android --variant=release",
    "ios:dev": "react-native run-ios --configuration Debug",
    "ios:release": "react-native run-ios --configuration Release",
    "build:android": "cd android && ./gradlew assembleRelease",
    "build:ios": "react-native build-ios --configuration Release",
    "clean": "react-native clean",
    "clean:android": "cd android && ./gradlew clean",
    "clean:ios": "cd ios && xcodebuild clean",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix",
    "type-check": "tsc --noEmit"
  }
}
```

### Cấu hình Environment Variables

```bash
# Tạo file .env
echo "API_BASE_URL=http://localhost:3000/api" > .env
echo "APP_NAME=BaoHiemXaHoiESCS" >> .env
echo "APP_VERSION=1.0.0" >> .env

# Cài đặt react-native-config
npm install react-native-config
```

---

## 10. Tạo documentation và testing setup

### Mô tả

Viết README, setup testing framework và tạo sample tests

### File: `README.md`

````markdown
# Bảo Hiểm Xã Hội ESCS - React Native App

## Tổng quan

Ứng dụng React Native cho hệ thống Bảo hiểm xã hội ESCS

## Yêu cầu hệ thống

- Node.js >= 16
- React Native CLI
- Android Studio (cho Android)
- Xcode (cho iOS)

## Cài đặt

### 1. Clone repository

```bash
git clone <repository-url>
cd bao-hiem-xa-hoi-escs
```
````

### 2. Cài đặt dependencies

```bash
npm install
```

### 3. Cài đặt iOS dependencies (chỉ macOS)

```bash
cd ios && pod install && cd ..
```

### 4. Chạy ứng dụng

```bash
# Android
npm run android:dev

# iOS
npm run ios:dev
```

## Cấu trúc dự án

```
src/
├── components/     # Reusable components
├── screens/        # Screen components
├── navigation/     # Navigation setup
├── store/          # Redux store
├── services/       # API services
├── utils/          # Utility functions
├── constants/      # Constants và theme
├── types/          # TypeScript types
└── hooks/          # Custom hooks
```

## Scripts có sẵn

- `npm run android:dev` - Chạy Android debug
- `npm run ios:dev` - Chạy iOS debug
- `npm run test` - Chạy tests
- `npm run lint` - Kiểm tra code style
- `npm run build:android` - Build Android release

## Testing

```bash
# Chạy tất cả tests
npm test

# Chạy tests với watch mode
npm run test:watch
```

## Deployment

Xem hướng dẫn deployment trong file `DEPLOYMENT.md`

````

### File: `jest.config.js`
```javascript
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/utils/testSetup.ts'],
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js)',
    '**/*.(test|spec).(ts|tsx|js)',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
};
````

### File: `src/utils/testSetup.ts`

```typescript
import "react-native-gesture-handler/jestSetup";

// Mock AsyncStorage
jest.mock("@react-native-async-storage/async-storage", () =>
  require("@react-native-async-storage/async-storage/jest/async-storage-mock")
);

// Mock react-native-vector-icons
jest.mock("react-native-vector-icons/MaterialIcons", () => "Icon");

// Mock navigation
jest.mock("@react-navigation/native", () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));
```

### Sample Test: `src/components/common/__tests__/Button.test.tsx`

```typescript
import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import Button from "../Button";

describe("Button Component", () => {
  it("renders correctly", () => {
    const { getByText } = render(
      <Button title="Test Button" onPress={() => {}} />
    );
    expect(getByText("Test Button")).toBeTruthy();
  });

  it("calls onPress when pressed", () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );

    fireEvent.press(getByText("Test Button"));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it("shows loading indicator when loading", () => {
    const { getByTestId } = render(
      <Button title="Test Button" onPress={() => {}} loading />
    );
    expect(getByTestId("activity-indicator")).toBeTruthy();
  });
});
```

---

## Thứ tự thực hiện

### Giai đoạn 1: Setup cơ bản (1-2 ngày)

1. Khởi tạo dự án React Native
2. Cài đặt dependencies chính
3. Thiết lập cấu trúc thư mục

### Giai đoạn 2: Core Architecture (2-3 ngày)

4. Cấu hình Redux Store và Slices
5. Thiết lập Axios và API Client
6. Cấu hình React Navigation

### Giai đoạn 3: UI Development (3-4 ngày)

7. Tạo components cơ bản và UI Kit
8. Thiết lập styling và theme system

### Giai đoạn 4: Production Ready (1-2 ngày)

9. Cấu hình build và deployment
10. Tạo documentation và testing setup

---

## Lưu ý quan trọng

### Trước khi bắt đầu:

- Đảm bảo đã cài đặt Node.js, React Native CLI
- Chuẩn bị Android Studio hoặc Xcode
- Kiểm tra kết nối mạng để tải dependencies

### Trong quá trình phát triển:

- Luôn test trên cả Android và iOS
- Commit code thường xuyên
- Viết tests cho các component quan trọng
- Tuân thủ coding standards và ESLint rules

### Sau khi hoàn thành:

- Chạy full test suite
- Build và test trên device thật
- Chuẩn bị cho deployment lên store

---

**Tổng thời gian ước tính: 7-11 ngày làm việc**

_File này có thể được sử dụng như một checklist để theo dõi tiến độ triển khai dự án React Native._
