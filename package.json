{"name": "BaoHiemXaHoiESCS", "version": "1.0.0", "private": true, "scripts": {"android": "npx react-native run-android", "ios": "npx react-native run-ios --simulator='iPhone 16 Pro Max'", "ip14": "npx react-native run-ios --simulator='iPhone 14'", "ipad": "npx react-native run-ios --simulator='iPad Air (5th generation)'", "lint": "eslint .", "start": "watchman watch-del-all && react-native start --reset-cache", "test": "jest", "devtools": "react-devtools", "android:dev": "react-native run-android --variant=debug", "android:release": "react-native run-android --variant=release", "ios:dev": "react-native run-ios --configuration Debug", "ios:release": "react-native run-ios --configuration Release", "build-apk": "cd android && ./gradlew assembleRelease", "build-aab": "cd android && ./gradlew bundleRelease && cd ../", "build:ios": "react-native build-ios --configuration Release", "clean": "react-native clean", "clean:android": "cd android && ./gradlew clean", "clean:ios": "cd ios && xcodebuild clean", "test:watch": "jest --watch", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "postinstall": "patch-package", "export-android": "mkdir -p android/output && react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/output/index.android.bundle --assets-dest android/output  && cd android && find output -type f | zip index.android.bundle.zip -@ && cd .. && rm -rf android/output", "export-ios": "mkdir -p ios/output && react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/output/main.jsbundle --assets-dest ios/output && cd ios && find output -type f | zip main.jsbundle.zip -@ && cd .. && rm -rf ios/output"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/datetimepicker": "8.2.0", "@react-native-ml-kit/text-recognition": "1.5.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.11.0", "crypto-js": "^4.2.0", "iconsax-react-native": "^0.0.8", "isomorphic-git": "^1.33.1", "js-sha256": "^0.11.1", "jsrsasign": "^11.1.0", "lucide-react-native": "^0.554.0", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "18.2.0", "react-fast-compare": "^3.2.2", "react-hook-form": "^7.54.2", "react-native": "0.74.7", "react-native-blob-util": "^0.22.2", "react-native-device-info": "^14.0.4", "react-native-document-scanner-plugin": "^1.0.1", "react-native-fast-image": "^8.6.3", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.16.2", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-ota-hot-update": "^2.3.4", "react-native-permissions": "^5.4.2", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "3.10.1", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "4.4.0", "react-native-svg": "15.11.2", "react-native-swiper-flatlist": "^3.2.5", "react-native-toast-notifications": "^3.4.0", "react-native-vector-icons": "^10.3.0", "react-native-vision-camera": "^4.7.2", "react-native-webview": "^13.16.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.74.83", "@react-native/metro-config": "^0.74.83", "@tsconfig/react-native": "^3.0.5", "@types/crypto-js": "^4.2.2", "@types/jsrsasign": "^10.5.15", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "^0.77.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^2.4.1", "react-devtools": "^6.1.5", "react-test-renderer": "18.2.0", "typescript": "^5.0.4"}, "engines": {"node": ">=16"}}