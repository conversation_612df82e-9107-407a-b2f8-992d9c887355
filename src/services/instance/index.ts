import {ACTION_CODE, AXIOS_CONFIGS, CONFIG_SERVER} from '@constants/axios';
import {store} from '@store/index';
import {deepParse} from '@utils/object';
import axios, {AxiosResponse, InternalAxiosRequestConfig} from 'axios';
import {KJUR, utf8tob64u} from 'jsrsasign';

export const axiosInstance = axios.create(AXIOS_CONFIGS);

/**
 * Unified signature generation function - Using same method as authEndpoints
 * Handles both login and authenticated requests with proper signature generation
 * @param payload - Request body data
 * @param secretKey - Secret key for signature generation
 * @returns Generated signature string
 */
const generateSignature = (payload: any, secretKey: string): string => {
  // 1. Stringify the payload
  const payloadString = JSON.stringify(payload);

  // 2. Encode the payload string using Base64URL
  const base64UrlPayload = utf8tob64u(payloadString);

  // 3. Concatenate with a dot and the secret key
  const signatureInput = base64UrlPayload + '.' + secretKey;

  // 4. Calculate the SHA256 hash using KJUR (same as authEndpoints)
  const md = new KJUR.crypto.MessageDigest({alg: 'sha256', prov: 'cryptojs'});
  md.updateString(signatureInput);
  return md.digest();
};

/**
 * Generate signature for FormData uploads - Using same method as authEndpoints
 * Handles special cases like Excel import and regular file uploads
 * @param data - FormData object converted to key-value pairs
 * @param token - Authentication token
 * @param secretKey - Secret key for signature generation
 * @returns Generated signature string
 */
const generateFormDataSignature = (data: {[key: string]: any}, token: string, secretKey: string): string => {
  let formatPayload = '';

  // Special signature format for Excel import
  if (data.actionCode === ACTION_CODE.UPLOAD_EXCEL_FILE) {
    const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd format
    formatPayload = `epartnercode=${CONFIG_SERVER.E_PARTNER_CODE}&eauthtoken=${token}&time=${currentDate}`;
  } else {
    // Default signature format for other file uploads
    formatPayload = `ma_doi_tac_ql=${data.ma_doi_tac_ql}&bt=${data.bt}&ten=${data.ten}&nhom=${data.nhom}`;
  }

  // Use same signature generation method as authEndpoints
  const base64UrlPayload = utf8tob64u(formatPayload);
  const signatureInput = base64UrlPayload + '.' + secretKey;
  const md = new KJUR.crypto.MessageDigest({alg: 'sha256', prov: 'cryptojs'});
  md.updateString(signatureInput);
  return md.digest();
};

const handleError = (error: {config: any; data: any; headers: any; request: any; status: number; statusText: ''}) => {
  // handle error
  return Promise.reject(error);
};

//có response từ server
const handleResponse = (response: {statusCode: number; data: any}) => {
  // handle response
  return deepParse(response);
};

axiosInstance.interceptors.request.use(
  async (request: InternalAxiosRequestConfig<any>) => {
    // Lấy token trực tiếp từ state.auth.token để tránh lỗi khi user là null
    const {token} = store.getState().auth;

    // Handle login requests (detect by URL since data might be stringified)
    const isLoginRequest = request.url === 'api/auth/get-token' || request.data?.actionCode === ACTION_CODE.DANG_NHAP;

    if (isLoginRequest) {
      // Parse data if it's already stringified
      let parsedData = request.data;
      if (typeof request.data === 'string') {
        try {
          parsedData = JSON.parse(request.data);
        } catch (e) {
          // ignore
        }
      }

      // Create clean payload without actionCode for signature generation (like original authEndpoints)
      const cleanPayload = {
        tai_khoan: parsedData.tai_khoan,
        mat_khau: parsedData.mat_khau,
        nguon: parsedData.nguon,
      };

      const signature = generateSignature(cleanPayload, CONFIG_SERVER.SECRET_KEY);

      // Update request data to clean payload (remove actionCode)
      request.data = cleanPayload;

      request.headers.set('eAction', ACTION_CODE.DANG_NHAP);
      request.headers.set('eAuthToken', CONFIG_SERVER.E_AUTH_TOKEN);
      request.headers.set('eSignature', signature);
      request.headers.set('Content-Type', 'application/json');
    }
    // Handle authenticated requests
    else if (token && request.data?.actionCode !== ACTION_CODE.DANG_NHAP) {
      // Handle FormData uploads (file uploads)
      if (request.data instanceof FormData || (request.data && request.data._parts)) {
        const data: {[key: string]: any} = {
          ma_doi_tac_ql: '',
          bt: '',
          ten: '',
          nhom: '',
          actionCode: '',
        };

        // FormData trong React Native không có entries(), dùng _parts để thay thế
        if (request.data._parts) {
          (request.data as any)._parts.forEach((part: any) => {
            data[part[0]] = part[1];
          });
        }

        logger.log('🚀 FormData detected, data extracted:', data);

        const eSignature = generateFormDataSignature(data, token, CONFIG_SERVER.SECRET_KEY);

        // Set headers for FormData uploads
        request.headers.set('eAction', data.actionCode);
        request.headers.set('eAuthToken', token);
        request.headers.set('eSignature', eSignature);
        request.headers.set('Content-Type', 'multipart/form-data');

        logger.log('🚀 FormData headers set:', {
          eAction: data.actionCode,
          eAuthToken: token ? 'present' : 'missing',
          eSignature: eSignature ? 'generated' : 'missing',
        });
      }
      // Handle regular JSON requests
      else {
        const actionCode = request.data?.actionCode;
        const requestPayload = {...request.data};
        delete requestPayload.actionCode;

        const eSignature = generateSignature(requestPayload, CONFIG_SERVER.SECRET_KEY);

        // Set headers for JSON requests
        request.headers.set('eAction', actionCode);
        request.headers.set('eAuthToken', token);
        request.headers.set('eSignature', eSignature);
        request.headers.set('Content-Type', 'application/json');

        // Update request data without actionCode
        request.data = requestPayload;
      }
    }
    // Handle case when no token is available but request needs authentication
    else if (!token && request.data?.actionCode && request.data?.actionCode !== ACTION_CODE.DANG_NHAP) {
      console.warn('🚨 No authentication token available for request:', request.data?.actionCode);
      return Promise.reject(new Error('Authentication token required but not available'));
    }

    logger.log('🚀 ~ request:', {
      url: request.url,
      method: request.method,
      headers: request.headers,
      hasToken: !!token,
      actionCode: request.data?.actionCode || 'none',
    });
    return request;
  },
  (error: {config: any; data: any; headers: any; request: any; status: number; statusText: ''}) => {
    return handleError(error);
  },
);

axiosInstance.interceptors.response.use(
  <T>(response: AxiosResponse<T>) => {
    // For blob responses (like PDF export), return raw response
    if (response.config.responseType === 'blob') {
      logger.log('🔄 Blob response detected, returning raw response');
      return response;
    }

    // Handle successful responses with data processing
    if (response.data !== null && response.data !== undefined) {
      return handleResponse(response.data as any);
    } else {
      return response;
    }
  },
  (error: {config: any; data: any; headers: any; request: any; status: number; statusText: ''}) => {
    logger.log('🚀 ~ response error:', error);
    return handleError(error);
  },
);

/**
 * Update default headers for axiosInstance
 * @param headers - Headers object to merge with existing headers
 */
export const updateHeader = (headers: any) => (axiosInstance.defaults.headers = {...axiosInstance.defaults.headers, ...headers});

/**
 * Unified API call function - Example usage for developers
 * This demonstrates how simple API calls become with the unified handler
 *
 * @example
 * // Login example
 * const loginResponse = await axiosInstance.post(URL_API.LOGIN, {
 *   tai_khoan: "username",
 *   mat_khau: "password",
 *   actionCode: ACTION_CODE.DANG_NHAP
 * });
 *
 * @example
 * // Authenticated API call example
 * const dataResponse = await axiosInstance.post(URL_API.COMMON_EXECUTE, {
 *   param1: "value1",
 *   param2: "value2",
 *   actionCode: ACTION_CODE.SOME_ACTION
 * });
 *
 * @example
 * // File upload example
 * const formData = new FormData();
 * formData.append('file', fileBlob);
 * formData.append('actionCode', ACTION_CODE.UPLOAD_FILE);
 * const uploadResponse = await axiosInstance.post(URL_API.UPLOAD_FILE, formData);
 */
