import axios from 'axios';
import {PhotoFile} from 'react-native-vision-camera';
import {Asset} from 'react-native-image-picker';
import {CONFIG_SERVER, URL_API, ACTION_CODE} from '@constants/axios';
import {axiosInstance} from './instance';

// Cấu trúc response thực tế từ API
// Axios response structure: response.data = -1, response.output = {bt, url_file}
interface UploadApiResponse extends Record<string, any> {
  // This can be -1 (success) or other values
}

// Interface cho response được xử lý
interface UploadImageResponse {
  bt_file: string;
  url_file: string;
}

type ImageSource = PhotoFile | Asset | {path: string; fileName?: string};

export const uploadImage = async (photo: ImageSource, fileName?: string): Promise<UploadImageResponse> => {
  try {
    const formData = new FormData();

    let uri: string;
    if ('uri' in photo && photo.uri) {
      // Image Picker Asset
      uri = photo.uri;
    } else if ('path' in photo) {
      // Vision Camera Photo
      uri = `file://${photo.path}`;
    } else {
      throw new Error('Invalid photo source');
    }

    // Tạo tên file từ parameter hoặc default
    const finalFileName = fileName || ('fileName' in photo ? photo.fileName : undefined) || `photo_${Date.now()}.jpg`;

    // Tạo file object cho FormData
    formData.append('file', {
      uri,
      type: 'image/jpeg',
      name: finalFileName,
    } as any);

    // Thêm các parameters cần thiết theo cấu trúc API
    formData.append('ma_doi_tac_ql', 'ESCS');
    formData.append('bt', '0');
    formData.append('ten', finalFileName);
    formData.append('quy_trinh', 'CA_NHAN');
    formData.append('nhom', 'CA_NHAN');
    formData.append('stt', '0');
    formData.append('file_public', '1');
    formData.append('id_folder', '-1');
    formData.append('thumbnail', '0');
    formData.append('actionCode', ACTION_CODE.UPLOAD_FILE);

    console.log('🚀 Upload request params:', {
      url: URL_API.UPLOAD_FILE,
      actionCode: ACTION_CODE.UPLOAD_FILE,
      fileName: 'photo.jpg',
    });

    const response = await axiosInstance.post<UploadApiResponse>(URL_API.UPLOAD_FILE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('🚀 ~ uploadImage ~ response:', response);

    console.log('🚀 Upload response:', JSON.stringify(response.data, null, 2));

    // Xử lý response theo cấu trúc thực tế
    // response.data = -1 có nghĩa là thành công, response.output chứa thông tin file
    const responseData = response as any;
    if (responseData.data === -1 && responseData.output?.bt && responseData.output?.url_file) {
      // Success case: response.data = -1, response.output = {bt: number, url_file: string}
      console.log('✅ Upload successful with response.data = -1');
      return {
        bt_file: responseData.output.bt.toString(),
        url_file: responseData.output.url_file,
      };
    }
    // Fallback cases cho các format khác có thể có
    else if (response.data?.output?.bt && response.data?.output?.url_file) {
      // Format 1: {data: number, output: {bt: number, url_file: string}}
      console.log('✅ Upload successful with output structure');
      return {
        bt_file: response.data.output.bt.toString(),
        url_file: response.data.output.url_file,
      };
    } else if (response.data?.bt_file && response.data?.url_file) {
      // Format 2: {bt_file: string, url_file: string}
      console.log('✅ Upload successful with bt_file structure');
      return {
        bt_file: response.data.bt_file,
        url_file: response.data.url_file,
      };
    } else if (response.data?.bt && response.data?.url_file) {
      // Format 3: {bt: number, url_file: string}
      console.log('✅ Upload successful with bt structure');
      return {
        bt_file: response.data.bt.toString(),
        url_file: response.data.url_file,
      };
    } else {
      console.error('🚨 Unexpected response structure:', response.data);
      throw new Error(`Invalid response structure from upload API. Response: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};
