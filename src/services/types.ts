declare type i18nbase = 'labels' | 'errors' | 'messages' | 'placeholders' | 'actions' | 'languages';
export declare type I18nApp = Record<i18nbase, Record<string, string | Record<string, string>>>;

export interface LooseObject {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

export interface IParamBaseLoad {
  page: number | string;
  limit: number | string;
  id: number | string;
}

export interface IResponseApi<T> {
  data_info?: any;
  out_value?: any;
  state_info?: any;
}

export interface IResponseCategory {
  image: any;
  name: string;
  type: string;
}

export interface IGroupParams extends IParamBaseLoad {}
export interface IGroup {}

