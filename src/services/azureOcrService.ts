import axios from 'axios';
import {AZURE_COMPUTER_VISION_CONFIG} from 'src/config/azure';

const {endpoint, apiKey} = AZURE_COMPUTER_VISION_CONFIG;

// Hàm chờ để kiểm tra kết quả
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * G<PERSON>i yêu cầu phân tích ảnh đến Azure Read API
 * @param imageUri URI của ảnh (local file path)
 * @returns URL để lấy kết quả phân tích
 */
const readTextFromImage = async (imageUri: string): Promise<string | null> => {
  const readApiUrl = `${endpoint}/vision/v3.2/read/analyze`;

  try {
    // Đọc dữ liệu ảnh từ URI và lấy blob
    const imageResponse = await fetch(imageUri);
    const imageBlob = await imageResponse.blob();

    // Gửi dữ liệu ảnh bằng fetch API
    const response = await fetch(readApiUrl, {
      method: 'POST',
      headers: {
        'Ocp-Apim-Subscription-Key': apiKey,
        'Content-Type': 'application/octet-stream',
      },
      body: imageBlob,
    });

    // Lấy URL để kiểm tra kết quả từ header
    if (response.status === 202) {
      return response.headers.get('operation-location');
    }
  } catch (error) {
    console.error('Azure OCR Error - readTextFromImage:', error);
  }

  return null;
};

/**
 * Lấy kết quả phân tích từ URL đã nhận
 * @param operationUrl URL để lấy kết quả
 * @returns Dữ liệu OCR đã được xử lý
 */
const getReadResult = async (operationUrl: string): Promise<any> => {
  let result: any = null;

  try {
    for (let i = 0; i < 10; i++) {
      const response = await axios.get(operationUrl, {
        headers: {
          'Ocp-Apim-Subscription-Key': apiKey,
        },
      });

      result = response.data;

      if (result.status === 'succeeded' || result.status === 'failed') {
        break;
      }

      await sleep(1000); // Chờ 1 giây trước khi kiểm tra lại
    }
  } catch (error) {
    console.error('Azure OCR Error - getReadResult:', error);
  }

  return result;
};

export const AzureOcrService = {
  readTextFromImage,
  getReadResult,
};
