export default {
  API_TIMEOUT: 300000,

  CAU_HINH_UAT: {
    BASE_URL_API: 'https://gic-cloudapi.escs.vn/',
    SECRET_KEY: '46b4791de283ff93312ef90f034d4a93',
    MA_DOI_TAC: 'GIC',
    HEADER_REQUEST: {
      alg: 'HS256',
      typ: 'JWT',
      cty: 'gic-api;v=1',
      cat: 'public',
      partner: 'XTI_MOBILE',
      user: 'gic_mobile',
      envcode: 'DEV',
    },
    E_PARTNER_CODE: 'GIC_MOBILE',
    E_ENVIRONMENT: 'DEV',
  },

  CAU_HINH_DEV: {
    BASE_URL_API: 'https://gic-cloudapi.escs.vn/',
    SECRET_KEY: '46b4791de283ff93312ef90f034d4a93',
    MA_DOI_TAC: 'GIC',
    HEADER_REQUEST: {
      alg: 'HS256',
      typ: 'JWT',
      cty: 'gic-api;v=1',
      cat: 'public',
      partner: 'XTI_MOBILE',
      user: 'gic_mobile',
      envcode: 'DEV',
    },
    E_PARTNER_CODE: 'GIC_MOBILE',
    E_ENVIRONMENT: 'DEV',
  },

  CAU_HINH_PRODUCT: {
    BASE_URL_API: 'https://cloudapiuat.escs.vn/',
    SECRET_KEY: '568c85c6c8c5e64ee71b098028a4d7a7',
    MA_DOI_TAC: 'CTYBHABC',
    HEADER_REQUEST: {
      alg: 'HS256',
      typ: 'JWT',
      cty: 'bsh-api;v=1',
      cat: 'public',
      partner: 'ESCS_MOBILE',
      user: 'escs_mobile',
      envcode: 'DEV',
    },
    E_PARTNER_CODE: 'ESCS_MOBILE',
    E_ENVIRONMENT: 'DEV',
  },

  CONFIG_SERVER: {
    BASE_URL_API: 'https://sanbaohiem-apigateway.escs.vn/',
    SECRET_KEY: 'a2ecf13450948f1e81f754445f50d4d4', // Secret key cho signature mới
    E_PARTNER_CODE: 'PORTAL',
    E_AUTH_TOKEN: '3c38d033466621cac31b7113b38fcfe9',
    // Cấu hình cũ cho JWT signature (giữ lại để tham khảo)
    HEADER_REQUEST: {
      alg: 'HS256',
      typ: 'JWT',
      cty: 'gic-api;v=1',
      cat: 'public',
      partner: 'PORTAL',
      user: 'escs_mobile',
      envcode: 'DEV',
    },
  },

  AXIOS_STATUS_CODE: {
    RESPONSE_SUCCESS: 0,
    EXPIRED_SESSION: 6,
    TIMEOUT: 300,
    NOT_FOUND: 404,
    CANCEL_REQUEST_ERROR: 1002,
    UNKNOWN_ERROR: 600,
    INTERNET_ERROR: 601,
    HANDLE_ERROR: 1000,
    CONFIG_ERROR: 1001,
  },

  SERVER_RESPONSE_STATUS: {
    OK: 'OK',
    NOT_OK: 'NotOK',
  },

  SERVER_RESPONSE_MESSAGE: {
    ACCESS_TOKEN_EXPIRED: 'Access Token đã hết hạn.',
    SESSION_EXPIRED: 'Phiên làm việc đã hết hạn',
    INVALID_AUTH: 'Lỗi xác thực tài khoản',
    INVALID_DEVICE: 'Tài khoản đang được đăng nhập ở thiết bị khác.',
    SU_DUNG_ACCESS_TOKEN_DOI_TAC_KHAC: 'Không được sử dụng access token của đối tác khác.',
    THONG_TIN_ACCESS_TOKEN_KHONG_HOP_LE: 'Thông tin access token không hợp lệ.',
    LOI_XAC_THUC_DO_CAU_TRUC_REFRESH_TOKEN_THAY_DOI: 'Lỗi trong quá trình xác thực refresh token do cấu trúc đã bị thay đổi.',
  },

  SERVER_RESPONSE_CODE: {
    ACCESS_TOKEN_EXPIRED: 'ERROR_CONN001',
    REFRESH_TOKEN_EXPIRED: 'ERROR_CONN001',
    INVALID_AUTH: 'INVALID_AUTH',
    INVALID_DEVICE: 'INVALID_DEVICE',
  },

  URL_API: {
    EXECUTE: 'api/esmartclaim/excute',
    LOGIN: 'api/auth/get-token', // đăng nhập theo tiêu chuẩn mới
    REFRESH_TOKEN: 'api/esmartclaim/refresh-token', //làm mới refresh token
    UPLOAD_FILE: 'api/esmartclaim/upload-file', //upload file
    MERGE_TO_PDF_FILE: 'api/esmartclaim/in-hoa-don', //in hoá đơn
    AUTHEN_CALL: 'api/esmartclaim/authen-call',
    EXPORT_PDF_BASE64: 'api/esmartclaim/export-pdf-base64',
    SIGNATURE_FILE: 'api/esmartclaim/signature-file',
    REMOVE_FILE: 'api/esmartclaim/remove-file',
    CREATE_SAVE_FILE: 'api/esmartclaim/create-save-file',
    GET_FILE_THUMBNAIL: 'api/esmartclaim/get-file-thumnail',
    GET_FILE_THUMBNAIL_DETAIL: '/api/esmartclaim/get-file',
    SEND_CHAT: 'api/esmartclaim/chat',
    CROP_IMAGE: 'api/esmartclaim/gateway/crop-image',
    PHE_DUYET_HO_SO: 'api/esmartclaim/approve',
    HUY_DUYET_HO_SO: 'api/esmartclaim/unapprove',
    XEM_TINH_TRANG_THANH_TOAN_PHI: 'api/partner/get-payment',
    TRA_CUU_GCN_XE_DOI_TAC: 'api/partner/search-policy',
    XEM_GCN_XE_DOI_TAC: 'api/partner/view-policy',
    CONNECT_FIREBASE: 'api/esmartclaim/add-connect',
    DISCONNECT_FIREBASE: 'api/esmartclaim/remove-connect',
    CHECK_APP_VERSION: 'api/app/get-version',
    READ_QR_CODE: 'api/health/read-qrcode',
    TAO_QR_CODE_HS_XE: 'api/carclaim/receive',
    TAO_QR_CODE_HS_NG: 'api/health/create',
    DOC_DU_LIEU_OCR: 'api/esmartclaim/price-quotation',
    LAY_DU_LIEU_OCR: 'api/esmartclaim/get-price-quotation',
    GET_NUMBER_CLAIM: 'api/carclaim/get-number-claim',
    GET_NUMBER_CLAIM_XM: 'api/motoclaim/get-number-claim',
    UPLOAD_VIDEO: 'api/esmartclaim/upload-video',
    GET_LIST_VIDEO: 'api/carclaim/get-list-video',
    GET_URL_VIDEO: 'api/carclaim/video-link',
    READ_QR_CODE_DANH_GIA_RUI_RO: 'api/contract/read-qrcode',
    LAY_UOC_TON_THAT_THEO_GARA: 'api/carclaim/estimate-money',
    NHAP_YC_TAM_UNG: 'api/carclaim/tam-ung-nh',
    XOA_TAM_UNG_BT: 'api/carclaim/tam-ung-xoa',
    XOA_HS_THU_DOI: 'api/carclaim/ntba-xoa',
    NHAP_HS_THU_DOI: 'api/carclaim/ntba-nh',
    WRITE_LOG: 'api/common/writelog',
    DONG_HS_THANH_TOAN_XE_OTO: 'api/esmartclaim/close-claim',
    HUY_DONG_HS_THANH_TOAN_XE_OTO: 'api/esmartclaim/un-close-claim',
    DONG_HS_THANH_TOAN_CON_NG: 'api/health/close-claim',
    HUY_DONG_HS_THANH_TOAN_CON_NG: 'api/health/un-close-claim',
    KET_THUC_GIAM_DINH_OTO: '/api/esmartclaim/ket-thuc-gd',
    HUY_KET_THUC_GIAM_DINH_OTO: 'api/esmartclaim/huy-ket-thuc-gd',
    NHAN_HO_SO_GIAM_DINH: 'api/carclaim/receive-assessment',
    // CHUYEN_THANH_TOAN: 'api/esmartclaim/transfer-payment',
    CHUYEN_THANH_TOAN: 'api/carclaim/transfer-payment',
    // HUY_CHUYEN_THANH_TOAN: 'api/esmartclaim/un-transfer-payment',
    HUY_CHUYEN_THANH_TOAN: 'api/carclaim/un-transfer-payment',
    HUY_HO_SO_GIAM_DINH_OTO: 'api/esmartclaim/destroy',
    GO_HUY_HO_SO_GIAM_DINH_OTO: 'api/esmartclaim/undestroy',
    HUY_HO_SO_GIAM_DINH_XE_MAY: 'api/esmartclaim/destroy',
    GO_HUY_HO_SO_GIAM_DINH_XE_MAY: 'api/esmartclaim/undestroy',
    CHUYEN_THANH_TOAN_XE_MAY: 'api/motoclaim/transfer-payment',
    HUY_CHUYEN_THANH_TOAN_XE_MAY: 'api/motoclaim/un-transfer-payment',
    LAY_ANH_CAP_DON: 'api/partner/list-file-v0',
    XAC_DINH_LAI_DOI_TUONG_HD: 'api/carclaim/certificate/identify',
    CHUYEN_GIAM_DINH: 'api/carclaim/assessment/tranfer',
    // TÀI SẢN KỸ THUẬT
    PHE_DUYET_HO_SO_TSKT: 'api/esmartclaim/approve-other',
    HUY_DUYET_HO_SO_TSKT: 'api/esmartclaim/unapprove-other',
    TRA_CUU_GCN_TSKT: 'api/partner/otherclaim/search-policy',
    LAY_THONG_TIN_GCN_TSKT: 'api/partner/otherclaim/get-policy',
    UPLOAD_FILE_TSKT: 'api/esmartclaim/upload-file-tskt', //upload file

    /// HEALTH
    URL_LAY_SO_HO_SO_CON_NGUOI: 'api/health/insurance',
  },

  ACTION_CODE: {
    //APP
    GET_HOT_UPDATE_INFO: '3GGS5EGUDH8G56A',

    //FILE
    UPLOAD_FILE: '0YMQCQK7OULAXCG', //upload file
    //AUTH
    LOGIN: 'D2UHP2EWWRRXN68', //đăng nhập theo tiêu chuẩn mới
  },
};
