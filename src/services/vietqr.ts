// Service tích hợp VietQR API
export interface VietQRRequest {
  accountNo: string;
  accountName: string;
  acqId: string; // Mã ngân hàng
  amount?: number;
  addInfo?: string;
  format?: 'text' | 'compact';
  template?: 'compact' | 'compact2' | 'qr_only' | 'print';
}

export interface VietQRResponse {
  code: string;
  desc: string;
  data: {
    qrCode: string;
    qrDataURL: string;
  };
}

export interface BankInfo {
  id: number;
  name: string;
  code: string;
  bin: string;
  shortName: string;
  logo: string;
  transferSupported: number;
  lookupSupported: number;
}

class VietQRService {
  private baseURL = 'https://api.vietqr.io/v2';

  // L<PERSON>y danh sách ngân hàng
  async getBanks(): Promise<BankInfo[]> {
    try {
      const response = await fetch(`${this.baseURL}/banks`);
      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error('Error fetching banks:', error);
      throw new Error('Không thể lấy danh sách ngân hàng');
    }
  }

  // Tạo mã QR VietQR
  async generateQR(request: VietQRRequest): Promise<VietQRResponse> {
    try {
      const response = await fetch(`${this.baseURL}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountNo: request.accountNo,
          accountName: request.accountName,
          acqId: request.acqId,
          amount: request.amount || 0,
          addInfo: request.addInfo || '',
          format: request.format || 'text',
          template: request.template || 'compact',
        }),
      });

      const result = await response.json();

      if (result.code === '00') {
        return result;
      } else {
        throw new Error(result.desc || 'Lỗi tạo mã QR');
      }
    } catch (error) {
      console.error('Error generating QR:', error);
      throw error;
    }
  }

  // Kiểm tra thông tin tài khoản
  async lookupAccount(bankCode: string, accountNumber: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseURL}/lookup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bin: bankCode,
          accountNumber: accountNumber,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error looking up account:', error);
      throw new Error('Không thể kiểm tra thông tin tài khoản');
    }
  }
}

export const vietQRService = new VietQRService();

// Danh sách mã ngân hàng phổ biến
export const POPULAR_BANKS = [
  {code: '970415', name: 'Vietinbank', shortName: 'VTB'},
  {code: '970436', name: 'Vietcombank', shortName: 'VCB'},
  {code: '970418', name: 'BIDV', shortName: 'BIDV'},
  {code: '970405', name: 'Agribank', shortName: 'AGB'},
  {code: '970422', name: 'Techcombank', shortName: 'TCB'},
  {code: '970407', name: 'Tienphongbank', shortName: 'TPB'},
  {code: '970432', name: 'VPBank', shortName: 'VPB'},
  {code: '970423', name: 'Sacombank', shortName: 'STB'},
  {code: '970454', name: 'VietCapitalBank', shortName: 'VCCB'},
  {code: '970448', name: 'OCB', shortName: 'OCB'},
  {code: '970426', name: 'Maritime Bank', shortName: 'MSB'},
  {code: '970414', name: 'Ocean Bank', shortName: 'OJB'},
  {code: '970431', name: 'Eximbank', shortName: 'EIB'},
  {code: '970409', name: 'BacABank', shortName: 'BAB'},
  {code: '970412', name: 'PVcomBank', shortName: 'PVB'},
  {code: '970403', name: 'SHB', shortName: 'SHB'},
  {code: '970424', name: 'ShinhanBank', shortName: 'SVB'},
  {code: '970441', name: 'VIB', shortName: 'VIB'},
  {code: '970443', name: 'SGB', shortName: 'SGB'},
  {code: '970440', name: 'SeABank', shortName: 'SEAB'},
  {code: '970429', name: 'SCB', shortName: 'SCB'},
  {code: '970437', name: 'HDBank', shortName: 'HDB'},
  {code: '970438', name: 'BaoVietBank', shortName: 'BVB'},
  {code: '970427', name: 'VietABank', shortName: 'VAB'},
  {code: '970428', name: 'NamABank', shortName: 'NAB'},
  {code: '970430', name: 'PGBank', shortName: 'PGB'},
  {code: '970433', name: 'VietBank', shortName: 'VBB'},
  {code: '970439', name: 'BanVietBank', shortName: 'BVT'},
  {code: '970442', name: 'HongLeongBank', shortName: 'HLBVN'},
  {code: '970449', name: 'LienVietPostBank', shortName: 'LPB'},
  {code: '970458', name: 'UnitedOverseas Bank', shortName: 'UOB'},
  {code: '970434', name: 'IndovinaBank', shortName: 'IVB'},
  {code: '970456', name: 'IBKBANK', shortName: 'IBK'},
  {code: '970455', name: 'VBSP', shortName: 'VBSP'},
  {code: '970457', name: 'WooriBank', shortName: 'WVN'},
  {code: '970410', name: 'StandardChartered', shortName: 'SCVN'},
  {code: '970416', name: 'ACB', shortName: 'ACB'},
  {code: '970425', name: 'ANZVN', shortName: 'ANZVN'},
  {code: '970406', name: 'DongABank', shortName: 'DAB'},
  {code: '970408', name: 'GPBank', shortName: 'GPB'},
  {code: '970419', name: 'NCB', shortName: 'NCB'},
  {code: '970421', name: 'VRB', shortName: 'VRB'},
  {code: '970446', name: 'CAKE', shortName: 'CAKE'},
  {code: '546034', name: 'CAKE', shortName: 'CAKE'},
  {code: '963388', name: 'Timo', shortName: 'TIMO'},
  {code: '971005', name: 'Ubank', shortName: 'UBANK'},
];

// Bảng mapping từ tên ngân hàng hiển thị sang mã ngân hàng VietQR
export const BANK_NAME_MAPPING: Record<string, string> = {
  // Các tên viết tắt phổ biến
  VCB: '970436',
  Vietcombank: '970436',
  VIETCOMBANK: '970436',

  VTB: '970415',
  Vietinbank: '970415',
  VIETINBANK: '970415',

  BIDV: '970418',
  bidv: '970418',

  AGB: '970405',
  Agribank: '970405',
  AGRIBANK: '970405',

  TCB: '970422',
  Techcombank: '970422',
  TECHCOMBANK: '970422',

  TPB: '970407',
  Tienphongbank: '970407',
  TIENPHONGBANK: '970407',
  TPBank: '970407',
  TPBANK: '970407',

  VPB: '970432',
  VPBank: '970432',
  VPBANK: '970432',

  STB: '970423',
  Sacombank: '970423',
  SACOMBANK: '970423',

  OCB: '970448',
  ocb: '970448',

  MSB: '970426',
  'Maritime Bank': '970426',
  'MARITIME BANK': '970426',
  MaritimeBank: '970426',
  MARITIMEBANK: '970426',

  ACB: '970416',
  acb: '970416',

  HDBank: '970437',
  HDBANK: '970437',
  HDB: '970437',

  VIB: '970441',
  vib: '970441',

  SHB: '970403',
  shb: '970403',

  SeABank: '970440',
  SEABANK: '970440',
  SEAB: '970440',

  SCB: '970429',
  scb: '970429',

  EIB: '970431',
  Eximbank: '970431',
  EXIMBANK: '970431',

  LPB: '970449',
  LienVietPostBank: '970449',
  LIENVIETPOSTBANK: '970449',
  LienViet: '970449',
  LIENVIET: '970449',

  MB: '970422', // MBBank thường được gọi nhầm với Techcombank
  MBBank: '970422',
  MBBANK: '970422',

  // Thêm MPBank - có thể là Maritime Bank hoặc MBBank
  MPBank: '970426', // Giả định là Maritime Bank
  MPBANK: '970426',
  'MP Bank': '970426',
  'MP BANK': '970426',

  // Các tên khác có thể xuất hiện
  'Viet Capital Bank': '970454',
  'VIET CAPITAL BANK': '970454',
  VietCapitalBank: '970454',
  VIETCAPITALBANK: '970454',
  VCCB: '970454',

  'BaoViet Bank': '970438',
  'BAOVIET BANK': '970438',
  BaoVietBank: '970438',
  BAOVIETBANK: '970438',
  BVB: '970438',

  'DongA Bank': '970406',
  'DONGA BANK': '970406',
  DongABank: '970406',
  DONGABANK: '970406',
  DAB: '970406',

  PVcomBank: '970412',
  PVCOMBANK: '970412',
  PVB: '970412',

  'BacA Bank': '970409',
  'BACA BANK': '970409',
  BacABank: '970409',
  BACABANK: '970409',
  BAB: '970409',

  'Nam A Bank': '970428',
  'NAM A BANK': '970428',
  NamABank: '970428',
  NAMABANK: '970428',
  NAB: '970428',

  'Viet A Bank': '970427',
  'VIET A BANK': '970427',
  VietABank: '970427',
  VIETABANK: '970427',
  VAB: '970427',

  'Shinhan Bank': '970424',
  'SHINHAN BANK': '970424',
  ShinhanBank: '970424',
  SHINHANBANK: '970424',
  SVB: '970424',

  'Standard Chartered': '970410',
  'STANDARD CHARTERED': '970410',
  StandardChartered: '970410',
  STANDARDCHARTERED: '970410',
  SCVN: '970410',

  'Hong Leong Bank': '970442',
  'HONG LEONG BANK': '970442',
  HongLeongBank: '970442',
  HONGLEONGBANK: '970442',
  HLBVN: '970442',

  'United Overseas Bank': '970458',
  'UNITED OVERSEAS BANK': '970458',
  'UnitedOverseas Bank': '970458',
  'UNITEDOVERSEAS BANK': '970458',
  UOB: '970458',

  'Woori Bank': '970457',
  'WOORI BANK': '970457',
  WooriBank: '970457',
  WOORIBANK: '970457',
  WVN: '970457',

  'IBK Bank': '970456',
  'IBK BANK': '970456',
  IBKBank: '970456',
  IBKBANK: '970456',
  IBK: '970456',

  CAKE: '970446',
  cake: '970446',

  Timo: '963388',
  TIMO: '963388',
  timo: '963388',

  Ubank: '971005',
  UBANK: '971005',
  ubank: '971005',
  'U Bank': '971005',
  'U BANK': '971005',
};

/**
 * Tìm mã ngân hàng VietQR từ tên ngân hàng hiển thị
 * @param bankName Tên ngân hàng hiển thị
 * @returns Mã ngân hàng VietQR hoặc null nếu không tìm thấy
 */
export const getBankCodeFromName = (bankName: string): string | null => {
  if (!bankName) return null;

  // Thử tìm exact match trước
  const exactMatch = BANK_NAME_MAPPING[bankName];
  if (exactMatch) return exactMatch;

  // Thử tìm case-insensitive match
  const upperCaseName = bankName.toUpperCase();
  const caseInsensitiveMatch = BANK_NAME_MAPPING[upperCaseName];
  if (caseInsensitiveMatch) return caseInsensitiveMatch;

  // Thử tìm partial match (chứa từ khóa)
  const normalizedName = bankName.toUpperCase().replace(/\s+/g, '');
  for (const [key, value] of Object.entries(BANK_NAME_MAPPING)) {
    const normalizedKey = key.toUpperCase().replace(/\s+/g, '');
    if (normalizedName.includes(normalizedKey) || normalizedKey.includes(normalizedName)) {
      return value;
    }
  }

  // Nếu không tìm thấy, trả về mã mặc định (Vietcombank)
  console.warn(`Không tìm thấy mã ngân hàng cho: ${bankName}, sử dụng mặc định VCB`);
  return '970436'; // Vietcombank default
};

/**
 * Lấy thông tin ngân hàng từ mã ngân hàng
 * @param bankCode Mã ngân hàng VietQR
 * @returns Thông tin ngân hàng hoặc null nếu không tìm thấy
 */
export const getBankInfoFromCode = (bankCode: string): (typeof POPULAR_BANKS)[0] | null => {
  return POPULAR_BANKS.find(bank => bank.code === bankCode) || null;
};
