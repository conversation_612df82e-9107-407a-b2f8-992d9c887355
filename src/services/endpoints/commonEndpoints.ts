import {URL_API} from '@constants/axios';
import NavigationUtil from '@navigation/NavigationUtil';
import {AUTH_SCREENS} from '@navigation/routes';
import {axiosInstance} from '@services/instance';
import {store} from '@store/index';
import {logout, setToken} from '@store/slices/authSlice';
import {ToastHelper} from '@utils/ToastHelper';
import {refreshAccessToken} from './authEndpoints';

const addDefaultParams = async () => {
  const {user, nsd, pas} = store.getState().auth;
  const defaultParams = {
    // pm: 'GD',
    // deviceid: await DeviceInfo.getUniqueId(),
    ma_doi_tac_nsd: user?.ma_doi_tac,
    ma_chi_nhanh_nsd: user?.ma_chi_nhanh || null,
    nsd: nsd || null,
    pas: pas || null,
  };
  return defaultParams;
};

export const getCommonExecute = async (params: any = {}): Promise<any> => {
  const body = {
    // ...(await addDefaultParams()),
    ...params,
  };
  try {
    const response = await axiosInstance.post(URL_API.COMMON_EXECUTE, body);
    return response;
  } catch (error) {
    logger.log('❌ ~ getCommonExecute ~ Error:', error);
    return handleError(error, body);
  }
};

const handleError = async (error: any, params: any) => {
  try {
    logger.log('handleError', error);
    if (error.status === 400) {
      if (error.response?.data?.error_message === 'Thông tin token không hợp lệ hoặc đã bị hết hạn.') {
        //nếu token hết hạn -> gọi api refresh lại access_token
        const {token, refreshToken} = store.getState().auth;
        const responseRefreshAccessToken = await refreshAccessToken({token: token, refresh_token: refreshToken});
        logger.log('responseRefreshAccessToken');
        logger.log(responseRefreshAccessToken);
        //cập nhật lại token
        if (typeof responseRefreshAccessToken === 'string') {
          store.dispatch(setToken(responseRefreshAccessToken));
          return await getCommonExecute(params);
        } else if (responseRefreshAccessToken.status === 400) {
          // Logout user when refresh token is invalid
          store.dispatch(logout());
          NavigationUtil.push(AUTH_SCREENS.LOGIN);
          ToastHelper.error('Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại', {duration: 3000});
        }
      } else if (error.response?.data?.error_message) {
        ToastHelper.error(error.response?.data?.error_message, {duration: 3000});
      }
    }
    return error;
  } catch (error) {
    return logger.log(error);
  }
};
