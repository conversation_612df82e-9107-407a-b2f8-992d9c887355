import {ACTION_CODE, URL_API} from '@constants/axios';
import {axiosInstance} from '@services/instance';

/**
 * Example login endpoint using the unified API handler
 * This demonstrates the simplified pattern developers can now use
 */
export const login = async (params: {tai_khoan: string; mat_khau: string}): Promise<any> => {
  return await axiosInstance.post(URL_API.LOGIN, {
    tai_khoan: params.tai_khoan,
    mat_khau: params.mat_khau,
    actionCode: ACTION_CODE.DANG_NHAP,
  });
};

/**
 * Example authenticated API call using the unified handler
 * Shows how simple authenticated requests become
 */
export const getMenuList = async (params: any = {}): Promise<any> => {
  return await axiosInstance.post(URL_API.COMMON_EXECUTE, {
    ...params,
    actionCode: ACTION_CODE.LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM,
  });
};

/**
 * Example file upload using the unified handler
 * Demonstrates FormData handling
 */
export const uploadFile = async (file: any, additionalParams: any = {}): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  // Add additional parameters to FormData
  Object.keys(additionalParams).forEach(key => {
    formData.append(key, additionalParams[key]);
  });

  formData.append('actionCode', ACTION_CODE.UPLOAD_FILE);

  return await axiosInstance.post(URL_API.UPLOAD_FILE, formData);
};

/**
 * Example paginated search using the unified handler
 */
export const searchCustomers = async (
  params: {
    page?: number;
    pageSize?: number;
    searchTerm?: string;
  } = {},
): Promise<any> => {
  return await axiosInstance.post(URL_API.COMMON_EXECUTE, {
    ...params,
    actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_KHACH_HANG,
  });
};

/**
 * Example Excel import using the unified handler
 * Shows special handling for Excel uploads
 */
export const importExcel = async (
  file: any,
  params: {
    ma_doi_tac_ql?: string;
    bt?: string;
    ten?: string;
    nhom?: string;
  } = {},
): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('ma_doi_tac_ql', params.ma_doi_tac_ql || '');
  formData.append('bt', params.bt || '');
  formData.append('ten', params.ten || '');
  formData.append('nhom', params.nhom || '');
  formData.append('actionCode', ACTION_CODE.UPLOAD_EXCEL_FILE);

  return await axiosInstance.post(URL_API.UPLOAD_EXCEL_FILE, formData);
};
