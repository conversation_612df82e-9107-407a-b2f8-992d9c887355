import {CONFIG_SERVER, URL_API} from '@constants/axios';
import {OcrApiResponse, OcrParseableResponse} from '../../types/ocrTypes';
import {CccdInfo} from '@utils/ocrParser';
import {store} from '@store/index';
import axios from 'axios';

// Helper functions for data formatting
const formatDate = (dateStr?: string): string | undefined => {
  if (!dateStr) return undefined;
  return dateStr.replace(/-/g, '/');
};

const formatGender = (gender?: string): string | undefined => {
  if (!gender) return undefined;
  const lowerGender = gender.toLowerCase();
  if (lowerGender === 'nam') return 'Nam';
  if (lowerGender === 'nữ' || lowerGender === 'nu') return 'Nữ';
  return gender;
};

const createFileObject = (imageUri: string) => {
  if (imageUri.startsWith('file://') || imageUri.startsWith('/')) {
    return {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'cccd_image.jpg',
    };
  }
  throw new Error('Invalid image URI format');
};

const mapApiResponseToCccdInfo = (apiData: any): CccdInfo => {
  return {
    id: apiData.so_cmnd || undefined,
    name: apiData.ten || undefined,
    dob: formatDate(apiData.ngay_sinh) || undefined,
    gender: formatGender(apiData.gioi_tinh) || undefined,
    address: apiData.noi_thuong_tru || apiData.que_quan || undefined,
  };
};

// Create dedicated axios instance for OCR API (bypass main interceptors)
const createOcrAxiosInstance = () => {
  const {token} = store.getState().auth;
  const authToken = token || '';

  return axios.create({
    baseURL: CONFIG_SERVER.BASE_URL_API,
    timeout: 30000,
    headers: {
      accept: 'application/json',
      ePartnerCode: CONFIG_SERVER.E_PARTNER_CODE,
      eAuthToken: authToken,
    },
  });
};

/**
 * Gọi API OCR để nhận dạng thông tin từ ảnh căn cước công dân
 * @param imageUri - URI của ảnh (local file path hoặc remote URL)
 * @returns Promise<CccdInfo> - Thông tin đã được parse từ OCR
 */
export const callOcrApi = async (imageUri: string): Promise<CccdInfo> => {
  try {
    // Create FormData with file
    const formData = new FormData();
    const fileToUpload = createFileObject(imageUri);
    formData.append('file', fileToUpload as any);

    // Create axios instance with OCR-specific headers
    const ocrAxios = createOcrAxiosInstance();

    // Call API using axios (following project pattern)
    const response = await ocrAxios.post(URL_API.OCR_CCCD, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    const responseData: OcrApiResponse = response.data;

    // Process response following project pattern
    if (responseData?.data) {
      const cccdInfo = mapApiResponseToCccdInfo(responseData.data);
      console.log('✅ OCR API parsed data:', cccdInfo);
      return cccdInfo;
    } else {
      console.warn('⚠️ OCR API returned no data field');
      return {};
    }
  } catch (error) {
    console.error('❌ OCR API Error:', error);
    return handleOcrError(error);
  }
};

// Error handling following project pattern
const handleOcrError = (error: any): CccdInfo => {
  // Log error details for debugging
  if (error.response) {
    console.error('Response error:', {
      status: error.response.status,
      data: error.response.data,
    });
  } else if (error.request) {
    console.error('Request error:', error.request);
  } else {
    console.error('Error:', error.message);
  }

  // Return empty object to allow graceful degradation
  return {};
};

/**
 * Alternative function that returns raw OCR data in parseable format
 * This can be used if the API returns raw text that needs to be parsed
 * @param imageUri - URI của ảnh
 * @returns Promise<OcrParseableResponse> - Raw OCR data in parseable format
 */
export const callOcrApiRaw = async (imageUri: string): Promise<OcrParseableResponse> => {
  try {
    const result = await callOcrApi(imageUri);
    return convertCccdInfoToParseableFormat(result);
  } catch (error) {
    console.error('❌ OCR API Raw Error:', error);
    return {
      status: 'failed',
      analyzeResult: {
        readResults: [],
      },
    };
  }
};

// Helper function to convert CccdInfo to parseable format
const convertCccdInfoToParseableFormat = (cccdInfo: CccdInfo): OcrParseableResponse => {
  const lines = [{text: cccdInfo.id || ''}, {text: cccdInfo.name || ''}, {text: cccdInfo.dob || ''}, {text: cccdInfo.gender || ''}, {text: cccdInfo.address || ''}].filter(
    line => line.text.trim() !== '',
  );

  return {
    status: 'succeeded',
    analyzeResult: {
      readResults: [{lines}],
    },
  };
};
