import {ACTION_CODE, URL_API} from '@constants/axios';
import {axiosInstance} from '@services/instance';

/**
 * Login function theo tiêu chuẩn API mới
 * @param params - {tai_khoan: string, mat_khau: string}
 */
export const login = async (params: any = {tai_khoan: '', mat_khau: '', actionCode: ACTION_CODE.DANG_NHAP}): Promise<any> => {
  return await axiosInstance.post(URL_API.LOGIN, params);
};
export const refreshAccessToken = async (params: any = {token: '', refresh_token: ''}): Promise<any> => {
  try {
    const response: any = await axiosInstance.post(URL_API.REFRESH_ACCESS_TOKEN, params);
    return response;
  } catch (error) {
    logger.log('refreshAccessToken error', error);
    return error;
  }
};
