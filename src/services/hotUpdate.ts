import {Alert, Platform} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import hotUpdate from 'react-native-ota-hot-update';
import ReactNativeBlobUtil from 'react-native-blob-util';
import {CONFIG_SERVER, ACTION_CODE} from '../constants/axios';

// Hot Update types based on gic-giam-dinh project
export interface HotUpdateInfo {
  app_version: string;
  codepush_version: number;
  path: string;
  force_update?: boolean;
  description?: string;
  release_notes?: string;
}

export interface UpdateProgress {
  receivedBytes: number;
  totalBytes: number;
}

export interface HotUpdateOptions {
  updateSuccess?: () => void;
  updateFail?: (message: string) => void;
  progress?: (received: number, total: number) => void;
  restartAfterInstall?: boolean;
}

/**
 * Hot Update Service
 * Based on gic-giam-dinh project implementation
 * Uses react-native-ota-hot-update library
 */
class HotUpdateService {
  /**
   * Check for available updates from server
   * Based on checkUpdateHotUpdate from SplashScreen.js
   */
  async checkForUpdates(): Promise<HotUpdateInfo | null> {
    try {
      logger.log('Checking for hot updates...');

      // For testing purposes, return a mock update info
      // TODO: Replace with real server check when ready
      const mockUpdate: HotUpdateInfo = {
        app_version: '1.0.0',
        codepush_version: 2,
        path: '/hot-update/main.jsbundle.zip',
        force_update: false,
        description: 'Cập nhật tính năng mới và sửa lỗi',
        release_notes: '- Sửa lỗi đăng nhập\n- Cải thiện hiệu suất\n- Thêm tính năng mới',
      };

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Enable mock update for ProfileScreen testing (disabled in SplashScreen)
      logger.log('Mock update available for ProfileScreen testing');
      return mockUpdate;

      /* Real server implementation:
      const response = await this.getHotUpdateInfo();

      if (!response?.data_info?.app_version) {
        return null;
      }

      const dataInfo = response.data_info;
      const appVersion = DeviceInfo.getVersion();

      // Check if app version matches (same logic as gic-giam-dinh)
      if (dataInfo.app_version === appVersion) {
        // Compare codepush version
        const currentVersion = await hotUpdate.getCurrentVersion();
        logger.log('Current hot update version:', currentVersion);

        // If server version is newer
        if (+dataInfo.codepush_version > currentVersion) {
          logger.log('Update available. Current:', currentVersion, 'Server:', dataInfo.codepush_version);
          return {
            app_version: dataInfo.app_version,
            codepush_version: +dataInfo.codepush_version,
            path: dataInfo.path,
            force_update: dataInfo.force_update || false,
            description: dataInfo.description,
            release_notes: dataInfo.release_notes,
          };
        }
      }

      logger.log('No updates available');
      return null;
      */
    } catch (error) {
      console.error('Failed to check for updates:', error);
      return null;
    }
  }

  /**
   * Download and install update using react-native-ota-hot-update
   * Based on hotUpdate.downloadBundleUri from SplashScreen.js
   */
  async downloadAndInstall(updateInfo: HotUpdateInfo, options: HotUpdateOptions = {}): Promise<boolean> {
    try {
      logger.log('Starting hot update download simulation...');

      // For testing purposes, simulate download progress
      // TODO: Replace with real download when server is ready

      // Simulate download progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        options.progress?.(i, 100);
      }

      logger.log('Hot update download simulation completed');
      options.updateSuccess?.();

      // Don't actually restart in test mode
      logger.log('Test mode: Skipping app restart');
      return true;

      /* Real implementation:
      const downloadUrl = CONFIG_SERVER.BASE_URL_API + updateInfo.path;
      logger.log('Starting hot update download from:', downloadUrl);

      // Use react-native-ota-hot-update library (same as gic-giam-dinh)
      await hotUpdate.downloadBundleUri(ReactNativeBlobUtil, downloadUrl, updateInfo.codepush_version, {
        updateSuccess: () => {
          logger.log('Hot update successful');
          options.updateSuccess?.();
        },
        updateFail: (message: string) => {
          logger.log('Hot update failed:', message);
          options.updateFail?.(message);
        },
        restartAfterInstall: options.restartAfterInstall !== false, // Default true
        progress: (received: number, total: number) => {
          options.progress?.(received, total);
        },
      });

      return true;
      */
    } catch (error) {
      console.error('Failed to download and install update:', error);
      options.updateFail?.(error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  /**
   * Get current hot update version
   */
  async getCurrentVersion(): Promise<number> {
    try {
      return await hotUpdate.getCurrentVersion();
    } catch (error) {
      console.error('Failed to get current version:', error);
      return 0;
    }
  }

  /**
   * Set current hot update version
   */
  async setCurrentVersion(version: number): Promise<void> {
    try {
      await hotUpdate.setCurrentVersion(version);
      logger.log('Hot update version set to:', version);
    } catch (error) {
      console.error('Failed to set current version:', error);
    }
  }

  /**
   * Reset app (restart)
   */
  async resetApp(): Promise<void> {
    try {
      await hotUpdate.resetApp();
    } catch (error) {
      console.error('Failed to reset app:', error);
    }
  }

  /**
   * Remove update data
   * Based on onPressClearCodepushData from SplashScreen.js
   */
  async removeUpdate(): Promise<void> {
    try {
      await hotUpdate.removeUpdate();
      await hotUpdate.setCurrentVersion(0);
      await hotUpdate.resetApp();
      logger.log('Hot update data removed and app restarted');
    } catch (error) {
      console.error('Failed to remove update:', error);
    }
  }

  /**
   * Get hot update info from server
   * Based on BenThuBaEndPoint.getHotUpdateInfo from gic-giam-dinh
   */
  private async getHotUpdateInfo(): Promise<any> {
    try {
      // Mock data for now - replace with actual API call
      const mockResponse = {
        data_info: {
          app_version: DeviceInfo.getVersion(),
          codepush_version: 1, // Mock version higher than current
          path: '/hot-update/main.jsbundle.zip',
          force_update: false,
          description: 'Cập nhật tính năng mới và sửa lỗi',
          release_notes: 'Phiên bản này bao gồm các cải tiến hiệu suất và sửa lỗi.',
        },
      };

      // Real implementation should be:
      // const params = {
      //   env: !IS_PROD ? 'dev' : 'prod',
      //   partner: 'ESCS',
      //   platform: Platform.OS === 'ios' ? 'ios' : 'android',
      //   ma_doi_tac_nsd: CONFIG_SERVER.MA_DOI_TAC,
      // };
      // const response = await commonEndpoint.execute(ACTION_CODE.GET_HOT_UPDATE_INFO, params);
      // return response;

      return mockResponse;
    } catch (error) {
      console.error('Failed to get hot update info:', error);
      throw error;
    }
  }

  /**
   * Show update confirmation dialog
   */
  async showUpdateDialog(updateInfo: HotUpdateInfo): Promise<boolean> {
    return new Promise(resolve => {
      const title = updateInfo.force_update ? 'Cập nhật bắt buộc' : 'Cập nhật ứng dụng';
      const message = updateInfo.description || 'Có phiên bản mới của ứng dụng. Bạn có muốn cập nhật không?';

      const buttons = updateInfo.force_update
        ? [{text: 'Cập nhật', onPress: () => resolve(true)}]
        : [
            {text: 'Bỏ qua', style: 'cancel' as const, onPress: () => resolve(false)},
            {text: 'Cập nhật', onPress: () => resolve(true)},
          ];

      Alert.alert(title, message, buttons);
    });
  }
}

export const hotUpdateService = new HotUpdateService();
