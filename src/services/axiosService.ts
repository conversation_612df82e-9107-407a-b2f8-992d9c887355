/* eslint-disable @typescript-eslint/no-explicit-any */
import {isIOS} from '@commons/Constant';
import {ACTION_CODE, AXIOS_CONFIGS, CONFIG_SERVER} from '@constants/axios';
import {FlashMessageHelper} from '@utils/FlashMessageHelper';
import {logErrorHttp} from '@utils/ShowErrorAlert';
import axios, {AxiosError, AxiosRequestConfig, AxiosResponse} from 'axios';
import moment from 'moment';
import DeviceInfo from 'react-native-device-info';
import {LooseObject} from './types';

const axiosInstance = axios.create({
  baseURL: CONFIG_SERVER.BASE_URL_API,
  timeout: AXIOS_CONFIGS.timeout,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'Access-Control-Allow-Origin': '*',
  },
});

const logRequest = (request: AxiosRequestConfig) => {
  if (__DEV__) {
    if (console.group) {
      console.group('%cAPI Request', 'color:white;font-weight:bold;background:#0194ff;padding:2px 6px', request.url);
    }
    console.log('HTTP Method:', request.method?.toUpperCase());
    console.log('Endpoint:', request.url);
    if (request.data) {
      console.log('Request Body:', request.data);
    }
    if (console.groupEnd) {
      console.groupEnd();
    }
  }
};

const logResponse = (response: AxiosResponse) => {
  if (__DEV__) {
    if (console.group) {
      console.group('%cAPI Response', 'color:white;font-weight:bold;background:green;padding:2px 6px', response.config.url);
    }
    console.log('Request:', response.config.data);
    console.log('Response Body:', response.data);
    if (console.groupEnd) {
      console.groupEnd();
    }
  }
};

const logError = (error: AxiosError) => {
  if (__DEV__) {
    if (console.group) {
      console.group('%cAPI Error', 'color:white;font-weight:bold;background:red;padding:2px 6px', error.config?.url);
    }
    console.log('Request:', error.config?.data);
    console.log('Error Data:', error.response?.data);
    if (console.groupEnd) {
      console.groupEnd();
    }
  }
};

axiosInstance.interceptors.request.use(async request => {
  logRequest(request);
  return request;
});

axiosInstance.interceptors.response.use(
  response => {
    logResponse(response);
    if (response.data !== null && response.data !== undefined && response.status.toString().startsWith('2')) {
      if (response.data) {
        return response.data;
      } else {
        throw new Error('Error');
      }
    } else {
      throw new Error(response.statusText);
    }
  },
  error => {
    logError(error);
    logErrorHttp(error);
    let errMessage = '';
    const listActionKhongCanHienThiAlert = [ACTION_CODE.UPLOAD_FILE];
    if (listActionKhongCanHienThiAlert.includes(error.config.headers.eAction)) {
      return Promise.reject(error);
    }
    if (error && error.message === 'Network Error') {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Truy vấn không thành công. Vui lòng thử lại' + errMessage);
      return Promise.reject(error);
    } else if (error?.response) {
      return Promise.reject(error);
    } else {
      FlashMessageHelper.showFlashMessage('Thông báo có lỗi kết nối đến server', JSON.stringify(error.message));
      return Promise.reject(error);
    }
  },
);

export const updateAxiosBaseUrl = (baseURL: string) => (axiosInstance.defaults.baseURL = baseURL);
export const updateHeader = (headers: any) => (axiosInstance.defaults.headers.common = {...axiosInstance.defaults.headers.common, ...headers});

export const getDefineInfoRequest = async () => {
  const userAgent = 'ESCS Mobile - ' + (!isIOS ? 'Android/' + DeviceInfo.getVersion() : 'iOS/' + DeviceInfo.getVersion());

  const defineInfo = {
    accept: '*/*',
    accept_encoding: 'gzip, deflate',
    host: 'com.escs.mobile',
    referer: 'com.escs.mobile',
    user_agent: userAgent,
    origin: 'com.escs.mobile',
    ip_remote_ipv4: await DeviceInfo.getUniqueId(),
    ip_remote_ipv6: await DeviceInfo.getUniqueId(),
    time: moment().format('YYYYMMDDHHmmss'),
  };
  return defineInfo;
};

export const ApiClient = {
  get: <T = any, R = AxiosResponse<T>>(url: string, option: AxiosRequestConfig = {}) => axiosInstance.get<T, R>(url, option),
  post: <T = any, R = AxiosResponse<T>, D = any>(url: string, payload: D, option: AxiosRequestConfig = {}) => axiosInstance.post<T, R, D>(url, payload, option),
  put: <T = any, R = AxiosResponse<T>, D = any>(url: string, payload: D, option: AxiosRequestConfig = {}) => axiosInstance.put<T, R, D>(url, payload, option),
  patch: <T = any, R = AxiosResponse<T>, D = any>(url: string, payload: D, option: AxiosRequestConfig = {}) => axiosInstance.patch<T, R, D>(url, payload, option),
  delete: <T = any, R = AxiosResponse<T>>(url: string, option: AxiosRequestConfig = {}) => axiosInstance.delete<T, R>(url, option),
};

export const handleUrlWithParam = (params: LooseObject = {}): string => {
  if (params) {
    let arrBody = [];
    for (let key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        arrBody.push(key + '=' + params[key]);
      }
    }
    const strQuery = arrBody.join('&');
    return '?' + strQuery;
  }
  return '';
};
