export const config = {
  API_BASE_URL: __DEV__ ? 'http://localhost:3000/api' : 'https://your-production-api.com/api',

  APP_NAME: '<PERSON><PERSON><PERSON>',
  APP_VERSION: '1.0.0',

  // Environment flags
  ENABLE_FLIPPER: __DEV__,
  ENABLE_LOGS: __DEV__,

  // API timeouts
  API_TIMEOUT: 10000,

  // Storage keys
  STORAGE_KEYS: {
    AUTH_TOKEN: 'auth_token',
    USER_DATA: 'user_data',
    APP_SETTINGS: 'app_settings',
  },

  // App settings
  DEFAULT_LANGUAGE: 'vi',
  SUPPORTED_LANGUAGES: ['vi', 'en'],

  // Validation rules
  VALIDATION: {
    PASSWORD_MIN_LENGTH: 6,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE_REGEX: /^[0-9]{10,11}$/,
  },
};
