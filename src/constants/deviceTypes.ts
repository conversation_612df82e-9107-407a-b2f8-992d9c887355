import {Dimensions, Platform} from 'react-native';

// Device type definitions
export type DeviceType = 'phone' | 'tablet';
export type SizeClass = 'compact' | 'regular';

// Device breakpoints (based on Apple's size classes)
export const DEVICE_BREAKPOINTS = {
  // Width breakpoints for size classes
  COMPACT_WIDTH_MAX: 414, // iPhone Pro Max width
  REGULAR_WIDTH_MIN: 768, // iPad minimum width
  
  // Height breakpoints
  COMPACT_HEIGHT_MAX: 667, // iPhone SE/8 height
  REGULAR_HEIGHT_MIN: 1024, // iPad minimum height
} as const;

// iPad specific models and their characteristics
export const IPAD_MODELS = {
  IPAD_AIR_5TH_GEN: {
    width: 820,
    height: 1180,
    name: 'iPad Air (5th generation)',
    safeAreaTop: 24,
    safeAreaBottom: 20,
  },
  IPAD_PRO_11_INCH: {
    width: 834,
    height: 1194,
    name: 'iPad Pro 11"',
    safeAreaTop: 24,
    safeAreaBottom: 20,
  },
  IPAD_PRO_12_9_INCH: {
    width: 1024,
    height: 1366,
    name: 'iPad Pro 12.9"',
    safeAreaTop: 24,
    safeAreaBottom: 20,
  },
  IPAD_10TH_GEN: {
    width: 820,
    height: 1180,
    name: 'iPad (10th generation)',
    safeAreaTop: 24,
    safeAreaBottom: 20,
  },
} as const;

// iPhone models for reference
export const IPHONE_MODELS = {
  IPHONE_SE: {
    width: 375,
    height: 667,
    name: 'iPhone SE',
    safeAreaTop: 20,
    safeAreaBottom: 0,
  },
  IPHONE_15_PRO: {
    width: 393,
    height: 852,
    name: 'iPhone 15 Pro',
    safeAreaTop: 59,
    safeAreaBottom: 34,
  },
  IPHONE_15_PRO_MAX: {
    width: 430,
    height: 932,
    name: 'iPhone 15 Pro Max',
    safeAreaTop: 59,
    safeAreaBottom: 34,
  },
} as const;

// Component sizing constants
export const COMPONENT_SIZES = {
  // Header heights
  HEADER_HEIGHT_PHONE: 56,
  HEADER_HEIGHT_TABLET: 64,
  
  // Tab bar heights (excluding safe area)
  TAB_BAR_HEIGHT_PHONE: 49,
  TAB_BAR_HEIGHT_TABLET: 56,
  
  // Icon sizes
  TAB_ICON_SIZE_PHONE: 24,
  TAB_ICON_SIZE_TABLET: 28,
  
  // Touch targets (minimum 44pt as per Apple HIG)
  MIN_TOUCH_TARGET: 44,
  RECOMMENDED_TOUCH_TARGET_TABLET: 48,
  
  // Spacing multipliers
  SPACING_MULTIPLIER_PHONE: 1,
  SPACING_MULTIPLIER_TABLET: 1.2,
} as const;

// Typography scaling
export const TYPOGRAPHY_SCALE = {
  PHONE: {
    multiplier: 1,
    maxFontSize: 34,
  },
  TABLET: {
    multiplier: 1.1,
    maxFontSize: 40,
  },
} as const;

// Get current device dimensions
export const getCurrentDeviceDimensions = () => {
  const {width, height} = Dimensions.get('window');
  return {width, height};
};

// Utility to check if current device matches a specific model
export const isDeviceModel = (model: typeof IPAD_MODELS[keyof typeof IPAD_MODELS] | typeof IPHONE_MODELS[keyof typeof IPHONE_MODELS]) => {
  const {width, height} = getCurrentDeviceDimensions();
  return width === model.width && height === model.height;
};
