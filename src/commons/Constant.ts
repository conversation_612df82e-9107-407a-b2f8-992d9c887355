import {Dimensions, Platform} from 'react-native';

export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';
export const PAGE_SIZE = 10;
export const APP_NAME = '<PERSON>àn Bảo Hiểm';
export const TARGET_SDK_VERSION = Platform.Version;
export const IS_PROD = true;
export const IS_REVIEW = true;
export const NGUON_MOBILE = 'SBH4SALE';

export const {width: screenWidth, height: screenHeight} = Dimensions.get('screen');

// Mối quan hệ với chủ hộ
export const MOI_QUAN_HE_VOI_CHU_HO = [
  {
    value: 'CHU_HO',
    label: 'Chủ hộ',
  },
  {
    value: 'CON_RUOT',
    label: 'Con ruột',
  },
  {
    value: 'VO_CHONG',
    label: 'Vợ/Chồng',
  },
  {
    value: 'ME_RUOT',
    label: 'Mẹ ruột',
  },
  {
    value: 'BO_RUOT',
    label: 'Bố ruột',
  },
  {
    value: 'ME_VO',
    label: 'Mẹ vợ',
  },
  {
    value: 'BO_CHONG',
    label: 'Bố chồng',
  },
  {
    value: 'ME_CHONG',
    label: 'Mẹ chồng',
  },
];

export const GIOI_TINH = [
  {
    value: 'NAM',
    label: 'Nam',
  },
  {
    value: 'NU',
    label: 'Nữ',
  },
];
