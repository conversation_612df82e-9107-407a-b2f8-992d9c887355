/**
 * Logger utility for development only
 * All logger.log statements should use this instead of direct logger.log
 * This will be completely removed in production builds
 */

const isDevelopment = __DEV__;

const logger = {
  log: (...args: any[]) => {
    if (isDevelopment) {
      logger.log(...args);
    }
  },
  warn: (...args: any[]) => {
    if (isDevelopment) {
      console.warn(...args);
    }
  },
  error: (...args: any[]) => {
    if (isDevelopment) {
      console.error(...args);
    }
  },
  info: (...args: any[]) => {
    if (isDevelopment) {
      console.info(...args);
    }
  },
  debug: (...args: any[]) => {
    if (isDevelopment) {
      console.debug(...args);
    }
  },
};

export default logger;
