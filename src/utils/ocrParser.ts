export interface CccdInfo {
  id?: string;
  name?: string;
  dob?: string;
  gender?: string;
  address?: string;
}

/**
 * <PERSON><PERSON> tích kết quả OCR từ Azure và trích xuất thông tin từ mặt trước CCCD.
 * @param ocrResult Dữ liệu trả về từ Azure Computer Vision Read API.
 * @returns Một object chứa thông tin đã được bóc tách.
 */
export const parseCccdFrontData = (ocrResult: any): CccdInfo => {
  if (ocrResult?.status !== 'succeeded') {
    return {};
  }

  const lines: string[] = [];
  ocrResult.analyzeResult.readResults.forEach((readResult: any) => {
    readResult.lines.forEach((line: any) => {
      lines.push(line.text);
    });
  });
  const info: CccdInfo = {};
  let addressStartIndex = -1; // -1: not found, >-1: found and capturing, -2: finished
  let nameStartIndex = -1;
  let dobFound = false;

  // Find ID first, as it's a distinct 12-digit number
  const fullText = lines.join('\n');
  const idRegex = /(\d{12})/;
  const idMatch = fullText.match(idRegex);
  if (idMatch) {
    info.id = idMatch[0];
  }

  for (let i = 0; i < lines.length; i++) {
    const currentLine = lines[i].toLowerCase();
    const originalLine = lines[i];

    // Find Name
    if (nameStartIndex === -1 && (currentLine.includes('họ và tên') || currentLine.includes('full name'))) {
      const parts = originalLine.split(':');
      if (parts.length > 1 && parts[1].trim()) {
        info.name = parts[1].trim();
      } else {
        nameStartIndex = i + 1; // Name is on the next line
      }
      continue;
    }
    if (nameStartIndex === i) {
      info.name = originalLine.trim();
      nameStartIndex = -2; // Mark as found
    }

    // Find Date of Birth
    if (!dobFound && (currentLine.includes('ngày sinh') || currentLine.includes('date of birth'))) {
      const dobRegex = /(\d{2}\/\d{2}\/\d{4})/;
      // Search from current line up to 3 lines down
      for (let j = i; j < lines.length && j <= i + 3; j++) {
        const dobMatch = lines[j].match(dobRegex);
        if (dobMatch) {
          info.dob = dobMatch[0];
          dobFound = true;
          break; // Exit the inner loop once found
        }
      }
      continue;
    }

    // Find Gender
    if (!info.gender && (currentLine.includes('giới tính') || currentLine.includes('sex'))) {
      const genderMatch = originalLine.match(/(Nam|Nữ|Male|Female)/i);
      if (genderMatch) {
        info.gender = genderMatch[0];
      }
      continue;
    }

    // Find Address (using Place of Origin as per new requirement)
    if (addressStartIndex === -1 && (currentLine.includes('quê quán') || currentLine.includes('place of origin'))) {
      addressStartIndex = i; // Mark that we have started finding the address
      const parts = originalLine.split(':');
      if (parts.length > 1 && parts[1].trim()) {
        info.address = parts[1].trim();
      } else {
        info.address = ''; // Address is on the next line(s)
      }
      continue;
    }

    if (addressStartIndex >= 0 && i > addressStartIndex) {
      // Stop capturing if we hit the next field
      if (currentLine.includes('nơi thường trú') || currentLine.includes('place of residence') || currentLine.includes('có giá trị đến') || currentLine.includes('date of expiry')) {
        addressStartIndex = -2; // Mark as finished
        continue;
      }
      info.address = (info.address + ' ' + originalLine).trim();
    }
  }

  // Capitalize name properly
  if (info.name) {
    info.name = info.name
      .toLowerCase()
      .split(' ')
      .map(s => s.charAt(0).toUpperCase() + s.substring(1))
      .join(' ');
  }

  return info;
};
