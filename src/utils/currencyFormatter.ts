/**
 * Currency formatter utility for Vietnamese Dong (VND)
 */

export interface CurrencyFormatterOptions {
  showSymbol?: boolean;
  showCurrency?: boolean;
  locale?: string;
}

/**
 * Format a number to Vietnamese Dong currency format
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number | string, options: CurrencyFormatterOptions = {}): string => {
  const {showSymbol = true, showCurrency = true, locale = 'vi-VN'} = options;

  // Convert string to number if needed
  const numericAmount = typeof amount === 'string' ? parseFloat(amount.replace(/[^\d.-]/g, '')) : amount;

  // Handle invalid numbers
  if (isNaN(numericAmount)) {
    return '';
  }

  try {
    // Format using Intl.NumberFormat for Vietnamese locale
    const formatter = new Intl.NumberFormat(locale, {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    let formatted = formatter.format(numericAmount);

    // Add currency symbol/text based on options
    if (showSymbol || showCurrency) {
      if (showSymbol && showCurrency) {
        formatted = `${formatted} ₫ VND`;
      } else if (showSymbol) {
        formatted = `${formatted}₫`;
      } else if (showCurrency) {
        formatted = `${formatted} VND`;
      }
    }
    return formatted;
  } catch (error) {
    // Fallback formatting if Intl is not available
    const formatted = numericAmount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    if (showSymbol || showCurrency) {
      if (showSymbol && showCurrency) {
        return `${formatted} ₫ VND`;
      } else if (showSymbol) {
        return `${formatted} ₫`;
      } else if (showCurrency) {
        return `${formatted} VND`;
      }
      return formatted;
    }

    return formatted;
  }
};

/**
 * Format a number to Vietnamese Dong currency format without currency symbol or text
 * @param amount - The amount to format
 * @param locale - Optional locale, default 'vi-VN'
 * @returns Formatted currency string without symbol or currency text
 */
export const formatCurrencyPlain = (amount: number | string, locale: string = 'vi-VN'): string => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount.replace(/[^\d.-]/g, '')) : amount;
  if (isNaN(numericAmount)) return '';
  try {
    return new Intl.NumberFormat(locale, {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numericAmount);
  } catch {
    return numericAmount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

/**
 * Parse a formatted currency string back to number
 * @param formattedAmount - The formatted currency string
 * @returns Numeric value
 */
export const parseCurrency = (formattedAmount: string | number): number => {
  if (!formattedAmount) return 0;

  // If it's already a number, return it
  if (typeof formattedAmount === 'number') return formattedAmount;

  // Convert to string if needed
  const amountStr = formattedAmount.toString();

  // Handle Vietnamese number format (dots as thousand separators)
  // First, remove all non-numeric characters except dots, commas, and minus sign
  let cleanedStr = amountStr.replace(/[^\d.,-]/g, '');

  // Check if this looks like Vietnamese format (dots as thousand separators)
  // Vietnamese: 1.500.000 or 1,500,000
  // Decimal: 1500.50 or 1500,50

  // If there are multiple dots, treat them as thousand separators
  const dotCount = (cleanedStr.match(/\./g) || []).length;
  const commaCount = (cleanedStr.match(/,/g) || []).length;

  if (dotCount > 1) {
    // Multiple dots = Vietnamese thousand separators, remove all dots
    cleanedStr = cleanedStr.replace(/\./g, '');
  } else if (dotCount === 1 && commaCount === 0) {
    // Single dot could be decimal or thousand separator
    // Check if there are exactly 3 digits after the dot (likely thousand separator)
    const parts = cleanedStr.split('.');
    if (parts.length === 2 && parts[1].length === 3 && /^\d+$/.test(parts[1])) {
      // Likely thousand separator, remove the dot
      cleanedStr = cleanedStr.replace('.', '');
    }
    // Otherwise keep the dot as decimal separator
  }

  // Handle commas as thousand separators
  if (commaCount > 1 || (commaCount === 1 && dotCount === 0)) {
    cleanedStr = cleanedStr.replace(/,/g, '');
  } else if (commaCount === 1 && dotCount === 0) {
    // Single comma could be decimal separator in European format
    const parts = cleanedStr.split(',');
    if (parts.length === 2 && parts[1].length <= 2) {
      // Likely decimal separator, convert to dot
      cleanedStr = cleanedStr.replace(',', '.');
    } else {
      // Likely thousand separator, remove comma
      cleanedStr = cleanedStr.replace(',', '');
    }
  }

  // Final cleanup - remove any remaining non-numeric characters except decimal point and minus
  const numericString = cleanedStr.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(numericString);

  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Format currency for input field (without currency symbols during editing)
 * @param amount - The amount to format
 * @returns Formatted string suitable for input
 */
export const formatCurrencyInput = (amount: string): string => {
  if (!amount) return '';

  // Remove all non-numeric characters
  const numericOnly = amount.replace(/[^\d]/g, '');

  if (!numericOnly) return '';

  // Convert to number and format with thousand separators
  const numericAmount = parseInt(numericOnly, 10);

  try {
    const formatted = new Intl.NumberFormat('vi-VN').format(numericAmount);
    return formatted;
  } catch (error) {
    // Fallback formatting
    const fallback = numericAmount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return fallback;
  }
};

/**
 * Validate if a string is a valid currency amount
 * @param amount - The amount string to validate
 * @returns Boolean indicating if valid
 */
export const isValidCurrencyAmount = (amount: string): boolean => {
  if (!amount) return false;

  const numericString = amount.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(numericString);

  return !isNaN(parsed) && parsed >= 0;
};

/**
 * Get currency display options for different contexts
 */
export const currencyDisplayOptions = {
  // For display in lists, cards, etc.
  display: {showSymbol: true, showCurrency: false},

  // For detailed views, receipts, etc.
  detailed: {showSymbol: true, showCurrency: true},

  // For input fields (no symbols during editing)
  input: {showSymbol: false, showCurrency: false},

  // For compact displays
  compact: {showSymbol: true, showCurrency: false},
} as const;

export default {
  formatCurrency,
  parseCurrency,
  formatCurrencyInput,
  isValidCurrencyAmount,
  currencyDisplayOptions,
};
