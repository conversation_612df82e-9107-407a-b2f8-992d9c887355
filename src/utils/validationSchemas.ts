import {RegisterOptions} from 'react-hook-form';
import moment from 'moment';
import {parseCurrency} from './currencyFormatter';

// ===========================
// VALIDATION PATTERNS
// ===========================

/**
 * Validation patterns for Vietnamese formats
 */
export const patterns = {
  // Số CCCD/CMND: 9-12 chữ số
  cccd: /^\d{9,12}$/,

  // Số điện thoại Việt Nam: bắt đầu bằng 0, theo sau 9-10 chữ số
  phoneNumber: /^0\d{9,10}$/,

  // Họ tên: chữ cái tiếng Việt, khoảng trắng
  fullName: /^[a-zA-ZÀ-ỹĐđ\s]+$/,

  // Ngày tháng định dạng DD/MM/YYYY
  dateFormat: /^\d{2}\/\d{2}\/\d{4}$/,

  // Email
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

  // Mã số thuế: 10 hoặc 13 chữ số
  taxCode: /^(\d{10}|\d{13})$/,

  // Mã hộ gia đình
  householdCode: /^[A-Z0-9]{8,12}$/,

  // Số tiền: số với dấu phân cách hàng nghìn
  currency: /^\d{1,3}(,\d{3})*(\.\d{2})?$/,

  // Mã số doanh nghiệp
  businessCode: /^\d{10,14}$/,

  // Số căn cước công dân mới: 12 chữ số
  newCccd: /^\d{12}$/,

  // Số chứng minh thư cũ: 9 chữ số
  oldCmnd: /^\d{9}$/,

  // Địa chỉ: không chứa ký tự đặc biệt
  address: /^[a-zA-ZÀ-ỹĐđ0-9\s,.\-\/]+$/,

  // Mã bưu chính Việt Nam: 5-6 chữ số
  zipCode: /^\d{5,6}$/,

  // Biển số xe: định dạng VN (29A-12345, 51F1-12345, etc.)
  bienSoXe: /^\d{2}[A-Z]{1,2}(-?\d{1})?-?\d{4,5}$/,

  // Số khung xe (VIN): 17 ký tự chữ và số
  soKhung: /^[A-HJ-NPR-Z0-9]$/,

  // Số máy xe: 6-17 ký tự chữ và số
  soMay: /^[A-Z0-9\-\/]{6,20}$/,
};

// ===========================
// ERROR MESSAGES
// ===========================

/**
 * Vietnamese error messages
 */
export const errorMessages = {
  required: (field: string) => `Trường bắt buộc`,
  invalid: (field: string = 'Giá trị') => `${field} không hợp lệ`,
  minLength: (field: string, min: number) => `${field} phải có ít nhất ${min} ký tự`,
  maxLength: (field: string, max: number) => `${field} không được vượt quá ${max} ký tự`,
  minValue: (field: string, min: number) => `${field} phải lớn hơn hoặc bằng ${min}`,
  maxValue: (field: string, max: number) => `${field} phải nhỏ hơn hoặc bằng ${max}`,
  email: 'Email không hợp lệ',
  phoneNumber: 'Số điện thoại không hợp lệ',
  cccd: 'Số CCCD/CMND không hợp lệ (9-12 chữ số)',
  dateFormat: 'Định dạng ngày không hợp lệ (DD/MM/YYYY)',
  password: 'Mật khẩu phải có ít nhất 6 ký tự',
  passwordMatch: 'Mật khẩu xác nhận không khớp',
  fullName: 'Họ tên chỉ chứa chữ cái và khoảng trắng',
  taxCode: 'Mã số thuế phải có 10 hoặc 13 chữ số',
  householdCode: 'Mã hộ gia đình không hợp lệ',
  ageRestriction: (min: number) => `Phải từ ${min} tuổi trở lên`,
  futureDate: 'Ngày không được trong tương lai',
  pastDate: 'Ngày phải trong quá khứ',
  invalidDate: 'Ngày không hợp lệ',
  pattern: (field: string = 'Giá trị') => `${field} không đúng định dạng`,
  notMatch: (field1: string, field2: string) => `${field1} không khớp với ${field2}`,
  duplicate: (field: string = 'Giá trị') => `${field} đã tồn tại`,
  notFound: (field: string = 'Dữ liệu') => `Không tìm thấy ${field}`,
  bienSoXe: 'Biển số xe không hợp lệ (VD: 29A-12345, 51F1-12345)',
  soKhung: 'Số khung xe không hợp lệ (phải có 17 ký tự)',
  soMay: 'Số máy xe không hợp lệ (6-17 ký tự)',
};

// ===========================
// VALIDATION RULES FACTORY
// ===========================

/**
 * Factory for creating validation rules with react-hook-form
 */
export const validationRules = {
  /**
   * Required field
   */
  required: (fieldName: string): RegisterOptions => ({
    required: errorMessages.required(fieldName),
  }),

  /**
   * Optional field - no validation if empty
   */
  optional: (): RegisterOptions => ({}),

  /**
   * String length validation
   */
  stringLength: (min?: number, max?: number, fieldName: string = 'Trường này'): RegisterOptions => ({
    minLength: min ? {value: min, message: errorMessages.minLength(fieldName, min)} : undefined,
    maxLength: max ? {value: max, message: errorMessages.maxLength(fieldName, max)} : undefined,
  }),

  /**
   * Number range validation
   */
  numberRange: (min?: number, max?: number, fieldName: string = 'Giá trị'): RegisterOptions => ({
    min: min !== undefined ? {value: min, message: errorMessages.minValue(fieldName, min)} : undefined,
    max: max !== undefined ? {value: max, message: errorMessages.maxValue(fieldName, max)} : undefined,
  }),

  /**
   * Pattern validation
   */
  pattern: (pattern: RegExp, errorMessage?: string): RegisterOptions => ({
    pattern: {
      value: pattern,
      message: errorMessage || errorMessages.pattern(),
    },
  }),

  /**
   * Email validation
   */
  email: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Email')}),
    pattern: {
      value: patterns.email,
      message: errorMessages.email,
    },
  }),

  /**
   * Phone number validation
   */
  phoneNumber: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Số điện thoại')}),
    pattern: {
      value: patterns.phoneNumber,
      message: errorMessages.phoneNumber,
    },
  }),

  /**
   * BHXH code validation
   */
  bhxhCode: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Mã số BHXH')}),
  }),

  /**
   * CCCD/CMND validation
   */
  cccd: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Số CCCD/CMND')}),
    pattern: {
      value: patterns.cccd,
      message: errorMessages.cccd,
    },
  }),

  /**
   * Full name validation
   */
  fullName: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Họ tên')}),
    pattern: {
      value: patterns.fullName,
      message: errorMessages.fullName,
    },
  }),

  /**
   * Date validation (DD/MM/YYYY format)
   */
  date: (
    required: boolean = true,
    options?: {
      minAge?: number;
      maxAge?: number;
      futureAllowed?: boolean;
      pastRequired?: boolean;
    },
  ): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Ngày')}),
    validate: {
      validDate: value => {
        if (!value && !required) return true;
        if (!value) return false;

        // Handle Date object
        if (value instanceof Date) {
          return moment(value).isValid() || errorMessages.invalidDate;
        }

        // Handle string format DD/MM/YYYY
        if (typeof value === 'string') {
          if (!patterns.dateFormat.test(value)) {
            return errorMessages.dateFormat;
          }
          const [day, month, year] = value.split('/').map(Number);
          const date = new Date(year, month - 1, day);
          if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
            return errorMessages.invalidDate;
          }
        }

        return true;
      },
      ...(options?.minAge && {
        minAge: value => {
          if (!value) return true;
          const birthDate = typeof value === 'string' ? moment(value, 'DD/MM/YYYY').toDate() : value;
          const age = moment().diff(moment(birthDate), 'years');
          return age >= options.minAge! || errorMessages.ageRestriction(options.minAge!);
        },
      }),
      ...(options?.futureAllowed === false && {
        notFuture: value => {
          if (!value) return true;
          const date = typeof value === 'string' ? moment(value, 'DD/MM/YYYY').toDate() : value;
          return date <= new Date() || errorMessages.futureDate;
        },
      }),
      ...(options?.pastRequired && {
        pastDate: value => {
          if (!value) return true;
          const date = typeof value === 'string' ? moment(value, 'DD/MM/YYYY').toDate() : value;
          return date < new Date() || errorMessages.pastDate;
        },
      }),
    },
  }),

  /**
   * Password validation
   */
  password: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Mật khẩu')}),
  }),

  /**
   * Confirm password validation
   */
  confirmPassword: (passwordFieldName: string = 'password', getValues: () => any): RegisterOptions => ({
    required: errorMessages.required('Xác nhận mật khẩu'),
    validate: value => {
      const password = getValues()[passwordFieldName];
      return value === password || errorMessages.passwordMatch;
    },
  }),

  /**
   * Address validation
   */
  address: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Địa chỉ')}),
  }),

  /**
   * Select/Dropdown validation
   */
  select: (fieldName: string, required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required(fieldName)}),
    validate: value => {
      if (!required) return true;
      return (value && value !== '') || errorMessages.required(fieldName);
    },
  }),

  /**
   * Gender validation
   */
  gender: (required: boolean = true): RegisterOptions => ({
    ...(required && {required: errorMessages.required('Giới tính')}),
    validate: value => {
      if (!required) return true;
      return (value && value !== '') || errorMessages.required('Giới tính');
    },
  }),

  /**
   * Custom validation
   */
  custom: (validate: (value: any) => true | string, required: boolean = false, fieldName?: string): RegisterOptions => ({
    ...(required && fieldName && {required: errorMessages.required(fieldName)}),
    validate,
  }),

  /**
   * Composite validation - combine multiple rules
   */
  compose: (...rules: RegisterOptions[]): RegisterOptions => {
    const merged: RegisterOptions = {};

    rules.forEach(rule => {
      if (!rule) return;

      // Merge each property carefully
      Object.keys(rule).forEach(key => {
        const ruleKey = key as keyof RegisterOptions;
        if (ruleKey === 'validate' && merged.validate) {
          // Special handling for validate functions
          const existingValidate = merged.validate;
          const newValidate = rule.validate;

          if (typeof existingValidate === 'function' && typeof newValidate === 'function') {
            merged.validate = value => {
              // Call existing validate function (react-hook-form expects 1 parameter)
              const result1 = (existingValidate as any)(value);
              if (result1 !== true) return result1;

              // Call new validate function
              const result2 = (newValidate as any)(value);
              return result2;
            };
          } else if (typeof existingValidate === 'object' && typeof newValidate === 'object') {
            merged.validate = {...existingValidate, ...newValidate};
          } else {
            merged.validate = newValidate;
          }
        } else {
          (merged as any)[ruleKey] = (rule as any)[ruleKey];
        }
      });
    });

    return merged;
  },
};

// ===========================
// CROSS-FIELD VALIDATION HELPERS
// ===========================

/**
 * Helper function for validating end date against start date
 * Use this with react-hook-form's validate option
 */
export const validateEndDate = (getValues: () => any) => (endDate: any) => {
  if (!endDate) return 'Vui lòng chọn ngày kết thúc';

  const formValues = getValues();
  const startDate = formValues?.startDate;

  if (!startDate) return true; // If no start date, only check if end date exists

  const endDateObj = new Date(endDate);
  const startDateObj = new Date(startDate);

  // Set time to start of day for accurate comparison
  startDateObj.setHours(0, 0, 0, 0);
  endDateObj.setHours(0, 0, 0, 0);

  // End date must be after start date
  if (endDateObj <= startDateObj) {
    return 'Ngày kết thúc phải sau ngày bắt đầu';
  }

  // Check minimum insurance period (at least 30 days)
  const daysDiff = Math.floor((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24));
  if (daysDiff < 30) {
    return 'Thời gian tham gia tối thiểu là 30 ngày';
  }

  // Check maximum insurance period (1 year)
  if (daysDiff > 365) {
    return 'Thời gian tham gia tối đa là 1 năm';
  }

  return true;
};

// ===========================
// FORM SPECIFIC VALIDATORS
// ===========================

/**
 * Login form validation
 */
export const loginFormValidation = {
  username: validationRules.compose(validationRules.required('Tên đăng nhập'), validationRules.stringLength(3, 50, 'Tên đăng nhập')),
  password: validationRules.password(),
  rememberMe: validationRules.optional(),
};

/**
 * Registration form validation
 */
export const registerFormValidation = {
  fullName: validationRules.fullName(),
  email: validationRules.email(),
  phoneNumber: validationRules.phoneNumber(),
  cccd: validationRules.cccd(),
  bhxhCode: validationRules.bhxhCode(false), // Optional
  password: validationRules.password(),
  confirmPassword: (getValues: () => any) => validationRules.confirmPassword('password', getValues),
  agreeTerms: validationRules.custom(value => value === true || 'Bạn phải đồng ý với điều khoản sử dụng', true),
};

/**
 * Search form validation (all fields optional but at least one required)
 */
export const searchFormValidation = (getValues: () => any) => ({
  bhxhCode: validationRules.compose(
    validationRules.bhxhCode(false),
    validationRules.custom(() => {
      const values = getValues();
      const hasAnyValue = values.bhxhCode || values.cccd || values.fullName || values.phoneNumber;
      return hasAnyValue || 'Vui lòng nhập ít nhất một thông tin để tìm kiếm';
    }),
  ),
  cccd: validationRules.cccd(false),
  fullName: validationRules.fullName(false),
  dateOfBirth: validationRules.date(false, {futureAllowed: false}),
  phoneNumber: validationRules.phoneNumber(false),
});

/**
 * Household form validation
 */
export const householdFormValidation = {
  // Household information
  typeHousehold: validationRules.select('Loại hộ gia đình'),
  provinceName: validationRules.select('Tỉnh/Thành phố'),
  wardName: validationRules.select('Phường/Xã'),
  address: validationRules.address(),

  // Household owner information
  householdOwnerCodeBHXH: validationRules.bhxhCode(),
  householdOwnerName: validationRules.fullName(),
  householdOwnerCCCD: validationRules.cccd(),
  householdOwnerGender: validationRules.gender(),
  householdOwnerDateOfBirth: validationRules.date(true, {
    minAge: 18,
    futureAllowed: false,
  }),
  householdOwnerPhone: validationRules.phoneNumber(), // Optional
  householdOwnerEmail: validationRules.email(false), // Optional
};

/**
 * Household member form validation
 * Used for adding/editing household member information
 */
export const householdMemberValidation = {
  // Household information
  typeHousehold: validationRules.select('Loại hộ gia đình'),
  provinceName: validationRules.select('Tỉnh/Thành phố'),
  wardName: validationRules.select('Phường/Xã'),
  address: validationRules.address(),

  // Member personal information
  name: validationRules.fullName(),
  cccd: validationRules.cccd(),
  codeBHXH: validationRules.bhxhCode(false), // Optional for members
  gender: validationRules.custom(
    value => {
      if (value === undefined || value === null) {
        return 'Vui lòng chọn giới tính';
      }
      return true;
    },
    true,
    'Giới tính',
  ),
  dateOfBirth: validationRules.date(true, {
    minAge: 0, // Allow babies and children
    futureAllowed: false,
  }),
  phone: validationRules.phoneNumber(),
  email: validationRules.email(), // Optional

  // Relationship with household head
  relationship: validationRules.select('Mối quan hệ với chủ hộ'),
};

/**
 * Comprehensive household member form validation
 * Includes all fields with proper validation rules
 */
export const householdMemberFormValidation = {
  // Basic member information
  codeBHXH: {
    required: false,
  },
  cccd: {
    required: errorMessages.required('Số CCCD'),
    pattern: {
      value: patterns.cccd,
      message: errorMessages.cccd,
    },
  },
  name: {
    required: errorMessages.required('Họ và tên'),
    minLength: {
      value: 2,
      message: errorMessages.minLength('Họ và tên', 2),
    },
    maxLength: {
      value: 100,
      message: errorMessages.maxLength('Họ và tên', 100),
    },
    pattern: {
      value: patterns.fullName,
      message: errorMessages.fullName,
    },
  },
  dateOfBirth: {
    required: errorMessages.required('Ngày sinh'),
    validate: {
      validDate: (value: any) => {
        if (!value) return errorMessages.required('Ngày sinh');

        // Handle Date object
        if (value instanceof Date) {
          if (!moment(value).isValid()) {
            return errorMessages.invalidDate;
          }
          // Check if date is in the future
          if (value > new Date()) {
            return errorMessages.futureDate;
          }
          return true;
        }

        return errorMessages.invalidDate;
      },
    },
  },
  phone: {
    required: false,
    pattern: {
      value: patterns.phoneNumber,
      message: errorMessages.phoneNumber,
    },
  },
  email: {
    required: false,
    pattern: {
      value: patterns.email,
      message: errorMessages.email,
    },
  },
  relationship: {
    required: errorMessages.required('Mối quan hệ với chủ hộ'),
    validate: {
      notEmpty: (value: any) => {
        return (value && value !== '') || errorMessages.required('Mối quan hệ với chủ hộ');
      },
    },
  },

  // Address information
  typeHousehold: {
    required: errorMessages.required('Loại hộ gia đình'),
    validate: {
      notEmpty: (value: any) => {
        return (value && value !== '') || errorMessages.required('Loại hộ gia đình');
      },
    },
  },
  provinceName: {
    required: errorMessages.required('Tỉnh/Thành phố'),
    validate: {
      notEmpty: (value: any) => {
        return (value && value !== '') || errorMessages.required('Tỉnh/Thành phố');
      },
    },
  },
  wardName: {
    required: errorMessages.required('Phường/Xã'),
    validate: {
      notEmpty: (value: any) => {
        return (value && value !== '') || errorMessages.required('Phường/Xã');
      },
    },
  },
  address: {
    required: errorMessages.required('Địa chỉ'),
    minLength: {
      value: 5,
      message: errorMessages.minLength('Địa chỉ', 5),
    },
    maxLength: {
      value: 200,
      message: errorMessages.maxLength('Địa chỉ', 200),
    },
  },
};

/**
 * Profile form validation
 */
export const profileFormValidation = {
  fullName: validationRules.fullName(),
  email: validationRules.email(),
  phoneNumber: validationRules.phoneNumber(),
  dateOfBirth: validationRules.date(true, {
    minAge: 16,
    futureAllowed: false,
  }),
  gender: validationRules.select('Giới tính'),
  address: validationRules.address(),
  cccd: validationRules.cccd(),
  bhxhCode: validationRules.bhxhCode(false),
};

/**
 * Change password form validation
 */
export const changePasswordValidation = (getValues: () => any) => ({
  currentPassword: validationRules.password(),
  newPassword: validationRules.compose(
    validationRules.password(),
    validationRules.custom(value => {
      const currentPassword = getValues().currentPassword;
      return value !== currentPassword || 'Mật khẩu mới phải khác mật khẩu hiện tại';
    }),
  ),
  confirmPassword: validationRules.confirmPassword('newPassword', getValues),
});

/**
 * Payment form validation
 */
export const paymentFormValidation = {
  amount: validationRules.compose(validationRules.required('Số tiền'), validationRules.numberRange(10000, *********, 'Số tiền')),
  paymentMethod: validationRules.select('Phương thức thanh toán'),
  bankAccount: validationRules.pattern(/^\d{10,20}$/, 'Số tài khoản không hợp lệ (10-20 chữ số)'),
  notes: validationRules.stringLength(0, 500, 'Ghi chú'),
};

// ===========================
// UTILITY FUNCTIONS
// ===========================

/**
 * Format phone number for display
 */
export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
  }
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
  }
  return phone;
};

/**
 * Format BHXH code for display
 */
export const formatBHXHCode = (code: string): string => {
  const cleaned = code.replace(/\D/g, '');
  // Format any length of BHXH code by grouping digits
  if (cleaned.length >= 8) {
    return cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d+)/, '$1 $2 $3 $4');
  }
  return code;
};

/**
 * Format currency for display
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

/**
 * Parse date from DD/MM/YYYY string
 */
export const parseDate = (dateString: string): Date | null => {
  if (!patterns.dateFormat.test(dateString)) return null;
  const [day, month, year] = dateString.split('/').map(Number);
  return new Date(year, month - 1, day);
};

/**
 * Format date to DD/MM/YYYY string
 */
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  if (!d || isNaN(d.getTime())) return '';
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};

/**
 * Validate single field for manual validation
 */
export const validateField = (value: any, rules: RegisterOptions): string | undefined => {
  if (rules.required && !value) {
    return typeof rules.required === 'string' ? rules.required : 'Trường này là bắt buộc';
  }

  if (rules.minLength && value) {
    const minLengthRule = rules.minLength;
    const minValue = typeof minLengthRule === 'number' ? minLengthRule : minLengthRule.value;
    const message = typeof minLengthRule === 'object' ? minLengthRule.message : undefined;

    if (value.length < minValue) {
      return message || `Tối thiểu ${minValue} ký tự`;
    }
  }

  if (rules.maxLength && value) {
    const maxLengthRule = rules.maxLength;
    const maxValue = typeof maxLengthRule === 'number' ? maxLengthRule : maxLengthRule.value;
    const message = typeof maxLengthRule === 'object' ? maxLengthRule.message : undefined;

    if (value.length > maxValue) {
      return message || `Tối đa ${maxValue} ký tự`;
    }
  }

  if (rules.pattern && value) {
    const patternRule = rules.pattern;
    const pattern = patternRule instanceof RegExp ? patternRule : patternRule.value;
    const message = typeof patternRule === 'object' && !(patternRule instanceof RegExp) ? patternRule.message : undefined;

    if (!pattern.test(value)) {
      return message || 'Định dạng không hợp lệ';
    }
  }

  if (rules.validate && value) {
    const result =
      typeof rules.validate === 'function'
        ? rules.validate(value, {}) // Add empty object as second parameter
        : Object.values(rules.validate)
            .map(fn => fn(value, {}))
            .find(r => r !== true); // Add empty object as second parameter

    if (result !== true) {
      return result as string;
    }
  }

  return undefined;
};

/**
 * Create dynamic validation based on conditions
 */
export const createDynamicValidation = (
  fieldType: string,
  options: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    customMessage?: string;
    [key: string]: any;
  },
): RegisterOptions => {
  const {required, min, max, pattern, customMessage, ...rest} = options;

  switch (fieldType) {
    case 'text':
      return validationRules.compose(required ? validationRules.required(customMessage || 'Trường này') : {}, validationRules.stringLength(min, max, customMessage || 'Trường này'));

    case 'email':
      return validationRules.email(required);

    case 'phone':
      return validationRules.phoneNumber(required);

    case 'bhxh':
      return validationRules.bhxhCode(required);

    case 'cccd':
      return validationRules.cccd(required);

    case 'date':
      return validationRules.date(required, rest);

    case 'select':
      return validationRules.select(customMessage || 'Trường này', required);

    case 'pattern':
      return validationRules.compose(required ? validationRules.required(customMessage || 'Trường này') : {}, pattern ? validationRules.pattern(pattern, customMessage) : {});

    default:
      return required ? validationRules.required(customMessage || 'Trường này') : {};
  }
};

/**
 * Validation for household form fields (for non-hook-form usage)
 */
export const validateHouseholdFormFields = (data: any) => {
  const errors: Record<string, string> = {};

  // Required fields validation
  if (!data.typeHousehold?.trim()) {
    errors.typeHousehold = errorMessages.required('Loại hộ gia đình');
  }

  if (!data.provinceName?.trim()) {
    errors.provinceName = errorMessages.required('Tỉnh/Thành phố');
  }

  if (!data.wardName?.trim()) {
    errors.wardName = errorMessages.required('Phường/Xã');
  }

  if (!data.address?.trim()) {
    errors.address = errorMessages.required('Địa chỉ');
  } else if (data.address.length < 5) {
    errors.address = errorMessages.minLength('Địa chỉ', 5);
  } else if (data.address.length > 200) {
    errors.address = errorMessages.maxLength('Địa chỉ', 200);
  }

  if (!data.householdOwnerCodeBHXH?.trim()) {
    errors.householdOwnerCodeBHXH = errorMessages.required('Mã BHXH');
  }

  if (!data.householdOwnerName?.trim()) {
    errors.householdOwnerName = errorMessages.required('Họ tên chủ hộ');
  } else if (!patterns.fullName.test(data.householdOwnerName)) {
    errors.householdOwnerName = errorMessages.fullName;
  }

  if (!data.householdOwnerCCCD?.trim()) {
    errors.householdOwnerCCCD = errorMessages.required('Số CCCD');
  } else if (!patterns.cccd.test(data.householdOwnerCCCD)) {
    errors.householdOwnerCCCD = errorMessages.cccd;
  }

  if (!data.householdOwnerDateOfBirth) {
    errors.householdOwnerDateOfBirth = errorMessages.required('Ngày sinh');
  }

  // Optional fields validation (if provided)
  if (data.householdOwnerPhone && !patterns.phoneNumber.test(data.householdOwnerPhone)) {
    errors.householdOwnerPhone = errorMessages.phoneNumber;
  }

  if (data.householdOwnerEmail && !patterns.email.test(data.householdOwnerEmail)) {
    errors.householdOwnerEmail = errorMessages.email;
  }

  return errors;
};

/**
 * Validation for household member form fields (for non-hook-form usage)
 */
export const validateHouseholdMemberFields = (data: any) => {
  const errors: Record<string, string> = {};

  // Required fields validation
  if (!data.name?.trim()) {
    errors.name = errorMessages.required('Họ và tên');
  } else if (data.name.length < 2) {
    errors.name = errorMessages.minLength('Họ và tên', 2);
  } else if (data.name.length > 100) {
    errors.name = errorMessages.maxLength('Họ và tên', 100);
  } else if (!patterns.fullName.test(data.name)) {
    errors.name = errorMessages.fullName;
  }

  if (!data.cccd?.trim()) {
    errors.cccd = errorMessages.required('Số CCCD');
  } else if (!patterns.cccd.test(data.cccd)) {
    errors.cccd = errorMessages.cccd;
  }

  if (!data.dateOfBirth) {
    errors.dateOfBirth = errorMessages.required('Ngày sinh');
  } else if (data.dateOfBirth > new Date()) {
    errors.dateOfBirth = errorMessages.futureDate;
  }

  if (data.gender === undefined || data.gender === null) {
    errors.gender = errorMessages.required('Giới tính');
  }

  if (!data.relationship?.trim()) {
    errors.relationship = errorMessages.required('Mối quan hệ với chủ hộ');
  }

  if (!data.typeHousehold?.trim()) {
    errors.typeHousehold = errorMessages.required('Loại hộ gia đình');
  }

  if (!data.provinceName?.trim()) {
    errors.provinceName = errorMessages.required('Tỉnh/Thành phố');
  }

  if (!data.wardName?.trim()) {
    errors.wardName = errorMessages.required('Phường/Xã');
  }

  if (!data.address?.trim()) {
    errors.address = errorMessages.required('Địa chỉ');
  } else if (data.address.length < 5) {
    errors.address = errorMessages.minLength('Địa chỉ', 5);
  } else if (data.address.length > 200) {
    errors.address = errorMessages.maxLength('Địa chỉ', 200);
  }

  // Optional fields validation (if provided)
  // BHXH code validation removed - no pattern required

  if (data.phone && !patterns.phoneNumber.test(data.phone)) {
    errors.phone = errorMessages.phoneNumber;
  }

  if (data.email && !patterns.email.test(data.email)) {
    errors.email = errorMessages.email;
  }

  return errors;
};

/**
 * Validation for detail product form (phiếu thu hộ bảo hiểm)
 */
export const detailProductFormValidation = {
  // Thông tin thu tiền
  so_bien_lai: validationRules.required('Số biên lai'),
  mau_bien_lai: validationRules.stringLength(0, 50, 'Mẫu biên lai'),
  quyen_bien_lai: validationRules.stringLength(0, 50, 'Quyển biên lai'),
  don_vi_bhxh: validationRules.select('Đơn vị bảo hiểm'),
  noi_dung_thu: validationRules.required('Nội dung thu'),
  ghi_chu: validationRules.stringLength(0, 500, 'Ghi chú'),

  // BHXH tự nguyện
  bhtn_so_thang_dong: validationRules.compose(validationRules.required('Số tháng đóng BHXH'), {
    validate: {
      positiveNumber: (value: string) => {
        const num = parseInt(value) || 0;
        return num > 0 || 'Số tháng đóng phải lớn hơn 0';
      },
    },
  }),
  bhtn_dong_tu_thang: validationRules.date(true, {futureAllowed: true}),
  bhtn_muc_tien_dong: validationRules.compose(validationRules.required('Mức tiền căn cứ'), {
    validate: {
      validAmount: (value: string) => {
        const amount = parseCurrency(value);
        return amount > 0 || 'Mức tiền căn cứ phải lớn hơn 0';
      },
    },
  }),
  bhtn_muc_dong_thang: validationRules.compose(validationRules.required('Mức đóng/tháng'), {
    validate: {
      validAmount: (value: string) => {
        const amount = parseCurrency(value);
        return amount > 0 || 'Mức đóng/tháng phải lớn hơn 0';
      },
    },
  }),
  bhtn_tai_tuc: validationRules.select('Tái tục BHXH', true),

  // BHYT
  bhyt_so_thang_dong: validationRules.compose(validationRules.required('Số tháng đóng BHYT'), {
    validate: {
      positiveNumber: (value: string) => {
        const num = parseInt(value) || 0;
        return num > 0 || 'Số tháng đóng phải lớn hơn 0';
      },
    },
  }),
  bhyt_dong_tu_thang: validationRules.date(true, {futureAllowed: true}),
  bhyt_noi_dang_ky_kcb: validationRules.compose(validationRules.required('Nơi đăng ký KCB'), validationRules.stringLength(2, 200, 'Nơi đăng ký KCB')),
  bhyt_muc_dong_thang: validationRules.compose(validationRules.required('Số tiền đóng BHYT'), {
    validate: {
      validAmount: (value: string) => {
        const amount = parseCurrency(value);
        return amount > 0 || 'Số tiền đóng phải lớn hơn 0';
      },
    },
  }),
  bhyt_tai_tuc: validationRules.select('Tái tục BHYT', true),
};

/**
 * Dynamic validation for detail product form based on selected products
 */
export const createDetailProductValidation = (selectedProducts: string[]) => {
  const baseValidation = {
    // Thông tin thu tiền - always required
    so_bien_lai: detailProductFormValidation.so_bien_lai,
    mau_bien_lai: detailProductFormValidation.mau_bien_lai,
    quyen_bien_lai: detailProductFormValidation.quyen_bien_lai,
    don_vi_bhxh: detailProductFormValidation.don_vi_bhxh,
    noi_dung_thu: detailProductFormValidation.noi_dung_thu,
    ghi_chu: detailProductFormValidation.ghi_chu,
  };

  const conditionalValidation: any = {};

  // Add BHXH validation if selected
  if (selectedProducts.includes('bhxh')) {
    conditionalValidation.bhtn_so_thang_dong = detailProductFormValidation.bhtn_so_thang_dong;
    conditionalValidation.bhtn_dong_tu_thang = detailProductFormValidation.bhtn_dong_tu_thang;
    conditionalValidation.bhtn_muc_tien_dong = detailProductFormValidation.bhtn_muc_tien_dong;
    conditionalValidation.bhtn_muc_dong_thang = detailProductFormValidation.bhtn_muc_dong_thang;
    conditionalValidation.bhtn_tai_tuc = detailProductFormValidation.bhtn_tai_tuc;
  }

  // Add BHYT validation if selected
  if (selectedProducts.includes('bhyt')) {
    conditionalValidation.bhyt_so_thang_dong = detailProductFormValidation.bhyt_so_thang_dong;
    conditionalValidation.bhyt_dong_tu_thang = detailProductFormValidation.bhyt_dong_tu_thang;
    conditionalValidation.bhyt_noi_dang_ky_kcb = detailProductFormValidation.bhyt_noi_dang_ky_kcb;
    conditionalValidation.bhyt_muc_dong_thang = detailProductFormValidation.bhyt_muc_dong_thang;
    conditionalValidation.bhyt_tai_tuc = detailProductFormValidation.bhyt_tai_tuc;
  }

  return {...baseValidation, ...conditionalValidation};
};

/**
 * Insurance participant form validation (Thông tin người được bảo hiểm)
 */
export const insuranceParticipantFormValidation = {
  // Mối quan hệ với người mua bảo hiểm
  relationshipWithBuyer: validationRules.select('Mối quan hệ với người mua bảo hiểm'),

  // Họ và tên
  fullName: validationRules.fullName(true),

  // Giới tính
  gender: validationRules.custom((value: string) => {
    if (!value) return 'Vui lòng chọn giới tính';
    if (!['NAM', 'NU', 'male', 'female'].includes(value)) return 'Giới tính không hợp lệ';
    return true;
  }, true),

  // Ngày sinh
  dateOfBirth: validationRules.compose(
    validationRules.required('Ngày sinh'),
    validationRules.custom((value: Date) => {
      if (!value) return 'Vui lòng chọn ngày sinh';

      const today = new Date();
      const birthDate = new Date(value);

      // Check if date is in the future
      if (birthDate > today) {
        return 'Ngày sinh không được là ngày trong tương lai';
      }

      // Calculate age
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      // Check minimum age (must be at least 1 year old)
      if (age < 1) {
        return 'Người được bảo hiểm phải từ 1 tuổi trở lên';
      }

      // Check maximum age (150 years)
      if (age > 150) {
        return 'Ngày sinh không hợp lệ';
      }

      return true;
    }),
  ),

  // Tuổi (auto-calculated, read-only but still validate)
  age: validationRules.compose(
    validationRules.required('Tuổi'),
    validationRules.custom((value: string) => {
      const age = parseInt(value);
      if (isNaN(age)) return 'Tuổi không hợp lệ';
      if (age < 1) return 'Tuổi phải từ 1 trở lên';
      if (age > 150) return 'Tuổi không hợp lệ';
      return true;
    }),
  ),

  // Số giấy tờ tùy thân (CCCD/CMND)
  idNumber: validationRules.cccd(true),

  // Địa chỉ
  address: validationRules.address(true),

  // Tỉnh/Thành phố
  province: validationRules.select('Tỉnh/Thành phố'),

  // Email
  email: validationRules.email(false),
  phone: validationRules.phoneNumber(true),
};

/**
 * Insurance package selection form validation (Chọn gói bảo hiểm)
 */
export const insurancePackageSelectionValidation = {
  // Ngày bắt đầu tham gia
  startDate: validationRules.compose(
    validationRules.required('Ngày bắt đầu'),
    validationRules.custom((value: Date) => {
      if (!value) return 'Vui lòng chọn ngày bắt đầu';

      const today = new Date();
      const startDate = new Date(value);

      // Set time to start of day for accurate comparison
      today.setHours(0, 0, 0, 0);
      startDate.setHours(0, 0, 0, 0);

      // Start date cannot be in the past
      if (startDate < today) {
        return 'Ngày bắt đầu không thể là ngày trong quá khứ';
      }

      return true;
    }),
  ),

  // Ngày kết thúc tham gia
  endDate: validationRules.required('Ngày kết thúc'),

  // Gói bảo hiểm được chọn
  selectedPackage: validationRules.compose(
    validationRules.required('Gói bảo hiểm'),
    validationRules.custom((value: string) => {
      if (!value || value === '') {
        return 'Vui lòng chọn một gói bảo hiểm';
      }

      const validPackages = ['package1', 'package2', 'package3'];
      if (!validPackages.includes(value)) {
        return 'Gói bảo hiểm được chọn không hợp lệ';
      }

      return true;
    }),
  ),

  // Quyền lợi bổ sung (không bắt buộc)
  additionalBenefits: validationRules.optional(),
};

/**
 * Thông tin xe VCX Ô tô (Bảo hiểm vật chất xe ô tô)
 */
export const ThongTinXeVCXOtoValidation = {
  // Thông tin xe
  md_sd: validationRules.custom((value: string) => {
    if (!value) return 'Vui lòng chọn mục đích sử dụng';
    if (!['C', 'K'].includes(value)) return 'Lựa chọn không hợp lệ';
    return true;
  }, true),

  loai_xe: validationRules.required('Loại xe'),
  hang_xe: validationRules.required('Hãng xe'),
  hieu_xe: validationRules.required('Hiệu xe'),

  nam_sx: validationRules.compose(validationRules.required('Năm sản xuất'), {
    validate: {
      validYear: (value: string) => {
        const year = parseInt(value, 10);
        if (isNaN(year)) return 'Năm sản xuất không hợp lệ';
        if (year < 1900 || year > new Date().getFullYear() + 1) {
          return `Năm sản xuất phải từ 1900 đến ${new Date().getFullYear() + 1}`;
        }
        return true;
      },
    },
  }),

  so_cho: validationRules.compose(validationRules.required('Số chỗ ngồi'), {
    validate: {
      validSeats: (value: string) => {
        const seats = parseInt(value, 10);
        if (isNaN(seats)) return 'Số chỗ ngồi không hợp lệ';
        if (seats < 1 || seats > 100) return 'Số chỗ ngồi phải từ 1 đến 100';
        return true;
      },
    },
  }),
  trong_tai: validationRules.compose(
    validationRules.required('Trọng tải'),
    validationRules.custom((value: number) => {
      if (!value || value === 0) return 'Trường bắt buộc';
      if (value < 0) return 'Trọng tải phải lớn hơn 0';
      return true;
    }, true),
  ),

  bs_xe: validationRules.required('Biển số xe'),
  so_khung: validationRules.required('Số khung'),
  so_may: validationRules.required('Số máy'),

  // Giá trị xe
  gtri_xe: validationRules.compose(validationRules.required('Trường bắt buộc'), {
    validate: {
      validAmount: (value: string) => {
        if (!value) return 'Trường bắt buộc';
        const amount = parseCurrency(value);
        return amount >= 1000000 || 'Giá trị xe tối thiểu 1,000,000 VNĐ';
      },
    },
  }),

  so_tien_tham_khao: {
    validate: {
      validAmount: (value: string) => {
        if (!value) return 'Trường bắt buộc';
        const amount = parseCurrency(value);
        return amount >= 0 || 'Số tiền không hợp lệ';
      },
    },
  },

  ngay_hl: validationRules.date(true),
  gio_hl: validationRules.required('Giờ hiệu lực'),
  ngay_kt: validationRules.date(true),
  gio_kt: validationRules.required('Giờ kết thúc'),

  // Thông tin chủ xe
  loai_chu_xe: validationRules.custom((value: string) => {
    if (!value) return 'Trường bắt buộc';
    if (!['C', 'T'].includes(value)) return 'Lựa chọn không hợp lệ';
    return true;
  }, true),

  // Cá nhân
  ten: validationRules.compose(validationRules.required('Họ và tên'), validationRules.pattern(patterns.fullName, errorMessages.fullName)),
  so_cmt: validationRules.cccd(true),

  // Tổ chức
  mst: validationRules.compose(validationRules.required('Mã số thuế'), validationRules.pattern(/^\d{10,13}$/, 'Mã số thuế phải có 10-13 số')),

  // Chung
  dia_chi: validationRules.address(true),
  dthoai: validationRules.phoneNumber(true),
};

/**
 * Thông tin xe
 */
export const ThongTinXeFormValidation = {
  ngayBatDau: validationRules.compose(
    validationRules.required('Ngày bắt đầu'),
    validationRules.custom((value: Date) => {
      if (!value) return 'Trường bắt buộc';

      return true;
    }),
  ),

  soNam: validationRules.custom((value: number) => {
    if (!value) return 'Trường bắt buộc';
    if (![1, 2, 3].includes(value)) return 'Số năm không hợp lệ';
    return true;
  }, true),

  loaiXe: validationRules.custom((value: string) => {
    if (!value) return 'Trường bắt buộc';
    return true;
  }, true),

  bienSoXe: validationRules.compose(validationRules.required('Biển số xe')),

  soKhung: validationRules.compose(validationRules.required('Số khung')),

  soMay: validationRules.compose(validationRules.required('Số máy')),

  isChuXe: validationRules.custom((value: string) => {
    if (!value) return 'Vui lòng chọn chủ xe có phải khách hàng không?';
    if (!['C', 'K'].includes(value)) return 'Lựa chọn không hợp lệ';
    return true;
  }, true),

  loaiChuXe: validationRules.custom((value: string) => {
    if (!value) return 'Vui lòng chọn chủ xe là cá nhân hay tổ chức';
    if (!['C', 'T'].includes(value)) return 'Lựa chọn không hợp lệ';
    return true;
  }, true),

  tenChuXe: validationRules.compose(validationRules.required('Tên chủ xe')),
  cmt: validationRules.compose(validationRules.required('CCCD'), validationRules.pattern(patterns.cccd, errorMessages.cccd)),
  mst: validationRules.compose(validationRules.required('Mã số thuế'), validationRules.pattern(/^\d{10,13}$/, 'Mã số thuế phải có 10-13 số')),
  diaChi: validationRules.compose(validationRules.required('Địa chỉ')),
  dthoai: validationRules.compose(validationRules.required('SĐT'), validationRules.pattern(patterns.phoneNumber, errorMessages.phoneNumber)),
  ghTienNam: validationRules.custom((value: number) => {
    if (!value || value === 0) return 'Truờng bắt buộc';
    return true;
  }, true),
  nhaBaoHiem: validationRules.custom((value: string) => {
    if (!value) return 'Vui lòng chọn nhà bảo hiểm';
    return true;
  }, true),
  soCho: validationRules.compose(
    validationRules.required('Số chỗ ngồi'),
    validationRules.custom((value: number) => {
      if (!value || value === 0) return 'Trường bắt buộc';
      if (value < 1) return 'Số chỗ ngồi phải lớn hơn 0';
      if (value > 100) return 'Số chỗ ngồi không hợp lệ';
      return true;
    }, true),
  ),
  trongTai: validationRules.compose(
    validationRules.required('Trọng tải'),
    validationRules.custom((value: number) => {
      if (!value || value === 0) return 'Trường bắt buộc';
      if (value < 0) return 'Trọng tải phải lớn hơn 0';
      return true;
    }, true),
  ),
  hangXe: validationRules.compose(validationRules.required('Hãng xe')),
};

/**
 * Thông tin mua form validation
 */
export const ThongTinMuaFormValidation = {
  tenKH: validationRules.compose(validationRules.required('Họ và tên')),
  gioiTinhKH: validationRules.custom((value: string) => {
    if (!value) return 'Trường bắt buộc';
    if (!['NAM', 'NU'].includes(value)) return 'Giới tính không hợp lệ';
    return true;
  }, true),
  cmtKH: validationRules.cccd(true),
  ngaySinhKH: validationRules.compose(
    validationRules.required('Ngày sinh'),
    validationRules.custom((value: Date) => {
      if (!value) return 'Vui lòng chọn ngày sinh';

      const today = new Date();
      const ngaySinh = new Date(value);

      // Check if date is in the future
      if (ngaySinh > today) {
        return 'Ngày sinh không được là ngày trong tương lai';
      }

      // Calculate age
      let tuoi = today.getFullYear() - ngaySinh.getFullYear();
      const monthDiff = today.getMonth() - ngaySinh.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < ngaySinh.getDate())) {
        tuoi--;
      }

      // Check minimum age (must be at least 1 year old)
      if (tuoi < 1) {
        return 'Người mua phải từ 1 tuổi trở lên';
      }

      return true;
    }),
  ),
  mstKH: validationRules.compose(validationRules.required('Mã số thuế'), validationRules.pattern(/^\d{10,13}$/, 'Mã số thuế phải có 10-13 số')),
  diaChiKH: validationRules.compose(validationRules.required('Địa chỉ')),
  dthoaiKH: validationRules.phoneNumber(true),
  emailKH: validationRules.email(false),
  mqhSoHuu: validationRules.compose(validationRules.required('Mối quan hệ sở hữu')),
  ndbhMoiQuanHe: validationRules.compose(validationRules.required('Mối quan hệ với chủ sở hữu')),
  nganHang: validationRules.compose(validationRules.required('Ngân hàng')),
  stk: validationRules.compose(validationRules.required('Số tài khoản')),
};

/**
 * House insurance information form validation (Thông tin nhà được bảo hiểm)
 */
export const houseInsuranceFormValidation = {
  // Thông tin ngôi nhà
  loai_nha: validationRules.select('Loại nhà/căn hộ'),
  md_sd: validationRules.select('Mục đích sử dụng'),
  tinh_thanh_ten: validationRules.select('Tỉnh/thành'),
  phuong_xa_ten: validationRules.select('Phường/xã'),
  dia_chi: validationRules.address(),
  so_tang: validationRules.compose(validationRules.required('Số tầng'), validationRules.numberRange(1, 100, 'Số tầng')),
  dien_tich: validationRules.compose(validationRules.required('Diện tích'), validationRules.numberRange(10, 10000, 'Diện tích')),
  nam_sd: validationRules.compose(validationRules.required('Năm sử dụng'), validationRules.numberRange(1900, new Date().getFullYear(), 'Năm sử dụng')),
  hinh_thuc_chu_sh: validationRules.select('Hình thức chủ sở hữu'),

  // Thông tin bảo hiểm
  so_tien_bh: validationRules.compose(validationRules.required('Số tiền bảo hiểm'), {
    validate: {
      positiveNumber: (value: string) => {
        if (!value) return true;
        const amount = parseCurrency(value);
        return amount > 0 || 'Số tiền bảo hiểm phải lớn hơn 0';
      },
      minAmount: (value: string) => {
        if (!value) return true;
        const amount = parseCurrency(value);
        return amount >= 10000000 || 'Số tiền bảo hiểm phải từ 10.000.000đ trở lên';
      },
    },
  }),
  thoi_han_bh: validationRules.select('Thời hạn bảo hiểm'),
  gio_bd: validationRules.select('Giờ bắt đầu'),
  ngay_bd: validationRules.select('Ngày bắt đầu'),
  gio_kt: validationRules.select('Giờ kết thúc'),
  ngay_kt: validationRules.select('Ngày kết thúc'),
};

/**
 * Buyer information form validation (Thông tin người mua bảo hiểm)
 */
export const buyerInformationFormValidation = {
  // Personal fo
  // rm validation
  personal: {
    // Họ và tên
    fullName: validationRules.fullName(true),

    // Ngày sinh
    birthDate: validationRules.date(true, {
      futureAllowed: false,
      minAge: 1,
      maxAge: 150,
    }),

    // Số giấy tờ tùy thân (CCCD/CMND)
    idNumber: validationRules.cccd(true),

    // Địa chỉ
    address: validationRules.address(true),

    // Số điện thoại
    phoneNumber: validationRules.phoneNumber(true),

    // Email
    email: validationRules.email(false),

    // Giới tính (NAM/NU format)
    gender: validationRules.custom((value: string) => {
      if (!value) return 'Vui lòng chọn giới tính';
      if (!['NAM', 'NU'].includes(value)) return 'Giới tính không hợp lệ';
      return true;
    }, true),
  },

  // Organization form validation
  organization: {
    // Tên tổ chức
    organizationName: validationRules.compose(validationRules.required('Tên tổ chức'), validationRules.stringLength(3, 200, 'Tên tổ chức')),

    // Mã số thuế
    taxCode: validationRules.compose(validationRules.required('Mã số thuế'), validationRules.pattern(/^\d{10,13}$/, 'Mã số thuế phải có 10-13 số')),

    // Địa chỉ
    address: validationRules.address(true),

    // Số điện thoại
    phoneNumber: validationRules.phoneNumber(true),

    // Email
    email: validationRules.email(true),
  },
};

// ===========================
// EXPORT
// ===========================

export default {
  patterns,
  errorMessages,
  validationRules,

  // Form validations
  loginFormValidation,
  registerFormValidation,
  searchFormValidation,
  householdFormValidation,
  householdMemberValidation,
  householdMemberFormValidation,
  profileFormValidation,
  changePasswordValidation,
  paymentFormValidation,
  detailProductFormValidation,
  insuranceParticipantFormValidation,
  insurancePackageSelectionValidation,
  houseInsuranceFormValidation,
  buyerInformationFormValidation,

  // Utilities
  formatPhoneNumber,
  formatBHXHCode,
  formatCurrency,
  parseDate,
  formatDate,
  validateField,
  createDynamicValidation,
  createDetailProductValidation,
  validateHouseholdFormFields,
  validateHouseholdMemberFields,
};
