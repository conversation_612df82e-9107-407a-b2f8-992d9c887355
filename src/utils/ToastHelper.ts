import {ToastItem, ToastType} from '@components/common/Toast';

class ToastHelperClass {
  private static instance: ToastHelperClass;
  private toastRef: any = null;

  private constructor() {}

  static getInstance(): ToastHelperClass {
    if (!ToastHelperClass.instance) {
      ToastHelperClass.instance = new ToastHelperClass();
    }
    return ToastHelperClass.instance;
  }

  setRef(ref: any) {
    this.toastRef = ref;
  }

  showToast(item: Omit<ToastItem, 'id'>) {
    if (this.toastRef && this.toastRef.showToast) {
      this.toastRef.showToast(item);
    } else {
      console.warn('Toast ref is not set');
    }
  }

  success(message: string, options?: Partial<Omit<ToastItem, 'id' | 'type' | 'message'>>) {
    this.showToast({
      type: 'success',
      message,
      ...options,
    });
  }

  error(message: string, options?: Partial<Omit<ToastItem, 'id' | 'type' | 'message'>>) {
    this.showToast({
      type: 'error',
      message,
      ...options,
    });
  }

  info(message: string, options?: Partial<Omit<ToastItem, 'id' | 'type' | 'message'>>) {
    this.showToast({
      type: 'info',
      message,
      ...options,
    });
  }

  warning(message: string, options?: Partial<Omit<ToastItem, 'id' | 'type' | 'message'>>) {
    this.showToast({
      type: 'warning',
      message,
      ...options,
    });
  }
}

export const ToastHelper = ToastHelperClass.getInstance();