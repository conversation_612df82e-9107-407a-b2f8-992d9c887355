/**
 * Utilities for formatting data display
 */

/**
 * Format currency to Vietnamese format
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  })
    .format(amount)
    .replace('₫', 'đ');
};

/**
 * Format large numbers with K, M, B suffixes
 * @param num - The number to format
 * @returns Formatted number string
 */
export const formatLargeNumber = (num: number): string => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(1) + 'B';
  }
  if (num >= 1e6) {
    return (num / 1e6).toFixed(1) + 'M';
  }
  if (num >= 1e3) {
    return (num / 1e3).toFixed(1) + 'K';
  }
  return num.toString();
};

/**
 * Format percentage
 * @param value - The value to format as percentage
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Format date to Vietnamese format
 * @param date - The date to format
 * @returns Formatted date string
 */
export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
};

/**
 * Parse date string from API and return Date object for DateTimePickerComponent
 * @param dateString - The date string from API (various formats)
 * @returns Date object or undefined if invalid
 */
export const parseDateFromAPI = (dateString: string): Date | undefined => {
  if (!dateString) return undefined;

  // Import moment locally to avoid global import
  const moment = require('moment');

  // Try parsing with common formats
  const formats = ['DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY/MM/DD'];

  for (const format of formats) {
    const parsedDate = moment(dateString, format, true);
    if (parsedDate.isValid()) {
      return parsedDate.toDate();
    }
  }

  return undefined;
};

/**
 * Format Date object to DD/MM/YYYY string for API
 * @param date - The Date object to format (can be undefined)
 * @returns Formatted date string in DD/MM/YYYY format or empty string
 */
export const formatDateForAPI = (date: Date | undefined): string => {
  if (!date) return '';

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

/**
 * Format gender from API response to display format
 * @param gender - Raw gender value from API (NAM, NU, 1, 0, etc.)
 * @returns Formatted gender string (Nam, Nữ)
 */
export const formatGender = (gender: string | number | undefined | null): string => {
  if (!gender && gender !== 0) {
    return 'N/A';
  }

  const genderStr = String(gender).toUpperCase().trim();

  switch (genderStr) {
    case 'NAM':
    case '1':
    case 'MALE':
    case 'M':
      return 'Nam';
    case 'NU':
    case 'NỮ':
    case '0':
    case 'FEMALE':
    case 'F':
      return 'Nữ';
    default:
      // If already formatted correctly, return as is
      if (genderStr === 'NAM') return 'Nam';
      if (genderStr === 'NỮ') return 'Nữ';
      return 'N/A';
  }
};

/**
 * Convert display gender back to API format (for form submissions)
 * @param displayGender - Formatted gender (Nam, Nữ, male, female) or boolean
 * @returns API gender format (NAM, NU)
 */
export const toApiGender = (displayGender: string | boolean): string => {
  if (typeof displayGender === 'boolean') {
    return displayGender ? 'NAM' : 'NU';
  }

  const genderStr = String(displayGender).toUpperCase().trim();

  switch (genderStr) {
    case 'NAM':
    case '1':
    case 'MALE':
    case 'M':
      return 'NAM';
    case 'NỮ':
    case 'NU':
    case '0':
    case 'FEMALE':
    case 'F':
      return 'NU';
    default:
      return 'NAM'; // Default fallback
  }
};

/**
 * Check if gender value represents male
 * @param gender - Gender value
 * @returns true if male, false otherwise
 */
export const isMale = (gender: string | number | boolean): boolean => {
  if (typeof gender === 'boolean') {
    return gender;
  }

  const genderStr = String(gender).toUpperCase().trim();
  return genderStr === 'NAM' || genderStr === '1' || genderStr === 'MALE' || genderStr === 'M';
};

/**
 * Format date number from API (YYYYMMDD) to display format (DD/MM/YYYY)
 * @param dateNumber - Date in YYYYMMDD format (e.g., 20250124)
 * @returns Formatted date string in DD/MM/YYYY format or empty string if invalid
 */
export const formatDateFromAPI = (dateNumber: number | string | undefined | null): string => {
  if (!dateNumber && dateNumber !== 0) {
    return '';
  }

  const dateStr = dateNumber.toString().padStart(8, '0');

  if (dateStr.length !== 8) {
    return '';
  }

  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);

  return `${day}/${month}/${year}`;
};

/**
 * Parse date number from API (YYYYMMDD) to Date object
 * @param dateNumber - Date in YYYYMMDD format (e.g., 20250124) as number or string
 * @returns Date object or undefined if invalid
 */
export const parseDateFromNumber = (dateNumber: number | string | undefined | null): Date | undefined => {
  if (!dateNumber && dateNumber !== 0) {
    return undefined;
  }

  const dateStr = dateNumber.toString().padStart(8, '0');

  if (dateStr.length !== 8) {
    return undefined;
  }

  const year = parseInt(dateStr.substring(0, 4), 10);
  const month = parseInt(dateStr.substring(4, 6), 10) - 1; // Month is 0-indexed in JS Date
  const day = parseInt(dateStr.substring(6, 8), 10);

  const date = new Date(year, month, day);

  // Validate the date
  if (isNaN(date.getTime())) {
    return undefined;
  }

  return date;
};

/**
 * Convert Date object to YYYYMMDD number format for API
 * @param date - Date object or string to convert
 * @returns Date in YYYYMMDD format as number (e.g., 20251108) or 0 if invalid
 */
export const toYMD = (date: Date | string | undefined | null): number => {
  if (!date) return 0;

  const d = date instanceof Date ? date : new Date(date);

  if (isNaN(d.getTime())) return 0;

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');

  return parseInt(`${year}${month}${day}`);
};
