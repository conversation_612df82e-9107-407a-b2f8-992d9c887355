import {APP_NAME, isAndroid, isIOS, TARGET_SDK_VERSION} from '@commons/Constant';
// import notifee, {IOSAuthorizationStatus} from '@notifee/react-native';
import {Alert, Linking, PermissionsAndroid} from 'react-native';
import {PERMISSIONS, check, checkMultiple, checkNotifications, request, requestMultiple} from 'react-native-permissions';

export const requestCameraPermissions = () => {
  return new Promise(async (resolve, reject) => {
    if (!isIOS) {
      let response = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA).catch(error => {
        resolve(false);
      });

      if (response === false) {
        Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập máy ảnh để chụp ảnh tổn thất', [
          {
            text: 'Để sau',
            onPress: () => resolve(false),
          },
          {
            text: 'Đồng ý',
            onPress: () => {
              PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA)
                .then(response => {
                  if (response === 'never_ask_again') {
                    setTimeout(() => {
                      Linking.openSettings();
                    }, 100);
                    resolve(false);
                  } else if (response === 'denied') resolve(false);
                  else if (response === 'granted') resolve(true);
                })
                .catch(error => {
                  resolve(false);
                });
            },
          },
        ]);
      } else resolve(true);
    } else if (isIOS) {
      let response = await check(PERMISSIONS.IOS.CAMERA);
      if (response !== 'granted') {
        Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập máy ảnh để chụp ảnh', [
          // {
          //   text: 'Để sau',
          //   onPress: () => resolve(false),
          // },
          {
            text: 'Tiếp tục',
            onPress: async () => {
              if (response === 'blocked') {
                setTimeout(() => {
                  Linking.openSettings();
                }, 100);
                resolve(false);
              } else {
                let status = await request(PERMISSIONS.IOS.CAMERA).catch(error => {
                  resolve(false);
                });
                if (status === 'granted') resolve(true);
                else resolve(false);
              }
            },
          },
        ]);
      } else resolve(true);
    }
  });
};

export const requestRecordAudioPermissions = () => {
  // console.log('requestRecordAudioPermissions');
  return new Promise(async (resolve, reject) => {
    if (!isIOS) {
      let response = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO).catch(error => {
        resolve(false);
      });
      // console.log('PERMISSIONS.RECORD_AUDIO', response);
      if (response === false) {
        Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập micro để quay video giám định', [
          {
            text: 'Để sau',
            onPress: () => resolve(false),
          },
          {
            text: 'Đồng ý',
            onPress: () => {
              PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO)
                .then(response => {
                  //console.log('PERMISSIONS.RECORD_AUDIO response', response);
                  if (response === 'never_ask_again') {
                    setTimeout(() => {
                      Linking.openSettings();
                    }, 100);
                    resolve(false);
                  } else if (response === 'denied') resolve(false);
                  else if (response === 'granted') resolve(true);
                })
                .catch(error => {
                  resolve(false);
                });
            },
          },
        ]);
      } else resolve(true);
    } else if (isIOS) {
      let response = await check(PERMISSIONS.IOS.MICROPHONE);
      if (response !== 'granted') {
        Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập micro để thực hiện cuộc gọi video', [
          // {
          //   text: 'Để sau',
          //   onPress: () => resolve(false),
          // },
          {
            text: 'Tiếp tục',
            onPress: async () => {
              if (response === 'blocked') {
                setTimeout(() => {
                  Linking.openSettings();
                }, 100);
                resolve(false);
              } else {
                let status = await request(PERMISSIONS.IOS.MICROPHONE).catch(error => {
                  resolve(false);
                });
                if (status === 'granted') resolve(true);
                else resolve(false);
              }
            },
          },
        ]);
      } else resolve(true);
    }
  });
};

export const checkNotificationPermission = () => {
  return new Promise(async (resolve, reject) => {
    if (!isIOS) {
      let response = await checkNotifications();
      if (response.status !== 'granted') resolve(false);
      else resolve(true);
    } else {
      let response = await checkNotifications();
      console.log('response', response);
      if (response.status !== 'granted') resolve(false);
      else resolve(true);
    }
  });
};

// export const requestThongbaoByModal = async closeModal => {
//   let response = await checkNotifications();
//   if (response.status === 'blocked') {
//     closeModal();
//     Linking.openSettings();
//   } else {
//     const settings = await notifee.requestPermission({
//       criticalAlert: true,
//       carPlay: true,
//       announcement: false,
//       alert: true,
//       badge: true,
//     });
//     closeModal();
//   }
// };

export const checkFaceIdPermission = () => {
  return new Promise(async (resolve, reject) => {
    if (!isIOS) {
    } else {
      try {
        let checkResponse = await check(PERMISSIONS.IOS.FACE_ID);
        if (checkResponse === 'denied' || checkResponse === 'blocked' || checkResponse === 'unavailable') resolve(false);
        else resolve(true);
      } catch (error) {
        console.log('check(PERMISSIONS.IOS.FACE_ID) error', error);
        resolve(false);
      }
    }
  });
};

// export const requestFaceIdByModal = async (callbackDongY, callBackTuChoi) => {
//   let checkResponse = await check(PERMISSIONS.IOS.FACE_ID);
//   if (checkResponse === 'denied' || checkResponse === 'unavailable') {
//     try {
//       let requestResponse = await request(PERMISSIONS.IOS.FACE_ID);
//       if (requestResponse === 'granted') callbackDongY();
//       else callBackTuChoi();
//     } catch (error) {
//       callBackTuChoi();
//     }
//   } else if (checkResponse === 'blocked') {
//     Linking.openSettings();
//     callBackTuChoi();
//   }
// };

// export const requestNotificationPermissions = () => {
//   return new Promise(async (resolve, reject) => {
//     if (!isIOS) {
//       let response = await checkNotifications();
//       if (response.status != 'granted') {
//         Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' gửi thông báo cho bạn', [
//           {
//             text: 'Để sau',
//             onPress: () => resolve(false),
//           },
//           {
//             text: 'Tiếp tục',
//             onPress: async () => {
//               if (response.status == 'blocked') {
//                 setTimeout(() => {
//                   notifee
//                     .openNotificationSettings()
//                     .then(response => {})
//                     .catch(err => {});
//                 }, 100);
//                 resolve(false);
//               } else {
//                 const settings = await notifee.requestPermission({
//                   criticalAlert: true,
//                   carPlay: true,
//                   announcement: false,
//                   alert: true,
//                   badge: true,
//                 });
//                 if (settings.authorizationStatus >= IOSAuthorizationStatus.AUTHORIZED) resolve(true);
//                 else resolve(false);
//               }
//             },
//           },
//         ]);
//       } else resolve(true);
//     } else if (isIOS) {
//       let response = await checkNotifications();
//       if (response.status !== 'granted') {
//         Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' gửi thông báo cho bạn', [
//           // {
//           //   text: 'Để sau',
//           //   onPress: () => resolve(false),
//           // },
//           {
//             text: 'Tiếp tục',
//             onPress: async () => {
//               if (response.status === 'blocked') {
//                 setTimeout(() => {
//                   Linking.openSettings();
//                 }, 100);
//                 resolve(false);
//               } else {
//                 const settings = await notifee.requestPermission({
//                   criticalAlert: true,
//                   carPlay: true,
//                   announcement: false,
//                   alert: true,
//                   badge: true,
//                 });
//                 if (settings.authorizationStatus >= IOSAuthorizationStatus.AUTHORIZED) resolve(true);
//                 else resolve(false);
//               }
//             },
//           },
//         ]);
//       } else resolve(true);
//     }
//   });
// };

export const requestLibraryPermissions = () => {
  // console.log('requestRecordAudioPermissions');
  return new Promise(async (resolve, reject) => {
    if (!isIOS) {
      let responseCheck = null;
      if (+TARGET_SDK_VERSION >= 33) {
        try {
          responseCheck = await checkMultiple([PERMISSIONS.ANDROID.READ_MEDIA_IMAGES, PERMISSIONS.ANDROID.READ_MEDIA_VIDEO]);
          if (responseCheck[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES] === 'denied' || responseCheck[PERMISSIONS.ANDROID.READ_MEDIA_VIDEO] === 'denied') responseCheck = false;
        } catch (error) {
          console.log('error', error);
          resolve(false);
        }
      }
      //  else if (+TARGET_SDK_VERSION >= 30) {
      //   responseCheck = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE).catch(error => {
      //     resolve(false);
      //   });
      //   console.log('responseCheck', responseCheck);
      // }
      else resolve(true);

      if (responseCheck === false) {
        Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập thư viện để chọn và lưu ảnh tài liệu', [
          {
            text: 'Để sau',
            onPress: () => resolve(false),
          },
          {
            text: 'Đồng ý',
            onPress: async () => {
              try {
                console.log('TARGET_SDK_VERSION', TARGET_SDK_VERSION);
                if (Number(TARGET_SDK_VERSION) >= 33) {
                  let responseRequest = await requestMultiple([PERMISSIONS.ANDROID.READ_MEDIA_IMAGES, PERMISSIONS.ANDROID.READ_MEDIA_VIDEO]);
                  if (responseRequest[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES] === 'granted' && responseRequest[PERMISSIONS.ANDROID.READ_MEDIA_VIDEO] === 'granted') resolve(true);
                  else if (responseRequest[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES] === 'denied' && responseRequest[PERMISSIONS.ANDROID.READ_MEDIA_VIDEO] === 'denied') resolve(false);
                  else if (responseRequest[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES] === 'blocked' && responseRequest[PERMISSIONS.ANDROID.READ_MEDIA_VIDEO] === 'blocked') {
                    setTimeout(() => {
                      Linking.openSettings();
                    }, 100);
                    resolve(false);
                  }
                } else if (Number(TARGET_SDK_VERSION) >= 30) {
                  let responseRequest = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE);
                  if (responseRequest === 'never_ask_again') {
                    setTimeout(() => {
                      Linking.openSettings();
                    }, 100);
                    resolve(false);
                  } else if (responseRequest === 'denied') resolve(false);
                  else if (responseRequest === 'granted') resolve(true);
                } else {
                  resolve(true);
                }
              } catch (error) {
                Alert.alert('Thông báo', error.message);
              }
            },
          },
        ]);
      } else resolve(true);
    } else if (isIOS) {
      let response = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);
      //console.log('check PERMISSIONS.IOS.MICROPHONE IOS ', response);
      if (response !== 'granted') {
        Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập thư viện để chọn và lưu ảnh tài liệu', [
          // {
          //   text: 'Để sau',
          //   onPress: () => resolve(false),
          // },
          {
            text: 'Tiếp tục',
            onPress: async () => {
              if (response === 'blocked') {
                setTimeout(() => {
                  Linking.openSettings();
                }, 100);
                resolve(false);
              } else {
                let status = await request(PERMISSIONS.IOS.PHOTO_LIBRARY).catch(error => {
                  // logErrorTryCatch(error);
                  resolve(false);
                });
                if (status === 'granted') resolve(true);
                else resolve(false);
              }
            },
          },
        ]);
      } else resolve(true);
    }
  });
};

export const requestFaceIdPermission = () => {
  return new Promise(async (resolve, reject) => {
    let checkResponse = await check(PERMISSIONS.IOS.FACE_ID);

    if (checkResponse === 'denied' || checkResponse === 'blocked' || checkResponse === 'unavailable') {
      Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập Face ID để đăng nhập nhanh chóng và an toàn vào tài khoản của bạn', [
        // {text: 'Để sau'},
        {
          text: 'Tiếp tục',
          onPress: async () => {
            if (checkResponse === 'denied' || checkResponse === 'unavailable') {
              try {
                let requestResponse = await request(PERMISSIONS.IOS.FACE_ID);
                resolve(requestResponse);
              } catch (error) {
                reject(error);
              }
            } else if (checkResponse === 'blocked') {
              Linking.openSettings();
              resolve(checkResponse);
            }
          },
        },
      ]);
    } else resolve(checkResponse);
  });
};

// export const requestCalenderPermission = () => {
//   // console.log('requestRecordAudioPermissions');
//   return new Promise(async (resolve, reject) => {
//     if (isAndroid) {
//       let response = await checkMultiple([PERMISSIONS.ANDROID.WRITE_CALENDAR, PERMISSIONS.ANDROID.READ_CALENDAR]);
//       if (
//         response[PERMISSIONS.ANDROID.WRITE_CALENDAR] === 'denied' ||
//         response[PERMISSIONS.ANDROID.READ_CALENDAR] === 'denied' ||
//         response[PERMISSIONS.ANDROID.WRITE_CALENDAR] === 'blocked' ||
//         response[PERMISSIONS.ANDROID.READ_CALENDAR] === 'blocked'
//       ) {
//         Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập vào lịch để thêm lịch giám định', [
//           {
//             text: 'Để sau',
//             onPress: () => resolve(false),
//           },
//           {
//             text: 'Tiếp tục',
//             onPress: async () => {
//               if (response[PERMISSIONS.ANDROID.WRITE_CALENDAR] === 'denied' || response[PERMISSIONS.ANDROID.READ_CALENDAR] === 'denied') {
//                 try {
//                   let responseRequestPermissions = await requestMultiple([PERMISSIONS.ANDROID.WRITE_CALENDAR, PERMISSIONS.ANDROID.READ_CALENDAR]);
//                   if (responseRequestPermissions === 'granted' || responseRequestPermissions === 'authorized') resolve(true);
//                 } catch (error) {
//                   reject(error);
//                 }
//               } else if (response[PERMISSIONS.ANDROID.WRITE_CALENDAR] === 'blocked' || response[PERMISSIONS.ANDROID.READ_CALENDAR] === 'blocked') {
//                 Linking.openSettings();
//                 resolve(response);
//               }
//             },
//           },
//         ]);
//       } else resolve(true);
//     } else if (isIOS) {
//       let response = await ReactNativeCalendarEvents.checkPermissions();
//       if (response !== 'authorized') {
//         Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập lịch để thêm lịch giám định', [
//           // {
//           //   text: 'Để sau',
//           //   onPress: () => resolve(false),
//           // },
//           {
//             text: 'Tiếp tục',
//             onPress: async () => {
//               if (response === 'unavailable' || response === 'undetermined' || response === 'restricted') {
//                 try {
//                   let responseRequest = await ReactNativeCalendarEvents.requestPermissions();
//                   console.log('requestResponse', responseRequest);
//                   // if(requestResponse !=='granted')
//                   resolve(responseRequest);
//                 } catch (error) {
//                   reject(error);
//                 }
//               } else if (response === 'denied') {
//                 Linking.openSettings();
//                 resolve(response);
//               }
//             },
//           },
//         ]);
//       } else resolve(true);
//     }
//   });
// };
