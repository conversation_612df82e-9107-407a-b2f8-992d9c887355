import NavigationUtil from '@src/navigation/NavigationUtil';
import {store} from '@src/store/index';
import {ApiClient} from '@src/services/axiosService';
import {TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID} from '@src/constants/index';

export const logErrorTryCatch = (errData: any) => {
  try {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'TRY CATCH LOG BUG',
      user: getUserData(),
      screen: getCurrentScreenData(),
      detail: (errData?.code || '') + '-' + (errData?.message || '') || '',
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  } catch (error: any) {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'LỖI HÀM LOG TRY CATCH',
      user: getUserData(),
      screen: getCurrentScreenData(),
      detail: error.message,
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  }
};

// LOG ERROR TRẢ VỀ TỪ SERVER
export const logErrorServer = (actionCode: string, params: any, errData: any) => {
  try {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'SERVER LOG BUG',
      user: getUserData(),
      screen: getCurrentScreenData(),
      actionCode,
      params,
      detail: errData,
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  } catch (error: any) {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'LỖI HÀM LOG ERROR SERVER',
      user: getUserData(),
      screen: getCurrentScreenData(),
      detail: error.message,
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  }
};

// LOG ERROR TRẢ VỀ TỪ SENTRY
export const logErrorSentry = async (event: any) => {
  try {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'SENTRY LOG BUG',
      user: getUserData(),
      screen: getCurrentScreenData(),
      sentry_data: {
        contexts: {
          app: {
            app_build: event.contexts.app.app_build,
            app_id: event.contexts.app.app_id,
            app_version: event.contexts.app.app_version,
            build_type: event.contexts.app.build_type,
            in_foreground: event.contexts.app.in_foreground,
            app_memory: event.contexts.app.app_memory,
            device_app_hash: event.contexts.app.device_app_hash, //app.device trên web sentry
          },
          device: event.contexts.device,
          os: event.contexts.os,
        },
        event_id: event.event_id,
        exception: {
          values: event.exception.values.map((item: any) => {
            return {
              type: item.type,
              value: item.value,
            };
          }),
        },
        level: event.level,
        platform: event.platform,
        user: event.user,
      },
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  } catch (error: any) {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'LỖI HÀM LOG ERROR SENTRY',
      user: getUserData(),
      screen: getCurrentScreenData(),
      detail: error.message,
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  }
};

// LOG ERROR TRẢ VỀ TỪ SERVER
export const logErrorHttp = (errData: any) => {
  try {
    //lỗi ERR_NETWORK là lỗi k có mạng -> k thể log ra được. nếu log sẽ bị loop
    if (errData.code === 'ERR_NETWORK') return;
    let detail = {
      code: errData.code,
      message: errData.message,
    };
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'HTTPS LOG BUG',
      user: getUserData(),
      screen: getCurrentScreenData(),
      actionCode: errData.config?.headers?.eAction || '',
      detail: detail,
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  } catch (error: any) {
    let data: any = {
      title: (__DEV__ ? 'DEV ' : '') + 'LỖI HÀM LOG ERROR HTTP',
      user: getUserData(),
      screen: getCurrentScreenData(),
      detail: error.message,
    };
    data = encodeURIComponent(JSON.stringify(data));
    // ApiClient.post(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage?chat_id=${TELEGRAM_CHANNEL_ID}&text=${data}`);
  }
};

const getUserData = () => {
  let user: any = null;
  //chèn thông tin user
  if (store?.getState()?.user?.data) {
    user = store?.getState()?.user?.data;
    if (user.nguoi_dung) {
      user = {
        nsd: user.nguoi_dung.nsd,
        ten: user.nguoi_dung.ten,
      };
    }
  }
  return user;
};

const getCurrentScreenData = () => {
  //chèn thông tin screen đang thao tác
  let currentScreen: any = NavigationUtil.getCurrentScreen();
  if (currentScreen) currentScreen = currentScreen.name;
  return currentScreen;
};

