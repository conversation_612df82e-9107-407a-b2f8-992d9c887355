import {showMessage} from 'react-native-flash-message';

export class FlashMessageHelper {
  static flashMessageRef: any;

  static setRef(ref: any) {
    this.flashMessageRef = ref;
  }

  static showFlashMessage(
    message = 'Thông báo',
    desc: string,
    type: 'info' | 'success' | 'warning' | 'danger' = 'info',
    position?: 'top' | 'bottom',
    callbackPress?: () => void,
  ) {
    showMessage({
      message: message,
      description: desc,
      type: type,
      onPress: callbackPress,
      position: position,
    });
  }
}

