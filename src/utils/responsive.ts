import {Dimensions, Platform} from 'react-native';
import {
  DeviceType,
  SizeClass,
  DEVICE_BREAKPOINTS,
  COMPONENT_SIZES,
  TYPOGRAPHY_SCALE,
  getCurrentDeviceDimensions,
} from '../constants/deviceTypes';

/**
 * Responsive utilities for handling different device types and sizes
 * Optimized for portrait orientation only
 */
export class ResponsiveUtils {
  private static _deviceType: DeviceType | null = null;
  private static _sizeClass: SizeClass | null = null;

  /**
   * Determine if current device is a tablet (iPad)
   */
  static isTablet(): boolean {
    if (Platform.OS !== 'ios') return false;
    
    const {width} = getCurrentDeviceDimensions();
    return width >= DEVICE_BREAKPOINTS.REGULAR_WIDTH_MIN;
  }

  /**
   * Get device type (phone or tablet)
   */
  static getDeviceType(): DeviceType {
    if (this._deviceType === null) {
      this._deviceType = this.isTablet() ? 'tablet' : 'phone';
    }
    return this._deviceType;
  }

  /**
   * Get size class based on device width
   */
  static getSizeClass(): SizeClass {
    if (this._sizeClass === null) {
      const {width} = getCurrentDeviceDimensions();
      this._sizeClass = width >= DEVICE_BREAKPOINTS.REGULAR_WIDTH_MIN ? 'regular' : 'compact';
    }
    return this._sizeClass;
  }

  /**
   * Get responsive spacing based on device type
   */
  static getResponsiveSpacing(baseSpacing: number): number {
    const deviceType = this.getDeviceType();
    const multiplier = deviceType === 'tablet' 
      ? COMPONENT_SIZES.SPACING_MULTIPLIER_TABLET 
      : COMPONENT_SIZES.SPACING_MULTIPLIER_PHONE;
    
    return Math.round(baseSpacing * multiplier);
  }

  /**
   * Get responsive font size based on device type
   */
  static getResponsiveFontSize(baseFontSize: number): number {
    const deviceType = this.getDeviceType();
    const scale = deviceType === 'tablet' ? TYPOGRAPHY_SCALE.TABLET : TYPOGRAPHY_SCALE.PHONE;
    
    const scaledSize = Math.round(baseFontSize * scale.multiplier);
    return Math.min(scaledSize, scale.maxFontSize);
  }

  /**
   * Get dynamic header height based on device type
   */
  static getDynamicHeaderHeight(): number {
    const deviceType = this.getDeviceType();
    return deviceType === 'tablet' 
      ? COMPONENT_SIZES.HEADER_HEIGHT_TABLET 
      : COMPONENT_SIZES.HEADER_HEIGHT_PHONE;
  }

  /**
   * Get dynamic tab bar height (excluding safe area)
   */
  static getDynamicTabBarHeight(): number {
    const deviceType = this.getDeviceType();
    return deviceType === 'tablet' 
      ? COMPONENT_SIZES.TAB_BAR_HEIGHT_TABLET 
      : COMPONENT_SIZES.TAB_BAR_HEIGHT_PHONE;
  }

  /**
   * Get appropriate icon size for tabs
   */
  static getTabIconSize(): number {
    const deviceType = this.getDeviceType();
    return deviceType === 'tablet' 
      ? COMPONENT_SIZES.TAB_ICON_SIZE_TABLET 
      : COMPONENT_SIZES.TAB_ICON_SIZE_PHONE;
  }

  /**
   * Get minimum touch target size
   */
  static getMinTouchTarget(): number {
    const deviceType = this.getDeviceType();
    return deviceType === 'tablet' 
      ? COMPONENT_SIZES.RECOMMENDED_TOUCH_TARGET_TABLET 
      : COMPONENT_SIZES.MIN_TOUCH_TARGET;
  }

  /**
   * Calculate responsive width percentage
   */
  static getResponsiveWidth(percentage: number): number {
    const {width} = getCurrentDeviceDimensions();
    return Math.round((width * percentage) / 100);
  }

  /**
   * Calculate responsive height percentage
   */
  static getResponsiveHeight(percentage: number): number {
    const {height} = getCurrentDeviceDimensions();
    return Math.round((height * percentage) / 100);
  }

  /**
   * Get device-specific padding for content
   */
  static getContentPadding(): number {
    const deviceType = this.getDeviceType();
    return deviceType === 'tablet' ? 24 : 16;
  }

  /**
   * Reset cached values (useful for orientation changes, though we only support portrait)
   */
  static resetCache(): void {
    this._deviceType = null;
    this._sizeClass = null;
  }

  /**
   * Get debug info about current device
   */
  static getDebugInfo(): object {
    const dimensions = getCurrentDeviceDimensions();
    return {
      dimensions,
      deviceType: this.getDeviceType(),
      sizeClass: this.getSizeClass(),
      isTablet: this.isTablet(),
      headerHeight: this.getDynamicHeaderHeight(),
      tabBarHeight: this.getDynamicTabBarHeight(),
      platform: Platform.OS,
    };
  }
}
