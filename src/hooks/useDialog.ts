import {useCallback} from 'react';
import NavigationUtil from '@navigation/NavigationUtil';

let dialogVisible = false;

export const useDialog = () => {
  const showDialog = useCallback(() => {
    if (!dialogVisible) {
      dialogVisible = true;
      NavigationUtil.navigate('DialogLoading');
    }
  }, []);

  const hideDialog = useCallback(() => {
    if (dialogVisible) {
      dialogVisible = false;
      NavigationUtil.goBack();
    }
  }, []);

  return {showDialog, hideDialog};
};

