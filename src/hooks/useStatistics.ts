import {useState, useEffect} from 'react';
import {formatCurrency} from '@utils/formatters';
import {statisticsService, StatisticsFilters} from '@services/statisticsService';

export interface StatisticItem {
  id: string;
  title: string;
  amount: number;
  subtitle: string;
  backgroundColor: string;
  icon?: any;
  type: 'revenue' | 'income' | 'expense' | 'profit' | 'custom';
  period?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  trend?: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  };
}

export interface StatisticsData {
  statistics: StatisticItem[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  filters: StatisticsFilters | null;
}

/**
 * Hook để quản lý dữ liệu thống kê với service layer
 */
export const useStatistics = (initialFilters?: StatisticsFilters) => {
  const [data, setData] = useState<StatisticsData>({
    statistics: [],
    isLoading: true,
    error: null,
    lastUpdated: null,
    filters: initialFilters || null,
  });

  // Fetch statistics using service layer
  const fetchStatistics = async (filters?: StatisticsFilters) => {
    try {
      const response = await statisticsService.getStatistics(filters);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Không thể tải dữ liệu thống kê');
      }
    } catch (error) {
      throw error;
    }
  };

  const loadStatistics = async (filters?: StatisticsFilters) => {
    try {
      setData(prev => ({...prev, isLoading: true, error: null}));
      const statistics = await fetchStatistics(filters || data.filters || undefined);
      setData(prev => ({
        ...prev,
        statistics,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Có lỗi xảy ra khi tải dữ liệu',
      }));
    }
  };

  const refreshStatistics = (filters?: StatisticsFilters) => {
    loadStatistics(filters);
  };

  const updateFilters = (newFilters: StatisticsFilters) => {
    setData(prev => ({...prev, filters: newFilters}));
    loadStatistics(newFilters);
  };

  const formatStatisticAmount = (amount: number): string => {
    return formatCurrency(amount);
  };

  const getDetailedStatistics = async (statisticId: string) => {
    try {
      return await statisticsService.getDetailedStatistics(statisticId);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Không thể tải chi tiết',
      };
    }
  };

  const exportData = async (format: 'excel' | 'pdf' | 'csv') => {
    try {
      return await statisticsService.exportStatistics(format, data.filters || undefined);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Không thể xuất dữ liệu',
      };
    }
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  return {
    ...data,
    refreshStatistics,
    updateFilters,
    formatStatisticAmount,
    getDetailedStatistics,
    exportData,
  };
};
