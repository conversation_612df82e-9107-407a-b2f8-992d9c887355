import {useEffect, useState} from 'react';
import {Platform} from 'react-native';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';

export const useCameraPermission = () => {
  const [hasPermission, setHasPermission] = useState(false);

  const checkPermission = async () => {
    try {
      const permission = Platform.select({
        ios: PERMISSIONS.IOS.CAMERA,
        android: PERMISSIONS.ANDROID.CAMERA,
      });

      if (!permission) {
        return;
      }

      const result = await check(permission);

      if (result === RESULTS.DENIED) {
        const requestResult = await request(permission);
        setHasPermission(requestResult === RESULTS.GRANTED);
      } else {
        setHasPermission(result === RESULTS.GRANTED);
      }
    } catch (error) {
      console.error('Error checking camera permission:', error);
      setHasPermission(false);
    }
  };

  useEffect(() => {
    checkPermission();
  }, []);

  return {
    hasPermission,
    checkPermission,
  };
};
