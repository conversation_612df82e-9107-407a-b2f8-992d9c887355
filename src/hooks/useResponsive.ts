import {useEffect, useState} from 'react';
import {Dimensions, ScaledSize} from 'react-native';
import {ResponsiveUtils} from '../utils/responsive';
import {DeviceType, SizeClass} from '../constants/deviceTypes';

interface ResponsiveHookReturn {
  deviceType: DeviceType;
  sizeClass: SizeClass;
  isTablet: boolean;
  dimensions: ScaledSize;
  spacing: (base: number) => number;
  fontSize: (base: number) => number;
  headerHeight: number;
  tabBarHeight: number;
  iconSize: number;
  minTouchTarget: number;
  contentPadding: number;
  width: (percentage: number) => number;
  height: (percentage: number) => number;
}

/**
 * Custom hook for responsive design utilities
 * Provides reactive values that update when screen dimensions change
 * Note: App only supports portrait orientation
 */
export const useResponsive = (): ResponsiveHookReturn => {
  const [dimensions, setDimensions] = useState(() => Dimensions.get('window'));
  const [deviceType, setDeviceType] = useState<DeviceType>(() => ResponsiveUtils.getDeviceType());
  const [sizeClass, setSizeClass] = useState<SizeClass>(() => ResponsiveUtils.getSizeClass());

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({window}) => {
      setDimensions(window);
      
      // Reset ResponsiveUtils cache when dimensions change
      ResponsiveUtils.resetCache();
      
      // Update device type and size class
      setDeviceType(ResponsiveUtils.getDeviceType());
      setSizeClass(ResponsiveUtils.getSizeClass());
    });

    return () => subscription?.remove();
  }, []);

  // Memoized values that depend on current device type
  const responsiveValues = {
    deviceType,
    sizeClass,
    isTablet: deviceType === 'tablet',
    dimensions,
    
    // Helper functions
    spacing: (base: number) => ResponsiveUtils.getResponsiveSpacing(base),
    fontSize: (base: number) => ResponsiveUtils.getResponsiveFontSize(base),
    width: (percentage: number) => ResponsiveUtils.getResponsiveWidth(percentage),
    height: (percentage: number) => ResponsiveUtils.getResponsiveHeight(percentage),
    
    // Component sizes
    headerHeight: ResponsiveUtils.getDynamicHeaderHeight(),
    tabBarHeight: ResponsiveUtils.getDynamicTabBarHeight(),
    iconSize: ResponsiveUtils.getTabIconSize(),
    minTouchTarget: ResponsiveUtils.getMinTouchTarget(),
    contentPadding: ResponsiveUtils.getContentPadding(),
  };

  return responsiveValues;
};

/**
 * Hook specifically for device type detection
 * Lighter weight alternative when you only need device type
 */
export const useDeviceType = (): DeviceType => {
  const [deviceType, setDeviceType] = useState<DeviceType>(() => ResponsiveUtils.getDeviceType());

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', () => {
      ResponsiveUtils.resetCache();
      setDeviceType(ResponsiveUtils.getDeviceType());
    });

    return () => subscription?.remove();
  }, []);

  return deviceType;
};

/**
 * Hook for getting responsive spacing
 * Useful when you only need spacing calculations
 */
export const useResponsiveSpacing = () => {
  const deviceType = useDeviceType();
  
  return (baseSpacing: number) => ResponsiveUtils.getResponsiveSpacing(baseSpacing);
};

/**
 * Hook for getting responsive font sizes
 * Useful when you only need typography scaling
 */
export const useResponsiveFontSize = () => {
  const deviceType = useDeviceType();
  
  return (baseFontSize: number) => ResponsiveUtils.getResponsiveFontSize(baseFontSize);
};

/**
 * Debug hook to log responsive information
 * Only use in development
 */
export const useResponsiveDebug = (componentName?: string) => {
  const responsive = useResponsive();
  
  useEffect(() => {
    if (__DEV__) {
      console.log(`[${componentName || 'ResponsiveDebug'}]`, {
        ...ResponsiveUtils.getDebugInfo(),
        hookValues: {
          deviceType: responsive.deviceType,
          isTablet: responsive.isTablet,
          headerHeight: responsive.headerHeight,
          tabBarHeight: responsive.tabBarHeight,
        },
      });
    }
  }, [responsive.deviceType, responsive.dimensions, componentName]);

  return responsive;
};
