import {useState, useEffect, useCallback} from 'react';
import {AppState, AppStateStatus} from 'react-native';
import {hotUpdateService, HotUpdateInfo} from '../services/hotUpdate';

interface UseHotUpdateOptions {
  checkOnAppStart?: boolean;
  checkOnAppResume?: boolean;
  autoUpdate?: boolean;
  showUpdateDialog?: boolean;
}

interface UseHotUpdateReturn {
  isChecking: boolean;
  updateInfo: HotUpdateInfo | null;
  isUpdateAvailable: boolean;
  showUpdateModal: boolean;
  checkForUpdates: () => Promise<void>;
  startUpdate: () => Promise<void>;
  dismissUpdate: () => void;
  getCurrentVersion: () => Promise<number>;
  clearUpdateData: () => Promise<void>;
}

/**
 * useHotUpdate Hook
 * Manages hot update functionality including checking, downloading, and installing updates
 *
 * @param options Configuration options for hot update behavior
 * @returns Object with hot update state and methods
 */
export const useHotUpdate = (options: UseHotUpdateOptions = {}): UseHotUpdateReturn => {
  const {checkOnAppStart = true, checkOnAppResume = false, autoUpdate = false, showUpdateDialog = true} = options;

  const [isChecking, setIsChecking] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<HotUpdateInfo | null>(null);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  // Initialize hot update service
  useEffect(() => {
    hotUpdateService.initialize();
  }, []);

  // Check for updates on app start
  useEffect(() => {
    if (checkOnAppStart) {
      // Delay initial check to allow app to fully load
      const timer = setTimeout(() => {
        checkForUpdates();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [checkOnAppStart]);

  // Handle app state changes
  useEffect(() => {
    if (!checkOnAppResume) return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Check for updates when app becomes active
        checkForUpdates();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [checkOnAppResume]);

  // Auto update when update is available
  useEffect(() => {
    if (updateInfo && autoUpdate && !showUpdateModal) {
      startUpdate();
    }
  }, [updateInfo, autoUpdate, showUpdateModal]);

  /**
   * Check for available updates
   */
  const checkForUpdates = useCallback(async (): Promise<void> => {
    if (isChecking) return;

    try {
      setIsChecking(true);
      logger.log('Checking for hot updates...');

      const availableUpdate = await hotUpdateService.checkForUpdates();

      if (availableUpdate) {
        logger.log('Update available:', availableUpdate);
        setUpdateInfo(availableUpdate);

        if (showUpdateDialog) {
          setShowUpdateModal(true);
        }
      } else {
        logger.log('No updates available');
        setUpdateInfo(null);
      }
    } catch (error) {
      console.error('Failed to check for updates:', error);
      setUpdateInfo(null);
    } finally {
      setIsChecking(false);
    }
  }, [isChecking, showUpdateDialog]);

  /**
   * Start update process
   */
  const startUpdate = useCallback(async (): Promise<void> => {
    if (!updateInfo) {
      console.warn('No update info available');
      return;
    }

    try {
      logger.log('Starting update process...');

      const success = await hotUpdateService.downloadAndInstall(updateInfo, {
        restartAfterInstall: true,
        updateSuccess: () => {
          logger.log('Update completed successfully');
          setShowUpdateModal(false);
          setUpdateInfo(null);
        },
        updateFail: (error: string) => {
          console.error('Update failed:', error);
          // Keep modal open to show error state
        },
      });

      if (!success) {
        console.warn('Update process failed');
      }
    } catch (error) {
      console.error('Failed to start update:', error);
    }
  }, [updateInfo]);

  /**
   * Dismiss update modal/notification
   */
  const dismissUpdate = useCallback((): void => {
    setShowUpdateModal(false);

    // If it's not a force update, clear the update info
    if (!updateInfo?.force_update) {
      setUpdateInfo(null);
    }
  }, [updateInfo?.force_update]);

  /**
   * Get current version
   */
  const getCurrentVersion = useCallback(async (): Promise<number> => {
    try {
      return await hotUpdateService.getCurrentVersion();
    } catch (error) {
      console.error('Failed to get current version:', error);
      return 0;
    }
  }, []);

  /**
   * Clear update data (for debugging/testing)
   */
  const clearUpdateData = useCallback(async (): Promise<void> => {
    try {
      await hotUpdateService.removeUpdate();
      setUpdateInfo(null);
      setShowUpdateModal(false);
      logger.log('Update data cleared');
    } catch (error) {
      console.error('Failed to clear update data:', error);
    }
  }, []);

  return {
    isChecking,
    updateInfo,
    isUpdateAvailable: updateInfo !== null,
    showUpdateModal,
    checkForUpdates,
    startUpdate,
    dismissUpdate,
    getCurrentVersion,
    clearUpdateData,
  };
};

/**
 * useHotUpdateVersion Hook
 * Simple hook to get and display current hot update version
 */
export const useHotUpdateVersion = () => {
  const [version, setVersion] = useState<number>(0);

  useEffect(() => {
    const getVersion = async () => {
      try {
        const currentVersion = await hotUpdateService.getCurrentVersion();
        setVersion(currentVersion);
      } catch (error) {
        console.error('Failed to get version:', error);
      }
    };

    getVersion();
  }, []);

  return version;
};
