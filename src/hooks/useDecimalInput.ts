import {useEffect, useRef, useState} from 'react';

/**
 * Custom hook for handling decimal input with react-hook-form
 * Preserves decimal point while typing (e.g., "1." won't be converted to "1")
 *
 * @param value - Current numeric value from form
 * @returns Object with text value and onChange handler
 *
 * @example
 * ```tsx
 * <Controller
 *   control={control}
 *   name="trong_tai"
 *   render={({field: {onChange, value}}) => {
 *     const {text, handleChange} = useDecimalInput(value, onChange);
 *     return (
 *       <TextField
 *         value={text}
 *         onChangeText={handleChange}
 *         keyboardType="decimal-pad"
 *       />
 *     );
 *   }}
 * />
 * ```
 */
export const useDecimalInput = (value: number | undefined | null, onChange: (value: number) => void) => {
  const [text, setText] = useState(value ? String(value) : '');
  const prevValueRef = useRef(value);

  useEffect(() => {
    // Only sync when value changes externally (not from this input)
    if (value !== prevValueRef.current && parseFloat(text || '0') !== value) {
      setText(value ? String(value) : '');
    }
    prevValueRef.current = value;
  }, [value, text]);

  const handleChange = (input: string) => {
    setText(input);
    onChange(input ? parseFloat(input) || 0 : 0);
  };

  return {text, handleChange};
};
