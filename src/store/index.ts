import {configureStore} from '@reduxjs/toolkit';
import {combineReducers} from '@reduxjs/toolkit';
import {persistStore, persistReducer} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import slices
import authSlice from './slices/authSlice';
import userSlice from './slices/userSlice';
import uiSlice from './slices/uiSlice';
import addressSlice from './slices/addressSlice';
import commonSlice from './slices/commonSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth', 'user', 'address', 'commonCategories'], // Lưu trữ auth, user, address và commonCategories state
};

const rootReducer = combineReducers({
  auth: authSlice,
  user: userSlice,
  ui: uiSlice,
  address: addressSlice,
  commonCategories: commonSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
      // Tắt ImmutableStateInvariantMiddleware để tránh warning với state lớn
      immutableCheck: __DEV__
        ? {
            warnAfter: 128, // Tăng threshold từ 32ms lên 128ms
          }
        : false,
    }),
  devTools: __DEV__,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
import {useDispatch, useSelector, TypedUseSelectorHook} from 'react-redux';
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
