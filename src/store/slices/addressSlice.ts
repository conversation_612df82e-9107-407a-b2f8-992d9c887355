import {ACTION_CODE} from '@constants/axios';
import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import {getCommonExecute} from '@services/endpoints';

// Define types for our data
interface Province {
  ma: string;
  ten: string;
}

interface District {
  ma: string;
  ten: string;
  ma_tinh: string;
}

interface Ward {
  ma: string;
  ten: string;
  ma_huyen: string;
}

// Define the state structure
interface AddressState {
  provinces: Province[];
  districts: District[];
  wards: Ward[];
  loadingProvinces: boolean;
  loadingDistricts: boolean;
  loadingWards: boolean;
  error: string | null;
}

const initialState: AddressState = {
  provinces: [],
  districts: [],
  wards: [],
  loadingProvinces: false,
  loadingDistricts: false,
  loadingWards: false,
  error: null,
};

const NGAY_AD = 20250701; //Ngày áp dụng đơn vị hành chính mới
// Async Thunk for fetching provinces
export const fetchProvinces = createAsyncThunk('address/fetchProvinces', async (_, {rejectWithValue}) => {
  try {
    const params = {
      ngay_ad: NGAY_AD,
      actionCode: ACTION_CODE.GET_DANH_MUC_TINH_THANH, // actionCode for provinces
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch provinces');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

// Async Thunk for fetching districts
export const fetchDistricts = createAsyncThunk('address/fetchDistricts', async (_, {rejectWithValue}) => {
  try {
    const params = {
      ngay_ad: NGAY_AD,
      actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_QUAN_HUYEN, // Placeholder - Cần actionCode đúng
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch districts');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

// Async Thunk for fetching wards
export const fetchWards = createAsyncThunk('address/fetchWards', async (_, {rejectWithValue}) => {
  try {
    const params = {
      ngay_ad: NGAY_AD,
      actionCode: ACTION_CODE.LIET_KE_DANH_SACH_PHUONG_XA, // actionCode for wards
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch wards');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

const addressSlice = createSlice({
  name: 'address',
  initialState,
  reducers: {
    clearAddressError: state => {
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchProvinces.pending, state => {
        state.loadingProvinces = true;
        state.error = null;
      })
      .addCase(fetchProvinces.fulfilled, (state, action: PayloadAction<Province[]>) => {
        state.loadingProvinces = false;
        state.provinces = action.payload;
      })
      .addCase(fetchProvinces.rejected, (state, action) => {
        state.loadingProvinces = false;
        state.error = action.payload as string;
      })
      // Handle fetchDistricts
      .addCase(fetchDistricts.pending, state => {
        state.loadingDistricts = true;
      })
      .addCase(fetchDistricts.fulfilled, (state, action: PayloadAction<District[]>) => {
        state.loadingDistricts = false;
        state.districts = action.payload;
      })
      .addCase(fetchDistricts.rejected, (state, action) => {
        state.loadingDistricts = false;
        state.error = action.payload as string;
      })
      // Handle fetchWards
      .addCase(fetchWards.pending, state => {
        state.loadingWards = true;
      })
      .addCase(fetchWards.fulfilled, (state, action: PayloadAction<Ward[]>) => {
        state.loadingWards = false;
        state.wards = action.payload;
      })
      .addCase(fetchWards.rejected, (state, action) => {
        state.loadingWards = false;
        state.error = action.payload as string;
      });
  },
});

export const {clearAddressError} = addressSlice.actions;
export default addressSlice.reducer;
