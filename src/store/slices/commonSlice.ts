import {ACTION_CODE} from '@constants/axios';
import {createAsyncThunk, createSlice, PayloadAction} from '@reduxjs/toolkit';
import {getCommonExecute} from '@services/endpoints';

// Define types for our data
interface LoaiHoGiaDinh {
  ma: string;
  ten: string;
  so_tien?: number;
}

// Define the state structure
interface commonCategoriesState {
  danhSachLoaiHoGiaDinh: LoaiHoGiaDinh[];
  danhSachMoiQuanHeNdbh: LoaiHoGiaDinh[];
  danhSachMoiQuanHeNguoiMua: LoaiHoGiaDinh[];
  danhSachLoaiXe: LoaiHoGiaDinh[];
  danhSachMucBaoVe: LoaiHoGiaDinh[];
  danhsachNhaBaoHiem: LoaiHoGiaDinh[];
  danhSachNhaBaoHiemSucKhoe: LoaiHoGiaDinh[];
  danhSachLoaiXeOTo: LoaiHoGiaDinh[];
  danhSachNhaBaoHiemOTo: LoaiHoGiaDinh[];
  danhSachMucBaoVeOTo: LoaiHoGiaDinh[];
  error: string | null;
}

const initialState: commonCategoriesState = {
  danhSachLoaiHoGiaDinh: [],
  danhSachMoiQuanHeNdbh: [],
  danhSachMoiQuanHeNguoiMua: [],
  danhSachLoaiXe: [],
  danhSachMucBaoVe: [],
  danhsachNhaBaoHiem: [],
  danhSachNhaBaoHiemSucKhoe: [],
  danhSachLoaiXeOTo: [],
  danhSachNhaBaoHiemOTo: [],
  danhSachMucBaoVeOTo: [],
  error: null,
};

// Async Thunk for fetching loai ho gia dinh
export const fetchLoaiHoGiaDinh = createAsyncThunk('commonCategories/fetchLoaiHoGiaDinh', async (_, {rejectWithValue}) => {
  try {
    const params = {
      actionCode: ACTION_CODE.GET_LOAI_HO_GIA_DINH,
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch loai ho gia dinh');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

// Async Thunk for fetching benh vien
export const fetchDanhSachBenhVien = createAsyncThunk('commonCategories/fetchDanhSachBenhVien', async (_, {rejectWithValue}) => {
  try {
    const params = {
      actionCode: ACTION_CODE.GET_DANH_SACH_BENH_VIEN,
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch danh sach benh vien');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});
export const fetchDanhMucHeThong = createAsyncThunk('commonCategories/fetchDanhMucHeThong', async (_, {rejectWithValue}) => {
  try {
    const params = {
      actionCode: ACTION_CODE.GET_DANH_MUC_HE_THONG,
    };
    const response = await getCommonExecute(params);
    logger.log('🚀 ~ response:', response);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch danh sach benh vien');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

export const fetchDanhMucSucKhoe = createAsyncThunk('commonCategories/fetchDanhMucSucKhoe', async (_, {rejectWithValue}) => {
  try {
    const params = {
      actionCode: ACTION_CODE.GET_DANH_MUC_SUC_KHOE,
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch danh sach suc khoe');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

export const fetchDanhMucXe = createAsyncThunk('commonCategories/fetchDanhMucXe', async (_, {rejectWithValue}) => {
  try {
    const params = {
      actionCode: ACTION_CODE.GET_DANH_MUC_XE,
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch danh sach xe');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

export const fetchDanhMucOTo = createAsyncThunk('commonCategories/fetchDanhMucOTo', async (_, {rejectWithValue}) => {
  try {
    const params = {
      actionCode: ACTION_CODE.GET_DANH_MUC_O_TO,
    };
    const response = await getCommonExecute(params);
    if (response.data) {
      return response.data;
    }
    return rejectWithValue(response.message || 'Failed to fetch danh sach o to');
  } catch (error: any) {
    return rejectWithValue(error.message || 'An unknown error occurred');
  }
});

const commonSlice = createSlice({
  name: 'commonCategories',
  initialState,
  reducers: {
    clearCommonCategoriesError: state => {
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchLoaiHoGiaDinh.fulfilled, (state, action: PayloadAction<LoaiHoGiaDinh[]>) => {
      state.danhSachLoaiHoGiaDinh = action.payload;
    });
    builder.addCase(fetchDanhMucHeThong.fulfilled, (state, action: PayloadAction<{mqh_ndbh: LoaiHoGiaDinh[]; mqh_nguoi_mua: LoaiHoGiaDinh[]}>) => {
      state.danhSachMoiQuanHeNdbh = action.payload.mqh_ndbh;
      state.danhSachMoiQuanHeNguoiMua = action.payload.mqh_nguoi_mua;
    });
    builder.addCase(fetchDanhMucXe.fulfilled, (state, action: PayloadAction<{loai_xe: LoaiHoGiaDinh[]; mtn_lphu_xe: LoaiHoGiaDinh[]; nha_bh: LoaiHoGiaDinh[]}>) => {
      state.danhSachLoaiXe = action.payload.loai_xe;
      state.danhSachMucBaoVe = action.payload.mtn_lphu_xe;
      state.danhsachNhaBaoHiem = action.payload.nha_bh;
    });
    builder.addCase(fetchDanhMucSucKhoe.fulfilled, (state, action: PayloadAction<{nha_bh: LoaiHoGiaDinh[]}>) => {
      state.danhSachNhaBaoHiemSucKhoe = action.payload.nha_bh;
    });
    builder.addCase(fetchDanhMucOTo.fulfilled, (state, action: PayloadAction<{loai_xe: LoaiHoGiaDinh[]; mtn_lphu_xe: LoaiHoGiaDinh[]; nha_bh: LoaiHoGiaDinh[]}>) => {
      state.danhSachLoaiXeOTo = action.payload.loai_xe;
      state.danhSachMucBaoVeOTo = action.payload.mtn_lphu_xe;
      state.danhSachNhaBaoHiemOTo = action.payload.nha_bh;
    });
  },
});

export const {clearCommonCategoriesError} = commonSlice.actions;

// Selectors
export const selectDanhSachLoaiHoGiaDinh = (state: any) => state.commonCategories.danhSachLoaiHoGiaDinh;
export const selectDanhSachMoiQuanHeNdbh = (state: any) => state.commonCategories.danhSachMoiQuanHeNdbh;
export const selectDanhSachMoiQuanHeNguoiMua = (state: any) => state.commonCategories.danhSachMoiQuanHeNguoiMua;
export const selectDanhSachLoaiXe = (state: any) => state.commonCategories.danhSachLoaiXe;
export const selectDanhSachMucBaoVe = (state: any) => state.commonCategories.danhSachMucBaoVe;
export const selectDanhSachNhaBaoHiem = (state: any) => state.commonCategories.danhsachNhaBaoHiem;
export const selectDanhSachNhaBaoHiemSucKhoe = (state: any) => state.commonCategories.danhSachNhaBaoHiemSucKhoe;
export const selectDanhSachLoaiXeOTo = (state: any) => state.commonCategories.danhSachLoaiXeOTo;
export const selectDanhSachMucBaoVeOTo = (state: any) => state.commonCategories.danhSachMucBaoVeOTo;
export const selectDanhSachNhaBaoHiemOTo = (state: any) => state.commonCategories.danhSachNhaBaoHiemOTo;
export const selectCommonCategoriesError = (state: any) => state.commonCategories.error;

export default commonSlice.reducer;
