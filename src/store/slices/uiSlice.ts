import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UiState {
  isDialogLoading: boolean;
}

const initialState: UiState = {
  isDialogLoading: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setDialogLoading(state, action: PayloadAction<boolean>) {
      state.isDialogLoading = action.payload;
    },
  },
});

export const { setDialogLoading } = uiSlice.actions;
export default uiSlice.reducer;

