import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  user: any | null;
  nsd: any | null;
  pas: any | null;
  loading: boolean;
  error: string | null;
  deletionRequest: {email: string; timestamp: number} | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  token: null,
  refreshToken: null,
  user: null,
  loading: false,
  error: null,
  nsd: null,
  pas: null,
  deletionRequest: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: state => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<{token: string; refreshToken: string; user: any; nsd: any; pas: any}>) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.refreshToken = action.payload.refreshToken;
      state.user = action.payload.user;
      state.nsd = action.payload.nsd;
      state.pas = action.payload.pas;
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: state => {
      state.isAuthenticated = false;
      state.token = null;
      state.refreshToken = null;
      state.user = null;
    },
    clearError: state => {
      state.error = null;
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
    },
    setDeletionRequest: (state, action: PayloadAction<{email: string; timestamp: number} | null>) => {
      state.deletionRequest = action.payload;
    },
  },
});

export const {loginStart, loginSuccess, loginFailure, logout, clearError, setToken, setDeletionRequest} = authSlice.actions;
export default authSlice.reducer;
