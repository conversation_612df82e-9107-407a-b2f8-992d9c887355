import {StackActions, CommonActions, createNavigationContainerRef} from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef<any>();

function navigate(name: string, params?: object) {
  if (navigationRef.isReady()) navigationRef.dispatch(CommonActions.navigate(name, params));
}

function replace(name: string, params?: object) {
  if (navigationRef.isReady()) navigationRef.dispatch(StackActions.replace(name, params));
}

function push(name: string, params?: object) {
  if (navigationRef.isReady()) navigationRef.dispatch(StackActions.push(name, params));
}

function goBack() {
  try {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      navigationRef.dispatch(CommonActions.goBack());
    }
  } catch (error) {
    console.error(error);
  }
}

function pop(count?: number) {
  if (navigationRef.isReady()) navigationRef.dispatch(StackActions.pop(count || 1));
}

function getCurrentScreen() {
  if (navigationRef.isReady()) return navigationRef.getCurrentRoute();
}

function dismiss() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.popToTop());
    goBack();
  }
}

function updateParams(key: string, params: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch({...CommonActions.setParams(params), source: key});
  }
}

function reset(index: number, screenName: string) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: index,
        routes: [{name: screenName}],
      }),
    );
  }
}

function getState() {
  if (navigationRef.isReady()) {
    return navigationRef.getState();
  }
}

function popToRootStack() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.popToTop());
  }
}

const NavigationUtil = {
  dismiss,
  navigate,
  goBack,
  push,
  replace,
  pop,
  getCurrentScreen,
  updateParams,
  reset,
  popToRootStack,
  getState,
};

export default NavigationUtil;
