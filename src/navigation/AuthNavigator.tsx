import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {StatusBar} from 'react-native';
import {colors} from '../constants/theme';
import {AUTH_SCREENS} from './routes';

// Import Screens
import LoginScreen from '../screens/auth/LoginScreen';
import PlaceholderScreen from '../screens/main/Placeholder/PlaceholderScreen';

const Stack = createStackNavigator();

const AuthNavigator = () => {
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      <Stack.Navigator screenOptions={{headerShown: false}}>
        <Stack.Screen name={AUTH_SCREENS.LOGIN} component={LoginScreen} />
        <Stack.Screen name="Support" component={PlaceholderScreen} />
        {/* Thêm các màn hình khác của luồng auth tại đây nếu có */}
        {/* <Stack.Screen name={AUTH_SCREENS.REGISTER} component={RegisterScreen} /> */}
      </Stack.Navigator>
    </>
  );
};

export default AuthNavigator;
