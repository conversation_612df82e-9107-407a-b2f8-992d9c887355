import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {MAIN_SCREENS} from './routes';
import {Dimensions, Image, Platform, StyleSheet, Text, View} from 'react-native';
import R from '../assets/R';
import {colors, spacing, typography} from '../constants/theme';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

// Import Screens
import CollectionScreen from '../screens/main/BHXH/Collection/CollectionScreen';
import SearchInformationScreen from '../screens/main/BHXH/SearchInfomation/SearchInformationScreen';
import InformationParticipantScreen from '../screens/main/BHXH/InformationParticipant/InformationParticipantScreen';
import DetailCollectionScreen from '../screens/main/BHXH/DetailCollection/DetailCollectionScreen';
import VietQRGenerator from '../components/QRPayment/VietQRGenerator';
import BankAccountScreen from '../screens/main/BankAccountScreen';
import DetailProductScreen from '@screens/main/BHXH/DetailProduct/DetailProductScreen';
import ImageScreen from '@screens/main/BHXH/Image/ImageScreen';
import PaymentScreen from '@screens/main/BHXH/Payment/PaymentScreen';
import {
  ChangePasswordScreen,
  ChonGoiBaoHiemNTNScreen,
  DanhSachDonNTNScreen,
  DanhSachDonOToScreen,
  DanhSachDonScreen,
  DanhSachGoiNTNScreen,
  DsGoiBhScreen,
  HomeScreen,
  HouseholdFormScreen,
  HouseholdListScreen,
  InsuranceInfoScreen,
  InsuranceSalesHistoryScreen,
  PersonalEditScreen,
  PersonalInfoScreen,
  ProcessingScreen,
  ProfileScreen,
  ThongTinDonBhScreen,
  ThongTinDonNTNScreen,
  ThongTinDonOToScreen,
  ThongTinDonScreen,
  ThongTinMuaNTNScreen,
  ThongTinMuaOToScreen,
  ThongTinMuaScreen,
  ThongTinNDBHScreen,
  ThongTinSanPhamScreen,
  ThongTinXeOToScreen,
  ThongTinXeScreen,
  TomTatQuyenLoiNTNScreen,
} from '@screens/main';
import {HouseholdInfoScreen, HouseholdMemberFormScreen, HouseholdMembersScreen, TransactionHistoryScreen} from '@screens/main/Household';
import CameraScreen from '../screens/main/CameraScreen';
import ChangeParticipantScreen from '@screens/main/BHXH/ChangeParticipant/ChangeParticipantScreen';
import PaymentHistoryScreen from '@screens/main/BHXH/PaymentHistory/PaymentHistoryScreen';
import PlaceholderScreen from '@screens/main/Placeholder/PlaceholderScreen';
import StatisticsScreen from '@screens/main/Statistics/StatisticsScreen';
import {RevenueDashboardScreen} from '@screens/main/Statistics';
import {CauHoiSkScreen, ChonGoiBhScreen, DanhSachBhskScreen, ThongTinNguoiThamGiaScreen} from '@screens/main/BHSK';
import TomTatQuyenLoiScreen from '@screens/main/BHSK/TomTatQuyenLoi/TomTatQuyenLoiScreen';
import ThongTinNguoiMuaScreen from '@screens/main/BHSK/ThongTinNguoiMua/ThongTinNguoiMuaScreen';
import ThanhToanScreen from '@screens/main/BHSK/ThanhToan/ThanhToanScreen';
import {ChonGoiBaoHiemVcxOtoScreen, DanhSachDonVCXOtoScreen, ThongTinDonVCXOtoScreen, ThongTinMuaVCXOtoScreen, ThongTinXeVCXOtoScreen} from '@screens/main/VCXOTo';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const screenOptions = {
  headerShown: false,
};

// Tab Navigator cho các màn hình chính
const HomeTabs = () => {
  const insets = useSafeAreaInsets();
  const {width} = Dimensions.get('window');
  const isTablet = width >= 768;
  const tabItemHeight = isTablet ? 56 : 52;
  const tabVerticalPadding = isTablet ? spacing.md : spacing.sm;
  const tabBarHeight = tabItemHeight + tabVerticalPadding * 2 + insets.bottom;
  const labelMarginTop = isTablet ? spacing.xs / 2 : spacing.xs;

  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        headerShown: false,
        tabBarIcon: ({focused}) => {
          let iconName;
          if (route.name === MAIN_SCREENS.HOME) {
            iconName = R.icons.ic_home_2_bold;
          } else if (route.name === MAIN_SCREENS.SUPPORT) {
            iconName = R.icons.ic_headphone_bold;
          } else if (route.name === MAIN_SCREENS.STATISTICS) {
            iconName = R.icons.ic_chart;
          } else if (route.name === MAIN_SCREENS.ACCOUNT) {
            iconName = R.icons.ic_user_square_bold;
          }
          return <Image source={iconName} style={[focused ? styles.tabIconFocus : styles.tabIcon, {tintColor: focused ? colors.green : colors.gray[500]}]} />;
        },
        tabBarLabel: ({focused}) => {
          let label;
          if (route.name === MAIN_SCREENS.HOME) {
            label = 'Trang chủ';
          } else if (route.name === MAIN_SCREENS.SUPPORT) {
            label = 'Hỗ trợ';
          } else if (route.name === MAIN_SCREENS.STATISTICS) {
            label = 'Thống kê';
          } else if (route.name === MAIN_SCREENS.ACCOUNT) {
            label = 'Tài khoản';
          }
          return <Text style={[styles.tabText, {marginTop: labelMarginTop, color: focused ? colors.green : colors.gray[500]}]}>{label}</Text>;
        },
        tabBarStyle: {
          ...styles.tabBar,
          height: Platform.select({
            ios: tabBarHeight,
            android: tabBarHeight,
          }),
          paddingTop: tabVerticalPadding,
          paddingBottom: Platform.select({
            ios: tabVerticalPadding + insets.bottom,
            android: tabVerticalPadding,
          }),
          paddingHorizontal: 0,
        },
        tabBarItemStyle: {
          height: tabItemHeight,
          justifyContent: 'center',
          alignItems: 'center',
        },
      })}>
      <Tab.Screen name={MAIN_SCREENS.HOME} component={HomeScreen} />
      <Tab.Screen name={MAIN_SCREENS.SUPPORT} component={PlaceholderScreen} />
      {/* <Tab.Screen name={MAIN_SCREENS.CAMERA} component={CameraScreen} /> */}
      <Tab.Screen name={MAIN_SCREENS.STATISTICS} component={StatisticsScreen} />
      <Tab.Screen name={MAIN_SCREENS.ACCOUNT} component={ProfileScreen} />
    </Tab.Navigator>
  );
};

// Main Stack Navigator
const MainNavigator = () => {
  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen name={MAIN_SCREENS.HOME_TAB} component={HomeTabs} />
      <Stack.Screen name={MAIN_SCREENS.COLLECTION} component={CollectionScreen} />
      <Stack.Screen name={MAIN_SCREENS.SEARCH_INFORMATION} component={SearchInformationScreen} />
      <Stack.Screen name={MAIN_SCREENS.INFORMATION_PARTICIPANT} component={InformationParticipantScreen} />
      <Stack.Screen name={MAIN_SCREENS.DETAIL_COLLECTION} component={DetailCollectionScreen} />
      <Stack.Screen name={MAIN_SCREENS.VIET_QR} component={VietQRGenerator} />
      <Stack.Screen name={MAIN_SCREENS.PROFILE} component={ProfileScreen} />
      <Stack.Screen name={MAIN_SCREENS.BANK_ACCOUNT} component={BankAccountScreen} />
      <Stack.Screen name={MAIN_SCREENS.DETAIL_PRODUCT} component={DetailProductScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.IMAGE} component={ImageScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.PAYMENT} component={PaymentScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_FORM as any} component={HouseholdFormScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.CHANGE_PARTICIPANT as any} component={ChangeParticipantScreen} />
      <Stack.Screen name={MAIN_SCREENS.PAYMENT_HISTORY} component={PaymentHistoryScreen} />
      <Stack.Screen name={MAIN_SCREENS.PROCESSING} component={ProcessingScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_LIST} component={HouseholdListScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_INFO} component={HouseholdInfoScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_MEMBERS} component={HouseholdMembersScreen} />
      <Stack.Screen name={MAIN_SCREENS.TRANSACTION_HISTORY} component={TransactionHistoryScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.PLACEHOLDER} component={PlaceholderScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM} component={HouseholdMemberFormScreen as any} />
      <Stack.Screen name={MAIN_SCREENS.INSURANCE_SALES_HISTORY} component={InsuranceSalesHistoryScreen} />
      <Stack.Screen name={MAIN_SCREENS.CAMERA} component={CameraScreen} />
      <Stack.Screen name={MAIN_SCREENS.INSURANCE_INFO} component={InsuranceInfoScreen} />
      <Stack.Screen name={MAIN_SCREENS.PERSONAL_INFO} component={PersonalInfoScreen} />
      <Stack.Screen name={MAIN_SCREENS.PERSONAL_EDIT} component={PersonalEditScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHANGE_PASSWORD} component={ChangePasswordScreen} />
      <Stack.Screen name={MAIN_SCREENS.REVENUE_DASHBOARD} component={RevenueDashboardScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_BHSK} component={DanhSachBhskScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA} component={ThongTinNguoiThamGiaScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHON_GOI_BH} component={ChonGoiBhScreen} />
      <Stack.Screen name={MAIN_SCREENS.CAU_HOI_SK} component={CauHoiSkScreen} />
      <Stack.Screen name={MAIN_SCREENS.TOM_TAT_QUYEN_LOI} component={TomTatQuyenLoiScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_NGUOI_MUA} component={ThongTinNguoiMuaScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_BH} component={ThongTinDonBhScreen} />
      <Stack.Screen name={MAIN_SCREENS.THANH_TOAN} component={ThanhToanScreen} />
      <Stack.Screen name={MAIN_SCREENS.DS_GOI_BH} component={DsGoiBhScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_SAN_PHAM} component={ThongTinSanPhamScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON} component={DanhSachDonScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_XE} component={ThongTinXeScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA} component={ThongTinMuaScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON} component={ThongTinDonScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON_O_TO} component={DanhSachDonOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_XE_O_TO} component={ThongTinXeOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA_O_TO} component={ThongTinMuaOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_O_TO} component={ThongTinDonOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON_NTN} component={DanhSachDonNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_NDBH_NTN} component={ThongTinNDBHScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHON_GOI_BH_NTN} component={ChonGoiBaoHiemNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.TOM_TAT_QUYEN_LOI_NTN} component={TomTatQuyenLoiNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_GOI_BH_NTN} component={DanhSachGoiNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA_NTN} component={ThongTinMuaNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_NTN} component={ThongTinDonNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON_VCX_O_TO} component={DanhSachDonVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_XE_VCX_O_TO} component={ThongTinXeVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA_VCX_O_TO} component={ThongTinMuaVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_VCX_O_TO} component={ThongTinDonVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHON_GOI_BH_VCX_O_TO} component={ChonGoiBaoHiemVcxOtoScreen} />
    </Stack.Navigator>
  );
};

// Placeholder cho các tab chưa có
const TabPlaceholderScreen = () => (
  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
    <Text>Placeholder Screen</Text>
  </View>
);

const styles = StyleSheet.create({
  tabBar: {
    paddingTop: 4,
  },
  tabIcon: {
    width: 24,
    height: 24,
    marginBottom: 4,
  },
  tabIconFocus: {
    width: 26,
    height: 26,
    marginBottom: 4,
  },
  tabText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default MainNavigator;
