import React, {Suspense} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {MAIN_SCREENS} from './routes';
import {Dimensions, Image, Platform, StyleSheet, Text, View, ActivityIndicator} from 'react-native';
import R from '../assets/R';
import {colors, spacing, typography} from '../constants/theme';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

// Loading component for lazy loading
const ScreenLoader = () => (
  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.white}}>
    <ActivityIndicator size="large" color={colors.green} />
    <Text style={{marginTop: spacing.md, color: colors.gray[600]}}>Đang tải...</Text>
  </View>
);

// Lazy load all screens
const CollectionScreen = React.lazy(() => import('../screens/main/BHXH/Collection/CollectionScreen').then(module => ({default: module.default})));
const SearchInformationScreen = React.lazy(() => import('../screens/main/BHXH/SearchInfomation/SearchInformationScreen').then(module => ({default: module.default})));
const InformationParticipantScreen = React.lazy(() => import('../screens/main/BHXH/InformationParticipant/InformationParticipantScreen').then(module => ({default: module.default})));
const DetailCollectionScreen = React.lazy(() => import('../screens/main/BHXH/DetailCollection/DetailCollectionScreen').then(module => ({default: module.default})));
const VietQRGenerator = React.lazy(() => import('../components/QRPayment/VietQRGenerator').then(module => ({default: module.default})));
const BankAccountScreen = React.lazy(() => import('../screens/main/BankAccountScreen').then(module => ({default: module.default})));
const DetailProductScreen = React.lazy(() => import('@screens/main/BHXH/DetailProduct/DetailProductScreen').then(module => ({default: module.default})));
const ImageScreen = React.lazy(() => import('@screens/main/BHXH/Image/ImageScreen').then(module => ({default: module.default})));
const PaymentScreen = React.lazy(() => import('@screens/main/BHXH/Payment/PaymentScreen').then(module => ({default: module.default})));
const ChangePasswordScreen = React.lazy(() => import('@screens/main/Profile/ChangePassword/ChangePasswordScreen').then(module => ({default: module.default})));
const ChonGoiBaoHiemNTNScreen = React.lazy(() => import('@screens/main/NhaTuNhan/ChonGoiBaoHiem/ChonGoiBaoHiemNTNScreen').then(module => ({default: module.default})));
const DanhSachDonNTNScreen = React.lazy(() => import('@screens/main/NhaTuNhan/DanhSachDon/DanhSachDonNTNScreen').then(module => ({default: module.default})));
const DanhSachDonOToScreen = React.lazy(() => import('@screens/main/BHTNDSOto/DanhSachDon/DanhSachDonOToScreen').then(module => ({default: module.default})));
const DanhSachDonScreen = React.lazy(() => import('@screens/main/BHTNDSXeMay/DanhSachDon/DanhSachDonScreen').then(module => ({default: module.default})));
const DanhSachGoiNTNScreen = React.lazy(() => import('@screens/main/NhaTuNhan/DanhSachGoi/DanhSachGoiNTNScreen').then(module => ({default: module.default})));
const DsGoiBhScreen = React.lazy(() => import('@screens/main/BHSK/DSGoiBH/DSGoiBHScreen').then(module => ({default: module.default})));
const HomeScreen = React.lazy(() => import('@screens/main/Home/HomeScreen').then(module => ({default: module.HomeScreen})));
const HouseholdFormScreen = React.lazy(() => import('@screens/main/BHXH/HouseholdForm/HouseholdFormScreen').then(module => ({default: module.default})));
const HouseholdListScreen = React.lazy(() => import('@screens/main/Household/HouseholdList/HouseholdListScreen').then(module => ({default: module.default})));
const InsuranceInfoScreen = React.lazy(() => import('@screens/main/Profile/InsuranceInfo/InsuranceInfoScreen').then(module => ({default: module.default})));
const InsuranceSalesHistoryScreen = React.lazy(() => import('@screens/main/Profile/InsuranceSalesHistory/InsuranceSalesHistoryScreen').then(module => ({default: module.default})));
const PersonalEditScreen = React.lazy(() => import('@screens/main/Profile/PersonalEdit/PersonalEditScreen').then(module => ({default: module.default})));
const PersonalInfoScreen = React.lazy(() => import('@screens/main/Profile/PersonalInfo/PersonalInfoScreen').then(module => ({default: module.default})));
const ProcessingScreen = React.lazy(() => import('@screens/main/BHXH/Processing/ProcessingScreen').then(module => ({default: module.default})));
const ProfileScreen = React.lazy(() => import('@screens/main/Profile/ProfileScreen').then(module => ({default: module.ProfileScreen})));
const ThongTinDonBhScreen = React.lazy(() => import('@screens/main/BHSK/ThongTinDonBH/ThongTinDonBHScreen').then(module => ({default: module.default})));
const ThongTinDonNTNScreen = React.lazy(() => import('@screens/main/NhaTuNhan/ThongTinDon/ThongTinDonNTNScreen').then(module => ({default: module.default})));
const ThongTinDonOToScreen = React.lazy(() => import('@screens/main/BHTNDSOto/ThongTinDon/ThongTinDonOToScreen').then(module => ({default: module.default})));
const ThongTinDonScreen = React.lazy(() => import('@screens/main/BHTNDSXeMay/ThongTinDon/ThongTinDonScreen').then(module => ({default: module.default})));
const ThongTinMuaNTNScreen = React.lazy(() => import('@screens/main/NhaTuNhan/ThongTinMua/ThongTinMuaNTNScreen').then(module => ({default: module.default})));
const ThongTinMuaOToScreen = React.lazy(() => import('@screens/main/BHTNDSOto/ThongTinMua/ThongTinMuaOToScreen').then(module => ({default: module.default})));
const ThongTinMuaScreen = React.lazy(() => import('@screens/main/BHTNDSXeMay/ThongTinMua/ThongTinMuaScreen').then(module => ({default: module.default})));
const ThongTinNDBHScreen = React.lazy(() => import('@screens/main/NhaTuNhan/ThongTinNDBH/ThongTinNDBHScreen').then(module => ({default: module.default})));
const ThongTinSanPhamScreen = React.lazy(() => import('@screens/main/BHSK/ThongTinSanPham/ThongTinSanPhamScreen').then(module => ({default: module.default})));
const ThongTinXeOToScreen = React.lazy(() => import('@screens/main/BHTNDSOto/ThongTinXe/ThongTinXeOToScreen').then(module => ({default: module.default})));
const ThongTinXeScreen = React.lazy(() => import('@screens/main/BHTNDSXeMay/ThongTinXe/ThongTinXeScreen').then(module => ({default: module.default})));
const TomTatQuyenLoiNTNScreen = React.lazy(() => import('@screens/main/NhaTuNhan/TomTatQuyenLoi/TomTatQuyenLoiNTNScreen').then(module => ({default: module.default})));
const HouseholdInfoScreen = React.lazy(() => import('@screens/main/Household/HouseholdInfo/HouseholdInfoScreen').then(module => ({default: module.default})));
const HouseholdMemberFormScreen = React.lazy(() => import('@screens/main/Household/HouseholdMemberForm/HouseholdMemberFormScreen').then(module => ({default: module.default})));
const HouseholdMembersScreen = React.lazy(() => import('@screens/main/Household/HouseholdMembers/HouseholdMembersScreen').then(module => ({default: module.default})));
const TransactionHistoryScreen = React.lazy(() => import('@screens/main/Household/TransactionHistory/TransactionHistoryScreen').then(module => ({default: module.default})));
const CameraScreen = React.lazy(() => import('../screens/main/CameraScreen').then(module => ({default: module.default})));
const ChangeParticipantScreen = React.lazy(() => import('@screens/main/BHXH/ChangeParticipant/ChangeParticipantScreen').then(module => ({default: module.default})));
const PaymentHistoryScreen = React.lazy(() => import('@screens/main/BHXH/PaymentHistory/PaymentHistoryScreen').then(module => ({default: module.default})));
const PlaceholderScreen = React.lazy(() => import('@screens/main/Placeholder/PlaceholderScreen').then(module => ({default: module.default})));
const StatisticsScreen = React.lazy(() => import('@screens/main/Statistics/StatisticsScreen').then(module => ({default: module.default})));
const RevenueDashboardScreen = React.lazy(() => import('@screens/main/Statistics/RevenueDashboard/RevenueDashboardScreen').then(module => ({default: module.default})));
const CauHoiSkScreen = React.lazy(() => import('@screens/main/BHSK/CauHoiSK/CauHoiSKScreen').then(module => ({default: module.default})));
const ChonGoiBhScreen = React.lazy(() => import('@screens/main/BHSK/ChonGoiBH/ChonGoiBHScreen').then(module => ({default: module.default})));
const DanhSachBhskScreen = React.lazy(() => import('@screens/main/BHSK/DanhSachBHSK/DanhSachBHSKScreen').then(module => ({default: module.default})));
const ThongTinNguoiThamGiaScreen = React.lazy(() => import('@screens/main/BHSK/ThongTinNguoiThamGia/ThongTinNguoiThamGiaScreen').then(module => ({default: module.default})));
const TomTatQuyenLoiScreen = React.lazy(() => import('@screens/main/BHSK/TomTatQuyenLoi/TomTatQuyenLoiScreen').then(module => ({default: module.default})));
const ThongTinNguoiMuaScreen = React.lazy(() => import('@screens/main/BHSK/ThongTinNguoiMua/ThongTinNguoiMuaScreen').then(module => ({default: module.default})));
const ThanhToanScreen = React.lazy(() => import('@screens/main/BHSK/ThanhToan/ThanhToanScreen').then(module => ({default: module.default})));
const ChonGoiBaoHiemVcxOtoScreen = React.lazy(() => import('@screens/main/VCXOTo/ChonGoiBaoHiem/ChonGoiBaoHiemVCXOtoScreen').then(module => ({default: module.default})));
const DanhSachDonVCXOtoScreen = React.lazy(() => import('@screens/main/VCXOTo/DanhSachDon/DanhSachDonVCXOtoScreen').then(module => ({default: module.default})));
const ThongTinDonVCXOtoScreen = React.lazy(() => import('@screens/main/VCXOTo/ThongTinDon/ThongTinDonVCXOtoScreen').then(module => ({default: module.default})));
const ThongTinMuaVCXOtoScreen = React.lazy(() => import('@screens/main/VCXOTo/ThongTinMua/ThongTinMuaVCXOtoScreen').then(module => ({default: module.default})));
const ThongTinXeVCXOtoScreen = React.lazy(() => import('@screens/main/VCXOTo/ThongTinXe/ThongTinXeVCXOtoScreen').then(module => ({default: module.default})));

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const screenOptions = {
  headerShown: false,
};

// Tab Navigator cho các màn hình chính
const HomeTabs = () => {
  const insets = useSafeAreaInsets();
  const {width} = Dimensions.get('window');
  const isTablet = width >= 768;
  const tabItemHeight = isTablet ? 56 : 52;
  const tabVerticalPadding = isTablet ? spacing.md : spacing.sm;
  const tabBarHeight = tabItemHeight + tabVerticalPadding * 2 + insets.bottom;
  const labelMarginTop = isTablet ? spacing.xs / 2 : spacing.xs;

  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        headerShown: false,
        tabBarIcon: ({focused}) => {
          let iconName;
          if (route.name === MAIN_SCREENS.HOME) {
            iconName = R.icons.ic_home_2_bold;
          } else if (route.name === MAIN_SCREENS.SUPPORT) {
            iconName = R.icons.ic_headphone_bold;
          } else if (route.name === MAIN_SCREENS.STATISTICS) {
            iconName = R.icons.ic_chart;
          } else if (route.name === MAIN_SCREENS.ACCOUNT) {
            iconName = R.icons.ic_user_square_bold;
          }
          return <Image source={iconName} style={[focused ? styles.tabIconFocus : styles.tabIcon, {tintColor: focused ? colors.green : colors.gray[500]}]} />;
        },
        tabBarLabel: ({focused}) => {
          let label;
          if (route.name === MAIN_SCREENS.HOME) {
            label = 'Trang chủ';
          } else if (route.name === MAIN_SCREENS.SUPPORT) {
            label = 'Hỗ trợ';
          } else if (route.name === MAIN_SCREENS.STATISTICS) {
            label = 'Thống kê';
          } else if (route.name === MAIN_SCREENS.ACCOUNT) {
            label = 'Tài khoản';
          }
          return <Text style={[styles.tabText, {marginTop: labelMarginTop, color: focused ? colors.green : colors.gray[500]}]}>{label}</Text>;
        },
        tabBarStyle: {
          ...styles.tabBar,
          height: Platform.select({
            ios: tabBarHeight,
            android: tabBarHeight,
          }),
          paddingTop: tabVerticalPadding,
          paddingBottom: Platform.select({
            ios: tabVerticalPadding + insets.bottom,
            android: tabVerticalPadding,
          }),
          paddingHorizontal: 0,
        },
        tabBarItemStyle: {
          height: tabItemHeight,
          justifyContent: 'center',
          alignItems: 'center',
        },
      })}>
      <Tab.Screen name={MAIN_SCREENS.HOME} component={HomeScreen} />
      <Tab.Screen name={MAIN_SCREENS.SUPPORT} component={PlaceholderScreen} />
      {/* <Tab.Screen name={MAIN_SCREENS.CAMERA} component={CameraScreen} /> */}
      <Tab.Screen name={MAIN_SCREENS.STATISTICS} component={StatisticsScreen} />
      <Tab.Screen name={MAIN_SCREENS.ACCOUNT} component={ProfileScreen} />
    </Tab.Navigator>
  );
};

// Lazy loaded screen components with proper props forwarding
const LazyCollectionScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <CollectionScreen {...props} />
  </Suspense>
);

const LazySearchInformationScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <SearchInformationScreen {...props} />
  </Suspense>
);

const LazyInformationParticipantScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <InformationParticipantScreen {...props} />
  </Suspense>
);

const LazyDetailCollectionScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DetailCollectionScreen {...props} />
  </Suspense>
);

const LazyVietQRGenerator = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <VietQRGenerator {...props} />
  </Suspense>
);

const LazyProfileScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ProfileScreen {...props} />
  </Suspense>
);

const LazyBankAccountScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <BankAccountScreen {...props} />
  </Suspense>
);

const LazyDetailProductScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DetailProductScreen {...props} />
  </Suspense>
);

const LazyImageScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ImageScreen {...props} />
  </Suspense>
);

const LazyPaymentScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <PaymentScreen {...props} />
  </Suspense>
);

const LazyHouseholdFormScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <HouseholdFormScreen {...props} />
  </Suspense>
);

const LazyChangeParticipantScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ChangeParticipantScreen {...props} />
  </Suspense>
);

const LazyPaymentHistoryScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <PaymentHistoryScreen {...props} />
  </Suspense>
);

const LazyProcessingScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ProcessingScreen {...props} />
  </Suspense>
);

const LazyHouseholdListScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <HouseholdListScreen {...props} />
  </Suspense>
);

const LazyHouseholdInfoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <HouseholdInfoScreen {...props} />
  </Suspense>
);

const LazyHouseholdMembersScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <HouseholdMembersScreen {...props} />
  </Suspense>
);

const LazyTransactionHistoryScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <TransactionHistoryScreen {...props} />
  </Suspense>
);

const LazyPlaceholderScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <PlaceholderScreen {...props} />
  </Suspense>
);

const LazyHouseholdMemberFormScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <HouseholdMemberFormScreen {...props} />
  </Suspense>
);

const LazyInsuranceSalesHistoryScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <InsuranceSalesHistoryScreen {...props} />
  </Suspense>
);

const LazyCameraScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <CameraScreen {...props} />
  </Suspense>
);

const LazyInsuranceInfoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <InsuranceInfoScreen {...props} />
  </Suspense>
);

const LazyPersonalInfoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <PersonalInfoScreen {...props} />
  </Suspense>
);

const LazyPersonalEditScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <PersonalEditScreen {...props} />
  </Suspense>
);

const LazyChangePasswordScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ChangePasswordScreen {...props} />
  </Suspense>
);

const LazyRevenueDashboardScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <RevenueDashboardScreen {...props} />
  </Suspense>
);

const LazyDanhSachBhskScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DanhSachBhskScreen {...props} />
  </Suspense>
);

const LazyThongTinNguoiThamGiaScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinNguoiThamGiaScreen {...props} />
  </Suspense>
);

const LazyChonGoiBhScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ChonGoiBhScreen {...props} />
  </Suspense>
);

const LazyCauHoiSkScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <CauHoiSkScreen {...props} />
  </Suspense>
);

const LazyTomTatQuyenLoiScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <TomTatQuyenLoiScreen {...props} />
  </Suspense>
);

const LazyThongTinNguoiMuaScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinNguoiMuaScreen {...props} />
  </Suspense>
);

const LazyThongTinDonBhScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinDonBhScreen {...props} />
  </Suspense>
);

const LazyThanhToanScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThanhToanScreen {...props} />
  </Suspense>
);

const LazyDsGoiBhScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DsGoiBhScreen {...props} />
  </Suspense>
);

const LazyThongTinSanPhamScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinSanPhamScreen {...props} />
  </Suspense>
);

const LazyDanhSachDonScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DanhSachDonScreen {...props} />
  </Suspense>
);

const LazyThongTinXeScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinXeScreen {...props} />
  </Suspense>
);

const LazyThongTinMuaScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinMuaScreen {...props} />
  </Suspense>
);

const LazyThongTinDonScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinDonScreen {...props} />
  </Suspense>
);

const LazyDanhSachDonOToScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DanhSachDonOToScreen {...props} />
  </Suspense>
);

const LazyThongTinXeOToScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinXeOToScreen {...props} />
  </Suspense>
);

const LazyThongTinMuaOToScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinMuaOToScreen {...props} />
  </Suspense>
);

const LazyThongTinDonOToScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinDonOToScreen {...props} />
  </Suspense>
);

const LazyDanhSachDonNTNScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DanhSachDonNTNScreen {...props} />
  </Suspense>
);

const LazyThongTinNDBHScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinNDBHScreen {...props} />
  </Suspense>
);

const LazyChonGoiBaoHiemNTNScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ChonGoiBaoHiemNTNScreen {...props} />
  </Suspense>
);

const LazyTomTatQuyenLoiNTNScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <TomTatQuyenLoiNTNScreen {...props} />
  </Suspense>
);

const LazyDanhSachGoiNTNScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DanhSachGoiNTNScreen {...props} />
  </Suspense>
);

const LazyThongTinMuaNTNScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinMuaNTNScreen {...props} />
  </Suspense>
);

const LazyThongTinDonNTNScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinDonNTNScreen {...props} />
  </Suspense>
);

const LazyDanhSachDonVCXOtoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <DanhSachDonVCXOtoScreen {...props} />
  </Suspense>
);

const LazyThongTinXeVCXOtoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinXeVCXOtoScreen {...props} />
  </Suspense>
);

const LazyThongTinMuaVCXOtoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinMuaVCXOtoScreen {...props} />
  </Suspense>
);

const LazyThongTinDonVCXOtoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ThongTinDonVCXOtoScreen {...props} />
  </Suspense>
);

const LazyChonGoiBaoHiemVcxOtoScreen = (props: any) => (
  <Suspense fallback={<ScreenLoader />}>
    <ChonGoiBaoHiemVcxOtoScreen {...props} />
  </Suspense>
);

// Main Stack Navigator
const MainNavigator = () => {
  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen name={MAIN_SCREENS.HOME_TAB} component={HomeTabs} />
      <Stack.Screen name={MAIN_SCREENS.COLLECTION} component={LazyCollectionScreen} />
      <Stack.Screen name={MAIN_SCREENS.SEARCH_INFORMATION} component={LazySearchInformationScreen} />
      <Stack.Screen name={MAIN_SCREENS.INFORMATION_PARTICIPANT} component={LazyInformationParticipantScreen} />
      <Stack.Screen name={MAIN_SCREENS.DETAIL_COLLECTION} component={LazyDetailCollectionScreen} />
      <Stack.Screen name={MAIN_SCREENS.VIET_QR} component={LazyVietQRGenerator} />
      <Stack.Screen name={MAIN_SCREENS.PROFILE} component={LazyProfileScreen} />
      <Stack.Screen name={MAIN_SCREENS.BANK_ACCOUNT} component={LazyBankAccountScreen} />
      <Stack.Screen name={MAIN_SCREENS.DETAIL_PRODUCT} component={LazyDetailProductScreen} />
      <Stack.Screen name={MAIN_SCREENS.IMAGE} component={LazyImageScreen} />
      <Stack.Screen name={MAIN_SCREENS.PAYMENT} component={LazyPaymentScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_FORM} component={LazyHouseholdFormScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHANGE_PARTICIPANT} component={LazyChangeParticipantScreen} />
      <Stack.Screen name={MAIN_SCREENS.PAYMENT_HISTORY} component={LazyPaymentHistoryScreen} />
      <Stack.Screen name={MAIN_SCREENS.PROCESSING} component={LazyProcessingScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_LIST} component={LazyHouseholdListScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_INFO} component={LazyHouseholdInfoScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_MEMBERS} component={LazyHouseholdMembersScreen} />
      <Stack.Screen name={MAIN_SCREENS.TRANSACTION_HISTORY} component={LazyTransactionHistoryScreen} />
      <Stack.Screen name={MAIN_SCREENS.PLACEHOLDER} component={LazyPlaceholderScreen} />
      <Stack.Screen name={MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM} component={LazyHouseholdMemberFormScreen} />
      <Stack.Screen name={MAIN_SCREENS.INSURANCE_SALES_HISTORY} component={LazyInsuranceSalesHistoryScreen} />
      <Stack.Screen name={MAIN_SCREENS.CAMERA} component={LazyCameraScreen} />
      <Stack.Screen name={MAIN_SCREENS.INSURANCE_INFO} component={LazyInsuranceInfoScreen} />
      <Stack.Screen name={MAIN_SCREENS.PERSONAL_INFO} component={LazyPersonalInfoScreen} />
      <Stack.Screen name={MAIN_SCREENS.PERSONAL_EDIT} component={LazyPersonalEditScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHANGE_PASSWORD} component={LazyChangePasswordScreen} />
      <Stack.Screen name={MAIN_SCREENS.REVENUE_DASHBOARD} component={LazyRevenueDashboardScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_BHSK} component={LazyDanhSachBhskScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA} component={LazyThongTinNguoiThamGiaScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHON_GOI_BH} component={LazyChonGoiBhScreen} />
      <Stack.Screen name={MAIN_SCREENS.CAU_HOI_SK} component={LazyCauHoiSkScreen} />
      <Stack.Screen name={MAIN_SCREENS.TOM_TAT_QUYEN_LOI} component={LazyTomTatQuyenLoiScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_NGUOI_MUA} component={LazyThongTinNguoiMuaScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_BH} component={LazyThongTinDonBhScreen} />
      <Stack.Screen name={MAIN_SCREENS.THANH_TOAN} component={LazyThanhToanScreen} />
      <Stack.Screen name={MAIN_SCREENS.DS_GOI_BH} component={LazyDsGoiBhScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_SAN_PHAM} component={LazyThongTinSanPhamScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON} component={LazyDanhSachDonScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_XE} component={LazyThongTinXeScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA} component={LazyThongTinMuaScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON} component={LazyThongTinDonScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON_O_TO} component={LazyDanhSachDonOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_XE_O_TO} component={LazyThongTinXeOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA_O_TO} component={LazyThongTinMuaOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_O_TO} component={LazyThongTinDonOToScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON_NTN} component={LazyDanhSachDonNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_NDBH_NTN} component={LazyThongTinNDBHScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHON_GOI_BH_NTN} component={LazyChonGoiBaoHiemNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.TOM_TAT_QUYEN_LOI_NTN} component={LazyTomTatQuyenLoiNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_GOI_BH_NTN} component={LazyDanhSachGoiNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA_NTN} component={LazyThongTinMuaNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_NTN} component={LazyThongTinDonNTNScreen} />
      <Stack.Screen name={MAIN_SCREENS.DANH_SACH_DON_VCX_O_TO} component={LazyDanhSachDonVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_XE_VCX_O_TO} component={LazyThongTinXeVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_MUA_VCX_O_TO} component={LazyThongTinMuaVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.THONG_TIN_DON_VCX_O_TO} component={LazyThongTinDonVCXOtoScreen} />
      <Stack.Screen name={MAIN_SCREENS.CHON_GOI_BH_VCX_O_TO} component={LazyChonGoiBaoHiemVcxOtoScreen} />
    </Stack.Navigator>
  );
};

// Placeholder cho các tab chưa có
const TabPlaceholderScreen = () => (
  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
    <Text>Placeholder Screen</Text>
  </View>
);

const styles = StyleSheet.create({
  tabBar: {
    paddingTop: 4,
  },
  tabIcon: {
    width: 24,
    height: 24,
    marginBottom: 4,
  },
  tabIconFocus: {
    width: 26,
    height: 26,
    marginBottom: 4,
  },
  tabText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default MainNavigator;
