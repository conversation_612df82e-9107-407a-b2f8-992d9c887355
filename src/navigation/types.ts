import type {StackNavigationProp} from '@react-navigation/stack';
import {AUTH_SCREENS, MAIN_SCREENS} from './routes';

type FormMode = 'create' | 'edit' | 'create_more';

// <PERSON><PERSON><PERSON> nghĩa các màn hình và tham số của chúng cho AuthNavigator
export type AuthStackParamList = {
  [AUTH_SCREENS.LOGIN]: undefined;
  // Thêm các màn hình khác của AuthStack ở đây
};

// Định nghĩa các màn hình và tham số của chúng cho MainNavigator (bao gồm cả các màn hình con)
export type MainStackParamList = {
  HomeTabs: undefined; // Đây là route cho BottomTabNavigator
  [MAIN_SCREENS.COLLECTION]: undefined;
  [MAIN_SCREENS.SEARCH_INFORMATION]: undefined;
  [MAIN_SCREENS.INFORMATION_PARTICIPANT]: {
    memberData?: any;
  };
  [MAIN_SCREENS.DETAIL_COLLECTION]: {
    ma: string;
  };
  [MAIN_SCREENS.VIET_QR]: undefined;
  [MAIN_SCREENS.PROFILE]: undefined;
  [MAIN_SCREENS.DETAIL_PRODUCT]:
    | {
        memberData?: any;
        productData?: any;
        receiptData?: any;
        mode?: 'create' | 'edit';
        chiTietBienLai?: any;
        onUpdateInfo?: (productData: any, receiptData: any) => void;
      }
    | undefined;
  [MAIN_SCREENS.IMAGE]: undefined;
  [MAIN_SCREENS.PAYMENT]: undefined;
  [MAIN_SCREENS.HOUSEHOLD_FORM]: {
    infoData?: any;
    isEditHouseholdOnly?: boolean;
    onUpdateInfo?: (updatedData: any) => void;
  };
  [MAIN_SCREENS.CHANGE_PARTICIPANT]: {
    nameHousehold: string;
    currentParticipant: any;
    participants?: any[];
    onUpdateInfo?: (updatedData: any) => void;
  };
  [MAIN_SCREENS.PAYMENT_HISTORY]: {
    bt_tvien?: number | string;
  };
  [MAIN_SCREENS.PROCESSING]: undefined;
  [MAIN_SCREENS.HOUSEHOLD_LIST]: undefined;
  [MAIN_SCREENS.HOUSEHOLD_INFO]: {
    householdId?: string;
  };
  [MAIN_SCREENS.HOUSEHOLD_MEMBERS]: {
    bt_ho_gia_dinh?: number | string;
    householdData?: {
      bt: number;
      cmt: string;
      dia_chi: string;
      dthoai: string;
      email: string;
      ngay_sinh: string;
      gioi_tinh: string;
      loai: string;
      ma_doi_tac: string;
      ngay_cap_nhat: string;
      ngay_tao: string;
      nguoi_cap_nhat: string;
      nguoi_tao: string;
      phuong_xa: string;
      phuong_xa_ten: string;
      sl_tvien: number;
      sott: number;
      stt: number;
      ten: string;
      tinh_thanh: string;
      tinh_thanh_ten: string;
      trang_thai: string;
      trang_thai_ten: string;
    };
  };
  [MAIN_SCREENS.TRANSACTION_HISTORY]: {
    householdData?: {
      id: string;
      ownerName: string;
      address: string;
      type: string;
      memberCount: number;
    };
  };
  [MAIN_SCREENS.PLACEHOLDER]:
    | {
        title?: string;
        icon?: any;
        color?: string;
      }
    | undefined;
  [MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM]:
    | {
        memberId?: string;
      }
    | undefined;
  [MAIN_SCREENS.INSURANCE_SALES_HISTORY]: undefined;
  [MAIN_SCREENS.INSURANCE_INFO]:
    | {
        dataValidity?: any;
        dataExpired?: any;
      }
    | undefined;
  [MAIN_SCREENS.PERSONAL_INFO]: undefined;
  [MAIN_SCREENS.PERSONAL_EDIT]:
    | {
        userData?: any;
      }
    | undefined;
  [MAIN_SCREENS.CHANGE_PASSWORD]: undefined;

  // Statistics
  [MAIN_SCREENS.STATISTICS]: undefined;
  [MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM]: undefined;
  [MAIN_SCREENS.REVENUE_DASHBOARD]: undefined;
  // Thêm các màn hình khác của MainStack ở đây

  // Bảo hiểm sức khỏe
  [MAIN_SCREENS.DANH_SACH_BHSK]: undefined;
  [MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA]:
    | {
        fromAddInsured?: boolean;
        currentOrderId?: number | string;
        editData?: {
          orderId?: number;
          relationshipWithBuyer?: string;
          fullName?: string;
          gender?: 'male' | 'female';
          dateOfBirth?: Date;
          age?: string;
          idNumber?: string;
          address?: string;
          province?: string;
          email?: string;
          frontCccdImage?: string;
        };
      }
    | undefined;
  [MAIN_SCREENS.CHON_GOI_BH]:
    | {
        fromAddInsured?: boolean;
        insuredPersonData?: any;
        formData?: any;
        // Data from previous screens
        originalFormData?: any;
      }
    | undefined;
  [MAIN_SCREENS.CAU_HOI_SK]:
    | {
        data?: any; // so_id, so_id_dt từ ChonGoiBH API response
        insuredPersonData?: any; // Thông tin người tham gia + gói BH
        fromAddInsured?: boolean;
        // Preserve previous data
        originalFormData?: any;
        packageSelectionData?: any;
      }
    | undefined;
  [MAIN_SCREENS.TOM_TAT_QUYEN_LOI]: undefined;
  [MAIN_SCREENS.THONG_TIN_NGUOI_MUA]:
    | {
        heathData?: any; // Response từ CauHoiSK API
        insuredPersonData?: any; // Thông tin người tham gia + gói BH
        data?: any; // so_id, so_id_dt từ ChonGoiBH
        fromAddInsured?: boolean;
        // Preserve all previous screen data
        originalFormData?: any; // From ThongTinNguoiThamGia
        packageSelectionData?: any; // From ChonGoiBH
        healthQuestionData?: any; // From CauHoiSK
        editData?: {
          buyerType?: 'individual' | 'organization';
          gender?: 'male' | 'female';
          frontIdImage?: string;
          backIdImage?: string;
          [key: string]: any;
        };
      }
    | undefined;
  [MAIN_SCREENS.THONG_TIN_DON_BH]:
    | {
        updatedBuyerData?: any;
        updatedInsuredData?: {
          orderId?: number;
          relationshipWithBuyer?: string;
          fullName?: string;
          gender?: 'male' | 'female';
          dateOfBirth?: Date;
          age?: string;
          idNumber?: string;
          address?: string;
          province?: string;
          email?: string;
          frontCccdImage?: string;
        };
        insuranceOrderData?: {
          contractNumber?: string;
          insuranceCompany?: string;
          insuredPerson?: string;
          buyerName?: string;
          premium?: string;
          startDate?: string;
          status?: 'pending' | 'paid';
          itemId?: string;
        };
        fromDanhSach?: boolean;
        refresh?: boolean;
        // Preserve all previous screen data
        originalFormData?: any; // From ThongTinNguoiThamGia
        packageSelectionData?: any; // From ChonGoiBH
        healthQuestionData?: any; // From CauHoiSK
        insuredPersonData?: any; // Combined insured person data
        heathData?: any; // Health question response
        previousData?: any; // so_id, so_id_dt from previous screens
        fromAddInsured?: boolean; // Original flag
      }
    | undefined;
  [MAIN_SCREENS.THANH_TOAN]: undefined;
  [MAIN_SCREENS.THONG_TIN_SAN_PHAM]: undefined;

  [MAIN_SCREENS.DANH_SACH_DON]: undefined;
  [MAIN_SCREENS.THONG_TIN_XE]:
    | {
        mode: FormMode;
      }
    | undefined;
  [MAIN_SCREENS.THONG_TIN_MUA]:
    | {
        mode: FormMode;
      }
    | undefined;
  [MAIN_SCREENS.THONG_TIN_DON]: {
    so_id?: number;
  };

  [MAIN_SCREENS.DANH_SACH_DON_O_TO]: undefined;
  [MAIN_SCREENS.THONG_TIN_XE_O_TO]:
    | {
        mode: FormMode;
      }
    | undefined;
  [MAIN_SCREENS.THONG_TIN_MUA_O_TO]:
    | {
        mode: FormMode;
      }
    | undefined;
  [MAIN_SCREENS.THONG_TIN_DON_O_TO]: {
    so_id?: number;
  };

  [MAIN_SCREENS.DANH_SACH_DON_NTN]: undefined;
  [MAIN_SCREENS.THONG_TIN_NDBH_NTN]:
    | {
        mode: FormMode;
        thongTinDonBaoHiem: any;
        editData: any;
      }
    | undefined;
  [MAIN_SCREENS.CHON_GOI_BH_NTN]:
    | {
        mode: FormMode;
        thongTinNDBH: any;
        editData: any;
      }
    | undefined;
  [MAIN_SCREENS.TOM_TAT_QUYEN_LOI_NTN]: undefined;
  [MAIN_SCREENS.DANH_SACH_GOI_BH_NTN]: undefined;
  [MAIN_SCREENS.THONG_TIN_MUA_NTN]: {
    mode: FormMode;
    thongTinGoiBaoHiem: any;
    editData: any;
  };
  [MAIN_SCREENS.THONG_TIN_DON_NTN]: {
    so_id: number;
  };
  [MAIN_SCREENS.DANH_SACH_DON_VCX_O_TO]: undefined;
  [MAIN_SCREENS.THONG_TIN_XE_VCX_O_TO]: {
    mode: FormMode;
    thongTinDonBaoHiem: any;
    editData: any;
  };
  [MAIN_SCREENS.THONG_TIN_MUA_VCX_O_TO]: {
    mode: FormMode;
    thongTinGoiBaoHiem: any;
    editData: any;
  };
  [MAIN_SCREENS.THONG_TIN_DON_VCX_O_TO]: {
    so_id: number;
  };
  [MAIN_SCREENS.CHON_GOI_BH_VCX_O_TO]: {
    mode: FormMode;
    thongTinNDBH: any;
    editData: any;
  };
};

// Kiểu cho navigation prop trong các màn hình thuộc MainStack
// Giúp `navigation.navigate()` có type-safety
export type MainNavigationProp = StackNavigationProp<MainStackParamList>;
