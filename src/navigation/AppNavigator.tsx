import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {useAppSelector} from '../store';
import AuthNavigator from './AuthNavigator';
import RootNavigator from './RootNavigator';
import {navigationRef} from './NavigationUtil';

const AppNavigator = () => {
  const {isAuthenticated} = useAppSelector(state => state.auth);

  return <NavigationContainer ref={navigationRef}>{isAuthenticated ? <RootNavigator /> : <AuthNavigator />}</NavigationContainer>;
};

export default AppNavigator;
