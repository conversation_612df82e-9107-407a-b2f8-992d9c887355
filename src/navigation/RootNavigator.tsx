import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import MainNavigator from './MainNavigator';
import {useAppSelector} from '@store/index';
import {View, ActivityIndicator, StyleSheet, Text, StatusBar} from 'react-native';
import {colors} from '@constants/theme';

const DialogLoadingScreen = () => (
  <View style={styles.dialogOverlay}>
    <View style={styles.dialogContent}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={styles.dialogText}>Vui lòng đợi...</Text>
    </View>
  </View>
);

const RootStack = createStackNavigator();

const RootNavigator = () => {
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={colors.green} />
      <RootStack.Navigator screenOptions={{headerShown: false}}>
        <RootStack.Screen name="Main" component={MainNavigator} />
        <RootStack.Screen
          name="DialogLoading"
          component={DialogLoadingScreen}
          options={{
            presentation: 'transparentModal',
            cardStyleInterpolator: ({current: {progress}}) => ({
              cardStyle: {
                opacity: progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1],
                }),
              },
            }),
          }}
        />
      </RootStack.Navigator>
    </>
  );
};

const styles = StyleSheet.create({
  dialogOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContent: {
    backgroundColor: colors.white,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 200,
  },
  dialogText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[700],
  },
});

export default RootNavigator;
