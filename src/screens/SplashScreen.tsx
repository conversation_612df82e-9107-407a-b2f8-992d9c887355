import React, {Component} from 'react';
import {View, Text, StyleSheet, Alert, ActivityIndicator, Image} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {hotUpdateService, HotUpdateInfo, UpdateProgress} from '../services/hotUpdate';
import {colors, spacing, typography} from '../constants/theme';
import R from '../assets/R';
import TextAnimator from '@components/common/TextAnimator';

interface SplashScreenState {
  isNeedUpdate: boolean;
  update: boolean;
  progress: UpdateProgress | null;
}

interface SplashScreenProps {
  onBootstrapComplete: () => void;
}

/**
 * SplashScreen with Hot Update functionality
 * Based on gic-giam-dinh project implementation
 */
class SplashScreen extends Component<SplashScreenProps, SplashScreenState> {
  constructor(props: SplashScreenProps) {
    super(props);
    this.state = {
      isNeedUpdate: false,
      update: false,
      progress: null,
    };
  }

  componentDidMount() {
    // Temporarily disable hot update to fix app freeze
    // this.checkUpdateHotUpdate();

    // Go directly to bootstrap with a small delay for splash effect
    setTimeout(() => {
      this._bootstrapAsync();
    }, 3000);
  }

  /**
   * Check for hot updates
   * Based on checkUpdateHotUpdate from gic-giam-dinh SplashScreen
   */
  checkUpdateHotUpdate = async () => {
    try {
      console.log('Checking for hot updates...');

      const updateInfo = await hotUpdateService.checkForUpdates();

      if (updateInfo) {
        console.log('Update available:', updateInfo);

        // Start download and installation
        await hotUpdateService.downloadAndInstall(updateInfo, {
          updateSuccess: () => {
            console.log('Hot update successful');
            // App will restart automatically
          },
          updateFail: (message: string) => {
            console.log('Hot update failed:', message);
            this._bootstrapAsync();
            Alert.alert('Cập nhật không thành công', message, [
              {
                text: 'Bỏ qua',
                style: 'cancel',
                onPress: () => this._bootstrapAsync(),
              },
            ]);
          },
          restartAfterInstall: true,
          progress: (received: number, total: number) => {
            this.setState({
              progress: {
                receivedBytes: received,
                totalBytes: total,
              },
              isNeedUpdate: true,
              update: false,
            });
          },
        });
      } else {
        console.log('No updates available');
        this._bootstrapAsync();
      }
    } catch (error) {
      console.error('Hot update check failed:', error);
      this._bootstrapAsync();
    }
  };

  /**
   * Complete bootstrap process
   */
  _bootstrapAsync = () => {
    console.log('Bootstrap complete');
    this.props.onBootstrapComplete();
  };

  /**
   * Format bytes for display
   */
  formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  /**
   * Get progress percentage
   */
  getProgressPercentage = (): number => {
    if (!this.state.progress || this.state.progress.totalBytes === 0) {
      return 0;
    }
    return (this.state.progress.receivedBytes / this.state.progress.totalBytes) * 100;
  };

  render() {
    const {isNeedUpdate, progress} = this.state;

    return (
      <View style={styles.container}>
        <View style={styles.content}>
          {/* App Logo */}
          <Image source={R.images.img_logo} style={styles.logo} resizeMode="contain" />

          {/* Loading indicator */}
          <TextAnimator
            content={'Sàn Bảo Hiểm'}
            textStyle={{fontSize: 24, color: colors.green, fontFamily: typography.fontFamily.bold}}
            timing={1200}
            // onFinish={_onFinish}
          />

          {/* Update progress */}
          {isNeedUpdate && progress && (
            <View style={styles.updateContainer}>
              <Text style={styles.updateText}>Đang cập nhật ứng dụng...</Text>

              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, {width: `${this.getProgressPercentage()}%`}]} />
                </View>

                <Text style={styles.progressText}>
                  {this.formatBytes(progress.receivedBytes)} / {this.formatBytes(progress.totalBytes)}
                </Text>

                <Text style={styles.percentageText}>{Math.round(this.getProgressPercentage())}%</Text>
              </View>
            </View>
          )}

          {/* Version info */}
          <Text style={styles.versionText}>Phiên bản {DeviceInfo.getVersion()}</Text>
          <ActivityIndicator size="small" color={colors.green} style={styles.loader} />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: spacing.xl,
  },
  loader: {
    marginVertical: spacing.xl,
  },
  updateContainer: {
    width: '100%',
    marginTop: spacing.xl,
    alignItems: 'center',
  },
  updateText: {
    fontSize: 16,
    color: colors.gray[700],
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: spacing.md,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily.regular,
  },
  percentageText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    fontFamily: typography.fontFamily.regular,
  },
  versionText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: spacing.xl,
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },
});

export default SplashScreen;
