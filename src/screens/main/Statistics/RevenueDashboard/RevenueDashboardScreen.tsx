import React, {useState, useCallback, useEffect} from 'react';
import {View, ScrollView, RefreshControl, Text, TouchableOpacity, Alert} from 'react-native';
import {ScreenComponent, Chart, RevenueCenterContent, Icon, Loading} from '@components/common';
import {colors} from '@constants/theme';
import {ChartDataItem} from '@components/common';
import {styles} from './Style';
import {useNavigation} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';

interface RevenueItem {
  id: string;
  label: string;
  color: string;
  percentage: number;
  totalRevenue: number;
  totalIncome: number;
}

export default function RevenueDashboardScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'confirmed' | 'pending'>('confirmed');
  const [loading, setLoading] = useState(false);

  // Sample data based on the image
  const revenueData: RevenueItem[] = [
    {
      id: '1',
      label: 'BHXH TN',
      color: '#6366F1', // Purple
      percentage: 40.52,
      totalRevenue: 0,
      totalIncome: 0,
    },
    {
      id: '2',
      label: 'BHYT',
      color: '#10B981', // Green
      percentage: 40.52,
      totalRevenue: 0,
      totalIncome: 0,
    },
    {
      id: '3',
      label: 'Vật chất xe',
      color: '#F59E0B', // Orange
      percentage: 40.52,
      totalRevenue: 0,
      totalIncome: 0,
    },
    {
      id: '4',
      label: 'Nhà tư nhân',
      color: '#EF4444', // Red
      percentage: 40.52,
      totalRevenue: 0,
      totalIncome: 0,
    },
  ];

  const chartData: ChartDataItem[] = revenueData.map(item => ({
    label: item.label,
    value: item.percentage,
    color: item.color,
  }));

  const totalRevenue = revenueData.reduce((sum, item) => sum + item.totalRevenue, 0);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      // TODO: Fetch actual data here
      console.log('Data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing data:', error);
      Alert.alert('Lỗi', 'Không thể tải dữ liệu. Vui lòng thử lại.');
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleFilterChange = useCallback((filter: 'confirmed' | 'pending') => {
    setSelectedFilter(filter);
    setLoading(true);
    // Simulate loading new data
    setTimeout(() => {
      setLoading(false);
    }, 800);
  }, []);

  const handleInfoPress = useCallback(() => {
    Alert.alert('Thông tin thống kê', 'Biểu đồ thể hiện tổng quan doanh thu theo các loại hình bảo hiểm.', [{text: 'Đóng', style: 'default'}]);
  }, []);

  const formatCurrency = (amount: number): string => {
    if (amount >= 1000000000) {
      return `${(amount / 1000000000).toFixed(1).replace(/\.0$/, '')} Tr`;
    } else if (amount >= 1000000) {
      const millions = Math.floor(amount / 1000000);
      const thousands = Math.floor((amount % 1000000) / 1000);
      if (thousands === 0) {
        return `${millions}.000.000đ`;
      }
      return `${millions}.${thousands.toString().padStart(3, '0')}.000đ`;
    }
    return `${amount.toLocaleString('vi-VN')}đ`;
  };

  const RevenueCard: React.FC<{item: RevenueItem}> = ({item}) => (
    <View style={styles.revenueCard}>
      <View style={styles.cardHeader}>
        <View style={styles.cardTitleRow}>
          <View style={[styles.colorIndicator, {backgroundColor: item.color}]} />
          <Text style={styles.cardTitle}>{item.label}</Text>
        </View>
        <Text style={[styles.percentage, {color: item.color}]}>{item.percentage}%</Text>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.revenueRow}>
          <Text style={styles.revenueLabel}>Tổng doanh thu</Text>
          <Text style={styles.revenueValue}>{formatCurrency(item.totalRevenue)}</Text>
        </View>
        <View style={styles.revenueRow}>
          <Text style={styles.incomeLabel}>Tổng thu nhập</Text>
          <Text style={styles.incomeValue}>{formatCurrency(item.totalIncome)}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <ScreenComponent
      showHeader
      dialogLoading={loading}
      headerTitle="Thống kê doanh thu"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}>
      <View style={styles.tabsContainer}>
        <TouchableOpacity style={[styles.tab, selectedFilter === 'confirmed' && styles.activeTab]} onPress={() => handleFilterChange('confirmed')} activeOpacity={0.8}>
          <Text style={[styles.tabText, selectedFilter === 'confirmed' && styles.activeTabText]}>Đã xác nhận</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.tab, selectedFilter === 'pending' && styles.activeTab]} onPress={() => handleFilterChange('pending')} activeOpacity={0.8}>
          <Text style={[styles.tabText, selectedFilter === 'pending' && styles.activeTabText]}>Chờ xác nhận</Text>
        </TouchableOpacity>
      </View>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}>
        {/* Filter Tabs */}

        {selectedFilter === 'confirmed' ? (
          <View style={styles.content}>
            {/* Chart Section */}
            <View style={styles.chartSection}>
              <View style={styles.chartHeader}>
                <TouchableOpacity style={styles.infoButton} onPress={handleInfoPress}>
                  <Icon name="InfoCircle" size={20} color={colors.primary} />
                </TouchableOpacity>
              </View>

              <View style={styles.chartContainer}>
                <Chart
                  data={chartData}
                  size={200}
                  strokeWidth={28}
                  labelPosition="bottom"
                  showValues={false}
                  centerContent={<RevenueCenterContent title="Tổng doanh thu" value="0" titleStyle={styles.chartCenterTitle} valueStyle={styles.chartCenterValue} />}
                  containerStyle={styles.chartWrapper}
                  labelStyle={styles.chartLabelStyle}
                  legendSpacing={8}
                />
              </View>
            </View>

            {/* Revenue Cards */}
            <View style={styles.cardsContainer}>
              {revenueData.map(item => (
                <RevenueCard key={item.id} item={item} />
              ))}
            </View>
          </View>
        ) : (
          <View style={styles.content}>
            {/* Chart Section */}
            <View style={styles.chartSection}>
              <View style={styles.chartHeader}>
                <TouchableOpacity style={styles.infoButton} onPress={handleInfoPress}>
                  <Icon name="InfoCircle" size={20} color={colors.primary} />
                </TouchableOpacity>
              </View>

              <View style={styles.chartContainer}>
                <Chart
                  data={chartData}
                  size={200}
                  strokeWidth={28}
                  labelPosition="bottom"
                  showValues={false}
                  centerContent={<RevenueCenterContent title="Tổng doanh thu" value="300.1 Tr" titleStyle={styles.chartCenterTitle} valueStyle={styles.chartCenterValue} />}
                  containerStyle={styles.chartWrapper}
                  labelStyle={styles.chartLabelStyle}
                  legendSpacing={8}
                />
              </View>
            </View>

            {/* Revenue Cards */}
            <View style={styles.cardsContainer}>
              {revenueData.map(item => (
                <RevenueCard key={item.id} item={item} />
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </ScreenComponent>
  );
}
