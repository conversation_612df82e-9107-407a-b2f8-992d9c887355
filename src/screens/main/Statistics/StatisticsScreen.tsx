import {Chart, ChartDataItem, RevenueCenterContent, ScreenComponent, StatisticCard} from '@components/common';
import {colors} from '@constants/theme';
import {MainNavigationProp} from '@navigation/types';
import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {RefreshControl, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {useStatistics} from '../../../hooks/useStatistics';
import {styles} from './Style';
import {MAIN_SCREENS} from '@navigation/routes';
import moment from 'moment';

export default function StatisticsScreen() {
  const {statistics, isLoading, refreshStatistics} = useStatistics();
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation<MainNavigationProp>();

  // Sample data based on the image
  const revenueData = {
    totalRevenue: 0,
    totalIncome: 0,
    trend: {
      revenue: {percentage: 0, isPositive: true, amount: 0},
      income: {percentage: 0, isPositive: false, amount: 0},
    },
  };

  const chartData: ChartDataItem[] = [
    {
      label: 'BHTN',
      value: 50,
      color: '#6366F1', // Purple
    },
    {
      label: 'BHYT',
      value: 50,
      color: '#10B981', // Green
    },
    {
      label: 'Vật chất xe',
      value: 50,
      color: '#F59E0B', // Orange
    },
    {
      label: 'Nhà tư nhân',
      value: 50,
      color: '#EF4444', // Red
    },
  ];

  const averageIncomeData = {
    period: '1 Th9 - 30 Th9.2025',
    amount: 0,
    trend: {amount: -0, description: 'so với tháng trước đó'},
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshStatistics();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number): string => {
    if (amount >= 1000000000) {
      return `${(amount / 1000000000).toFixed(1)}Tr`;
    } else if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(0)}.${String(Math.floor((amount % 1000000) / 100000)).padStart(3, '0')}.${String(Math.floor((amount % 100000) / 1000)).padStart(3, '0')}đ`;
    } else {
      return `${amount.toLocaleString('vi-VN')}đ`;
    }
  };

  const formatTrendAmount = (amount: number): string => {
    if (amount >= 1000000000) {
      return `${amount > 0 ? '+' : ''}${(amount / 1000000000).toFixed(1)}Tr`;
    } else if (amount >= 1000000) {
      return `${amount > 0 ? '+' : ''}${(amount / 1000000).toFixed(1)}tr`;
    } else {
      return `${amount > 0 ? '+' : ''}${amount.toLocaleString('vi-VN')}đ`;
    }
  };

  return (
    <ScreenComponent dialogLoading={isLoading || refreshing} showHeader headerTitle="Thống kê" showBackButton onPressBack={() => navigation.goBack()}>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}>
        {/* Revenue Cards */}
        <StatisticCard
          title="Tổng doanh thu"
          amount={formatCurrency(revenueData.totalRevenue)}
          subtitle={formatTrendAmount(revenueData.trend.revenue.amount)}
          text={'so với cùng kỳ'}
          backgroundColor={colors.white}
          trend={{
            direction: revenueData.trend.revenue.isPositive ? 'up' : 'down',
            percentage: revenueData.trend.revenue.percentage,
          }}
        />

        <StatisticCard
          title="Tổng thu nhập"
          amount={formatCurrency(revenueData.totalIncome)}
          subtitle={formatTrendAmount(revenueData.trend.income.amount)}
          text={'so với cùng kỳ'}
          backgroundColor={colors.white}
          trend={{
            direction: revenueData.trend.income.isPositive ? 'up' : 'down',
            percentage: Math.abs(revenueData.trend.income.percentage),
          }}
        />

        {/* Revenue Breakdown Chart */}
        <View style={styles.chartSection}>
          <View style={styles.chartHeader}>
            <Text style={styles.chartTitle}>Tổng quan doanh thu</Text>
            <Text style={styles.chartSubtitle}>Cập nhật {moment().fromNow()}</Text>
          </View>

          <View style={styles.chartContainer}>
            <Chart
              data={chartData}
              size={220}
              strokeWidth={32}
              labelPosition="right"
              showValues={false}
              valueFormatter={value => `${value.toFixed(2)} Tr`}
              centerContent={<RevenueCenterContent title="" value="0" valueStyle={styles.chartCenterValue} />}
              containerStyle={styles.chartWrapper}
              labelStyle={styles.chartLabelStyle}
              valueStyle={styles.chartValueStyle}
              legendSpacing={12}
            />
          </View>

          <TouchableOpacity onPress={() => navigation.navigate(MAIN_SCREENS.REVENUE_DASHBOARD)} style={styles.viewDetailButton}>
            <Text style={styles.viewDetailText}>Xem chi tiết</Text>
          </TouchableOpacity>
        </View>

        {/* Average Income Section */}
        <View style={styles.incomeSection}>
          <Text style={styles.incomeSectionTitle}>Thu nhập bình quân</Text>
          <Text style={styles.incomePeriod}>({averageIncomeData.period})</Text>
          <Text style={styles.incomeAmount}>{averageIncomeData.amount.toLocaleString('vi-VN')}đ</Text>
          <Text style={styles.incomeTrend}>
            {formatTrendAmount(averageIncomeData.trend.amount)} {averageIncomeData.trend.description}
          </Text>

          <TouchableOpacity style={styles.viewDetailButton}>
            <Text style={styles.viewDetailText}>Xem chi tiết</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScreenComponent>
  );
}
