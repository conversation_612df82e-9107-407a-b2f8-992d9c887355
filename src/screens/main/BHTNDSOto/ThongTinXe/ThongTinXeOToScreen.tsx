import {FlatList, Image, KeyboardAvoidingView, Platform, ScrollView, Switch, Text, TextStyle, View} from 'react-native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActionSheetModal, Button, Card, ConfirmModal, createToastHelpers, DateTimePickerComponent, Icon, Radio, ScreenComponent, TextField, useToast} from '@components/common';
import {styles} from './Styles';
import {colors, spacing} from '@constants/theme';
import {ACTION_CODE, CONFIG_SERVER} from '@constants/axios';
import {Controller, useForm} from 'react-hook-form';
import {ThongTinXeFormValidation} from '@utils/validationSchemas';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {MAIN_SCREENS} from '@navigation/routes';
import {useSelector} from 'react-redux';
import {RootState} from '@store/index';
import {getCommonExecute} from '@services/endpoints';
import {formatCurrency} from '@utils/formatters';
import {findItemByMaOrTen, parseDate, parseSoCho, parseTrongTai, toYMD} from './helpers';

interface FormProps {
  so_nam?: number;
  ngay_hl: Date | string;
  ngay_kt: Date | string;
  md_sd: string;
  loai_xe: string;
  so_cho: number;
  trong_tai: number;
  bien_xe: string;
  so_khung: string;
  so_may: string;
  loai_chu_xe: string;
  ten_chu_xe: string;
  so_cmt: string;
  mst: string;
  dia_chi: string;
  dthoai: string;
  chu_xe_la_nguoi_mua: string;
  tang_cuong: boolean;
  muc_tn_lphu_xe: number;
  nha_bao_hiem: string;
  phi_tnds: number;
  phi_lphu_xe: number;
  phi_bh: number;
}

function getDefaultValues(data?: Partial<FormProps>, danhSachLoaiXe?: any[], maDoiTacQLFromDon?: string, isCreateMore?: boolean): FormProps {
  const ngayHLDate = data?.ngay_hl ? parseDate(data.ngay_hl) : new Date();
  const ngayKTDate = data?.ngay_kt ? parseDate(data.ngay_kt) : '';

  let calculatedSoNam = data?.so_nam ?? 1;
  if (ngayHLDate instanceof Date && ngayKTDate instanceof Date) {
    const diffYears = ngayKTDate.getFullYear() - ngayHLDate.getFullYear();
    calculatedSoNam = diffYears > 0 ? diffYears : 1;
  }

  let loaiXeValue = data?.loai_xe ?? '';
  if (loaiXeValue && danhSachLoaiXe && danhSachLoaiXe.length > 0) {
    const found = findItemByMaOrTen(danhSachLoaiXe, loaiXeValue);
    if (found) {
      loaiXeValue = found.ten;
    }
  }

  // Suy ra tang_cuong từ phi_lphu_xe và muc_tn_lphu_xe nếu không có trong data
  let tangCuongValue = false;
  if (data?.tang_cuong !== undefined) {
    tangCuongValue = data.tang_cuong;
  } else if (data?.phi_lphu_xe !== undefined || data?.muc_tn_lphu_xe !== undefined) {
    tangCuongValue = (data?.phi_lphu_xe ?? 0) > 0 || (data?.muc_tn_lphu_xe ?? 0) > 0;
  }

  return {
    so_nam: calculatedSoNam,
    ngay_hl: ngayHLDate,
    ngay_kt: ngayKTDate,
    md_sd: data?.md_sd ?? 'C',
    loai_xe: loaiXeValue,
    so_cho: parseSoCho(data?.so_cho),
    trong_tai: parseTrongTai(data?.trong_tai),
    bien_xe: data?.bien_xe ?? '',
    so_khung: data?.so_khung ?? '',
    so_may: data?.so_may ?? '',
    chu_xe_la_nguoi_mua: isCreateMore ? 'K' : data?.chu_xe_la_nguoi_mua ?? 'C',
    loai_chu_xe: data?.loai_chu_xe ?? 'C',
    ten_chu_xe: data?.ten_chu_xe ?? '',
    so_cmt: data?.so_cmt ?? '',
    mst: data?.mst ?? '',
    dia_chi: data?.dia_chi ?? '',
    dthoai: data?.dthoai ?? '',
    tang_cuong: tangCuongValue,
    muc_tn_lphu_xe: data?.muc_tn_lphu_xe ?? 0,
    nha_bao_hiem: data?.nha_bao_hiem ?? (data as any)?.ma_doi_tac_ql ?? maDoiTacQLFromDon ?? '',
    phi_tnds: data?.phi_tnds ?? 0,
    phi_lphu_xe: data?.phi_lphu_xe ?? 0,
    phi_bh: data?.phi_bh ?? 0,
  };
}

export default function ThongTinXeOToScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showLoaiXeModal, setShowLoaiXeModal] = useState(false);
  const [showMucBaoVeModal, setShowMucBaoVeModal] = useState(false);
  const {danhSachLoaiXeOTo, danhSachMucBaoVeOTo, danhSachNhaBaoHiemOTo} = useSelector((state: RootState) => state.commonCategories);
  const {mode, thongTinDon, thongTinXe} = route?.params || {};
  console.log('🚀 ~ ThongTinXeOToScreen ~ thongTinDon:', thongTinDon);
  const isEdit = mode === 'edit';
  const isCreateMore = mode === 'create_more';

  const defaultValues = useMemo(
    () => getDefaultValues(thongTinXe, danhSachLoaiXeOTo, thongTinDon?.ma_doi_tac_ql, isCreateMore),
    [thongTinXe, danhSachLoaiXeOTo, thongTinDon?.ma_doi_tac_ql, isCreateMore],
  );

  const {
    control,
    getValues,
    setValue,
    reset,
    clearErrors,
    watch,
    trigger,
    formState: {errors, isSubmitting},
    handleSubmit,
    unregister,
  } = useForm<FormProps>({
    mode: 'onChange',
    defaultValues,
  });

  const loaiChuXe = watch('loai_chu_xe');
  const tangCuong = watch('tang_cuong');
  const loaiXeValue = watch('loai_xe');
  const mucBaoVeValue = watch('muc_tn_lphu_xe');
  const soNam = watch('so_nam');
  const ngayHL = watch('ngay_hl');
  const ngayKT = watch('ngay_kt');
  const phiTNDS = watch('phi_tnds');
  const phiLphuXxe = watch('phi_lphu_xe');
  const phiBH = watch('phi_bh');
  const mucDichValue = watch('md_sd');
  const trongTaiValue = watch('trong_tai');
  const soChoValue = watch('so_cho');
  const nhaBaoHiemValue = watch('nha_bao_hiem');
  const chuXeLaNguoiMua = watch('chu_xe_la_nguoi_mua');

  // Reset form khi defaultValues thay đổi
  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  // Autofill thông tin chủ xe từ người mua khi toggle switch trong chế độ create_more
  useEffect(() => {
    if (isCreateMore && thongTinDon) {
      if (chuXeLaNguoiMua === 'C') {
        const loaiKH = thongTinDon?.loai_kh || 'C';

        // Set loại chủ xe dựa trên loại khách hàng
        setValue('loai_chu_xe', loaiKH, {shouldValidate: true});

        // Điền thông tin chủ xe từ thông tin người mua
        setValue('ten_chu_xe', thongTinDon?.ten_kh || '', {shouldValidate: true});
        setValue('dia_chi', thongTinDon?.dia_chi_kh || '', {shouldValidate: true});
        setValue('dthoai', thongTinDon?.dthoai_kh || '', {shouldValidate: true});

        // Điền CCCD hoặc MST tùy theo loại
        if (loaiKH === 'C') {
          setValue('so_cmt', thongTinDon?.cmt_kh || '', {shouldValidate: true});
          setValue('mst', '');
        } else {
          setValue('mst', thongTinDon?.mst_kh || '', {shouldValidate: true});
          setValue('so_cmt', '');
        }
      } else if (chuXeLaNguoiMua === 'K') {
        // Khi gạt về 'K', xóa tất cả thông tin chủ xe
        setValue('ten_chu_xe', '');
        setValue('so_cmt', '');
        setValue('mst', '');
        setValue('dia_chi', '');
        setValue('dthoai', '');
        // Reset loại chủ xe về mặc định
        setValue('loai_chu_xe', 'C');
      }
    }
  }, [chuXeLaNguoiMua, isCreateMore, thongTinDon, setValue]);

  const tinhPhiBaoHiem = useCallback(async () => {
    try {
      const data = getValues();

      const loaiXeItem = findItemByMaOrTen(danhSachLoaiXeOTo || [], data.loai_xe);
      const maLoaiXe = loaiXeItem?.ma || data.loai_xe;
      const nhaBHItem = findItemByMaOrTen(danhSachNhaBaoHiemOTo || [], data.nha_bao_hiem);
      const maNhaBH = nhaBHItem?.ma || data.nha_bao_hiem;

      const mucBaoVe = data.tang_cuong ? data.muc_tn_lphu_xe : 0;
      if (!data.tang_cuong) {
        setValue('phi_lphu_xe', 0);
      }

      const params = {
        ma_doi_tac_ql: maNhaBH,
        md_sd: data.md_sd,
        so_cho: data.so_cho,
        trong_tai: data.trong_tai,
        muc_tn_lphu_xe: mucBaoVe,
        loai_xe: maLoaiXe,
        ngay_hl: toYMD(data.ngay_hl),
        ngay_kt: toYMD(data.ngay_kt),
        actionCode: ACTION_CODE.TINH_PHI_O_TO,
      };

      console.log('🚀 ~ ThongTinXeOToScreen ~ tinh phi ~ params: ', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ ThongTinXeOToScreen ~ tinh phi ~ response: ', response);

      if (response.data) {
        setValue('phi_tnds', response.data.phi_tnds ?? 0);
        setValue('phi_lphu_xe', response.data.phi_lphu_xe ?? 0);
        setValue('phi_bh', response.data.phi_bh ?? 0);
      }
    } catch (error) {
      console.log('🚀 ~ ThongTinXeOToScreen ~ error: ', error);
    }
  }, [getValues, setValue, danhSachLoaiXeOTo, danhSachNhaBaoHiemOTo]);

  useEffect(() => {
    if (!tangCuong) {
      clearErrors('muc_tn_lphu_xe');
      setValue('muc_tn_lphu_xe', 0);
      setValue('phi_lphu_xe', 0);
    }
  }, [tangCuong, clearErrors, setValue]);

  useEffect(() => {
    if (!ngayHL || !soNam) return;

    const parseDate = (val: Date | string): Date | null => {
      if (val instanceof Date) return val;
      if (typeof val === 'string') {
        const parts = val.split('/');
        if (parts.length !== 3) return null;
        const [day, month, year] = parts.map(Number);
        if (isNaN(day) || isNaN(month) || isNaN(year)) return null;
        return new Date(year, month - 1, day);
      }
      return null;
    };

    const startDate = parseDate(ngayHL);
    if (!startDate) return;

    const endDate = new Date(startDate.getFullYear() + soNam, startDate.getMonth(), startDate.getDate());
    setValue('ngay_kt', endDate as any, {shouldValidate: true, shouldDirty: true});
  }, [soNam, ngayHL, setValue]);

  useEffect(() => {
    if (!ngayHL || !ngayKT || !loaiXeValue || !mucDichValue || !soChoValue || !trongTaiValue || !nhaBaoHiemValue) return;

    if (!tangCuong) {
      tinhPhiBaoHiem();
      return;
    }

    if (tangCuong && !mucBaoVeValue) return;

    tinhPhiBaoHiem();
  }, [ngayKT, mucBaoVeValue, tinhPhiBaoHiem, ngayHL, tangCuong, loaiXeValue, mucDichValue, soChoValue, trongTaiValue, nhaBaoHiemValue]);

  const rendeFooter = (label: string, value: string, textStyle?: TextStyle) => (
    <View style={styles.footerContainer}>
      <Text style={[styles.footerLabel, textStyle]}>{label}</Text>
      <Text style={[styles.footerValue, textStyle]}>{value}</Text>
    </View>
  );

  const renderNhaBaoHiemItem = ({id, uriLogo, ten, selected, onPress}: any) => (
    <TouchableOpacity onPress={onPress}>
      <View style={[styles.itemContainer, selected && styles.selectedItem]}>
        {uriLogo ? (
          <View style={styles.logoContainer}>
            <Image source={{uri: uriLogo}} style={styles.logoItem} />
          </View>
        ) : (
          <View style={styles.logoPlaceholder}>
            <Text style={styles.textPlaceholder}>{id}</Text>
          </View>
        )}
        <View style={styles.itemContent}>
          <Text style={styles.tenNhaBH} numberOfLines={2} ellipsizeMode="tail">
            {ten}
          </Text>
          <TouchableOpacity onPress={() => {}}>
            <Text style={styles.textChiTiet}>Xem chi tiết</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const nhaBaoHiemList = useMemo(
    () =>
      (danhSachNhaBaoHiemOTo || []).map((it: any) => ({
        id: it.ma,
        uriLogo: it.logo ? (it.logo.startsWith('http') ? it.logo : `${CONFIG_SERVER.BASE_URL_API}${it.logo}`) : null,
        ten: it.ma,
      })),
    [danhSachNhaBaoHiemOTo],
  );

  const loaiXeOptions = useMemo(
    () =>
      (danhSachLoaiXeOTo || []).map((item: any) => ({
        id: item.ma,
        title: item.ten,
        onPress: () => setValue('loai_xe', item.ten, {shouldValidate: true, shouldTouch: true, shouldDirty: true}),
      })),
    [danhSachLoaiXeOTo, setValue],
  );

  const selectedLoaiXeId = useMemo(() => {
    const found = (danhSachLoaiXeOTo || []).find((x: any) => x.ten === loaiXeValue || x.ma === loaiXeValue);
    return found?.ma;
  }, [danhSachLoaiXeOTo, loaiXeValue]);

  const mucBaoVeOptions = useMemo(
    () =>
      (danhSachMucBaoVeOTo || []).map((item: any) => ({
        id: item.so_tien,
        title: item.ten,
        onPress: () => setValue('muc_tn_lphu_xe', item.so_tien, {shouldValidate: true, shouldTouch: true, shouldDirty: true}),
      })),
    [danhSachMucBaoVeOTo, setValue],
  );

  const selectedMucBaoVeId = useMemo(() => {
    const found = (danhSachMucBaoVeOTo || []).find((x: any) => x.so_tien === mucBaoVeValue);
    return found?.so_tien;
  }, [danhSachMucBaoVeOTo, mucBaoVeValue]);

  const mucBaoVeDisplayValue = useMemo(() => {
    const found = (danhSachMucBaoVeOTo || []).find((x: any) => x.so_tien === mucBaoVeValue);
    return found?.ten || '';
  }, [danhSachMucBaoVeOTo, mucBaoVeValue]);

  const handleLoaiXe = () => setShowLoaiXeModal(true);
  const handleMucBaoVe = () => setShowMucBaoVeModal(true);

  const onSubmit = async (data: FormProps) => {
    try {
      const loaiXeItem = findItemByMaOrTen(danhSachLoaiXeOTo || [], data.loai_xe);
      const maLoaiXe = loaiXeItem?.ma || data.loai_xe;
      const doiTacQLItem = findItemByMaOrTen(danhSachNhaBaoHiemOTo || [], data.nha_bao_hiem);
      const maDoiTacQL = doiTacQLItem?.ma || data.nha_bao_hiem;
      const params = {
        ...(isCreateMore || isEdit ? thongTinDon : {}),
        so_cmt_kh: isCreateMore || isEdit ? thongTinDon?.cmt_kh : null,
        dchi_kh: isCreateMore || isEdit ? thongTinDon?.dia_chi_kh : null,
        so_id: isCreateMore || isEdit ? thongTinDon.so_id : 0,
        so_id_dt: isEdit ? thongTinXe.so_id_dt : 0,
        loai_chu_xe: data.loai_chu_xe,
        ma_doi_tac_ql: maDoiTacQL,
        ten_chu_xe: data.ten_chu_xe,
        so_cmt: data.so_cmt,
        mst: data.mst,
        dia_chi: data.dia_chi,
        dthoai: data.dthoai,
        chu_xe_la_nguoi_mua: data.chu_xe_la_nguoi_mua,
        loai_xe: maLoaiXe,
        md_sd: data.md_sd,
        so_cho: data.so_cho || 0,
        trong_tai: data.trong_tai || 0,
        bien_xe: data.bien_xe,
        so_khung: data.so_khung,
        so_may: data.so_may,
        muc_tn_lphu_xe: data.muc_tn_lphu_xe,
        ngay_hl: toYMD(data.ngay_hl),
        ngay_kt: toYMD(data.ngay_kt),
        actionCode: ACTION_CODE.LUU_DOI_TUONG_O_TO,
      };
      console.log('🚀 ~ onSubmit ~ params: ', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ onSubmit ~ response: ', response);

      if (response?.data) {
        toast.success('Lưu thông tin xe tham gia thành công', {
          duration: 1000,
        });
        if (isCreateMore || isEdit) {
          setTimeout(() => {
            navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_O_TO, {
              so_id: thongTinDon?.so_id,
            });
          }, 1020);
        } else {
          setTimeout(() => {
            navigation.popTo(MAIN_SCREENS.THONG_TIN_MUA_O_TO, {
              thongTinXe: {
                ...data,
                ...response.output,
                ngay_hl: toYMD(data.ngay_hl),
                ngay_kt: toYMD(data.ngay_kt),
                loai_xe: maLoaiXe,
                ma_doi_tac_ql: maDoiTacQL,
                so_cmt: data.so_cmt,
              },
            });
          }, 1020);
        }
      } else {
        toast.error('Lưu thông tin xe tham gia thất bại');
      }
    } catch (error) {
      console.log('🚀 ~ onSubmit ~ error: ', error);
    }
  };

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin xe tham gia"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        <View>
          <View style={styles.contentFooter}>
            {rendeFooter('Phí TNDS', formatCurrency(phiTNDS ?? 0))}
            {rendeFooter('Phí bảo hiểm người ngồi trên xe', formatCurrency(phiLphuXxe ?? 0))}
            {rendeFooter('Tổng phí bảo hiểm', formatCurrency(phiBH ?? 0), styles.tongPhi)}
          </View>
          <View style={styles.footerButton}>
            {mode === 'create_more' && <Button style={styles.secondButton} onPress={() => setShowConfirmModal(true)} title={'Đơn bảo hiểm'} variant={'outline'} />}
            <Button loading={isSubmitting} title={isEdit ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} style={styles.button} />
          </View>
        </View>
      }>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.sm}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          <Card title="Thời hạn bảo hiểm">
            <Controller
              control={control}
              name="so_nam"
              rules={ThongTinXeFormValidation.soNam as any}
              render={({field: {value, onChange}}) => (
                <Radio.Group required orientation="horizontal" radioContainerStyle={styles.radio} value={value} onChange={onChange} error={errors?.so_nam?.message}>
                  <Radio label="1 năm" value={1} />
                  <Radio label="2 năm" value={2} />
                  <Radio label="3 năm" value={3} />
                </Radio.Group>
              )}
            />
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <DateTimePickerComponent
                  required
                  control={control}
                  name="ngay_hl"
                  label="Ngày bắt đầu"
                  placeholder={'Chọn ngày bắt đầu'}
                  containerStyle={styles.input}
                  rules={ThongTinXeFormValidation.ngayBatDau}
                  error={errors?.ngay_hl?.message}
                />
                <DateTimePickerComponent
                  required
                  disabled
                  control={control}
                  name="ngay_kt"
                  label="Ngày kết thúc"
                  placeholder={'DD/MM/YYYY'}
                  containerStyle={styles.input}
                  error={errors?.ngay_kt?.message}
                />
              </View>
            </View>
          </Card>

          <Card title="Thông tin xe" style={styles.card}>
            <Controller
              control={control}
              name={'md_sd'}
              render={({field: {value, onChange}}) => (
                <Radio.Group label={'Mục đích sử dụng'} orientation="horizontal" radioContainerStyle={styles.radio} value={value} onChange={onChange} error={errors?.md_sd?.message}>
                  <Radio value={'C'} label={'Kinh doanh'} />
                  <Radio value={'K'} label={'Không kinh doanh'} />
                </Radio.Group>
              )}
            />

            <View style={styles.formContainer}>
              <TouchableOpacity onPress={handleLoaiXe}>
                <View pointerEvents="none">
                  <TextField
                    control={control}
                    name={'loai_xe'}
                    required
                    editable={false}
                    label={'Loại xe'}
                    placeholder={'Chọn loại xe'}
                    rightIconType={'dropdown'}
                    inputContainerStyle={errors.loai_xe ? {borderColor: colors.danger} : undefined}
                    labelStyle={errors.loai_xe ? {color: colors.danger} : undefined}
                    rules={ThongTinXeFormValidation.loaiXe as any}
                    error={errors?.loai_xe?.message}
                  />
                </View>
              </TouchableOpacity>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name={'so_cho'}
                  rules={ThongTinXeFormValidation.soCho as any}
                  render={({field: {onChange, value, ...rest}}) => (
                    <TextField
                      required
                      label={'Số chỗ ngồi'}
                      placeholder={'Nhập số chỗ ngồi'}
                      keyboardType="numeric"
                      containerStyle={styles.input}
                      value={value ? String(value) : ''}
                      onChangeText={(text: string) => {
                        const parsed = text === '' ? 0 : parseInt(text, 10);
                        onChange(isNaN(parsed) ? 0 : parsed);
                      }}
                      error={errors?.so_cho?.message}
                    />
                  )}
                />
                <Controller
                  control={control}
                  name={'trong_tai'}
                  rules={ThongTinXeFormValidation.trongTai as any}
                  render={({field: {onChange, value, ...rest}}) => (
                    <TextField
                      {...rest}
                      required
                      label={'Trọng tải (tấn)'}
                      placeholder={'Nhập trọng tải'}
                      keyboardType="decimal-pad"
                      containerStyle={styles.input}
                      value={value ? String(value) : ''}
                      onChangeText={(text: string) => {
                        const parsed = text === '' ? 0 : parseFloat(text);
                        onChange(isNaN(parsed) ? 0 : parsed);
                      }}
                      error={errors?.trong_tai?.message}
                    />
                  )}
                />
              </View>

              <TextField
                control={control}
                name={'bien_xe'}
                required
                label={'Biển số xe'}
                placeholder={'Nhập biển số xe'}
                autoCapitalize="characters"
                rules={ThongTinXeFormValidation.bienSoXe as any}
                error={errors?.bien_xe?.message}
              />

              <TextField
                control={control}
                name={'so_khung'}
                required
                label={'Số khung'}
                placeholder={'Nhập số khung xe'}
                autoCapitalize="characters"
                rules={ThongTinXeFormValidation.soKhung as any}
                error={errors?.so_khung?.message}
              />
              <TextField
                control={control}
                name={'so_may'}
                required
                label={'Số máy'}
                placeholder={'Nhập số máy xe'}
                autoCapitalize="characters"
                rules={ThongTinXeFormValidation.soMay as any}
                error={errors?.so_may?.message}
              />
            </View>
          </Card>

          <Card title={'Thông tin chủ xe'} style={styles.card}>
            <Controller
              control={control}
              name={'loai_chu_xe'}
              rules={ThongTinXeFormValidation.loaiChuXe as any}
              render={({field: {value, onChange}}) => (
                <Radio.Group value={value} onChange={onChange} label={'Chủ xe là cá nhân hay tổ chức?'} orientation="horizontal" radioContainerStyle={styles.radio}>
                  <Radio label={'Cá nhân'} value={'C'} />
                  <Radio label={'Tổ chức'} value={'T'} />
                </Radio.Group>
              )}
            />
            {loaiChuXe && (
              <View style={styles.formContainer}>
                {loaiChuXe === 'T' ? (
                  <>
                    <TextField
                      control={control}
                      name={'ten_chu_xe'}
                      required
                      label={'Tên tổ chức'}
                      placeholder={'Nhập tên tổ chức'}
                      rules={ThongTinXeFormValidation.tenChuXe as any}
                      error={errors?.ten_chu_xe?.message}
                    />
                    <TextField
                      key={'mst'}
                      control={control}
                      name={'mst'}
                      required
                      label={'Mã số thuế'}
                      placeholder={'Nhập mã số thuế'}
                      rules={ThongTinXeFormValidation.mst as any}
                      error={errors?.mst?.message}
                    />
                    <TextField
                      control={control}
                      name={'dia_chi'}
                      required
                      label={'Địa chỉ'}
                      placeholder={'Nhập địa chỉ'}
                      rules={ThongTinXeFormValidation.diaChi as any}
                      error={errors?.dia_chi?.message}
                    />
                    <TextField
                      control={control}
                      name={'dthoai'}
                      required
                      label={'Số điện thoại'}
                      placeholder={'Nhập số điện thoại'}
                      rules={ThongTinXeFormValidation.dthoai as any}
                      error={errors?.dthoai?.message}
                    />
                  </>
                ) : (
                  <>
                    <TextField
                      control={control}
                      name={'ten_chu_xe'}
                      required
                      label={'Tên chủ xe'}
                      placeholder={'Nhập tên chủ xe'}
                      rules={ThongTinXeFormValidation.tenChuXe as any}
                      error={errors?.ten_chu_xe?.message}
                    />
                    <TextField
                      key={'so_cmt'}
                      control={control}
                      name={'so_cmt'}
                      required
                      label={'CCCD'}
                      placeholder={'Nhập số CCCD'}
                      rules={ThongTinXeFormValidation.cmt as any}
                      error={errors?.so_cmt?.message}
                    />
                    <TextField
                      control={control}
                      name={'dia_chi'}
                      required
                      label={'Địa chỉ'}
                      placeholder={'Nhập địa chỉ'}
                      rules={ThongTinXeFormValidation.diaChi as any}
                      error={errors?.dia_chi?.message}
                    />
                    <TextField
                      control={control}
                      name={'dthoai'}
                      required
                      label={'SĐT'}
                      placeholder={'Nhập số điện thoại'}
                      rules={ThongTinXeFormValidation.dthoai as any}
                      error={errors?.dthoai?.message}
                    />
                  </>
                )}
                <View style={styles.cardHeader}>
                  <View style={styles.headerLabel}>
                    <Text style={styles.headerTitle}>Chủ xe có phải khách hàng không?</Text>
                  </View>
                  <Controller
                    control={control}
                    name="chu_xe_la_nguoi_mua"
                    render={({field: {value, onChange}}) => (
                      <Switch value={value === 'C'} onValueChange={newValue => onChange(newValue ? 'C' : 'K')} trackColor={{false: colors.gray[300], true: colors.green}} />
                    )}
                  />
                </View>
              </View>
            )}
          </Card>

          <Card title={'Tăng cường bảo hiểm'} style={styles.card}>
            <View style={styles.cardHeader}>
              <View style={styles.headerLabel}>
                <Text style={styles.headerTitle}>
                  Bạn có muốn bảo vệ cho lái xe và người ngồi trên xe? <Icon name={'InfoCircle'} size={16} color={colors.info} />
                </Text>
              </View>
              <Controller
                control={control}
                name="tang_cuong"
                render={({field: {value, onChange}}) => <Switch value={!!value} onValueChange={onChange} trackColor={{false: colors.gray[300], true: colors.green}} />}
              />
            </View>
            {tangCuong && (
              <TouchableOpacity onPress={handleMucBaoVe}>
                <View pointerEvents="none">
                  <Controller
                    control={control}
                    name={'muc_tn_lphu_xe'}
                    rules={ThongTinXeFormValidation.ghTienNam as any}
                    render={({field: {value}}) => (
                      <TextField
                        value={mucBaoVeDisplayValue}
                        onChangeText={() => {}}
                        required
                        editable={false}
                        label={'Mức bảo vệ'}
                        placeholder={'Chọn mức bảo vệ'}
                        rightIconType={'dropdown'}
                        inputContainerStyle={errors.muc_tn_lphu_xe ? {borderColor: colors.danger} : undefined}
                        labelStyle={errors.muc_tn_lphu_xe ? {color: colors.danger} : undefined}
                        error={errors?.muc_tn_lphu_xe?.message}
                      />
                    )}
                  />
                </View>
              </TouchableOpacity>
            )}
          </Card>
          <Card title={'Nhà bảo hiểm'}>
            <Controller
              control={control}
              name={'nha_bao_hiem'}
              rules={ThongTinXeFormValidation.nhaBaoHiem as any}
              render={({field: {value, onChange}}) => (
                <FlatList
                  data={nhaBaoHiemList}
                  numColumns={2}
                  scrollEnabled={false}
                  keyExtractor={item => item.id}
                  ItemSeparatorComponent={() => <View style={{height: spacing.sm}} />}
                  columnWrapperStyle={styles.nhaBHContainer}
                  contentContainerStyle={{paddingBottom: 4}}
                  renderItem={({item}) => (
                    <View style={styles.nhaBHItem}>
                      {renderNhaBaoHiemItem({
                        id: item.id,
                        uriLogo: item.uriLogo,
                        ten: item.ten,
                        selected: value === item.id,
                        onPress: () => onChange(item.id),
                      })}
                    </View>
                  )}
                />
              )}
            />
            {errors.nha_bao_hiem && <Text style={styles.error}>{errors.nha_bao_hiem.message}</Text>}
          </Card>
        </ScrollView>
        <ConfirmModal
          visible={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          title={'Thông báo'}
          message={'Bạn có quay trở lại đơn bảo hiểm?'}
          cancelText={'Không'}
          confirmText={'Đồng ý'}
          onConfirm={() => navigation.goBack()}
        />
        <ActionSheetModal
          isVisible={showLoaiXeModal}
          onClose={() => setShowLoaiXeModal(false)}
          title="Chọn loại xe"
          subtitle="Chọn một loại xe phù hợp"
          options={loaiXeOptions}
          selectedValue={selectedLoaiXeId}
          showSearchField
          searchPlaceholder="Tìm theo tên hoặc mã..."
        />
        <ActionSheetModal
          isVisible={showMucBaoVeModal}
          onClose={() => setShowMucBaoVeModal(false)}
          title="Chọn mức bảo vệ"
          subtitle="Chọn mức bảo vệ phù hợp"
          options={mucBaoVeOptions}
          selectedValue={selectedMucBaoVeId}
          showSearchField
          searchPlaceholder="Tìm theo tên hoặc mã..."
        />
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
