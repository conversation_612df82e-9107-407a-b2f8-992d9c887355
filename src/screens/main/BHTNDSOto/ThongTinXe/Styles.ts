import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.sm,
    backgroundColor: colors.light,
  },
  formContainer: {
    marginTop: spacing.md,
  },
  inputContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  input: {
    flex: 1,
  },
  radio: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  card: {
    paddingBottom: 4,
    marginBottom: spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  headerLabel: {
    flexDirection: 'row',
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.bold,
    flex: 1,
  },
  iconStyle: {
    marginLeft: spacing.md,
  },
  itemContainer: {
    flexDirection: 'row',
    padding: spacing.sm + 3,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.lg,
    gap: spacing.sm,
  },
  selectedItem: {
    borderWidth: 1,
    borderColor: colors.green,
    backgroundColor: '#edf8eb',
  },
  logoContainer: {
    width: 48,
    height: 48,
    padding: spacing.sm,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  logoPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.gray[300],
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioGroup: {
    marginBottom: spacing.md,
  },
  textPlaceholder: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  logoItem: {
    width: 32,
    height: 32,
    resizeMode: 'contain',
  },
  itemContent: {
    flex: 1,
    gap: spacing.sm,
    justifyContent: 'center',
  },
  tenNhaBH: {
    color: colors.dark,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    lineHeight: 20,
  },
  textChiTiet: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
  },
  nhaBHContainer: {
    flexDirection: 'row',
    alignItems: 'stretch',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  nhaBHItem: {
    flex: 1,
    maxWidth: '49%',
    minHeight: 80,
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
  },
  footerLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
  },
  footerValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
  },
  contentFooter: {
    marginBottom: spacing.md,
    gap: spacing.sm + 2,
  },
  tongPhi: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
  },
  error: {
    color: colors.danger,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    flex: 1.1,
    marginLeft: 4,
  },
  secondButton: {
    flex: 1,
    marginRight: 4,
  },
  secondTextButton: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
});
