// Helper functions for ThongTinXeOToScreen
export const parseDate = (val: any): Date | string => {
  if (val instanceof Date) return val;
  if (typeof val === 'number') {
    const str = val.toString().padStart(8, '0');
    if (str.length === 8) {
      const year = parseInt(str.substring(0, 4), 10);
      const month = parseInt(str.substring(4, 6), 10) - 1;
      const day = parseInt(str.substring(6, 8), 10);
      return new Date(year, month, day);
    }
  }
  if (typeof val === 'string') {
    const parts = val.split('/');
    if (parts.length === 3) {
      const [day, month, year] = parts.map(Number);
      return new Date(year, month - 1, day);
    }
  }
  return '';
};

export const parseSoCho = (val: any): number => {
  if (typeof val === 'number') return val;
  if (typeof val === 'string') {
    const parsed = parseInt(val, 10);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

export const parseTrongTai = (val: any): number => {
  if (typeof val === 'number') return val;
  if (typeof val === 'string') {
    const parsed = parseFloat(val);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

export const toYMD = (val: any): number => {
  if (!val) return 0;
  if (val instanceof Date) {
    const y = val.getFullYear();
    const m = String(val.getMonth() + 1).padStart(2, '0');
    const d = String(val.getDate()).padStart(2, '0');
    return parseInt(`${y}${m}${d}`);
  }
  if (typeof val === 'string') {
    if (/^\d{8}$/.test(val)) return parseInt(val);
    const [dd, mm, yyyy] = val.split('/');
    if (dd && mm && yyyy) return parseInt(`${yyyy}${mm.padStart(2, '0')}${dd.padStart(2, '0')}`);
  }
  return 0;
};

export const findItemByMaOrTen = (list: any[], value: string) => {
  return list.find((x: any) => x.ma === value || x.ten === value);
};

