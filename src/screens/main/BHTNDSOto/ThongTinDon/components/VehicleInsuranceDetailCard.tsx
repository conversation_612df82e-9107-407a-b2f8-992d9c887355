import React, {useEffect, useRef} from 'react';
import {Animated, Platform, StyleSheet, Text, TouchableOpacity, UIManager, View} from 'react-native';
import Icon from '@components/common/Icon';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';
import {InfoRow} from '@screens/main/BHSK/ThongTinDonBH/components/InfoRow';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface ChiTietGoiBH {
  danh_sach_quyen_loi: Array<{label: string; value: string}>;
  phi_chinh?: string;
  phi_bo_sung?: string;
  tong_phi?: string;
}

interface ThongTinXeVaChuXe {
  // Thông tin chủ xe
  ten_chu_xe: string;
  so_cmt: string;
  dthoai: string;
  dia_chi: string;
  loai_chu_xe: string;
  mst: string;
  // Thông tin xe
  loai_xe: string;
  ten_loai_xe: string;
  bien_xe: string;
  so_khung: string;
  so_may: string;
  // Thông tin bổ sung cho ô tô
  md_sd: string;
  so_cho: number;
  trong_tai: number;
  hang_xe?: string;
  hieu_xe?: string;
}

interface VehicleInsuranceDetailCardProps {
  data: ChiTietGoiBH;
  vehicleAndOwner?: ThongTinXeVaChuXe;
  visible: boolean;
  onHide: () => void;
}

export const VehicleInsuranceDetailCard: React.FC<VehicleInsuranceDetailCardProps> = ({data: chiTietGoiBH, vehicleAndOwner: thongTinXeVaChuXe, visible, onHide}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-20)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const [shouldRender, setShouldRender] = React.useState(visible);

  useEffect(() => {
    if (visible) {
      setShouldRender(true);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -20,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(({finished}) => {
        if (finished) {
          setShouldRender(false);
        }
      });
    }
  }, [visible, fadeAnim, slideAnim, scaleAnim]);

  if (!shouldRender) return null;

  return (
    <Animated.View
      style={[
        {
          opacity: fadeAnim,
          transform: [{translateY: slideAnim}, {scale: scaleAnim}],
        },
      ]}>
      <View style={styles.card}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Thông tin chi tiết gói bảo hiểm</Text>
          <TouchableOpacity onPress={onHide} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Icon name="ArrowUp2" size={20} color={colors.green} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {/* Benefits */}
          <View style={styles.benefitsSection}>
            {chiTietGoiBH.danh_sach_quyen_loi.map((quyen_loi: any, index: number) => (
              <InfoRow key={index} label={quyen_loi.label} value={quyen_loi.value} />
            ))}
          </View>
        </View>

        {/* Thông tin xe được bảo hiểm */}
        {thongTinXeVaChuXe && (
          <>
            <View style={styles.divider} />
            <View style={styles.content}>
              <Text style={styles.sectionTitle}>Thông tin xe được bảo hiểm</Text>
              <InfoRow label="Mục đich sử dụng" value={thongTinXeVaChuXe.md_sd} />
              <InfoRow label="Loại xe" value={thongTinXeVaChuXe.ten_loai_xe} />
              <InfoRow label="Số chỗ ngồi" value={`${thongTinXeVaChuXe.so_cho} chỗ`} />
              <InfoRow label="Trọng tải (tấn)" value={`${thongTinXeVaChuXe.trong_tai} tấn`} />
              <InfoRow label="Biển xe" value={thongTinXeVaChuXe.bien_xe} />
              <InfoRow label="Số khung" value={thongTinXeVaChuXe.so_khung} />
              <InfoRow label="Số máy" value={thongTinXeVaChuXe.so_may} />
              {/* Thông tin bổ sung cho ô tô */}
            </View>
          </>
        )}

        {/* Thông tin chủ xe */}
        {thongTinXeVaChuXe && (
          <>
            <View style={styles.divider} />
            <View style={styles.content}>
              <Text style={styles.sectionTitle}>Thông tin chủ xe</Text>
              {thongTinXeVaChuXe.loai_chu_xe === 'T' ? (
                <View>
                  <InfoRow label="Tên tổ chức" value={thongTinXeVaChuXe.ten_chu_xe} />
                  <InfoRow label="Mã số thuế" value={thongTinXeVaChuXe.mst} />
                </View>
              ) : (
                <View>
                  <InfoRow label="Họ và tên" value={thongTinXeVaChuXe.ten_chu_xe} />
                  <InfoRow label="Số giấy tờ tùy thân" value={thongTinXeVaChuXe.so_cmt} />
                </View>
              )}
              <InfoRow label="Số điện thoại" value={thongTinXeVaChuXe.dthoai} />
              <InfoRow label="Địa chỉ" value={thongTinXeVaChuXe.dia_chi} />
            </View>
          </>
        )}

        {/* Hide Button */}
        <TouchableOpacity style={styles.hideButton} onPress={onHide}>
          <Icon name="ArrowUp2" size={16} color={colors.green} />
          <Text style={styles.hideButtonText}>Ẩn chi tiết</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  animatedContainer: {
    marginHorizontal: spacing.sm,
    marginVertical: spacing.sm,
    borderRadius: borderRadius.lg,
    ...shadows.base,
    backgroundColor: colors.gray[400], // Border color
    padding: 1, // Border width
  },
  card: {
    backgroundColor: '#f6f6f6ff',
    marginHorizontal: spacing.sm,
    padding: spacing.md,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginVertical: spacing.sm,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  content: {
    gap: spacing.sm,
  },
  benefitsSection: {
    gap: spacing.xs,
  },
  hideButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.xs,
    marginTop: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  hideButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginVertical: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
    marginBottom: spacing.sm,
  },
});
