import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  scrollContent: {
    padding: spacing.sm,
    paddingBottom: spacing.lg,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginTop: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  sectionHeader: {
    marginTop: spacing.md,
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
    marginBottom: spacing.sm,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.green + '10',
    borderRadius: borderRadius.lg + 2,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
  emptyBuyerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  emptyBuyerText: {
    fontSize: typography.fontSize.sm,
    color: colors.danger,
    fontFamily: typography.fontFamily.regular,
  },
  disabledText: {
    color: colors.gray[400],
    fontFamily: typography.fontFamily.regular,
  },
  emptyCard: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  footer: {
    flexDirection: 'row',
    marginTop: spacing.sm,
  },
  secondButton: {
    flex: 1,
    marginRight: 4,
  },
  secondTextButton: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
  noteContainer: {
    flexDirection: 'row',
    borderRadius: borderRadius.xl,
    marginHorizontal: spacing.sm,
    padding: spacing.sm,
    paddingBottom: spacing.md - 4,
    borderColor: colors.gray[400],
    borderWidth: 1,
  },
  noteText: {
    flex: 1,
    fontSize: 15,
    color: colors.gray[700],
    lineHeight: 24,
    marginLeft: -12,
    marginTop: 3,
    fontFamily: typography.fontFamily.regular,
  },
  noteLink: {
    color: colors.primary,
  },
  thanhToanButton: {
    flex: 1.1,
    marginLeft: 4,
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
  },
  footerLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
  },
  footerValue: {
    fontSize: typography.fontSize.xl,
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
  },
});
