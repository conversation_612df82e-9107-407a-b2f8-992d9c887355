import {<PERSON>ton, Card, Checkbox, ConfirmModal, createToastHelpers, Icon, IOSAlert, ScreenComponent, useIOSAlert, useToast} from '@components/common';
import {colors, typography} from '@constants/theme';
import {ExpandableCard} from '@screens/main/BHSK/ThongTinDonBH/components/ExpandableCard';
import {InfoRow} from '@screens/main/BHSK/ThongTinDonBH/components/InfoRow';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, FlatList, ScrollView, Text, TextStyle, TouchableOpacity, View} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {VehicleInsuranceDetailCard} from './components/VehicleInsuranceDetailCard';
import {styles} from './Styles';
import {MAIN_SCREENS} from '@navigation/routes';
import NavigationUtil from '@navigation/NavigationUtil';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import {formatCurrency, formatDateFromAPI, formatGender} from '@utils/formatters';

interface ItemProps {
  ma_doi_tac_ql: string;
  nv: string;
  so_id: number;
  // Bên mua
  ten_kh: string;
  cmt_kh: string;
  mst_kh: string;
  gioi_tinh_kh: string;
  dia_chi_kh: string;
  dthoai_kh: string;
  email_kh: string;
  loai_kh: string;
  // Đơn bảo hiểm
  ngay_hl: string;
  ngay_kt: string;
  phi_bh: number;
  ten_doi_tac_ql: string;
  phi_tnds: number;
  phi_lphu_xe: number;
  hieu_luc: number;
  // Thông tin xe
  bien_xe: string;
  loai_xe: string;
  ten_loai_xe: string;
  so_khung: string;
  so_may: string;
  // Thông tin bổ sung cho ô tô
  md_sd: string;
  so_cho: number;
  trong_tai: number;
  // Thông tin chủ xe
  ten_chu_xe: string;
  so_cmt: string;
  dthoai: string;
  dia_chi: string;
  loai_chu_xe: string;
  mst: string;
  // Phí
  tong_phi: number;
  ma_doi_tac: string;
  so_id_dt: number;
}

export default function ThongTinDonOToScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [showDetailCards, setShowDetailCards] = useState<Set<number>>(new Set());
  const [data, setData] = useState<ItemProps>({} as ItemProps);
  const [danhSachXe, setDanhSachXe] = useState<ItemProps[]>([]);
  console.log('🚀 ~ ThongTinDonOToScreen ~ danhSachXe:', danhSachXe);
  const [isLoading, setIsLoading] = useState(false);
  console.log('🚀 ~ ThongTinDonOToScreen ~ danhSachXe: ', danhSachXe);
  console.log('🚀 ~ ThongTinDonOToScreen ~ data: ', data);
  const [showModalDelete, setShowModalDelete] = useState(false);
  const [xeCanXoa, setXeCanXoa] = useState<ItemProps | null>(null);
  console.log('🚀 ~ ThongTinDonOToScreen ~ xeCanXoa: ', xeCanXoa);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {so_id} = route?.params;
  console.log('🚀 ~ ThongTinDonOToScreen ~ so_id: ', so_id);
  const alert = useIOSAlert();

  const getThongTinDon = async () => {
    try {
      setIsLoading(true);
      const params = {
        so_id: so_id,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_THONG_TIN_DON_O_TO,
      };
      console.log('🚀 ~ getThongTinDon ~ params: ', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ getThongTinDon ~ response: ', response);
      if (response?.data) {
        setData(response?.data);
        const validXe = (response?.data?.dsxe || []).filter((xe: ItemProps) => xe.so_khung);
        setDanhSachXe(validXe);
      } else {
        toast.error('Lỗi tải thông tin đơn bảo hiểm');
      }
    } catch (error) {
      console.log('🚀 ~ getThongTinDon ~ error: ', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getThongTinDon();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      if (!isInitialMount && needsRefresh) {
        console.log('useFocusEffect: Loading latest data...');
        getThongTinDon();
        setNeedsRefresh(false);
      }
    }, [isInitialMount, needsRefresh]),
  );

  useEffect(() => {
    if (danhSachXe.length > 0 || data.cmt_kh || data.mst_kh) {
      if (isInitialMount) {
        setIsInitialMount(false);
      }
    }
  }, [danhSachXe, data, isInitialMount]);

  // Sửa người mua
  const handleEditNguoiMua = () => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_MUA_O_TO, {
      mode: 'edit',
      thongTinDon: data,
    });
  };

  // Sửa thông tin xe
  const handleEditXe = (thongTinXe: ItemProps) => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_XE_O_TO, {
      mode: 'edit',
      thongTinXe: thongTinXe,
      thongTinDon: data,
    });
  };

  const handleDeleteXe = (thongTinXe: ItemProps) => {
    setXeCanXoa(thongTinXe);
    setShowModalDelete(true);
  };

  const handleConfirmDelete = async () => {
    if (!xeCanXoa) return;

    try {
      const params = {
        so_id: so_id,
        so_id_dt: xeCanXoa.so_id_dt,
        actionCode: ACTION_CODE.XOA_DOI_TUONG_O_TO,
      };
      console.log('🚀 ~ handleConfirmDelete ~ params: ', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ handleConfirmDelete ~ response: ', response);

      if (response?.data) {
        toast.success('Xóa xe thành công');
        await getThongTinDon();
      } else {
        toast.error(response?.message || 'Có lỗi xảy ra khi xóa xe');
      }

      setShowModalDelete(false);
      setXeCanXoa(null);
    } catch (error) {
      console.log('🚀 ~ handleConfirmDelete ~ error: ', error);
      toast.error('Có lỗi xảy ra khi xóa xe');
    }
  };

  const handlePayment = async () => {
    try {
      if (!agreedToTerms) {
        Alert.alert('Vui lòng đồng ý với điều khoản để tiếp tục');
        return;
      }
      const params = {
        so_id: so_id,
        actionCode: ACTION_CODE.INIT_THANH_TOAN,
      };
      const response = await getCommonExecute(params);
      console.log('🚀 ~ handlePayment ~ response:', response);
      if (response?.data) {
        NavigationUtil.push(MAIN_SCREENS.THANH_TOAN, {
          nv: data?.nv,
          soId: data?.so_id,
        });
      }
    } catch (error) {
      console.log('🚀 ~ handlePayment ~ error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleMuaThem = () => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_XE_O_TO, {
      mode: 'create_more',
      thongTinDon: {
        ...data,
      },
    });
  };

  const toggleDetailCard = useCallback((index: number) => {
    setShowDetailCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  const hideDetailCard = useCallback((index: number) => {
    setShowDetailCards(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });
  }, []);

  const rendeFooter = (label: string, value: string | number, textStyle?: TextStyle) => {
    return (
      <View style={styles.footerContainer}>
        <Text style={[styles.footerLabel, textStyle]}>{label}</Text>
        <Text style={[styles.footerValue, textStyle]}>{value}</Text>
      </View>
    );
  };

  const renderDonBaoHiemCard = useCallback(
    (thongTinXe: ItemProps, index: number) => {
      const chiTietGoiBH = {
        danh_sach_quyen_loi: [
          {label: 'Công ty bảo hiểm', value: data?.ma_doi_tac_ql},
          {label: 'Phí bảo hiểm TNDS', value: formatCurrency(thongTinXe?.phi_tnds ?? 0)},
          {label: 'Bảo hiểm Người ngồi trên xe', value: formatCurrency(thongTinXe?.phi_lphu_xe ?? 0)},
          {label: 'Ngày bắt đầu', value: formatDateFromAPI(thongTinXe?.ngay_hl)},
          {label: 'Ngày kết thúc', value: formatDateFromAPI(thongTinXe?.ngay_kt)},
        ],
        phi_chinh: formatCurrency(thongTinXe?.phi_tnds ?? 0),
        phi_bo_sung: formatCurrency(thongTinXe?.phi_lphu_xe ?? 0),
        tong_phi: formatCurrency(thongTinXe?.phi_bh ?? 0),
      };

      const thongTinXeVaChuXe = {
        loai_chu_xe: thongTinXe?.loai_chu_xe,
        mst: thongTinXe?.mst,
        ten_chu_xe: thongTinXe?.ten_chu_xe,
        so_cmt: thongTinXe?.so_cmt,
        dthoai: thongTinXe?.dthoai,
        dia_chi: thongTinXe?.dia_chi,
        loai_xe: thongTinXe?.loai_xe,
        ten_loai_xe: thongTinXe?.ten_loai_xe,
        bien_xe: thongTinXe?.bien_xe,
        so_khung: thongTinXe?.so_khung,
        so_may: thongTinXe?.so_may,
        // Thông tin bổ sung cho ô tô
        md_sd: thongTinXe?.md_sd,
        so_cho: thongTinXe?.so_cho,
        trong_tai: thongTinXe?.trong_tai,
      };

      const isDetailVisible = showDetailCards.has(index);

      return (
        <View key={`vehicle-${index}-${thongTinXe?.so_khung}`}>
          <ExpandableCard
            defaultExpanded={true}
            showViewDetails
            showEdit
            showDelete
            onViewDetails={() => toggleDetailCard(index)}
            onEdit={() => handleEditXe(thongTinXe)}
            onDelete={() => handleDeleteXe(thongTinXe)}>
            <InfoRow label="Biển số xe" value={thongTinXe?.bien_xe} />
            <InfoRow label="Tên chủ xe" value={thongTinXe?.ten_chu_xe} />
            <InfoRow label="Số khung" value={thongTinXe?.so_khung} />
            <InfoRow label="Ngày bắt đầu" value={formatDateFromAPI(thongTinXe?.ngay_hl)} />
            <InfoRow label="Ngày kết thúc" value={formatDateFromAPI(thongTinXe?.ngay_kt)} />
            <InfoRow label="Phí gói bảo hiểm" value={formatCurrency(thongTinXe?.phi_bh ?? 0)} />
          </ExpandableCard>

          <VehicleInsuranceDetailCard data={chiTietGoiBH} vehicleAndOwner={thongTinXeVaChuXe} visible={isDetailVisible} onHide={() => hideDetailCard(index)} />
        </View>
      );
    },
    [data?.ma_doi_tac_ql, handleDeleteXe, handleEditXe, hideDetailCard, showDetailCards, toggleDetailCard],
  );

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin đơn bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      dialogLoading={isLoading}
      showFooter
      footer={
        <View>
          {rendeFooter('Tổng phí', formatCurrency(data?.tong_phi) ?? 0)}
          <View style={styles.footer}>
            <Button title={'Mua thêm'} style={styles.secondButton} onPress={handleMuaThem} variant={'outline'} disabled={isSubmitting} />
            <Button title="Thanh toán" onPress={handlePayment} disabled={!agreedToTerms} style={styles.thanhToanButton} loading={isSubmitting} />
          </View>
        </View>
      }>
      <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Thông tin bên mua bảo hiểm */}
        <View style={styles.titleContainer}>
          <Icon name="Profile" color={colors.green} size={20} />
          <Text style={styles.title}>Thông tin bên mua bảo hiểm</Text>
        </View>
        {data?.cmt_kh || data?.mst_kh ? (
          <ExpandableCard defaultExpanded showEdit onEdit={handleEditNguoiMua}>
            {data?.loai_kh === 'C' ? (
              <View>
                <InfoRow label="Họ và tên" value={data?.ten_kh} />
                <InfoRow label="Số giấy tờ tùy thân" value={data?.cmt_kh} />
                <InfoRow label="Giới tính" value={formatGender(data?.gioi_tinh_kh)} />
                <InfoRow label="Địa chỉ" value={data?.dia_chi_kh} />
                <InfoRow label="Số điện thoại" value={data?.dthoai_kh} />
                <InfoRow label="Email" value={data?.email_kh} />
              </View>
            ) : (
              <View>
                <InfoRow label="Tên tổ chức" value={data?.ten_kh} />
                <InfoRow label="Mã số thuế" value={data?.mst_kh} />
                <InfoRow label="Địa chỉ" value={data?.dia_chi_kh} />
                <InfoRow label="Số điện thoại" value={data?.dthoai_kh} />
                <InfoRow label="Email" value={data?.email_kh} />
              </View>
            )}
          </ExpandableCard>
        ) : (
          <Card>
            <Text style={{color: colors.danger, fontFamily: typography.fontFamily.regular}}>Chưa có thông tin bên mua bảo hiểm</Text>
            <TouchableOpacity style={styles.actionButton} onPress={handleEditNguoiMua}>
              <Icon name="Edit2" size={16} color={colors.green} />
              <Text style={styles.actionText}>Sửa</Text>
            </TouchableOpacity>
          </Card>
        )}

        {/* Thông tin đơn bảo hiểm */}
        <View style={styles.titleContainer}>
          <Icon name="DocumentText" color={colors.green} size={20} />
          <Text style={styles.title}>Thông tin đơn bảo hiểm</Text>
        </View>
        {danhSachXe.length > 0 ? (
          <FlatList
            data={danhSachXe}
            keyExtractor={(item, index) => `${item.so_khung}-${index}`}
            renderItem={({item, index}) => renderDonBaoHiemCard(item, index)}
            scrollEnabled={false}
            contentContainerStyle={{paddingBottom: 0}}
          />
        ) : (
          <Card>
            <Text style={styles.emptyText}>Chưa có thông tin đơn bảo hiểm</Text>
          </Card>
        )}
        <Card style={styles.noteContainer}>
          <TouchableOpacity onPress={() => (agreedToTerms ? setAgreedToTerms(true) : setAgreedToTerms(false))}>
            <Checkbox value={agreedToTerms} onValueChange={setAgreedToTerms} />
          </TouchableOpacity>
          <Text style={styles.noteText}>
            Bên mua bảo hiểm/người được bảo hiểm cam kết các thông tin khai báo là chính xác, trung thực và hoàn toàn chịu trách nhiệm về các thông tin đã khai báo. Đồng thời tôi đã đọc, hiểu và đồng
            ý với{' '}
            <Text style={styles.noteLink} onPress={() => console.log('View terms')}>
              điều kiện, điều khoản, quy tắc của Sàn Bảo Hiểm.
            </Text>
          </Text>
        </Card>
      </ScrollView>

      <ConfirmModal
        visible={showModalDelete}
        onClose={() => setShowModalDelete(false)}
        type="danger"
        title="Thông báo"
        message={`Bạn có chắc chắn muốn xóa xe ${xeCanXoa?.bien_xe || ''} không?`}
        onConfirm={handleConfirmDelete}
        cancelText="Không"
      />
      <IOSAlert visible={alert.visible} title={alert.title} message={alert.message} buttons={alert.buttons} onClose={alert.hide} />
    </ScreenComponent>
  );
}
