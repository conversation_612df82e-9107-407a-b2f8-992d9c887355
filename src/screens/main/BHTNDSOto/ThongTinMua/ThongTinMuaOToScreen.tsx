import {Image, KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {<PERSON>ton, Card, ConfirmModal, createToastHelpers, DateTimePickerComponent, Icon, Radio, ScreenComponent, TextField, useToast} from '@components/common';
import {styles} from './Styles';
import {colors, spacing} from '@constants/theme';
import R from '@assets/R';
import {Controller, useForm} from 'react-hook-form';
import {ThongTinMuaFormValidation} from '@utils/validationSchemas';
import {MAIN_SCREENS} from '@navigation/routes';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import {parseDateFromNumber} from '@utils/formatters';

type TabProp = 'C' | 'T';

interface FormProps {
  ten_kh: string;
  gioi_tinh_kh: string;
  ngay_sinh_kh: Date | string;
  cmt_kh: string;
  mst_kh: string;
  dia_chi_kh: string;
  dthoai_kh: string;
  email_kh: string;
  loai_kh: string;
}

function getDefaultValues(data?: Partial<FormProps>, thongTinXe?: any): FormProps {
  let ngaySinhDate: Date | string = '';
  if (data?.ngay_sinh_kh) {
    if (data.ngay_sinh_kh instanceof Date) {
      ngaySinhDate = data.ngay_sinh_kh;
    } else if (typeof data.ngay_sinh_kh === 'number' || typeof data.ngay_sinh_kh === 'string') {
      const parsed = parseDateFromNumber(data.ngay_sinh_kh);
      ngaySinhDate = parsed || '';
    }
  }

  // Autofill từ thông tin chủ xe nếu chủ xe là người mua (mode create)
  const shouldAutofill = thongTinXe?.chu_xe_la_nguoi_mua === 'C' && !data?.cmt_kh && !data?.mst_kh;

  return {
    ten_kh: data?.ten_kh ?? (shouldAutofill ? thongTinXe?.ten_chu_xe : ''),
    gioi_tinh_kh: data?.gioi_tinh_kh ?? 'NAM',
    ngay_sinh_kh: ngaySinhDate,
    cmt_kh: data?.cmt_kh ?? (shouldAutofill ? thongTinXe?.so_cmt : ''),
    mst_kh: data?.mst_kh ?? (shouldAutofill ? thongTinXe?.mst : ''),
    dia_chi_kh: data?.dia_chi_kh ?? (shouldAutofill ? thongTinXe?.dia_chi : ''),
    dthoai_kh: data?.dthoai_kh ?? (shouldAutofill ? thongTinXe?.dthoai : ''),
    email_kh: data?.email_kh ?? '',
    loai_kh: data?.loai_kh ?? (shouldAutofill ? thongTinXe?.loai_chu_xe : 'C'),
  };
}
export default function ThongTinMuaOToScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [tab, setTab] = useState<TabProp>('C');
  const [isFrontScannerVisible, setFrontScannerVisible] = useState(false);
  const [isBackScannerVisible, setBackScannerVisible] = useState(false);
  const [frontIdImage, setFrontIdImage] = useState<string | null>();
  const [backIdImage, setBackIdImage] = useState<string | null>();
  const [showModal, setShowModal] = useState(false);
  const [chiTietDon, setChiTietDon] = useState([]);

  const {thongTinXe, thongTinDon, mode} = route?.params || {};
  const isEdit = mode === 'edit';
  logger.log('🚀 ~ ThongTinMuaOToScreen ~ mode: ', mode);
  logger.log('🚀 ~ ThongTinMuaOToScreen ~ thongTinDon: ', thongTinDon);
  logger.log('🚀 ~ ThongTinMuaOToScreen ~ thongTinXe: ', thongTinXe);

  const defaultValues = useMemo(() => getDefaultValues(thongTinDon, thongTinXe), [thongTinDon, thongTinXe]);

  const {
    control,
    watch,
    reset,
    setValue,
    getValues,
    formState: {errors, isSubmitting},
    handleSubmit,
  } = useForm<FormProps>({
    mode: 'onChange',
    defaultValues,
  });

  // Sync tab state với loai_kh từ data khi edit hoặc autofill từ chủ xe
  useEffect(() => {
    if (thongTinDon?.loai_kh && (thongTinDon.loai_kh === 'C' || thongTinDon.loai_kh === 'T')) {
      setTab(thongTinDon.loai_kh as TabProp);
    } else if (thongTinXe?.chu_xe_la_nguoi_mua === 'C' && thongTinXe?.loai_chu_xe && (thongTinXe.loai_chu_xe === 'C' || thongTinXe.loai_chu_xe === 'T')) {
      setTab(thongTinXe.loai_chu_xe as TabProp);
    }
  }, [thongTinDon?.loai_kh, thongTinXe?.chu_xe_la_nguoi_mua, thongTinXe?.loai_chu_xe]);

  const toYMD = useCallback((val: any): number => {
    if (!val) return 0;
    if (val instanceof Date) {
      const y = val.getFullYear();
      const m = String(val.getMonth() + 1).padStart(2, '0');
      const d = String(val.getDate()).padStart(2, '0');
      return parseInt(`${y}${m}${d}`);
    }
    if (typeof val === 'string') {
      if (/^\d{8}$/.test(val)) return parseInt(val);
      const [dd, mm, yyyy] = val.split('/');
      if (dd && mm && yyyy) return parseInt(`${yyyy}${mm.padStart(2, '0')}${dd.padStart(2, '0')}`);
    }
    return 0;
  }, []);

  // render Top tab
  const renderTopTab = ({icon, title, selected, onPress}: any) => {
    return (
      <TouchableOpacity onPress={onPress} style={[styles.tabContainer, selected && styles.tabSelected]}>
        <View style={styles.tabWrap}>
          <Icon name={icon} size={20} color={selected ? colors.white : colors.green} variant={selected ? 'Bold' : 'Linear'} />
          <Text style={[styles.title, selected && styles.titleSelected]}>{title}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Chụp mặt trước
  const handlePickFrontImage = useCallback(() => {
    setFrontScannerVisible(true);
  }, []);

  // Chụp mặt sau
  const handlePickBackImage = useCallback(() => {
    setBackScannerVisible(true);
  }, []);

  // render cmt
  const renderSinglePhotoUpload = useCallback(() => {
    return (
      <TouchableOpacity onPress={handlePickFrontImage} style={{flexDirection: 'row', flex: 1, alignItems: 'center', gap: spacing.sm}} activeOpacity={0.7}>
        <Image source={frontIdImage ? {uri: frontIdImage} : R.images.img_cccd} style={styles.idCardIcon} resizeMode="contain" />
        <Text style={styles.idCardLabel}>Chụp hoặc tải ảnh căn cước công dân (không bắt buộc)</Text>
      </TouchableOpacity>
    );
  }, [frontIdImage, handlePickFrontImage]);

  const handleShowModal = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const onSubmit = async (data: FormProps) => {
    try {
      const params = {
        ...(isEdit ? {...thongTinDon} : {...thongTinXe}),
        so_id: isEdit ? thongTinDon?.so_id : thongTinXe.so_id,
        ten_kh: data.ten_kh,
        gioi_tinh_kh: data.gioi_tinh_kh,
        ngay_sinh_kh: toYMD(data.ngay_sinh_kh),
        so_cmt_kh: data.cmt_kh,
        mst_kh: data.mst_kh,
        loai_kh: tab,
        dchi_kh: data.dia_chi_kh,
        dthoai_kh: data.dthoai_kh,
        email_kh: data.email_kh,
        actionCode: ACTION_CODE.LUU_DOI_TUONG_O_TO,
      };
      logger.log('🚀 ~ onSubmit ~ params: ', params);
      const response = await getCommonExecute(params);
      logger.log('🚀 ~ onSubmit ~ response: ', response);
      if (response?.data) {
        toast.success('Lưu thông tin bên mua thành công', {
          duration: 1000,
        });
        if (isEdit) {
          setTimeout(() => {
            navigation.goBack();
          }, 1020);
        } else {
          setTimeout(() => {
            handleShowModal();
          }, 1020);
        }
      } else {
        toast.error('Lưu thông tin bên mua thất bại');
      }
    } catch (error) {
      logger.log('🚀 ~ onSubmit ~ error: ', error);
    }
  };

  const handleConfirmModal = async () => {
    handleCloseModal();
    try {
      const params = {
        so_id: thongTinXe?.so_id,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_THONG_TIN_DON_O_TO,
      };
      logger.log('🚀 ~ handleConfirmModal ~ params:', params);
      const response = await getCommonExecute(params);
      logger.log('🚀 ~ handleConfirmModal ~ response:', response);
      if (response?.data) {
        setChiTietDon(response?.data);
        navigation.popTo(MAIN_SCREENS.THONG_TIN_XE_O_TO, {
          mode: 'create_more',
          thongTinDon: response?.data,
        });
      } else {
        logger.log(response?.error);
      }
    } catch (error) {
      logger.log('🚀 ~ handleConfirmModal ~ error:', error);
    }
  };

  const handleCancelModal = () => {
    handleCloseModal();
    navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_O_TO, {
      so_id: thongTinXe?.so_id,
    });
  };

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin bên mua"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title={isEdit ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} loading={isSubmitting} />}>
      {/* Tab */}
      <View style={styles.tabRow}>
        {renderTopTab({
          title: 'Cá nhân',
          icon: 'Profile',
          selected: tab === 'C',
          onPress: () => setTab('C'),
        })}
        {renderTopTab({
          title: 'Tổ chức',
          icon: 'Building',
          selected: tab === 'T',
          onPress: () => setTab('T'),
        })}
      </View>

      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.lg}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          {tab === 'C' ? (
            <Card title={'Thông tin bên mua'} style={styles.card}>
              {/* Chụp/Tải ảnh giấy tờ tùy thân */}
              {renderSinglePhotoUpload()}

              {/* Form */}
              <View style={styles.form}>
                {/* Tên */}
                <TextField name="ten_kh" control={control} required label="Họ và tên" placeholder="Nhập họ và tên" rules={ThongTinMuaFormValidation.tenKH as any} error={errors?.ten_kh?.message} />
                {/* Giới tính */}
                <Controller
                  control={control}
                  name="gioi_tinh_kh"
                  rules={ThongTinMuaFormValidation.gioiTinhKH as any}
                  render={({field: {value, onChange}}) => (
                    <Radio.Group label="Giới tính" required value={value} onChange={onChange} orientation="horizontal" containerStyle={styles.gioiTinhSection} error={errors?.gioi_tinh_kh?.message}>
                      <Radio.Button label="Nam" value="NAM" />
                      <Radio.Button label="Nữ" value="NU" />
                    </Radio.Group>
                  )}
                />
                {/* Ngày sinh */}
                <DateTimePickerComponent
                  control={control}
                  name="ngay_sinh_kh"
                  label="Ngày sinh"
                  placeholder="Chọn ngày sinh"
                  required
                  rules={ThongTinMuaFormValidation.ngaySinhKH}
                  error={errors?.ngay_sinh_kh?.message}
                />
                {/* Số CCCD */}
                <TextField control={control} name="cmt_kh" required label="Số CCCD" placeholder="Nhập CCCD" rules={ThongTinMuaFormValidation.cmtKH as any} error={errors?.cmt_kh?.message} />
                {/* Địa chỉ */}
                <TextField
                  control={control}
                  name="dia_chi_kh"
                  required
                  label="Địa chỉ"
                  placeholder="Nhập địa chỉ"
                  rules={ThongTinMuaFormValidation.diaChiKH as any}
                  error={errors?.dia_chi_kh?.message}
                />
                {/* Số điện thoại */}
                <TextField
                  control={control}
                  name="dthoai_kh"
                  required
                  label="SĐT"
                  placeholder="Nhập số điện thoại"
                  rules={ThongTinMuaFormValidation.dthoaiKH as any}
                  error={errors?.dthoai_kh?.message}
                />
                {/* Email */}
                <TextField control={control} name="email_kh" label="Email" placeholder="Nhập email" rules={ThongTinMuaFormValidation.emailKH as any} error={errors?.email_kh?.message} />
              </View>
            </Card>
          ) : (
            <View>
              <Card title={'Thông tin bên mua'} style={styles.card}>
                {/* Tên tổ chức */}
                <TextField name="ten_kh" control={control} required label="Tên tổ chức" placeholder="Nhập tên tổ chức" rules={ThongTinMuaFormValidation.tenKH as any} error={errors?.ten_kh?.message} />
                {/* Mã số thuế */}
                <TextField name="mst_kh" control={control} required label="Mã số thuế" placeholder="Nhập mã số thuế" error={errors?.ten_kh?.message} />
                {/* Địa chỉ */}
                <TextField
                  control={control}
                  name="dia_chi_kh"
                  required
                  label="Địa chỉ"
                  placeholder="Nhập địa chỉ"
                  rules={ThongTinMuaFormValidation.diaChiKH as any}
                  error={errors?.dia_chi_kh?.message}
                />
                {/* Số điện thoại */}
                <TextField
                  control={control}
                  name="dthoai_kh"
                  required
                  label="SĐT"
                  placeholder="Nhập số điện thoại"
                  rules={ThongTinMuaFormValidation.dthoaiKH as any}
                  error={errors?.dthoai_kh?.message}
                />
                {/* Email */}
                <TextField control={control} name="email_kh" label="Email" placeholder="Nhập email" rules={ThongTinMuaFormValidation.emailKH as any} error={errors?.email_kh?.message} />
              </Card>
            </View>
          )}
        </ScrollView>

        <ConfirmModal
          visible={showModal}
          title="Thông báo"
          message="Bạn có muốn mua thêm cho xe khác không?"
          confirmText="Đồng ý"
          cancelText="Không"
          onClose={handleCloseModal}
          onCancel={handleCancelModal}
          onConfirm={handleConfirmModal}
        />
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
