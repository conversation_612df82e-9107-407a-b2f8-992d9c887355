import {Button, Card, Checkbox, ConfirmModal, createToastHelpers, Icon, IOSAlert, ScreenComponent, useIOSAlert, useToast} from '@components/common';
import {colors, typography} from '@constants/theme';
import {ExpandableCard} from '@screens/main/BHSK/ThongTinDonBH/components/ExpandableCard';
import {InfoRow} from '@screens/main/BHSK/ThongTinDonBH/components/InfoRow';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, FlatList, ScrollView, Text, TextStyle, TouchableOpacity, View} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {VehicleInsuranceDetailCard} from '@screens/main/BHTNDSOto/ThongTinDon/components/VehicleInsuranceDetailCard';
import {styles} from './Styles';
import {MAIN_SCREENS} from '@navigation/routes';
import NavigationUtil from '@navigation/NavigationUtil';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import {formatCurrency, formatDateFromAPI, formatGender} from '@utils/formatters';

interface ItemProps {
  ma_doi_tac_ql: string;
  nv: string;
  so_id: number;
  // Bên mua
  ten_kh: string;
  cmt_kh: string;
  mst_kh: string;
  gioi_tinh_kh: string;
  dia_chi_kh: string;
  dthoai_kh: string;
  email_kh: string;
  loai_kh: string;
  // Đơn bảo hiểm
  ngay_hl: string;
  ngay_kt: string;
  phi_bh: number;
  ten_doi_tac_ql: string;
  phi_tnds: number;
  phi_lphu_xe: number;
  hieu_luc: number;
  // Thông tin xe
  bien_xe: string;
  loai_xe: string;
  ten_loai_xe: string;
  so_khung: string;
  so_may: string;
  hang_xe: string;
  hieu_xe: string;
  // Thông tin bổ sung cho ô tô
  md_sd: string;
  so_cho: number;
  trong_tai: number;
  // Thông tin chủ xe
  ten_chu_xe: string;
  so_cmt: string;
  dthoai: string;
  dia_chi: string;
  loai_chu_xe: string;
  mst: string;
  // Phí
  tong_phi: number;
  ma_doi_tac: string;
  so_id_dt: number;
}

export default function ThongTinDonVCXOtoScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [showDetailCards, setShowDetailCards] = useState<Set<number>>(new Set());
  const [data, setData] = useState<ItemProps>({} as ItemProps);
  const [danhSachXe, setDanhSachXe] = useState<ItemProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showModalDelete, setShowModalDelete] = useState(false);
  const [xeCanXoa, setXeCanXoa] = useState<ItemProps | null>(null);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {so_id} = route?.params || {};
  const alert = useIOSAlert();

  const getThongTinDon = async () => {
    try {
      setIsLoading(true);

      // Mock data for development
      if (__DEV__ && !so_id) {
        const mockData = {
          so_id: 12345,
          nv: 'VCX001',
          ma_doi_tac_ql: 'Bảo Việt',
          ten_doi_tac_ql: 'Tổng Công ty Bảo Việt Nhân thọ',
          // Thông tin bên mua
          ten_kh: 'Nguyễn Văn A',
          cmt_kh: '001234567890',
          mst_kh: '',
          gioi_tinh_kh: 'NAM',
          dia_chi_kh: '123 Đường ABC, Quận 1, TP.HCM',
          dthoai_kh: '0901234567',
          email_kh: '<EMAIL>',
          loai_kh: 'C',
          tong_phi: 15000000,
          dsxe: [
            {
              so_id: 12345,
              so_id_dt: 1,
              ma_doi_tac_ql: 'Bảo Việt',
              // Thông tin xe
              bien_xe: '30A-12345',
              hang_xe: 'Toyota',
              hieu_xe: 'Vios',
              loai_xe: 'XE_CON',
              ten_loai_xe: 'Xe con',
              so_khung: 'VNKKK123456789012',
              so_may: 'ABC123456',
              md_sd: 'Kinh doanh vận tải',
              so_cho: 5,
              trong_tai: 2,
              // Thông tin chủ xe
              ten_chu_xe: 'Nguyễn Văn A',
              so_cmt: '001234567890',
              dthoai: '0901234567',
              dia_chi: '123 Đường ABC, Quận 1, TP.HCM',
              loai_chu_xe: 'C',
              mst: '',
              // Thông tin đơn
              ngay_hl: '20241117',
              ngay_kt: '20251117',
              phi_bh: 12000000,
              phi_tnds: 9000000,
              phi_lphu_xe: 3000000,
              hieu_luc: 1,
            },
            {
              so_id: 12345,
              so_id_dt: 2,
              ma_doi_tac_ql: 'Bảo Việt',
              // Thông tin xe
              bien_xe: '51G-98765',
              hang_xe: 'Honda',
              hieu_xe: 'City',
              loai_xe: 'XE_CON',
              ten_loai_xe: 'Xe con',
              so_khung: 'VNHHH987654321098',
              so_may: 'XYZ987654',
              md_sd: 'Cá nhân',
              so_cho: 5,
              trong_tai: 1.5,
              // Thông tin chủ xe
              ten_chu_xe: 'Trần Thị B',
              so_cmt: '009876543210',
              dthoai: '0912345678',
              dia_chi: '456 Đường XYZ, Quận 3, TP.HCM',
              loai_chu_xe: 'C',
              mst: '',
              // Thông tin đơn
              ngay_hl: '20241117',
              ngay_kt: '20251117',
              phi_bh: 8000000,
              phi_tnds: 6000000,
              phi_lphu_xe: 2000000,
              hieu_luc: 1,
            },
          ],
        };

        setTimeout(() => {
          setData(mockData as any);
          const validXe = (mockData.dsxe || []).filter((xe: any) => xe.so_khung);
          setDanhSachXe(validXe as any);
          setIsLoading(false);
        }, 500);
        return;
      }

      const params = {
        so_id: so_id,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_THONG_TIN_DON_VCX_O_TO,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        setData(response?.data);
        const validXe = (response?.data?.dsxe || []).filter((xe: ItemProps) => xe.so_khung);
        setDanhSachXe(validXe);
      } else {
        toast.error('Lỗi tải thông tin đơn bảo hiểm');
      }
    } catch (error) {
      logger.log('🚀 ~ getThongTinDon ~ error: ', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getThongTinDon();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      if (!isInitialMount && needsRefresh) {
        getThongTinDon();
        setNeedsRefresh(false);
      }
    }, [isInitialMount, needsRefresh]),
  );

  useEffect(() => {
    if (danhSachXe.length > 0 || data.cmt_kh || data.mst_kh) {
      if (isInitialMount) {
        setIsInitialMount(false);
      }
    }
  }, [danhSachXe, data, isInitialMount]);

  // Sửa người mua
  const handleEditNguoiMua = () => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_MUA_VCX_O_TO, {
      mode: 'edit',
      thongTinGoiBaoHiem: data,
      editData: data,
    });
  };

  // Sửa thông tin xe
  const handleEditXe = (thongTinXe: ItemProps) => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_XE_VCX_O_TO, {
      mode: 'edit',
      thongTinDonBaoHiem: thongTinXe,
      editData: thongTinXe,
    });
  };

  const handleDeleteXe = (thongTinXe: ItemProps) => {
    setXeCanXoa(thongTinXe);
    setShowModalDelete(true);
  };

  const handleConfirmDelete = async () => {
    if (!xeCanXoa) {
      return;
    }

    try {
      const params = {
        so_id: so_id,
        so_id_dt: xeCanXoa.so_id_dt,
        actionCode: ACTION_CODE.XOA_DOI_TUONG_VCX_O_TO,
      };
      const response = await getCommonExecute(params);

      if (response?.data) {
        toast.success('Xóa xe thành công');
        await getThongTinDon();
      } else {
        toast.error(response?.message || 'Có lỗi xảy ra khi xóa xe');
      }

      setShowModalDelete(false);
      setXeCanXoa(null);
    } catch (error) {
      logger.log('🚀 ~ handleConfirmDelete ~ error: ', error);
      toast.error('Có lỗi xảy ra khi xóa xe');
    }
  };

  const handlePayment = async () => {
    try {
      if (!agreedToTerms) {
        Alert.alert('Vui lòng đồng ý với điều khoản để tiếp tục');
        return;
      }
      const params = {
        so_id: so_id,
        actionCode: ACTION_CODE.INIT_THANH_TOAN,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        NavigationUtil.push(MAIN_SCREENS.THANH_TOAN, {
          nv: data?.nv,
          soId: data?.so_id,
        });
      } else {
        toast.error(response?.error);
      }
    } catch (error) {
      logger.log('🚀 ~ handlePayment ~ error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleMuaThem = () => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_XE_VCX_O_TO, {
      mode: 'create_more',
      thongTinDonBaoHiem: {
        ...data,
      },
      editData: null,
    });
  };

  const toggleDetailCard = useCallback((index: number) => {
    setShowDetailCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  const hideDetailCard = useCallback((index: number) => {
    setShowDetailCards(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });
  }, []);

  const rendeFooter = (label: string, value: string | number, textStyle?: TextStyle) => {
    return (
      <View style={styles.footerContainer}>
        <Text style={[styles.footerLabel, textStyle]}>{label}</Text>
        <Text style={[styles.footerValue, textStyle]}>{value}</Text>
      </View>
    );
  };

  const renderDonBaoHiemCard = useCallback(
    (thongTinXe: ItemProps, index: number) => {
      const chiTietGoiBH = {
        danh_sach_quyen_loi: [
          {label: 'Công ty bảo hiểm', value: data?.ma_doi_tac_ql},
          {label: 'Phí bảo hiểm TNDS', value: formatCurrency(thongTinXe?.phi_tnds ?? 0)},
          {label: 'Bảo hiểm Người ngồi trên xe', value: formatCurrency(thongTinXe?.phi_lphu_xe ?? 0)},
          {label: 'Ngày bắt đầu', value: formatDateFromAPI(thongTinXe?.ngay_hl)},
          {label: 'Ngày kết thúc', value: formatDateFromAPI(thongTinXe?.ngay_kt)},
        ],
        phi_chinh: formatCurrency(thongTinXe?.phi_tnds ?? 0),
        phi_bo_sung: formatCurrency(thongTinXe?.phi_lphu_xe ?? 0),
        tong_phi: formatCurrency(thongTinXe?.phi_bh ?? 0),
      };

      const thongTinXeVaChuXe = {
        loai_chu_xe: thongTinXe?.loai_chu_xe,
        mst: thongTinXe?.mst,
        ten_chu_xe: thongTinXe?.ten_chu_xe,
        so_cmt: thongTinXe?.so_cmt,
        dthoai: thongTinXe?.dthoai,
        dia_chi: thongTinXe?.dia_chi,
        loai_xe: thongTinXe?.loai_xe,
        ten_loai_xe: thongTinXe?.ten_loai_xe,
        bien_xe: thongTinXe?.bien_xe,
        so_khung: thongTinXe?.so_khung,
        so_may: thongTinXe?.so_may,
        hang_xe: thongTinXe?.hang_xe,
        hieu_xe: thongTinXe?.hieu_xe,
        // Thông tin bổ sung cho ô tô
        md_sd: thongTinXe?.md_sd,
        so_cho: thongTinXe?.so_cho,
        trong_tai: thongTinXe?.trong_tai,
      };

      const isDetailVisible = showDetailCards.has(index);

      return (
        <View key={`vehicle-${index}-${thongTinXe?.so_khung}`}>
          <ExpandableCard
            defaultExpanded={true}
            showViewDetails
            showEdit
            showDelete
            onViewDetails={() => toggleDetailCard(index)}
            onEdit={() => handleEditXe(thongTinXe)}
            onDelete={() => handleDeleteXe(thongTinXe)}>
            <InfoRow label="Biển số xe" value={thongTinXe?.bien_xe} />
            <InfoRow label="Tên chủ xe" value={thongTinXe?.ten_chu_xe} />
            <InfoRow label="Ngày bắt đầu" value={formatDateFromAPI(thongTinXe?.ngay_hl)} />
            <InfoRow label="Ngày kết thúc" value={formatDateFromAPI(thongTinXe?.ngay_kt)} />
            <InfoRow label="Phí gói bảo hiểm" value={formatCurrency(thongTinXe?.phi_bh ?? 0)} />
          </ExpandableCard>

          <VehicleInsuranceDetailCard data={chiTietGoiBH} vehicleAndOwner={thongTinXeVaChuXe} visible={isDetailVisible} onHide={() => hideDetailCard(index)} />
        </View>
      );
    },
    [data?.ma_doi_tac_ql, handleDeleteXe, handleEditXe, hideDetailCard, showDetailCards, toggleDetailCard],
  );

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin đơn bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      dialogLoading={isLoading}
      showFooter
      footer={
        <View>
          {rendeFooter('Tổng phí', formatCurrency(data?.tong_phi || 0))}
          <View style={styles.footer}>
            <Button title={'Mua thêm'} style={styles.secondButton} onPress={handleMuaThem} variant={'outline'} disabled={isSubmitting} />
            <Button title="Thanh toán" onPress={handlePayment} disabled={!agreedToTerms} style={styles.thanhToanButton} loading={isSubmitting} />
          </View>
        </View>
      }>
      <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Thông tin bên mua bảo hiểm */}
        <View style={styles.titleContainer}>
          <Icon name="Profile" color={colors.green} size={20} />
          <Text style={styles.title}>Thông tin bên mua bảo hiểm</Text>
        </View>
        {data?.cmt_kh || data?.mst_kh ? (
          <ExpandableCard defaultExpanded showEdit onEdit={handleEditNguoiMua}>
            {data?.loai_kh === 'C' ? (
              <View>
                <InfoRow label="Họ và tên" value={data?.ten_kh} />
                <InfoRow label="Số giấy tờ tùy thân" value={data?.cmt_kh} />
                <InfoRow label="Giới tính" value={formatGender(data?.gioi_tinh_kh)} />
                <InfoRow label="Địa chỉ" value={data?.dia_chi_kh} />
                <InfoRow label="Số điện thoại" value={data?.dthoai_kh} />
                <InfoRow label="Email" value={data?.email_kh} />
              </View>
            ) : (
              <View>
                <InfoRow label="Tên tổ chức" value={data?.ten_kh} />
                <InfoRow label="Mã số thuế" value={data?.mst_kh} />
                <InfoRow label="Địa chỉ" value={data?.dia_chi_kh} />
                <InfoRow label="Số điện thoại" value={data?.dthoai_kh} />
                <InfoRow label="Email" value={data?.email_kh} />
              </View>
            )}
          </ExpandableCard>
        ) : (
          <Card>
            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
              <Text style={{color: colors.danger, fontFamily: typography.fontFamily.regular, flex: 1}}>Chưa có thông tin bên mua bảo hiểm</Text>
              <TouchableOpacity style={styles.actionButton} onPress={handleEditNguoiMua}>
                <Icon name="Edit2" size={16} color={colors.green} />
                <Text style={styles.actionText}>Sửa</Text>
              </TouchableOpacity>
            </View>
          </Card>
        )}

        {/* Thông tin đơn bảo hiểm */}
        <View style={styles.titleContainer}>
          <Icon name="DocumentText" color={colors.green} size={20} />
          <Text style={styles.title}>Thông tin xe tham gia</Text>
        </View>
        {danhSachXe.length > 0 ? (
          <FlatList
            data={danhSachXe}
            keyExtractor={(item, index) => `${item.so_khung}-${index}`}
            renderItem={({item, index}) => renderDonBaoHiemCard(item, index)}
            scrollEnabled={false}
            contentContainerStyle={{paddingBottom: 0}}
          />
        ) : (
          <Card>
            <Text style={styles.emptyText}>Chưa có thông tin đơn bảo hiểm</Text>
          </Card>
        )}
        <Card style={styles.noteContainer}>
          <TouchableOpacity onPress={() => (agreedToTerms ? setAgreedToTerms(true) : setAgreedToTerms(false))}>
            <Checkbox value={agreedToTerms} onValueChange={setAgreedToTerms} />
          </TouchableOpacity>
          <Text style={styles.noteText}>
            Bên mua bảo hiểm/người được bảo hiểm cam kết các thông tin khai báo là chính xác, trung thực và hoàn toàn chịu trách nhiệm về các thông tin đã khai báo. Đồng thời tôi đã đọc, hiểu và đồng
            ý với{' '}
            <Text style={styles.noteLink} onPress={() => logger.log('View terms')}>
              điều kiện, điều khoản, quy tắc của Sàn Bảo Hiểm.
            </Text>
          </Text>
        </Card>
      </ScrollView>

      <ConfirmModal
        visible={showModalDelete}
        onClose={() => setShowModalDelete(false)}
        type="danger"
        title="Thông báo"
        message={`Bạn có chắc chắn muốn xóa xe ${xeCanXoa?.bien_xe || ''} không?`}
        onConfirm={handleConfirmDelete}
        cancelText="Không"
      />
      <IOSAlert visible={alert.visible} title={alert.title} message={alert.message} buttons={alert.buttons} onClose={alert.hide} />
    </ScreenComponent>
  );
}
