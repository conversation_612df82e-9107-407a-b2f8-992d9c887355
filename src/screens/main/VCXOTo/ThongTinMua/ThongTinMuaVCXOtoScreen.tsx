import {Image, KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {Button, Card, createToastHelpers, DateTimePickerComponent, Icon, Radio, ScreenComponent, TextField, useToast} from '@components/common';
import {styles} from './Styles';
import {colors, spacing} from '@constants/theme';
import R from '@assets/R';
import {Controller, useForm} from 'react-hook-form';
import {ThongTinMuaFormValidation} from '@utils/validationSchemas';
import {MAIN_SCREENS} from '@navigation/routes';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import {parseDateFromNumber} from '@utils/formatters';

type TabProp = 'C' | 'T';

interface FormProps {
  ten_kh: string;
  gioi_tinh_kh: string;
  ngay_sinh_kh: Date | string;
  cmt_kh: string;
  mst_kh: string;
  dia_chi_kh: string;
  dthoai_kh: string;
  email_kh: string;
  loai_kh: string;
}

function getDefaultValues(data?: Partial<FormProps>): FormProps {
  let ngaySinhDate: Date | string = '';
  if (data?.ngay_sinh_kh) {
    if (data.ngay_sinh_kh instanceof Date) {
      ngaySinhDate = data.ngay_sinh_kh;
    } else if (typeof data.ngay_sinh_kh === 'number' || typeof data.ngay_sinh_kh === 'string') {
      const parsed = parseDateFromNumber(data.ngay_sinh_kh);
      ngaySinhDate = parsed || '';
    }
  }

  return {
    ten_kh: data?.ten_kh ?? '',
    gioi_tinh_kh: data?.gioi_tinh_kh ?? 'NAM',
    ngay_sinh_kh: ngaySinhDate,
    cmt_kh: data?.cmt_kh ?? '',
    mst_kh: data?.mst_kh ?? '',
    dia_chi_kh: data?.dia_chi_kh ?? '',
    dthoai_kh: data?.dthoai_kh ?? '',
    email_kh: data?.email_kh ?? '',
    loai_kh: data?.loai_kh ?? 'C',
  };
}

export default function ThongTinMuaVCXOtoScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [tab, setTab] = useState<TabProp>('C');
  const [frontIdImage, setFrontIdImage] = useState<string | null>();

  const {thongTinGoiBaoHiem, editData, mode} = route?.params || {};
  const isEdit = mode === 'edit';

  const defaultValues = useMemo(() => getDefaultValues(editData), [editData]);

  const {
    control,
    setValue,
    formState: {errors, isSubmitting},
    handleSubmit,
  } = useForm<FormProps>({
    mode: 'onChange',
    defaultValues,
  });

  const toYMD = useCallback((val: any): number => {
    if (!val) return 0;
    if (val instanceof Date) {
      const y = val.getFullYear();
      const m = String(val.getMonth() + 1).padStart(2, '0');
      const d = String(val.getDate()).padStart(2, '0');
      return parseInt(`${y}${m}${d}`);
    }
    if (typeof val === 'string') {
      if (/^\d{8}$/.test(val)) return parseInt(val);
      const [dd, mm, yyyy] = val.split('/');
      if (dd && mm && yyyy) return parseInt(`${yyyy}${mm.padStart(2, '0')}${dd.padStart(2, '0')}`);
    }
    return 0;
  }, []);

  // render Top tab
  const renderTopTab = ({icon, title, selected, onPress}: any) => {
    return (
      <TouchableOpacity onPress={onPress} style={[styles.tabContainer, selected && styles.tabSelected]}>
        <View style={styles.tabWrap}>
          <Icon name={icon} size={20} color={selected ? colors.white : colors.green} variant={selected ? 'Bold' : 'Linear'} />
          <Text style={[styles.title, selected && styles.titleSelected]}>{title}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Chụp mặt trước
  const handlePickFrontImage = useCallback(() => {
    // TODO: Implement image picker
    logger.log('Pick front image');
  }, []);

  // render cmt
  const renderSinglePhotoUpload = useCallback(() => {
    return (
      <TouchableOpacity onPress={handlePickFrontImage} style={{flexDirection: 'row', flex: 1, alignItems: 'center', gap: spacing.sm}} activeOpacity={0.7}>
        <Image source={frontIdImage ? {uri: frontIdImage} : R.images.img_cccd} style={styles.idCardIcon} resizeMode="contain" />
        <Text style={styles.idCardLabel}>Chụp hoặc tải ảnh căn cước công dân (không bắt buộc)</Text>
      </TouchableOpacity>
    );
  }, [frontIdImage, handlePickFrontImage]);

  // Fill sample data
  const fillSampleData = useCallback(() => {
    if (tab === 'C') {
      // Cá nhân
      setValue('ten_kh', 'Nguyễn Văn A');
      setValue('gioi_tinh_kh', 'NAM');
      setValue('ngay_sinh_kh', new Date(1990, 0, 1));
      setValue('cmt_kh', '001234567890');
      setValue('dia_chi_kh', '123 Đường ABC, Quận 1, TP.HCM');
      setValue('dthoai_kh', '0901234567');
      setValue('email_kh', '<EMAIL>');
    } else {
      // Tổ chức
      setValue('ten_kh', 'Công ty TNHH ABC');
      setValue('mst_kh', '0123456789');
      setValue('dia_chi_kh', '456 Đường XYZ, Quận 2, TP.HCM');
      setValue('dthoai_kh', '0287654321');
      setValue('email_kh', '<EMAIL>');
    }
  }, [tab, setValue]);

  const onSubmit = async (data: FormProps) => {
    try {
      navigation.navigate(MAIN_SCREENS.THONG_TIN_DON_VCX_O_TO);
    } catch (error) {
      logger.log('🚀 ~ onSubmit ~ error: ', error);
    }
  };

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin bên mua"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title={isEdit ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} loading={isSubmitting} />}>
      {/* Tab */}
      <View style={styles.tabRow}>
        {renderTopTab({
          title: 'Cá nhân',
          icon: 'Profile',
          selected: tab === 'C',
          onPress: () => setTab('C'),
        })}
        {renderTopTab({
          title: 'Tổ chức',
          icon: 'Building',
          selected: tab === 'T',
          onPress: () => setTab('T'),
        })}
      </View>

      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.lg}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          {__DEV__ && <Button title="🔧 Fill Mock Data (Dev Only)" onPress={fillSampleData} variant="outline" style={{marginBottom: spacing.md}} />}

          {tab === 'C' ? (
            <Card title={'Thông tin bên mua'} style={styles.card}>
              {/* Chụp/Tải ảnh giấy tờ tùy thân */}
              {renderSinglePhotoUpload()}

              {/* Form */}
              <View style={styles.form}>
                {/* Tên */}
                <TextField name="ten_kh" control={control} required label="Họ và tên" placeholder="Nhập họ và tên" rules={ThongTinMuaFormValidation.tenKH as any} error={errors?.ten_kh?.message} />
                {/* Giới tính */}
                <Controller
                  control={control}
                  name="gioi_tinh_kh"
                  rules={ThongTinMuaFormValidation.gioiTinhKH as any}
                  render={({field: {value, onChange}}) => (
                    <Radio.Group label="Giới tính" required value={value} onChange={onChange} orientation="horizontal" containerStyle={styles.gioiTinhSection} error={errors?.gioi_tinh_kh?.message}>
                      <Radio.Button label="Nam" value="NAM" />
                      <Radio.Button label="Nữ" value="NU" />
                    </Radio.Group>
                  )}
                />
                {/* Ngày sinh */}
                <DateTimePickerComponent
                  control={control}
                  name="ngay_sinh_kh"
                  label="Ngày sinh"
                  placeholder="Chọn ngày sinh"
                  required
                  rules={ThongTinMuaFormValidation.ngaySinhKH}
                  error={errors?.ngay_sinh_kh?.message}
                />
                {/* Số CCCD */}
                <TextField control={control} name="cmt_kh" required label="Số CCCD" placeholder="Nhập CCCD" rules={ThongTinMuaFormValidation.cmtKH as any} error={errors?.cmt_kh?.message} />
                {/* Địa chỉ */}
                <TextField
                  control={control}
                  name="dia_chi_kh"
                  required
                  label="Địa chỉ"
                  placeholder="Nhập địa chỉ"
                  rules={ThongTinMuaFormValidation.diaChiKH as any}
                  error={errors?.dia_chi_kh?.message}
                />
                {/* Số điện thoại */}
                <TextField
                  control={control}
                  name="dthoai_kh"
                  required
                  label="SĐT"
                  placeholder="Nhập số điện thoại"
                  rules={ThongTinMuaFormValidation.dthoaiKH as any}
                  error={errors?.dthoai_kh?.message}
                />
                {/* Email */}
                <TextField control={control} name="email_kh" label="Email" placeholder="Nhập email" rules={ThongTinMuaFormValidation.emailKH as any} error={errors?.email_kh?.message} />
              </View>
            </Card>
          ) : (
            <View>
              <Card title={'Thông tin bên mua'} style={styles.card}>
                {/* Tên tổ chức */}
                <TextField name="ten_kh" control={control} required label="Tên tổ chức" placeholder="Nhập tên tổ chức" rules={ThongTinMuaFormValidation.tenKH as any} error={errors?.ten_kh?.message} />
                {/* Mã số thuế */}
                <TextField name="mst_kh" control={control} required label="Mã số thuế" placeholder="Nhập mã số thuế" error={errors?.mst_kh?.message} />
                {/* Địa chỉ */}
                <TextField
                  control={control}
                  name="dia_chi_kh"
                  required
                  label="Địa chỉ"
                  placeholder="Nhập địa chỉ"
                  rules={ThongTinMuaFormValidation.diaChiKH as any}
                  error={errors?.dia_chi_kh?.message}
                />
                {/* Số điện thoại */}
                <TextField
                  control={control}
                  name="dthoai_kh"
                  required
                  label="SĐT"
                  placeholder="Nhập số điện thoại"
                  rules={ThongTinMuaFormValidation.dthoaiKH as any}
                  error={errors?.dthoai_kh?.message}
                />
                {/* Email */}
                <TextField control={control} name="email_kh" label="Email" placeholder="Nhập email" rules={ThongTinMuaFormValidation.emailKH as any} error={errors?.email_kh?.message} />
              </Card>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
