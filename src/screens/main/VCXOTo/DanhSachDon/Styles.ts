import {StyleSheet} from 'react-native';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.sm,
    backgroundColor: colors.light,
  },
  input: {
    marginHorizontal: spacing.sm,
  },
  card: {
    padding: spacing.md,
  },
  cardHeader: {
    marginBottom: spacing.sm,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: spacing.sm,
  },
  label: {
    fontSize: typography.fontSize.base - 1,
    color: colors.gray[600],
    flex: 1,
    fontFamily: typography.fontFamily.regular,
  },
  value: {
    fontSize: typography.fontSize.base - 1,
    color: colors.gray[800],
    fontFamily: typography.fontFamily.medium,
    flex: 1,
    textAlign: 'right',
  },
  soHD: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  trangThai: {
    fontFamily: typography.fontFamily.semibold,
  },
  phiBH: {
    color: colors.gray[800],
    fontFamily: typography.fontFamily.semibold,
  },
  listContainer: {
    paddingBottom: spacing.xl,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    paddingTop: spacing['3xl'],
  },
  emptyText: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.gray[700],
    marginTop: spacing.md,
  },
  emptySubText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    marginTop: spacing.xs,
    textAlign: 'center',
    paddingHorizontal: spacing.md,
    lineHeight: 20,
    fontFamily: typography.fontFamily.regular,
  },
  fabButton: {
    position: 'absolute',
    right: spacing.md,
    bottom: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: borderRadius.full,
    backgroundColor: colors.green,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerLoader: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
  endDataContainer: {
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
    alignItems: 'center',
  },
  endDataTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  endDataText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },
});
