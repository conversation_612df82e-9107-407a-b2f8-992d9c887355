import {FlatList, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useFocusEffect} from '@react-navigation/native';
import {Card, createToastHelpers, CustomTouchableOpacity, Icon, Loading, ScreenComponent, TextField, useToast} from '@components/common';
import {styles} from './Styles';
import {colors} from '@constants/theme';
import {formatCurrencyPlain} from '@utils/currencyFormatter';
import {MAIN_SCREENS} from '@navigation/routes';
import {PAGE_SIZE} from '@commons/Constant';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import NavigationUtil from '@navigation/NavigationUtil';

interface ItemProps {
  so_id: number;
  so_hd: string;
  ten_kh: string;
  ten_doi_tac_ql: string;
  sl_doi_tuong: number;
  hieu_luc: string;
  tong_phi: number;
  trang_thai: string;
  trang_thai_ten: string;
  ngay_hl: number;
  ngay_kt: number;
}

interface LoadMoreState {
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

export default function DanhSachDonVCXOtoScreen({navigation}: any) {
  const [danhSachDon, setDanhSachDon] = useState<ItemProps[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);

  const [loadMoreState, setLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: PAGE_SIZE,
  });

  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const hasSearchedBefore = useRef(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [needsRefresh, setNeedsRefresh] = useState(false);

  const getDanhSachDon = useCallback(
    async (isLoadMore: boolean = false, customSearchValue?: string) => {
      try {
        let currentPage: number = 1;
        let pageSize: number = PAGE_SIZE;

        setLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize;
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        const searchTerm = customSearchValue !== undefined ? customSearchValue : searchValue;

        const params = {
          nd_tim: searchTerm || '',
          nv: 'XE',
          trang: currentPage,
          so_dong: pageSize,
          actionCode: ACTION_CODE.GET_DS_DON_BH,
        };

        logger.log('getDanhSachDon VCX params', params);
        const response = await getCommonExecute(params);
        logger.log('getDanhSachDon VCX response', response);

        const listDon: ItemProps[] = response.data.data || [];
        const totalItems = response.data.tong_so_dong || listDon.length;

        setDanhSachDon(prev => {
          let result: ItemProps[];
          if (isLoadMore) {
            result = [...prev, ...listDon];
          } else {
            result = listDon;
          }
          return result;
        });

        setLoadMoreState(prev => {
          const hasMoreItems = currentPage * pageSize < totalItems;

          return {
            ...prev,
            isLoading: false,
            isLoadingMore: false,
            currentPage: currentPage,
            totalItems: totalItems,
            hasMore: hasMoreItems,
          };
        });
      } catch (error) {
        logger.log('getDanhSachDon VCX ~ error:', error);
        toast.error('Lỗi tải danh sách đơn bảo hiểm');
        setLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      }
    },
    [toast],
  );

  // Load more handler
  const handleLoadMore = useCallback(() => {
    if (!loadMoreState.isLoadingMore && loadMoreState.hasMore && !loadMoreState.isLoading) {
      getDanhSachDon(true, searchValue);
    }
  }, [loadMoreState.isLoadingMore, loadMoreState.hasMore, loadMoreState.isLoading, searchValue, getDanhSachDon]);

  // Refresh handler
  const onRefreshData = useCallback(() => {
    setIsPullToRefresh(true);
    getDanhSachDon(false, searchValue).then(() => {
      setIsPullToRefresh(false);
    });
  }, [searchValue, getDanhSachDon]);

  // Search handler
  const handleTimKiem = useCallback(() => {
    getDanhSachDon(false, searchValue);
  }, [searchValue, getDanhSachDon]);

  // Auto search when searchValue becomes empty and has searched before
  useEffect(() => {
    if (searchValue !== '') {
      hasSearchedBefore.current = true;
      return;
    }

    if (searchValue === '' && hasSearchedBefore.current) {
      getDanhSachDon(false, '');
    }
  }, [searchValue]);

  // Initial data load
  useEffect(() => {
    getDanhSachDon(false).then(() => {
      setTimeout(() => setIsInitialMount(false), 1000);
    });
  }, []);

  // Use useFocusEffect to refresh data when returning from other screens
  useFocusEffect(
    useCallback(() => {
      if (!isInitialMount && needsRefresh) {
        getDanhSachDon(false, searchValue);
        setNeedsRefresh(false);
      }
    }, [isInitialMount, needsRefresh, searchValue]),
  );

  const renderItem = ({item}: {item: ItemProps}) => {
    const statusColor = item.trang_thai === 'D' ? colors.success : colors.warning;
    const statusText = item.trang_thai_ten;
    const renderInfoRow = (title: string, value: string, style?: object) => {
      return (
        <View style={styles.cardRow}>
          <Text style={styles.label}>{title}</Text>
          <Text style={[styles.value, style, value === 'Chưa xác định' && {color: colors.warning}]}>{value}</Text>
        </View>
      );
    };

    return (
      <CustomTouchableOpacity onPress={() => handleThongTinDon(item.so_id)} activeOpacity={1}>
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.soHD}>{item.so_hd}</Text>
          </View>
          {renderInfoRow('Công ty bảo hiểm', item.ten_doi_tac_ql)}
          {renderInfoRow('Tên khách hàng', item.ten_kh)}
          {renderInfoRow('Số lượng xe', item.sl_doi_tuong + ' xe')}
          {renderInfoRow('Hiệu lực hợp đồng', item.hieu_luc)}
          {renderInfoRow('Phí bảo hiểm', `${formatCurrencyPlain(item.tong_phi)}đ`, styles.phiBH)}
          {renderInfoRow('Trạng thái', statusText, {...styles.trangThai, color: statusColor})}
        </Card>
      </CustomTouchableOpacity>
    );
  };

  const renderEmpty = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <Icon name="DocumentText" size={64} color={colors.gray[400]} variant="Bulk" />
        <Text style={styles.emptyText}>{searchValue ? 'Không tìm thấy đơn bảo hiểm' : 'Chưa có đơn bảo hiểm nào'}</Text>
        <Text style={styles.emptySubText}>
          {searchValue ? 'Không có đơn bảo hiểm nào phù hợp với từ khóa tìm kiếm của bạn' : 'Chưa có đơn bảo hiểm nào trong hệ thống. Hãy thêm đơn bảo hiểm mới.'}
        </Text>
      </View>
    );
  }, [searchValue]);

  const renderFooter = useCallback(() => {
    if (loadMoreState.isLoadingMore) {
      return (
        <View style={styles.footerLoader}>
          <Loading size={'small'} message={'Đang tải thêm'} />
        </View>
      );
    }
    if (!loadMoreState.hasMore && danhSachDon.length > 0) {
      return (
        <View style={styles.endDataContainer}>
          <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
          <Text style={styles.endDataText}>Bạn đã xem tất cả {danhSachDon.length} đơn bảo hiểm</Text>
        </View>
      );
    }
    return null;
  }, [loadMoreState.isLoadingMore, loadMoreState.hasMore, danhSachDon.length]);

  const handleThemDonBaoHiem = () => {
    setNeedsRefresh(true);
    navigation.navigate(MAIN_SCREENS.THONG_TIN_XE_VCX_O_TO, {
      mode: 'create',
    });
  };

  const handleThongTinDon = (so_id: number) => {
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_DON_VCX_O_TO, {so_id});
  };

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Danh sách đơn bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      dialogLoading={loadMoreState.isLoading && !loadMoreState.isLoadingMore}>
      {/* TextField tìm kiếm */}
      <TextField
        placeholder="Nhập thông tin tìm kiếm..."
        value={searchValue}
        onChangeText={setSearchValue}
        showPlaceholderWhenEmpty={true}
        rightIconType={'search'}
        onRightIconPress={handleTimKiem}
        autoCorrect={false}
        autoCapitalize="none"
        containerStyle={styles.input}
        onSubmitEditing={handleTimKiem}
      />

      {/* Flatlist hiển thị danh sách đơn */}
      <FlatList
        data={danhSachDon}
        keyExtractor={(item, index) => `${item.so_id}-${index}`}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={danhSachDon.length === 0 ? styles.emptyContainer : styles.listContainer}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={10}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={isPullToRefresh && loadMoreState.isLoading && !loadMoreState.isLoadingMore} onRefresh={onRefreshData} tintColor={colors.primary} colors={[colors.primary]} />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
      />

      {/* Button thêm */}
      <TouchableOpacity style={styles.fabButton} activeOpacity={0.8} onPress={handleThemDonBaoHiem}>
        <Icon name="Add" size={32} color={colors.white} variant="Linear" />
      </TouchableOpacity>
    </ScreenComponent>
  );
}
