import {colors, spacing, typography, borderRadius} from '@constants/theme';
import {StyleSheet, Platform} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  buttonContainer: {
    marginTop: spacing.lg,
  },
  radioContainer: {
    marginBottom: spacing.md,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.sm,
  },
  input: {
    flex: 1,
  },
  textDescription: {
    color: colors.green,
    fontStyle: 'italic',
    fontFamily: Platform.OS == 'android' ? typography.fontFamily.regular_italic : undefined,
    marginTop: -spacing.sm,
    marginBottom: spacing.md,
  },
});
