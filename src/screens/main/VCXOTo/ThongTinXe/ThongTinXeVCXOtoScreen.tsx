import {KeyboardAvoidingView, Platform, ScrollView, Text, View} from 'react-native';
import React, {useCallback} from 'react';
import {Button, Card, CustomTouchableOpacity, DateTimePickerComponent, Radio, ScreenComponent, TextField} from '@components/common';
import {styles} from './Styles';
import {Controller, useForm} from 'react-hook-form';
import {ThongTinXeVCXOtoValidation} from '@utils/validationSchemas';
import {colors, spacing} from '@constants/theme';
import {toYMD} from '@utils/formatters';
import {MAIN_SCREENS} from '@navigation/routes';

interface ThongTinXeForm {
  // Thong tin xe
  md_sd: string;
  loai_xe: string;
  hang_xe: string;
  hieu_xe: string;
  nam_sx: number;
  so_cho: number;
  trong_tai: number;
  bs_xe: string;
  so_khung: string;
  so_may: string;
  // Gia tri xe
  gtri_xe: number;
  so_tien_tham_khao: number;
  gio_hl: Date | string;
  ngay_hl: Date | string;
  gio_kt: Date | string;
  ngay_kt: Date | string;
  // Thong tin chu xe
  loai_chu_xe: string;
  ten: string;
  so_cmt: string;
  mst: string;
  dia_chi: string;
  dthoai: string;
}

const defaultValues: Partial<ThongTinXeForm> = {
  md_sd: 'C',
  loai_chu_xe: 'C',
};
export default function ThongTinXeVcxOtoScreen({navigation, route}: any) {
  const {mode} = route?.params || {};
  const isEditMode = mode === 'edit';
  const isCreateMore = mode === 'create_more';
  const {
    control,
    watch,
    setValue,
    formState: {errors, isSubmitting},
    handleSubmit,
  } = useForm<ThongTinXeForm>({
    defaultValues: defaultValues,
  });

  const isDev = __DEV__;

  const fillMockData = useCallback(() => {
    const mockData: Partial<ThongTinXeForm> = {
      md_sd: 'K',
      loai_xe: 'Xe con',
      hang_xe: 'Toyota',
      hieu_xe: 'Camry',
      nam_sx: 2020,
      so_cho: 5,
      trong_tai: 1.5,
      bs_xe: '30A-12345',
      so_khung: 'JTDKB20U123456789',
      so_may: 'ABC123456',
      gtri_xe: 800000000,
      so_tien_tham_khao: 800000000,
      gio_hl: new Date(new Date().setHours(0, 0, 0, 0)),
      ngay_hl: new Date(),
      gio_kt: new Date(new Date().setHours(23, 59, 0, 0)),
      ngay_kt: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      loai_chu_xe: 'C',
      ten: 'Nguyễn Văn A',
      so_cmt: '001234567890',
      mst: '001234567893',
      dia_chi: '123 Đường ABC, Quận 1, TP.HCM',
      dthoai: '0901234567',
    };

    (Object.keys(mockData) as Array<keyof ThongTinXeForm>).forEach(key => {
      const value = mockData[key];
      if (value !== undefined) {
        // Convert number to string for text fields if needed
        const finalValue = key === 'nam_sx' || key === 'so_cho' || key === 'trong_tai' ? String(value) : value;
        setValue(key, finalValue as any, {
          shouldValidate: true,
          shouldDirty: true,
        });
      }
    });
  }, [setValue]);

  const onSubmit = useCallback(async (data: ThongTinXeForm) => {
    try {
      console.log('🚀 ~ onSubmit ~ data:', data);
      const params = {
        ...data,
        ngay_hl: toYMD(data.ngay_hl),
        ngay_kt: toYMD(data.ngay_kt),
      };
      console.log('🚀 ~ onSubmit ~ params:', params);
      navigation.navigate(MAIN_SCREENS.CHON_GOI_BH_VCX_O_TO);
    } catch (error) {
      console.log('🚀 ~ ThongTinXeVcxOtoScreen ~ error:', error);
    }
  }, []);

  const loaiChuXe = watch('loai_chu_xe');
  const gtriXe = watch('gtri_xe');
  const soTienThamKhao = watch('so_tien_tham_khao');
  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin xe tham gia"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        <View style={styles.buttonContainer}>
          <Button title={'Tiếp tục'} onPress={handleSubmit(onSubmit)} loading={isSubmitting} />
        </View>
      }>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 80} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{padding: spacing.sm}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          {isDev && <Button title="🔧 Fill Mock Data (Dev Only)" onPress={fillMockData} variant="outline" style={{marginBottom: spacing.md}} />}
          <Card title="Thông tin chi tiết xe">
            <Controller
              control={control}
              name="md_sd"
              rules={ThongTinXeVCXOtoValidation.md_sd as any}
              render={({field: {value, onChange}}) => (
                <Radio.Group label="Mục đích sử dụng" required orientation="horizontal" radioContainerStyle={styles.radioContainer} value={value} onChange={onChange} error={errors.md_sd?.message}>
                  <Radio value={'C'} label="Kinh doanh" />
                  <Radio value={'K'} label="Không kinh doanh" />
                </Radio.Group>
              )}
            />
            <CustomTouchableOpacity>
              <View pointerEvents="none">
                <TextField
                  control={control}
                  name="loai_xe"
                  label="Loại xe"
                  placeholder="Chọn loại xe"
                  rightIconType="dropdown"
                  required
                  editable={false}
                  inputContainerStyle={errors.loai_xe ? {borderColor: colors.danger} : undefined}
                  labelStyle={errors.loai_xe ? {color: colors.danger} : undefined}
                  rules={ThongTinXeVCXOtoValidation.loai_xe as any}
                  error={errors.loai_xe?.message}
                />
              </View>
            </CustomTouchableOpacity>
            <View style={styles.inputRow}>
              <CustomTouchableOpacity style={styles.input}>
                <View pointerEvents="none">
                  <TextField
                    control={control}
                    name="hang_xe"
                    label="Hãng xe"
                    placeholder="Nhập hãng xe"
                    required
                    editable={false}
                    rightIconType="dropdown"
                    inputContainerStyle={errors.hang_xe ? {borderColor: colors.danger} : undefined}
                    labelStyle={errors.hang_xe ? {color: colors.danger} : undefined}
                    rules={ThongTinXeVCXOtoValidation.hang_xe as any}
                    error={errors.hang_xe?.message}
                  />
                </View>
              </CustomTouchableOpacity>
              <CustomTouchableOpacity style={styles.input}>
                <View pointerEvents="none">
                  <TextField
                    control={control}
                    name="hieu_xe"
                    label="Hiệu xe"
                    placeholder="Nhập hiệu xe"
                    required
                    editable={false}
                    rightIconType="dropdown"
                    inputContainerStyle={errors.hieu_xe ? {borderColor: colors.danger} : undefined}
                    labelStyle={errors.hieu_xe ? {color: colors.danger} : undefined}
                    rules={ThongTinXeVCXOtoValidation.hieu_xe as any}
                    error={errors.hieu_xe?.message}
                  />
                </View>
              </CustomTouchableOpacity>
            </View>
            <CustomTouchableOpacity>
              <View pointerEvents="none">
                <TextField
                  control={control}
                  name="nam_sx"
                  label="Năm sản xuất"
                  placeholder="Chọn năm sản xuất"
                  required
                  editable={false}
                  rightIconType="dropdown"
                  inputContainerStyle={errors.nam_sx ? {borderColor: colors.danger} : undefined}
                  labelStyle={errors.nam_sx ? {color: colors.danger} : undefined}
                  rules={ThongTinXeVCXOtoValidation.nam_sx as any}
                  error={errors.nam_sx?.message}
                />
              </View>
            </CustomTouchableOpacity>
            <View style={styles.inputRow}>
              <Controller
                control={control}
                name="so_cho"
                rules={ThongTinXeVCXOtoValidation.so_cho as any}
                render={({field: {onChange, value}}) => (
                  <TextField
                    value={value?.toString() || ''}
                    onChangeText={text => onChange(text ? parseFloat(text) || 0 : 0)}
                    label="Số chỗ"
                    placeholder="Nhập số chỗ"
                    keyboardType="numeric"
                    required
                    containerStyle={styles.input}
                    error={errors.so_cho?.message}
                  />
                )}
              />
              <Controller
                control={control}
                name="trong_tai"
                rules={ThongTinXeVCXOtoValidation.trong_tai as any}
                render={({field: {onChange, value}}) => (
                  <TextField
                    value={value?.toString() || ''}
                    onChangeText={text => onChange(text ? parseFloat(text) || 0 : 0)}
                    label="Trọng tải (tấn)"
                    placeholder="Nhập trọng tải"
                    keyboardType="numeric"
                    required
                    containerStyle={styles.input}
                    error={errors.trong_tai?.message}
                  />
                )}
              />
            </View>
            <TextField control={control} name="bs_xe" label="Biển số xe" placeholder="Nhập biển số xe" required rules={ThongTinXeVCXOtoValidation.bs_xe as any} error={errors.bs_xe?.message} />
            <TextField control={control} name="so_khung" label="Số khung" placeholder="Nhập số khung" required rules={ThongTinXeVCXOtoValidation.so_khung as any} error={errors.so_khung?.message} />
            <TextField control={control} name="so_may" label="Số máy" placeholder="Nhập số máy" required rules={ThongTinXeVCXOtoValidation.so_may as any} error={errors.so_may?.message} />
          </Card>
          <Card title="Giá trị xe tham gia">
            <TextField
              control={control}
              name="gtri_xe"
              required
              keyboardType="numeric"
              isCurrency
              currencyValue={gtriXe}
              onCurrencyChange={value => setValue('gtri_xe', value)}
              label="Giá trị xe khai báo"
              placeholder="Nhập giá trị xe khai báo"
              rules={ThongTinXeVCXOtoValidation.gtri_xe as any}
              error={errors.gtri_xe?.message}
            />
            <Text style={styles.textDescription}>Nhập từ</Text>
            <TextField
              control={control}
              name="so_tien_tham_khao"
              required
              keyboardType="numeric"
              isCurrency
              currencyValue={soTienThamKhao}
              onCurrencyChange={value => setValue('so_tien_tham_khao', value)}
              label="Số tiền tham gia bảo hiểm"
              placeholder="Nhập số tiền tham gia bảo hiểm"
              rules={ThongTinXeVCXOtoValidation.so_tien_tham_khao as any}
              error={errors.so_tien_tham_khao?.message}
            />
            <View style={styles.inputRow}>
              <DateTimePickerComponent
                control={control}
                name="gio_hl"
                required
                label="Giờ bắt đầu"
                mode="time"
                placeholder="Nhập giờ bắt đầu"
                containerStyle={styles.input}
                rules={ThongTinXeVCXOtoValidation.gio_hl as any}
                error={errors.gio_hl?.message}
              />
              {/* <DateTimePickerComponent
                control={control}
                name="ngay_hl"
                label="Ngày bắt đầu"
                placeholder="Chọn ngày bắt đầu"
                mode="date"
                defaultValue={new Date()}
                containerStyle={styles.input}
                rules={ThongTinXeVCXOtoValidation.ngay_hl}
                error={errors.ngay_hl?.message}
              /> */}
              <DateTimePickerComponent
                control={control}
                name="ngay_hl"
                label="Ngày bắt đầu"
                placeholder="Chọn ngày bắt đầu"
                containerStyle={styles.input}
                rules={ThongTinXeVCXOtoValidation.ngay_hl}
                error={errors.ngay_hl?.message}
              />
            </View>
            <View style={styles.inputRow}>
              <DateTimePickerComponent
                control={control}
                name="gio_kt"
                required
                label="Giờ kết thúc"
                mode="time"
                placeholder="Nhập ngày kết thúc"
                disabled
                containerStyle={styles.input}
                rules={ThongTinXeVCXOtoValidation.gio_kt as any}
                error={errors.gio_kt?.message}
              />
              <DateTimePickerComponent
                control={control}
                name="ngay_kt"
                required
                label="Ngày kết thúc"
                placeholder="Chọn ngày kết thúc"
                mode="date"
                disabled
                containerStyle={styles.input}
                rules={ThongTinXeVCXOtoValidation.ngay_kt}
                error={errors.ngay_kt?.message}
              />
            </View>
          </Card>
          <Card title="Thông tin chủ xe">
            <Controller
              control={control}
              name="loai_chu_xe"
              rules={ThongTinXeVCXOtoValidation.loai_chu_xe as any}
              render={({field: {value, onChange}}) => (
                <Radio.Group
                  label="Chủ xe là cá nhân hay tổ chức?"
                  orientation="horizontal"
                  containerStyle={styles.radioContainer}
                  value={value}
                  onChange={onChange}
                  error={errors.loai_chu_xe?.message}>
                  <Radio value={'C'} label="Cá nhân" />
                  <Radio value={'T'} label="Tổ chức" />
                </Radio.Group>
              )}
            />
            <TextField control={control} name="ten" required label="Tên chủ xe" placeholder="Nhập tên chủ xe" rules={ThongTinXeVCXOtoValidation.ten as any} error={errors.ten?.message} />
            {loaiChuXe === 'C' ? (
              <TextField control={control} name="so_cmt" required label="Số CCCD" placeholder="Nhập số CCCD" rules={ThongTinXeVCXOtoValidation.so_cmt as any} error={errors.so_cmt?.message} />
            ) : (
              <TextField control={control} name="mst" required label="Mã số thuế" placeholder="Nhập mã số thuế" rules={ThongTinXeVCXOtoValidation.mst as any} error={errors.mst?.message} />
            )}
            <TextField control={control} name="dia_chi" required label="Địa chỉ" placeholder="Nhập địa chỉ" rules={ThongTinXeVCXOtoValidation.dia_chi as any} error={errors.dia_chi?.message} />
            <TextField
              control={control}
              name="dthoai"
              required
              label="Số điện thoại"
              placeholder="Nhập số điện thoại"
              keyboardType="phone-pad"
              rules={ThongTinXeVCXOtoValidation.dthoai as any}
              error={errors.dthoai?.message}
            />
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
