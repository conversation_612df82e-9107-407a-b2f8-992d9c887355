import {KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {Button, Card, CustomModal, CustomTouchableOpacity, Icon, ScreenComponent, Slider, TextField} from '@components/common';
import {styles} from './Styles';
import {colors, spacing} from '@constants/theme';
import {formatCurrency, formatCurrencyPlain} from '@utils/currencyFormatter';
import {Controller, useForm} from 'react-hook-form';
import {InfoRow} from '@screens/main/BHSK/ThongTinDonBH/components';
import {MAIN_SCREENS} from '@navigation/routes';

interface ChonGoiBaoHiem {
  goi_quyen_loi: string;
  muc_mien_thuong: string;
  gtri_xe: number;
  phi_bh: number;
  phi_dkbs: number;
  tong_phi: number;
}
export default function ChonGoiBaoHiemVcxOtoScreen({navigation, route}: any) {
  const [gtriXeModal, setGtriXeModal] = useState(false);
  const isDev = __DEV__;
  const {
    control,
    watch,
    setValue,
    formState: {errors, isSubmitting},
    handleSubmit,
  } = useForm<ChonGoiBaoHiem>({
    defaultValues: {
      gtri_xe: 700000000,
    },
  });

  const gtriXe = watch('gtri_xe');
  const phiBH = watch('phi_bh');
  const phiDKBS = watch('phi_dkbs');
  const tongPhi = watch('tong_phi');

  const fillMockData = useCallback(() => {
    const mockData: ChonGoiBaoHiem = {
      goi_quyen_loi: 'Gói tiêu chuẩn',
      muc_mien_thuong: '5.000.000đ',
      gtri_xe: 800000000,
      phi_bh: 12000000,
      phi_dkbs: 3000000,
      tong_phi: 15000000,
    };

    (Object.keys(mockData) as (keyof ChonGoiBaoHiem)[]).forEach(key => {
      setValue(key, mockData[key], {
        shouldValidate: true,
        shouldDirty: true,
      });
    });
  }, [setValue]);

  const onSubmit = useCallback(() => {
    navigation.navigate(MAIN_SCREENS.THONG_TIN_MUA_VCX_O_TO);
  }, [navigation]);

  const renderFooter = useMemo(
    () => (
      <View style={styles.footerContainer}>
        <View style={styles.footerContent}>
          <InfoRow label="Phí bảo hiểm chính" value={formatCurrency(phiBH || 0)} labelStyle={styles.footerLabel} />
          <InfoRow label="Quyền lợi bổ sung" value={formatCurrency(phiDKBS || 0)} labelStyle={styles.footerLabel} />
          <InfoRow label="Tổng phí" value={formatCurrency(tongPhi || 0)} labelStyle={[styles.footerLabel, styles.tongPhiLabel]} valueStyle={styles.tongPhiLabel} />
        </View>
        <Button title="Chọn gói" loading={isSubmitting} onPress={handleSubmit(onSubmit)} />
      </View>
    ),
    [phiBH, phiDKBS, tongPhi, handleSubmit, onSubmit, isSubmitting],
  );

  const handleThayDoiGtriXe = useCallback(() => {}, []);

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Chọn gói bảo hiểm"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}
      showFooter
      footer={renderFooter}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{padding: spacing.sm}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          {isDev && <Button title="🔧 Fill Mock Data (Dev Only)" onPress={fillMockData} variant="outline" style={{marginBottom: spacing.md}} />}
          <Card>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Giá trị xe tham gia BH</Text>
              <View style={{alignItems: 'flex-end', gap: spacing.sm}}>
                <Text style={styles.giaTriXeText}>{formatCurrencyPlain(15000000)}đ</Text>
                <TouchableOpacity style={styles.actionButton} onPress={() => setGtriXeModal(true)}>
                  <Icon name="Edit2" size={16} color={colors.green} />
                  <Text style={styles.actionButtonTitle}>Thay đổi giá trị xe</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={styles.inputRow}>
              <CustomTouchableOpacity style={styles.input}>
                <View pointerEvents="none">
                  <TextField
                    control={control}
                    name="goi_quyen_loi"
                    required
                    label="Gói quyền lợi"
                    placeholder="Chọn gói quyền lợi"
                    rightIconType="dropdown"
                    editable={false}
                    inputContainerStyle={errors.goi_quyen_loi ? {borderColor: colors.danger} : undefined}
                    labelStyle={errors.goi_quyen_loi ? {color: colors.danger} : undefined}
                    error={errors.goi_quyen_loi?.message}
                  />
                </View>
              </CustomTouchableOpacity>
              <CustomTouchableOpacity style={styles.input}>
                <View pointerEvents="none">
                  <TextField
                    control={control}
                    name="muc_mien_thuong"
                    required
                    label="Mức miễn thường"
                    placeholder="Chọn mức miễn thường"
                    rightIconType="dropdown"
                    editable={false}
                    inputContainerStyle={errors.muc_mien_thuong ? {borderColor: colors.danger} : undefined}
                    labelStyle={errors.muc_mien_thuong ? {color: colors.danger} : undefined}
                    error={errors.muc_mien_thuong?.message}
                  />
                </View>
              </CustomTouchableOpacity>
            </View>
          </Card>
          <Card title="Chọn nhà cung cấp" />
        </ScrollView>
        <CustomModal isVisible={gtriXeModal} onClose={() => setGtriXeModal(false)} title="Giá trị xe tham gia BH" animationIn="slideInUp" animationOut="slideOutDown">
          <TextField
            control={control}
            name="gtri_xe"
            label="Giá trị xe tham gia BH"
            placeholder="Nhập giá trị xe tham gia BH"
            keyboardType="numeric"
            isCurrency
            currencyValue={gtriXe}
            onCurrencyChange={value => setValue('gtri_xe', value)}
            error={errors.gtri_xe?.message}
          />
          <Controller
            control={control}
            name="gtri_xe"
            render={({field: {onChange, value}}) => (
              <Slider min={650000000} max={1650000000} step={1000000} value={value} onValueChange={onChange} containerStyle={{marginTop: -spacing.sm, marginBottom: spacing.md}} />
            )}
          />
          <Button title="Áp dụng" onPress={handleThayDoiGtriXe} style={{marginHorizontal: 0}} />
        </CustomModal>
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
