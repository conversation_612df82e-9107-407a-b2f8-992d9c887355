import {Dimensions, StyleSheet} from 'react-native';
import {borderRadius, colors, spacing, typography} from '@constants/theme';

const {width, height} = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    position: 'relative',
  },

  // Background decoration
  backgroundCircle: {
    position: 'absolute',
    borderRadius: 1000,
  },
  backgroundCircle1: {
    width: width * 0.8,
    height: width * 0.8,
    top: -width * 0.4,
    right: -width * 0.2,
  },
  backgroundCircle2: {
    width: width * 0.6,
    height: width * 0.6,
    bottom: -width * 0.3,
    left: -width * 0.15,
  },
  backgroundCircle3: {
    width: width * 0.4,
    height: width * 0.4,
    top: height * 0.2,
    left: -width * 0.1,
  },

  content: {
    flex: 1,
    paddingTop: spacing.md,
    paddingHorizontal: spacing.md,
    zIndex: 1,
  },

  mainCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    padding: spacing.xl,
    alignItems: 'center',
    width: '100%',
    marginBottom: spacing.xl,
    borderWidth: 1,
    borderColor: colors.gray[400],
  },

  iconContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },

  iconInnerCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },

  icon: {
    width: 50,
    height: 50,
  },

  title: {
    fontSize: typography.fontSize.xl + 2,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.md,
  },

  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm + 2,
    borderRadius: borderRadius.full,
    marginBottom: spacing.xl,
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.white,
    marginRight: spacing.xs,
    opacity: 0.8,
  },

  statusText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.white,
  },

  descriptionContainer: {
    alignItems: 'center',
  },

  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 8,
    fontFamily: typography.fontFamily.regular,
  },

  subDescription: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: typography.fontFamily.regular,
  },

  // Features preview
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: spacing.lg,
  },

  featureItem: {
    alignItems: 'center',
    flex: 1,
  },

  featureIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },

  featureIconText: {
    fontSize: 20,
    fontFamily: typography.fontFamily.regular,
  },

  featureText: {
    fontSize: typography.fontSize.base - 2,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },

  buttonContainer: {
    width: '100%',
    gap: spacing.md,
    alignItems: 'center',
  },

  primaryButton: {
    width: '100%',
  },

  secondaryButton: {
    width: '100%',
    backgroundColor: 'transparent',
    borderWidth: 1.5,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.sm,
  },

  notificationIcon: {
    width: 18,
    height: 18,
  },

  secondaryButtonText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
  },
});
