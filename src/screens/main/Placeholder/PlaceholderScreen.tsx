import React from 'react';
import {View, Text, Image, TouchableOpacity, Dimensions, Linking} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ScreenComponent, Button} from '@components/common';
import {MainNavigationProp} from '@navigation/types';
import R from '@assets/R';
import {styles} from './placeholder.style';

const {width} = Dimensions.get('window');

interface PlaceholderRouteParams {
  title?: string;
  icon?: any;
  color?: string;
  buttonTitle?: string;
}

export default function PlaceholderScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const route = useRoute();
  const params = route.params as PlaceholderRouteParams;

  const title = params?.title || 'Thông tin liên hệ';
  const icon = params?.icon || R.icons.ic_support;
  const color = params?.color || '#4CAF50';
  const buttonTitle = params?.buttonTitle || 'Quay lại trang chủ';

  return (
    <ScreenComponent showHeader headerTitle={title} showBackButton onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        {/* Background decoration */}
        <View style={[styles.backgroundCircle, styles.backgroundCircle1, {backgroundColor: color + '08'}]} />
        <View style={[styles.backgroundCircle, styles.backgroundCircle2, {backgroundColor: color + '06'}]} />
        <View style={[styles.backgroundCircle, styles.backgroundCircle3, {backgroundColor: color + '04'}]} />

        <View style={styles.content}>
          {/* Main Card */}
          <View style={styles.mainCard}>
            {/* Icon */}
            <View style={[styles.iconContainer, {backgroundColor: color + '15'}]}>
              <View style={[styles.iconInnerCircle, {backgroundColor: color + '25'}]}>
                <Image source={icon} style={[styles.icon, {tintColor: color}]} resizeMode="contain" />
              </View>
            </View>

            {/* Title */}
            <Text style={styles.title}>{title}</Text>

            {/* Status Badge */}
            {/* <View style={[styles.statusBadge, {backgroundColor: color}]}>
              <View style={styles.statusDot} />
              <Text style={styles.statusText}>Sắp ra mắt</Text>
            </View> */}

            {/* Description */}
            <View style={[styles.descriptionContainer, {flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}]}>
              <Text style={styles.description}>Hotline:</Text>
              <TouchableOpacity onPress={() => Linking.openURL('tel:19009196')}>
                <Text style={styles.subDescription}>1900 9196</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.descriptionContainer}>
              <Text style={styles.description}>
                Email: <Text style={styles.subDescription}><EMAIL></Text>
              </Text>
            </View>

            {/* Features Preview */}
            <View style={styles.featuresContainer}>
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, {backgroundColor: color + '20'}]}>
                  <Text style={[styles.featureIconText, {color: color}]}>⚡</Text>
                </View>
                <Text style={styles.featureText}>Nhanh chóng</Text>
              </View>
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, {backgroundColor: color + '20'}]}>
                  <Text style={[styles.featureIconText, {color: color}]}>🔒</Text>
                </View>
                <Text style={styles.featureText}>Bảo mật</Text>
              </View>
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, {backgroundColor: color + '20'}]}>
                  <Text style={[styles.featureIconText, {color: color}]}>📱</Text>
                </View>
                <Text style={styles.featureText}>Tiện lợi</Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <Button title={buttonTitle} onPress={() => navigation.goBack()} style={[styles.primaryButton, {backgroundColor: '#96bf49'}]} />

            {/* <TouchableOpacity
              style={[styles.secondaryButton, {borderColor: color}]}
              onPress={() => {
                // TODO: Implement notification subscription
                logger.log('Subscribe to notifications');
              }}
              activeOpacity={0.7}>
              <Image source={R.icons.ic_fill_notification} style={[styles.notificationIcon, {tintColor: color}]} />
              <Text style={[styles.secondaryButtonText, {color: color}]}>Nhận thông báo khi ra mắt</Text>
            </TouchableOpacity> */}
          </View>
        </View>
      </View>
    </ScreenComponent>
  );
}
