import {Platform, StyleSheet} from 'react-native';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
    marginHorizontal: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    ...shadows.sm,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.gray[200],
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
  },
  activeTab: {
    backgroundColor: colors.green,
  },
  tabText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as any,
  },
  benefitsCard: {
    padding: 0,
    marginBottom: spacing.md,
  },
  benefitsList: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  benefitItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  benefitContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginRight: spacing.md,
  },
  benefitNumber: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.dark,
    marginRight: spacing.xs,
    minWidth: 20,
  },
  benefitTitle: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    lineHeight: typography.lineHeight.normal * typography.fontSize.sm,
    marginTop: Platform.OS === 'ios' ? -2 : -1,
  },
  benefitAmount: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.green,
    textAlign: 'right',
    minWidth: 100,
  },
  separator: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginHorizontal: spacing.xs,
  },
});
