import React, {useCallback, useState} from 'react';
import {FlatList, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {Card, ScreenComponent} from '@components/common';
import {styles} from '@screens/main/BHSK/TomTatQuyenLoi/Styles';
import {formatCurrencyPlain} from '@utils/currencyFormatter';

interface BenefitItem {
  id: string;
  title: string;
  amount: string | number;
  order: number;
}

type TabType = 'main' | 'additional';

export default function TomTatQuyenLoiScreen({navigation, route}: any) {
  const [activeTab, setActiveTab] = useState<TabType>('main');

  // Lấy data từ params
  const mainBenefitsFromAPI = route?.params?.mainBenefits || [];
  const additionalBenefitsFromAPI = route?.params?.additionalBenefits || [];

  // Convert data từ API sang format BenefitItem
  const mainBenefits: BenefitItem[] = mainBenefitsFromAPI.map((item: any, index: number) => ({
    id: item.id || `main_${index}`,
    order: index + 1,
    title: item.label || item.ten_qloi || '',
    amount: item.amount || item.gh_tien_nam || 0,
  }));

  const additionalBenefits: BenefitItem[] = additionalBenefitsFromAPI.map((item: any, index: number) => ({
    id: item.id || `additional_${index}`,
    order: index + 1,
    title: item.label || item.ten_qloi || '',
    amount: item.amount || item.gh_tien_nam || 0,
  }));

  const handleTabPress = useCallback((tab: TabType) => {
    setActiveTab(tab);
  }, []);

  const formatCurrency = useCallback((amount: string | number) => {
    if (typeof amount === 'number') {
      return `${formatCurrencyPlain(amount)} VNĐ`;
    }
    return amount;
  }, []);

  const renderBenefitItem = useCallback(
    ({item}: {item: BenefitItem}) => {
      return (
        <View style={styles.benefitItem}>
          <View style={styles.benefitContent}>
            <Text style={styles.benefitNumber}>{item.order}.</Text>
            <Text style={styles.benefitTitle}>{item.title}</Text>
          </View>
          <Text style={styles.benefitAmount}>{formatCurrency(item.amount)}</Text>
        </View>
      );
    },
    [formatCurrency],
  );

  const currentBenefits = activeTab === 'main' ? mainBenefits : additionalBenefits;

  return (
    <ScreenComponent bodyStyle={styles.container} showHeader headerTitle={'Tóm tắt chi tiết quyền lợi'} showBackButton onPressBack={() => navigation.goBack()}>
      {/* Tab Section */}
      <View style={styles.tabContainer}>
        <TouchableOpacity style={[styles.tab, activeTab === 'main' && styles.activeTab]} onPress={() => handleTabPress('main')}>
          <Text style={[styles.tabText, activeTab === 'main' && styles.activeTabText]}>Quyền lợi chính</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.tab, activeTab === 'additional' && styles.activeTab]} onPress={() => handleTabPress('additional')}>
          <Text style={[styles.tabText, activeTab === 'additional' && styles.activeTabText]}>Quyền lợi bổ sung</Text>
        </TouchableOpacity>
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Benefits List */}
        <Card style={styles.benefitsCard}>
          <FlatList
            data={currentBenefits}
            renderItem={renderBenefitItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.benefitsList}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            scrollEnabled={false}
          />
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
