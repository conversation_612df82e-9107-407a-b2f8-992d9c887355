import R from '@assets/R';
import {<PERSON><PERSON>, Card, ConfirmModal, createToastHelpers, Radio, ScreenComponent, TextField, useToast} from '@components/common';
import ActionSheetModal, {ActionSheetOption} from '@components/common/ActionSheetModal';
import DateTimePickerComponent from '@components/common/DateTimePickerComponent';
import DocumentScanner from '@components/common/DocumentScanner';
import {ACTION_CODE} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import {MAIN_SCREENS} from '@navigation/routes';
import {styles} from '@screens/main/BHSK/ThongTinNguoiThamGia/Styles';
import {getCommonExecute} from '@services/endpoints';
import {RootState} from '@store/index';
import {toApiGender} from '@utils/formatters';
import {insuranceParticipantFormValidation} from '@utils/validationSchemas';
import React, {useEffect, useMemo, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {Image, KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';

// Types
interface FormData {
  mqh_nguoi_mua?: string;
  ten?: string;
  gioi_tinh?: string;
  ngay_sinh?: Date; // Form sẽ dùng Date object, nhưng API sẽ nhận string YYYYMMDD
  tuoi?: string;
  so_cmt?: string;
  dia_chi?: string;
  dthoai?: string;
  email?: string;
  ngdpt_so_id_dt?: string;
  ngdpt_moi_qhe?: string;
}

// Utility function để convert Date/ISO string sang string YYYYMMDD
const formatDateToYYYYMMDD = (date: Date | string | undefined | null): string => {
  if (!date) return '';

  let d: Date;
  if (date instanceof Date) {
    // Handle Date object - typeof Date is "object"
    d = date;
  } else if (typeof date === 'string') {
    // Handle ISO string like "1985-10-10T08:14:08.000Z"
    d = new Date(date);
  } else {
    // Fallback - try to convert anything else
    d = new Date(date as any);
  }

  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// Utility function để convert any date format to Date object
const convertToDateObject = (dateValue: any): Date | undefined => {
  if (!dateValue) return undefined;

  // If already a Date object, return it
  if (dateValue instanceof Date) {
    return isNaN(dateValue.getTime()) ? undefined : dateValue;
  }

  // If it's a number or string in YYYYMMDD format (8 digits)
  if (typeof dateValue === 'number' || (typeof dateValue === 'string' && /^\d{8}$/.test(dateValue))) {
    const dateStr = dateValue.toString();
    const year = parseInt(dateStr.substring(0, 4));
    const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
    const day = parseInt(dateStr.substring(6, 8));
    const date = new Date(year, month, day);
    return isNaN(date.getTime()) ? undefined : date;
  }

  // If it's a string in DD/MM/YYYY format
  if (typeof dateValue === 'string' && dateValue.includes('/')) {
    const parts = dateValue.split('/');
    if (parts.length === 3) {
      const [day, month, year] = parts.map(Number);
      const date = new Date(year, month - 1, day);
      return isNaN(date.getTime()) ? undefined : date;
    }
  }

  // Try to parse as ISO string or other date format
  try {
    const date = new Date(dateValue);
    return isNaN(date.getTime()) ? undefined : date;
  } catch {
    return undefined;
  }
};

// Utility function để serialize form data cho navigation (convert Date objects to strings)
const serializeFormDataForNavigation = (data: FormData) => {
  return {
    ...data,
    ngay_sinh: data.ngay_sinh ? formatDateToYYYYMMDD(data.ngay_sinh) : undefined,
  };
};

export default function ThongTinNguoiThamGiaScreen({navigation, route}: any) {
  console.log('ThongTinNguoiThamGiaScreen');

  const {thongTinDonBH} = route?.params;
  const fromAddInsured = route?.params?.fromAddInsured || false;
  const editData = route?.params?.editData || null;
  const isEditMode = !!editData;
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const {danhSachMoiQuanHeNdbh, danhSachMoiQuanHeNguoiMua} = useSelector((state: RootState) => state.commonCategories);

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: {errors},
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      gioi_tinh: editData?.gender ? toApiGender(editData.gender) : 'NAM',
      mqh_nguoi_mua: editData?.relationshipWithBuyer || '',
      ten: editData?.fullName || '',
      ngay_sinh: convertToDateObject(editData?.dateOfBirth),
      tuoi: editData?.age || '',
      so_cmt: editData?.idNumber || '',
      dia_chi: editData?.address || '',
      email: editData?.email || '',
      dthoai: editData?.phone || '',
      ngdpt_so_id_dt: editData?.ngdpt_so_id_dt || '',
      ngdpt_moi_qhe: editData?.ngdpt_moi_qhe || '',
    },
  });

  // State
  const [isRelationshipModalVisible, setIsRelationshipModalVisible] = useState(false);
  const [showModalBack, setShowModalBack] = useState(false);
  const [isNguoiPhuThuocModalVisible, setIsNguoiPhuThuocModalVisible] = useState(false);
  const [isMoiQuanHeNguoiPhuThuocModalVisible, setIsMoiQuanHeNguoiPhuThuocModalVisible] = useState(false);

  const [frontCccdImage, setFrontCccdImage] = useState<string | null>(editData?.frontCccdImage || null);

  const [isScannerVisible, setScannerVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Watch values
  const selectedGender = watch('gioi_tinh');
  const selectedNguoiMuaMQhe = watch('mqh_nguoi_mua');
  const selectedDateOfBirth = watch('ngay_sinh');
  const selectedNgdptSoIdDt = watch('ngdpt_so_id_dt');
  const selectedNgdptMoiQhe = watch('ngdpt_moi_qhe');

  // Computed values - lấy tên từ mã để hiển thị
  const selectedNguoiMuaMQheLabel = useMemo(() => {
    return danhSachMoiQuanHeNguoiMua.find((item: {ma: string; ten: string}) => item.ma === selectedNguoiMuaMQhe)?.ten || '';
  }, [selectedNguoiMuaMQhe, danhSachMoiQuanHeNguoiMua]);

  const selectedNgdptLabel = useMemo(() => {
    return thongTinDonBH?.ndbh?.find((item: {so_id_dt: string}) => item.so_id_dt === selectedNgdptSoIdDt)?.ten || '';
  }, [selectedNgdptSoIdDt, thongTinDonBH?.ndbh]);

  const selectedNgdptMoiQheLabel = useMemo(() => {
    return danhSachMoiQuanHeNdbh.find((item: {ma: string; ten: string}) => item.ma === selectedNgdptMoiQhe)?.ten || '';
  }, [selectedNgdptMoiQhe, danhSachMoiQuanHeNdbh]);

  // Effect to populate form when in edit mode
  useEffect(() => {
    if (isEditMode && editData) {
      // Set form values manually to ensure they're properly set
      Object.keys(editData).forEach(key => {
        let value = editData[key as keyof typeof editData];

        // Handle gender conversion from API format
        if (key === 'gioi_tinh' && value) {
          value = toApiGender(String(value));
        }

        // Handle date conversion from API format using the utility function
        if (key === 'ngay_sinh' && value) {
          value = convertToDateObject(value) as any;
        }

        if (value !== null && value !== undefined && value !== '') {
          setValue(key as keyof FormData, value, {shouldValidate: false});
        }
      });

      // Set image if available
      if (editData.frontCccdImage) {
        setFrontCccdImage(editData.frontCccdImage);
      }
    } else {
      // Ensure default gender value is set for new form
      const currentGender = watch('gioi_tinh');
      if (!currentGender || currentGender === '') {
        setValue('gioi_tinh', 'NAM', {shouldValidate: false});
      }
    }
  }, [isEditMode, editData, setValue, watch]);

  // Auto calculate age from date of birth
  useEffect(() => {
    if (selectedDateOfBirth) {
      const today = new Date();
      const birthDate = new Date(selectedDateOfBirth);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // Adjust age if birthday hasn't occurred this year
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      setValue('tuoi', age.toString());
    }
  }, [selectedDateOfBirth, setValue]);

  // Dropdown 1 trong card "Thông tin người được phụ thuộc" - sử dụng danhSachMoiQuanHeNdbh
  const moiQuanHeNdbhOptions: ActionSheetOption[] = useMemo(
    () =>
      danhSachMoiQuanHeNdbh.map((item: {ma: string; ten: string}) => ({
        id: item.ma,
        title: item.ten,
        onPress: () => handleMoiQuanHeNguoiPhuThuocSelect(item.ma),
      })),
    [danhSachMoiQuanHeNdbh],
  );

  const nguoiPhuThuocOptions: ActionSheetOption[] = useMemo(
    () =>
      (thongTinDonBH?.ndbh || []).map((item: {so_id_dt: string; ten: string}) => ({
        id: item.so_id_dt,
        title: item.ten,
        onPress: () => handleNguoiPhuThuocSelect(item.so_id_dt),
      })),
    [thongTinDonBH?.ndbh],
  );

  // Dropdown 2 trong card "Thông tin người được phụ thuộc" - sử dụng danhSachMoiQuanHeNguoiMua
  const moiQuanHeNguoiMuaOptions: ActionSheetOption[] = useMemo(
    () =>
      danhSachMoiQuanHeNguoiMua.map((item: {ma: string; ten: string}) => ({
        id: item.ma,
        title: item.ten,
        onPress: () => handleMoiQuanHeNguoiMuaSelect(item.ma),
      })),
    [danhSachMoiQuanHeNguoiMua],
  );

  // Handlers
  const handleMoiQuanHeNguoiMuaSelect = (id: string) => {
    setValue('mqh_nguoi_mua', id, {shouldValidate: true}); // Lưu mã
    setIsRelationshipModalVisible(false);
  };

  const handleNguoiPhuThuocSelect = (id: string) => {
    setValue('ngdpt_so_id_dt', id, {shouldValidate: true}); // Lưu mã người phụ thuộc
    setIsNguoiPhuThuocModalVisible(false);
  };

  const handleMoiQuanHeNguoiPhuThuocSelect = (id: string) => {
    setValue('ngdpt_moi_qhe', id, {shouldValidate: true}); // Lưu mã mối quan hệ
    setIsMoiQuanHeNguoiPhuThuocModalVisible(false);
  };

  const handleOcrComplete = (info: any) => {
    if (info.id) setValue('so_cmt', info.id, {shouldValidate: true});
    if (info.name) setValue('ten', info.name, {shouldValidate: true});
    if (info.address) setValue('dia_chi', info.address, {shouldValidate: true});
    if (info.gender) {
      // Convert gender from OCR to API format
      const convertedGender = toApiGender(info.gender);
      setValue('gioi_tinh', convertedGender, {shouldValidate: true});
    }
    if (info.dob) {
      // Convert dob to Date object using utility function
      const date = convertToDateObject(info.dob);
      if (date) {
        setValue('ngay_sinh', date, {shouldValidate: true});
      }
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);
      // Convert date to YYYYMMDD string format
      const ngaySinhFormatted = formatDateToYYYYMMDD(data.ngay_sinh);
      // Prepare params matching API
      const params = {
        ...(fromAddInsured ? {...thongTinDonBH} : null),
        ...(isEditMode ? {...thongTinDonBH, ...editData} : null),
        so_id: fromAddInsured ? thongTinDonBH?.so_id || 0 : editData?.orderId || 0, // Use current order ID for new insured person
        so_id_dt: editData?.orderIdDt || 0,
        mqh_nguoi_mua: data.mqh_nguoi_mua || '', // Gửi mã
        ten: data.ten || '',
        dkbs: editData?.dkbs || '',
        so_id_goi_bh: editData?.soIdGoi || '',
        ngay_hl: editData?.ngay_hl || '',
        ngay_kt: editData?.ngay_kt || '',
        dchi_kh: thongTinDonBH?.dia_chi_kh || '',
        so_cmt_kh: thongTinDonBH?.cmt_kh || '',
        gioi_tinh: data.gioi_tinh || '',
        ngay_sinh: ngaySinhFormatted,
        so_cmt: data.so_cmt || '',
        dia_chi: data.dia_chi || '',
        dthoai: data.dthoai || '',
        email: data.email || '',
        ngdpt_so_id_dt: data.ngdpt_so_id_dt, // Gửi mã
        ngdpt_moi_qhe: data.ngdpt_moi_qhe, // Gửi mã
        actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN,
      };
      console.log('🚀 ~ onSubmit ~ params:', params);

      const response = await getCommonExecute(params);
      console.log('🚀 ~ onSubmit ~ response:', response);

      if (response?.data) {
        toast.success(isEditMode ? 'Cập nhật thành công!' : 'Lưu thông tin thành công!', {
          position: 'top',
          duration: 2000,
        });

        setTimeout(() => {
          // Serialize form data để đảm bảo ngay_sinh là string khi pass sang screen khác
          const serializedData = serializeFormDataForNavigation(data);

          if (isEditMode) {
            // Ở chế độ edit: truyền editData với thông tin gói và quyền lợi đã chọn
            navigation.goBack();
          } else {
            navigation.popTo(MAIN_SCREENS.CHON_GOI_BH, {
              fromAddInsured,
              thongTinDonBH,
              insuredPersonData: response?.output,
              formData: serializedData, // Pass serialized data with string dates
            });
          }
        }, 1000);
      } else {
        toast.error(response?.error_message || 'Có lỗi xảy ra', {
          position: 'top',
          duration: 3000,
        });
      }
    } catch (error: any) {
      console.error('❌ API error:', error);
      toast.error(error?.response?.data?.error_message || 'Có lỗi xảy ra khi lưu thông tin', {
        position: 'top',
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditGoi = async () => {
    const data = getValues();
    const serializedData = serializeFormDataForNavigation(data);

    navigation.navigate(MAIN_SCREENS.CHON_GOI_BH, {
      fromAddInsured,
      thongTinDonBH,
      formData: serializedData,
      editData: {
        ...editData,
        ma_doi_tac_ql: thongTinDonBH?.ma_doi_tac_ql,
      }, // Truyền editData có soIdGoi và dkbs
    });
  };

  const handleGoToOrderInfo = () => {
    // Navigate back to order information screen
    setShowModalBack(true);
  };

  const handleCloseBackModal = () => {
    setShowModalBack(false);
  };

  const handleConfirmBackModal = () => {
    // Navigate back to main screen
    navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH);
  };

  // Render ID card placeholder
  // const renderIdCardPlaceholder = (type: 'front' | 'back', imageUri: string | null, onPress: () => void) => {
  //   const title = type === 'front' ? 'Mặt trước' : 'Mặt sau';
  //   return (
  //     <View style={styles.idCardContainer}>
  //       <TouchableOpacity style={styles.idCardPlaceholder} onPress={onPress} activeOpacity={0.7}>
  //         {imageUri ? (
  //           <>
  //             <Image source={{uri: imageUri}} style={styles.idCardImage} resizeMode="cover" />
  //             <View style={styles.scanCorners}>
  //               <View style={[styles.corner, styles.cornerTopLeft]} />
  //               <View style={[styles.corner, styles.cornerTopRight]} />
  //               <View style={[styles.corner, styles.cornerBottomLeft]} />
  //               <View style={[styles.corner, styles.cornerBottomRight]} />
  //             </View>
  //           </>
  //         ) : (

  //         )}
  //       </TouchableOpacity>
  //     </View>
  //   );
  // };

  return (
    <ScreenComponent
      showHeader
      headerTitle={isEditMode ? 'Cập nhật thông tin NĐBH' : 'Thông tin người được bảo hiểm (NĐBH)'}
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        fromAddInsured ? (
          <View style={styles.footerContainer}>
            <Button title={'Đơn bảo hiểm'} onPress={handleGoToOrderInfo} variant={'outline'} style={styles.secondaryButton} />
            <Button loading={isSubmitting} title={isEditMode ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} style={styles.primaryButton} />
          </View>
        ) : (
          <View style={styles.footerContainer}>
            <Button loading={isSubmitting} title={isEditMode ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} style={styles.primaryButton1} />
            {isEditMode && <Button title={'Tiếp tục'} style={styles.secondaryButton1} onPress={handleEditGoi} variant={'outline'} />}
          </View>
        )
      }>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: spacing.lg}}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}>
          {/* Chụp/Tải ảnh giấy tờ tùy thân */}
          <Card title="Chụp/Tải ảnh giấy tờ tùy thân">
            <TouchableOpacity onPress={() => setScannerVisible(true)} style={{flexDirection: 'row', flex: 1, alignItems: 'center', gap: spacing.sm}}>
              <Image source={frontCccdImage ? {uri: frontCccdImage} : R.images.img_cccd} style={styles.idCardIcon} resizeMode="contain" />
              <Text style={styles.idCardLabel}>Chụp hoặc tải ảnh căn cước công dân (không bắt buộc)</Text>
            </TouchableOpacity>
          </Card>

          {/* Form fields */}
          <Card title={'Thông tin nguời được bảo hiểm (NĐBH)'}>
            {/* Mối quan hệ với người mua bảo hiểm */}
            <Controller
              control={control}
              name="mqh_nguoi_mua"
              rules={insuranceParticipantFormValidation.relationshipWithBuyer as any}
              render={({field}) => (
                <TouchableOpacity onPress={() => setIsRelationshipModalVisible(true)}>
                  <View pointerEvents="none">
                    <TextField
                      label="Mối quan hệ với người mua bảo hiểm"
                      placeholder="Chọn mối quan hệ"
                      value={selectedNguoiMuaMQheLabel}
                      onChangeText={field.onChange} // Empty function since field is read-only
                      labelStyle={errors.mqh_nguoi_mua ? {color: colors.danger} : undefined}
                      inputContainerStyle={errors.mqh_nguoi_mua ? {borderColor: colors.danger} : {borderColor: selectedNguoiMuaMQheLabel ? colors.gray[500] : colors.gray[400]}}
                      required
                      editable={false}
                      rightIconType="dropdown"
                      error={errors.mqh_nguoi_mua?.message}
                    />
                  </View>
                </TouchableOpacity>
              )}
            />
            {/* Họ và tên */}
            <TextField control={control} name="ten" label="Họ và tên" placeholder="Nhập họ và tên" required rules={insuranceParticipantFormValidation.fullName as any} error={errors.ten?.message} />
            {/* Giới tính */}
            <Controller
              control={control}
              name="gioi_tinh"
              rules={insuranceParticipantFormValidation.gender as any}
              render={({field: {value, onChange}}) => (
                <Radio.Group label="Giới tính" required value={value} onChange={onChange} orientation="horizontal" containerStyle={styles.radioGroup} error={errors.gioi_tinh?.message}>
                  <Radio.Button label="Nam" value="NAM" />
                  <Radio.Button label="Nữ" value="NU" />
                </Radio.Group>
              )}
            />
            {/* Ngày sinh & Tuổi */}
            <View style={styles.inputRow}>
              <DateTimePickerComponent
                control={control}
                name="ngay_sinh"
                label="Ngày sinh"
                placeholder={'Chọn ngày sinh'}
                required
                containerStyle={styles.inputHalf}
                rules={insuranceParticipantFormValidation.dateOfBirth}
                error={errors.ngay_sinh?.message}
              />
              <TextField
                control={control}
                name="tuoi"
                label="Tuổi"
                inputContainerStyle={{backgroundColor: colors.gray[100]}}
                labelStyle={{backgroundColor: colors.gray[100]}}
                placeholder="Tuổi"
                required
                editable={false}
                containerStyle={styles.inputHalf}
              />
            </View>
            {/* Số giấy tờ tùy thân */}
            <TextField
              control={control}
              name="so_cmt"
              label="Số CCCD"
              placeholder="Nhập số CCCD/CMND"
              required
              keyboardType="numeric"
              rules={insuranceParticipantFormValidation.idNumber as any}
              error={errors.so_cmt?.message}
            />
            {/* Địa chỉ */}
            <TextField control={control} name="dia_chi" label="Địa chỉ" placeholder="Nhập địa chỉ" required rules={insuranceParticipantFormValidation.address as any} error={errors.dia_chi?.message} />
            <TextField
              control={control}
              name="dthoai"
              label="Số điện thoại"
              placeholder="Nhập số điện thoại"
              required
              keyboardType="numeric"
              autoCapitalize="none"
              rules={insuranceParticipantFormValidation.phone as any}
              error={errors.dthoai?.message}
            />
            {/* Email */}
            <TextField
              control={control}
              name="email"
              label="Email"
              placeholder="Nhập email"
              keyboardType="email-address"
              autoCapitalize="none"
              rules={insuranceParticipantFormValidation.email as any}
              error={errors.email?.message}
            />
          </Card>
          <Card title="Thông tin người được phụ thuộc">
            {/* Chọn người phụ thuộc */}
            <TouchableOpacity onPress={() => setIsNguoiPhuThuocModalVisible(true)}>
              <View pointerEvents="none">
                <TextField label="Người được phụ thuộc" placeholder="Chọn người phụ thuộc" value={selectedNgdptLabel} onChangeText={() => {}} editable={false} rightIconType="dropdown" />
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setIsMoiQuanHeNguoiPhuThuocModalVisible(true)}>
              <View pointerEvents="none">
                <TextField
                  label="Mối quan hệ với người được phụ thuộc"
                  placeholder="Chọn mối quan hệ"
                  value={selectedNgdptMoiQheLabel}
                  onChangeText={() => {}}
                  editable={false}
                  rightIconType="dropdown"
                />
              </View>
            </TouchableOpacity>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Modal - Mối quan hệ với người mua bảo hiểm (Form chính) */}
      <ActionSheetModal
        isVisible={isRelationshipModalVisible}
        onClose={() => setIsRelationshipModalVisible(false)}
        title="Chọn mối quan hệ"
        subtitle="Vui lòng chọn mối quan hệ với người mua bảo hiểm"
        options={moiQuanHeNguoiMuaOptions}
        selectedValue={selectedNguoiMuaMQhe}
        showSearchField={false}
        cancelButtonText="Hủy"
      />

      {/* Modal - Người được phụ thuộc (Card phụ thuộc) */}
      <ActionSheetModal
        isVisible={isNguoiPhuThuocModalVisible}
        onClose={() => setIsNguoiPhuThuocModalVisible(false)}
        title="Chọn người phụ thuộc"
        subtitle="Vui lòng chọn người được phụ thuộc"
        options={nguoiPhuThuocOptions}
        selectedValue={selectedNgdptSoIdDt}
        showSearchField
        cancelButtonText="Hủy"
      />

      {/* Modal - Mối quan hệ với người được phụ thuộc (Card phụ thuộc) - dùng danhSachMoiQuanHeNdbh */}
      <ActionSheetModal
        isVisible={isMoiQuanHeNguoiPhuThuocModalVisible}
        onClose={() => setIsMoiQuanHeNguoiPhuThuocModalVisible(false)}
        title="Chọn mối quan hệ"
        subtitle="Vui lòng chọn mối quan hệ với người được phụ thuộc"
        options={moiQuanHeNdbhOptions}
        selectedValue={selectedNgdptMoiQhe}
        showSearchField={false}
        cancelButtonText="Hủy"
      />

      <ConfirmModal
        visible={showModalBack}
        onClose={handleCloseBackModal}
        title={'Thông báo'}
        message={'Bạn có quay trở lại đơn bảo hiểm?'}
        onConfirm={handleConfirmBackModal}
        onCancel={handleCloseBackModal}
        confirmText={'Đồng ý'}
        cancelText={'Không'}
      />

      {/* Document Scanner Modal */}
      <DocumentScanner
        isVisible={isScannerVisible}
        onClose={() => setScannerVisible(false)}
        initialImageUri={frontCccdImage}
        onOcrComplete={(info, imageUri) => {
          handleOcrComplete(info);
          if (imageUri) {
            setFrontCccdImage(imageUri);
          }
        }}
      />
    </ScreenComponent>
  );
}
