import {Dimensions, StyleSheet} from 'react-native';
import {borderRadius, colors, spacing, typography} from '@constants/theme';

const {width: SCREEN_WIDTH} = Dimensions.get('window');
const ID_CARD_WIDTH = (SCREEN_WIDTH - spacing.sm * 2 - spacing.md * 2 - spacing.sm) / 2;

export const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: spacing.lg,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  modalRadioGroup: {
    marginBottom: spacing.lg,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  radio: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  radioSelected: {
    backgroundColor: colors.primary,
  },
  radioLabel: {
    fontSize: 16,
  },
  imagePreviewContainer: {
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  imagePreview: {
    width: '80%',
    height: 150,
    borderRadius: 8,
    marginBottom: spacing.md,
    backgroundColor: colors.gray[100],
  },
  imagePreviewLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.lg,
  },
  libraryButton: {
    flex: 1,
    padding: spacing.md,
    backgroundColor: colors.gray[200],
    borderRadius: 8,
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  libraryButtonText: {
    color: colors.dark,
    fontWeight: 'bold',
  },
  cameraButton: {
    flex: 1,
    padding: spacing.md,
    backgroundColor: colors.green,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  cameraButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  cancelButton: {
    padding: spacing.md,
    backgroundColor: colors.gray[200],
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontWeight: 'bold',
  },
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },

  // ID Cards
  idCardsRow: {
    flexDirection: 'row',
    gap: spacing.sm,
    justifyContent: 'space-between',
  },
  idCardContainer: {
    flex: 1,
    alignItems: 'center',
  },
  idCardPlaceholder: {
    width: '100%',
    aspectRatio: 1.586, // Standard ID card ratio (85.6mm x 54mm)
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.sm,
    position: 'relative',
    overflow: 'hidden',
  },
  idCardImage: {
    width: '100%',
    height: '100%',
    borderRadius: borderRadius.lg - 2,
  },
  idCardIcon: {
    width: 70,
    height: 50,
  },
  idUploadContainer: {
    minHeight: 50,
    justifyContent: 'center',
  },
  idCardPreview: {
    width: '100%',
    height: 150,
    borderRadius: 8,
  },
  scanCorners: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  corner: {
    position: 'absolute',
    width: 16,
    height: 16,
    borderColor: colors.green,
  },
  cornerTopLeft: {
    top: 4,
    left: 4,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderTopLeftRadius: 4,
  },
  cornerTopRight: {
    top: 4,
    right: 4,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderTopRightRadius: 4,
  },
  cornerBottomLeft: {
    bottom: 4,
    left: 4,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderBottomLeftRadius: 4,
  },
  cornerBottomRight: {
    bottom: 4,
    right: 4,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderBottomRightRadius: 4,
  },
  idCardLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    marginTop: spacing.sm,
    marginBottom: spacing.sm,
    flex: 1,
    lineHeight: 21,
  },

  // Form
  radioGroup: {
    marginBottom: spacing.md,
  },
  inputRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  inputHalf: {
    flex: 1,
  },

  // Footer
  footerContainer: {
    flexDirection: 'row',
  },
  secondaryButton: {
    flex: 1,
    marginRight: 4,
  },
  secondaryButton1: {
    flex: 1,
    marginLeft: 4,
  },
  secondaryButtonText: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
  primaryButton: {
    flex: 1.1,
    marginLeft: 4,
  },
  primaryButton1: {
    flex: 1.1,
    marginRight: 4,
  },
});
