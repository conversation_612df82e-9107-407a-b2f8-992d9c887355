import React, {useCallback, useEffect, useState} from 'react';
import {RefreshControl, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {useForm} from 'react-hook-form';
import {<PERSON><PERSON>, Card, createToastHelpers, IOSAlert, Radio, ScreenComponent, useIOSAlert, useToast} from '@components/common';
import {styles} from './Styles';
import {MAIN_SCREENS} from '@navigation/routes';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';

interface HealthQuestion {
  id?: string;
  ma?: string;
  ten?: string;
  mo_ta?: string;
  question?: string;
  description?: string;
}

interface HealthFormData {
  [key: string]: string; // Dynamic form fields
}

export default function CauHoiSkScreen({navigation, route}: any) {
  const {fromAddInsured, data, insuredPersonData, thongTinDonBH, editData, formDataGoi} = route?.params;
  console.log('🚀 ~ CauHoiSkScreen ~ insuredPersonData: ', insuredPersonData);
  console.log('🚀 ~ CauHoiSkScreen ~ thongTinDonBH: ', thongTinDonBH);
  const isEditMode = !!editData;
  const [loadingState, setLoadingState] = useState({isSubmitting: false});
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [cauHoi, setCauHoi] = useState([]);
  const [giaTri, setGiaTri] = useState([]);
  const alert = useIOSAlert();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors},
  } = useForm<HealthFormData>({
    defaultValues: {
      question1: '',
      question2: '',
      question3: '',
      question4: '',
      question5: '',
    },
  });

  const getDanhSachCauHoi = async () => {
    try {
      const params = {
        so_id: data?.so_id || 0,
        so_id_dt: data?.so_id_dt || 0,
        actionCode: ACTION_CODE.GET_DS_CAU_HOI,
      };
      console.log('🚀 ~ getDanhSachCauHoi ~ params: ', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ getDanhSachCauHoi ~ response: ', response);

      if (response?.data) {
        // Lưu dữ liệu câu hỏi từ API
        setCauHoi(response.data.cau_hoi || []);
        setGiaTri(response.data.gia_tri || []);

        // Auto-fill giá trị từ dap_an của câu hỏi hoặc mặc định từ gia_tri
        if (response.data.cau_hoi && response.data.cau_hoi.length > 0) {
          const formValues: {[key: string]: string} = {};

          response.data.cau_hoi.forEach((question: any) => {
            const questionId = `question_${question.ma}`;

            // Ưu tiên lấy dap_an đã có (nếu user đã trả lời trước đó)
            if (question.dap_an) {
              formValues[questionId] = question.dap_an;
            } else {
              // Nếu chưa có dap_an, lấy giá trị mặc định từ gia_tri
              const defaultGiaTri = response.data.gia_tri?.find((giaTriItem: any) => giaTriItem.ma_cau_hoi === question.ma && giaTriItem.mac_dinh === 'C');
              if (defaultGiaTri) {
                formValues[questionId] = defaultGiaTri.gia_tri;
              }
            }
          });

          // Set values to form
          Object.keys(formValues).forEach(key => {
            setValue(key, formValues[key]);
          });
        }
      }
    } catch (error) {
      toast.error('Có lỗi khi tải danh sách câu hỏi', {
        position: 'top',
        duration: 3000,
      });
    }
  };

  const getChiTietDonBH = async () => {
    try {
      const params = {
        so_id: data?.so_id,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_CHI_TIET_DON_BH,
      };

      const response = await getCommonExecute(params);
      console.log('🚀 ~ getChiTietDonBH ~ response:', response);
    } catch (error) {
      console.error('Error getChiTietDonBH:', error);
    }
  };

  useEffect(() => {
    getDanhSachCauHoi();
    getChiTietDonBH();
  }, []);

  const handleShowDescription = useCallback(
    (question: HealthQuestion) => {
      alert.show({
        title: 'Mô tả chi tiết',
        message: question?.mo_ta || 'Không có mô tả chi tiết.',
        buttons: [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
      });
    },
    [alert],
  );

  const onSubmit = useCallback(
    async (formData: HealthFormData) => {
      try {
        setLoadingState({isSubmitting: true});

        // Validate: Kiểm tra tất cả câu hỏi bắt buộc đã được trả lời chưa
        const unansweredQuestions: string[] = [];
        cauHoi.forEach((question: any) => {
          if (question.bat_buoc === 'C') {
            const questionId = `question_${question.ma}`;
            const answer = formData[questionId];
            if (!answer) {
              unansweredQuestions.push(question.ten);
            }
          }
        });

        if (unansweredQuestions.length > 0) {
          toast.error('Vui lòng trả lời tất cả các câu hỏi bắt buộc', {
            position: 'top',
            duration: 3000,
          });
          setLoadingState({isSubmitting: false});
          return;
        }

        // Chuẩn bị dữ liệu dgsk (đánh giá sức khỏe)
        // Mỗi object có 2 field: ma (mã câu hỏi) và dap_an (đáp án)
        const dgsk: Array<{ma: string; dap_an: string}> = [];

        cauHoi.forEach((question: any) => {
          const questionId = `question_${question.ma}`;
          const answer = formData[questionId];

          if (answer) {
            dgsk.push({
              ma: question.ma, // Mã câu hỏi (CH0001, CH0002,...)
              dap_an: answer, // Đáp án (K hoặc C)
            });
          }
        });
        const mappedDkbs =
          thongTinDonBH?.dkbs?.map((item: any) => ({
            ma: item.id,
          })) || [];

        // Gọi API lưu thông tin
        const params = {
          ...(fromAddInsured ? {...thongTinDonBH} : {}),
          ...(isEditMode ? {...thongTinDonBH, ...editData} : {}),
          ...insuredPersonData,
          ...(isEditMode && formDataGoi ? formDataGoi : {}),
          dchi_kh: thongTinDonBH?.dia_chi_kh || '',
          so_cmt_kh: thongTinDonBH?.cmt_kh || '',
          so_id: data?.so_id,
          dkbs: mappedDkbs,
          so_id_dt: data?.so_id_dt,
          actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN,
        };
        console.log('🚀 ~ CauHoiSkScreen ~ params:', params);

        const response = await getCommonExecute(params);
        console.log('🚀 ~ CauHoiSkScreen ~ response:', response);

        if (response?.data) {
          toast.success('Lưu câu hỏi sức khỏe thành công', {
            position: 'top',
            duration: 3000,
          });

          // Kiểm tra nếu từ flow "Thêm NĐBH" thì quay về ThongTinDonBH
          if (fromAddInsured) {
            // Quay về màn ThongTinDonBH và refresh data
            setTimeout(() => {
              navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH, {
                heathData: response?.output, // Response từ API câu hỏi SK
                insuranceOrderData: {...thongTinDonBH}, // Thông tin người tham gia + gói BH từ màn ChonGoiBH
                data, // so_id, so_id_dt từ màn ChonGoiBH
                refresh: true,
                refreshData: true, // Flag để trigger refresh
              });
            }, 2000);
          } else if (isEditMode) {
            setTimeout(() => {
              navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH, {
                needsRefresh: true,
                soID: editData.orderId,
              });
            }, 1000);
          } else {
            // Flow bình thường: sang màn ThongTinNguoiMua
            navigation.popTo(MAIN_SCREENS.THONG_TIN_NGUOI_MUA, {
              heathData: response?.output, // Response từ API câu hỏi SK
              insuredPersonData, // Thông tin người tham gia + gói BH từ màn ChonGoiBH
              data, // so_id, so_id_dt từ màn ChonGoiBH
              thongTinDonBH,
            });
          }
        } else {
          toast.error(response?.message || 'Có lỗi khi lưu câu hỏi sức khỏe', {
            position: 'top',
            duration: 3000,
          });
        }
      } catch (error: any) {
        console.error('Error onSubmit:', error);
        toast.error(error?.message || 'Có lỗi khi lưu câu hỏi sức khỏe', {
          position: 'top',
          duration: 3000,
        });
      } finally {
        setLoadingState({isSubmitting: false});
      }
    },
    [navigation, cauHoi, data, toast, insuredPersonData, fromAddInsured],
  );

  const handleGoToOrderInfo = useCallback(() => {
    alert.show({
      title: 'Thông báo',
      message: 'Bạn có quay trở lại đơn bảo hiểm?',
      buttons: [
        {
          text: 'Không',
          style: 'cancel',
        },
        {
          text: 'Đồng ý',
          style: 'default',
          onPress: () => {
            navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH);
          },
        },
      ],
    });
  }, [alert, navigation]);

  return (
    <ScreenComponent
      showHeader
      headerTitle={'Câu hỏi sức khỏe'}
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        fromAddInsured ? (
          <View style={styles.footerContainer}>
            <TouchableOpacity style={styles.secondaryButton} onPress={handleGoToOrderInfo} activeOpacity={0.7}>
              <Text style={styles.secondaryButtonText}>Đơn bảo hiểm</Text>
            </TouchableOpacity>
            <Button title="Tiếp tục" onPress={handleSubmit(onSubmit)} loading={loadingState.isSubmitting} disabled={loadingState.isSubmitting} style={styles.primaryButton} />
          </View>
        ) : (
          <Button title="Tiếp tục" onPress={handleSubmit(onSubmit)} loading={loadingState.isSubmitting} disabled={loadingState.isSubmitting} />
        )
      }>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={<RefreshControl onRefresh={() => getDanhSachCauHoi()} refreshing={false} />}>
        {/* Header Section */}
        <Card>
          <Text style={styles.title}>Thông tin tình trạng sức khỏe của người được bảo hiểm</Text>
          <Text style={styles.description}>Để đảm bảo quyền lợi cho Người được bảo hiểm, bạn vui lòng đọc kỹ và trả lời những câu hỏi về sức khỏe sau đây:</Text>
        </Card>

        {/* Questions Section */}
        <View>
          {cauHoi && cauHoi.length > 0 ? (
            cauHoi.map((question: any, index: number) => {
              const questionId = `question_${question.ma}`;

              // Lấy các giá trị tương ứng với câu hỏi này
              const questionValues = giaTri.filter((giaTriItem: any) => giaTriItem.ma_cau_hoi === question.ma);
              // Sắp xếp theo stt để đảm bảo thứ tự đúng
              questionValues.sort((a: any, b: any) => a.stt - b.stt);

              return (
                <Card key={questionId}>
                  <View style={styles.questionHeader}>
                    <Text style={styles.questionNumber}>{index + 1}.</Text>
                    <Text style={styles.questionText}>{question.ten}</Text>
                  </View>

                  <TouchableOpacity style={styles.descriptionButton} onPress={() => handleShowDescription(question)}>
                    <Text style={styles.descriptionButtonText}>Xem mô tả</Text>
                  </TouchableOpacity>

                  <Radio.Group value={watch(questionId)} onChange={value => setValue(questionId, value as string)} orientation="horizontal" containerStyle={styles.radioContainer} spacing={32}>
                    {questionValues.map((valueItem: any) => (
                      <Radio.Button key={valueItem.gia_tri} label={valueItem.ten_gia_tri} value={valueItem.gia_tri} />
                    ))}
                  </Radio.Group>
                </Card>
              );
            })
          ) : (
            <Card>
              <Text style={styles.description}>Không có danh sách câu hỏi.</Text>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* IOSAlert */}
      <IOSAlert visible={alert.visible} title={alert.title} message={alert.message} buttons={alert.buttons} onClose={alert.hide} />
    </ScreenComponent>
  );
}
