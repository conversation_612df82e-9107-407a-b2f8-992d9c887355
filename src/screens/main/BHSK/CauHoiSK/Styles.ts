import { StyleSheet } from 'react-native';
import { borderRadius, colors, shadows, spacing, typography } from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: spacing.xl,
  },
  headerSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    ...shadows.sm,
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    lineHeight: typography.lineHeight.tight * typography.fontSize.lg,
    marginBottom: spacing.sm,
    textAlign: 'left',
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    lineHeight: typography.lineHeight.normal * typography.fontSize.base,
    textAlign: 'left',
    fontFamily: typography.fontFamily.regular,
  },
  questionsSection: {
    gap: spacing.sm,
  },
  questionContainer: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    ...shadows.sm,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  questionNumber: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginRight: spacing.xs,
    minWidth: 20,
  },
  questionText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    color: colors.dark,
    lineHeight: typography.lineHeight.normal * typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    marginTop: -2,
    fontFamily: typography.fontFamily.regular,
  },
  descriptionButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: colors.green + '20',
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: colors.green + '35',
    marginBottom: spacing.md,
  },
  descriptionButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
  },
  radioContainer: {
    paddingTop: spacing.sm,
  },
  submitSection: {
    paddingTop: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  submitButton: {
    marginTop: spacing.lg,
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    ...shadows.base,
  },
  modalContent: {
    alignItems: 'center',
  },
  modalDescription: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    lineHeight: typography.lineHeight.normal * typography.fontSize.base,
    textAlign: 'auto',
    marginBottom: spacing.lg,
    fontFamily: typography.fontFamily.regular,

  },
  modalButton: {
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    minWidth: 80,
    ...shadows.sm,
  },
  modalTextButton: {
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as 600,
    fontSize: typography.fontSize.base,
  },

  // Footer Buttons
  footerContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.green,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: colors.green,
  },
});
