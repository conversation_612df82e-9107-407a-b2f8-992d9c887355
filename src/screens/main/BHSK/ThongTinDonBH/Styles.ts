import {StyleSheet} from 'react-native';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  scrollContent: {
    paddingBottom: spacing.xl * 2,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.sm,
    gap: spacing.xs,
    marginTop: spacing.sm,
    marginBottom: spacing.xs / 3,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  footer: {},
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingHorizontal: spacing.sm,
  },
  totalLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },
  totalValue: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },
  footerButtons: {
    flexDirection: 'row',
  },
  addButton: {
    flex: 1,
    marginRight: 4,
  },
  addButtonText: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
  payButton: {
    flex: 1.1,
    marginLeft: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    marginBottom: spacing.md,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.gray[400],
    marginRight: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    borderColor: colors.green,
    backgroundColor: colors.white,
  },
  checkboxLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.regular,
  },
  noteContainer: {
    flexDirection: 'row',
    borderRadius: borderRadius.xl,
    marginHorizontal: spacing.sm,
    padding: spacing.sm,
    paddingBottom: spacing.md - 4,
    borderColor: colors.gray[400],
    borderWidth: 1,
  },
  noteText: {
    flex: 1,
    fontSize: 15,
    color: colors.gray[700],
    lineHeight: 24,
    marginLeft: -12,
    marginTop: 3,
    fontFamily: typography.fontFamily.regular,
  },
  noteLink: {
    color: colors.primary,
  },
  emptyContainer: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    margin: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
    ...shadows.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  emptySubText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[500],
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },
  selectedUnitContainer: {
    backgroundColor: colors.green + '10',
    padding: spacing.sm,
    borderRadius: borderRadius.base,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.green + '30',
  },
  selectedUnitLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.medium,
  },
  selectedUnitValue: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  unitListContainer: {
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: colors.gray[300],
    overflow: 'hidden',
  },
  unitItem: {
    backgroundColor: colors.white,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
  },
  unitItemSelected: {
    backgroundColor: colors.green + '08',
  },
  unitItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  unitItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  unitInfo: {
    flex: 1,
  },
  unitCode: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
  },
  unitName: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  checkmarkContainer: {
    marginLeft: spacing.sm,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  selectedItem: {
    borderColor: colors.green,
    backgroundColor: colors.green + '08',
  },
  logoPlaceholder: {
    width: 48,
    height: 48,
    paddingHorizontal: spacing.xs,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[400],
    padding: spacing.sm,
  },
  logoItem: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  textPlaceholder: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.semibold,
    color: colors.gray[600],
    textAlign: 'center',
  },
  itemContent: {
    flex: 1,
    gap: spacing.xs,
  },
  tenNhaBH: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
  },
  donViContainer: {
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  donViItem: {
    flex: 1,
    maxWidth: '49%',
  },
  subText: {
    color: colors.green,
    fontFamily: typography.fontFamily.regular,
  },
  emptyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.green + '20',
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: colors.green + '35',
  },
  actionButtonFitContent: {
    alignSelf: 'flex-start',
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
  },
});
