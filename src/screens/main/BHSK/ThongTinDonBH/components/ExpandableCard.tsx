import React, {useState} from 'react';
import {LayoutAnimation, Platform, StyleProp, StyleSheet, Text, TouchableOpacity, UIManager, View, ViewStyle} from 'react-native';
import {Card} from '@components/common';
import Icon from '@components/common/Icon';
import {borderRadius, colors, spacing, typography} from '@constants/theme';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface ExpandableCardProps {
  containerStyle?: StyleProp<ViewStyle>;
  title?: string;
  titleIcon?: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  showViewDetails?: boolean;
  showViewCertificate?: boolean;
  showEdit?: boolean;
  showDelete?: boolean;
  onViewDetails?: () => void;
  onViewCertificate?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export const ExpandableCard: React.FC<ExpandableCardProps> = ({
  containerStyle,
  title,
  titleIcon,
  children,
  defaultExpanded = false,
  showViewDetails = false,
  showViewCertificate = false,
  showEdit = false,
  showDelete = false,
  onViewDetails,
  onViewCertificate,
  onEdit,
  onDelete,
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };

  return (
    <Card style={styles.card}>
      {/* Header */}
      <View style={styles.header}>
        {title && (
          <View style={styles.titleContainer}>
            {titleIcon && <Icon name={titleIcon as any} size={20} color={colors.green} />}
            <Text style={styles.title}>{title}</Text>
          </View>
        )}
        {/* <TouchableOpacity onPress={toggleExpanded} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
          <Icon name={expanded ? 'ArrowUp2' : 'ArrowDown2'} size={20} color={colors.green} />
        </TouchableOpacity> */}
      </View>

      {/* Expandable Content */}
      {expanded && (
        <>
          <View style={styles.content}>{children}</View>

          {/* Action Buttons */}
          {(showViewDetails || showViewCertificate || showEdit || showDelete) && (
            <View style={styles.actionView}>
              {/* Row 1: View Certificate button */}
              {showViewCertificate && (
                <TouchableOpacity style={[styles.actionButton, styles.actionButtonFitContent]} onPress={onViewCertificate}>
                  <Icon name="DocumentText1" size={16} color={colors.green} />
                  <Text style={styles.actionText}>Xem trước GCN bảo hiểm</Text>
                </TouchableOpacity>
              )}

              {/* Row 2: Edit & Delete buttons */}
              {(showEdit || showDelete) && (
                <View style={[styles.actionsRow, !showViewDetails && styles.actionsRowRight]}>
                  {showViewDetails && (
                    <TouchableOpacity style={styles.actionButton} onPress={onViewDetails}>
                      <Icon name="Eye" size={16} color={colors.green} />
                      <Text style={styles.actionText}>Xem chi tiết đơn</Text>
                    </TouchableOpacity>
                  )}
                  <View style={styles.actionsRight}>
                    {showEdit && (
                      <TouchableOpacity style={styles.actionButton} onPress={onEdit}>
                        <Icon name="Edit2" size={16} color={colors.green} />
                        <Text style={styles.actionText}>Sửa</Text>
                      </TouchableOpacity>
                    )}
                    {showDelete && (
                      <TouchableOpacity style={[styles.actionButton, styles.actionButtonDanger]} onPress={onDelete}>
                        <Icon name="Trash" size={16} color={colors.danger} />
                        <Text style={[styles.actionText, styles.actionTextDanger]}>Xóa</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              )}
            </View>
          )}
        </>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    // Default card styling can be added here if needed
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    flex: 1,
    marginBottom: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base + 1,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  line: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  content: {
    gap: spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.green + '20',
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: colors.green + '35',
    alignSelf: 'flex-start',
  },
  actionButtonFitContent: {
    alignSelf: 'flex-start',
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
  },
  actionButtonDanger: {
    backgroundColor: colors.danger + '15',
    borderColor: colors.danger + '30',
  },
  actionTextDanger: {
    color: colors.danger,
    fontFamily: typography.fontFamily.regular,
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  actionsRowRight: {
    justifyContent: 'flex-end',
  },
  actionsRight: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionView: {
    flexDirection: 'column',
    gap: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: spacing.md,
    marginTop: spacing.md,
  },
});
