import React, {useEffect, useRef} from 'react';
import {Animated, Platform, StyleSheet, Text, TouchableOpacity, UIManager, View} from 'react-native';
import Icon from '@components/common/Icon';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';
import {InfoRow} from './InfoRow';
import {Card} from '@components/common';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface InsurancePackageData {
  packageName: string;
  benefits: Array<{label: string; value: string}>;
  mainBenefitFee?: string;
  additionalFee?: string;
  totalPremium?: string;
}

interface InsuredPersonData {
  name: string;
  birthDate: string;
  idNumber: string;
  gender: string;
  phoneNumber: string;
  address: string;
}

interface InsurancePackageCardProps {
  data: InsurancePackageData;
  insuredPerson?: InsuredPersonData;
  visible: boolean;
  onHide: () => void;
}

export const InsurancePackageCard: React.FC<InsurancePackageCardProps> = ({data, insuredPerson, visible = false, onHide}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-20)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const [shouldRender, setShouldRender] = React.useState(visible);

  useEffect(() => {
    if (visible) {
      setShouldRender(true);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -20,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(({finished}) => {
        if (finished) {
          setShouldRender(false);
        }
      });
    }
  }, [visible, fadeAnim, slideAnim, scaleAnim]);

  if (!shouldRender) return null;

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{translateY: slideAnim}, {scale: scaleAnim}],
      }}>
      <Card style={styles.card}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Thông tin chi tiết gói bảo hiểm</Text>
          <TouchableOpacity onPress={onHide} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Icon name="ArrowUp2" size={20} color={colors.green} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {/* Package Name */}
          <InfoRow label="Gói bảo hiểm" value={data.packageName} valueStyle={styles.packageName} />
          {/* Benefits */}
          <View style={styles.benefitsSection}>
            <Text style={styles.benefitsTitle}>Quyền lợi bổ sung</Text>
            {data.benefits.map((benefit, index) => (
              <View key={index} style={styles.benefitItem}>
                <Text style={styles.benefitLabel}>{benefit.label}</Text>
                <Text style={styles.benefitValue}>{benefit.value}</Text>
              </View>
            ))}
          </View>

          {/* Fees */}
          {data.mainBenefitFee && <InfoRow label="Phí bảo hiểm chính" value={data.mainBenefitFee} />}
          {data.additionalFee && <InfoRow label="Phí bảo hiểm bổ sung" value={data.additionalFee} />}
          {data.totalPremium && <InfoRow label="Tổng phí bảo hiểm" value={data.totalPremium} valueStyle={styles.totalPremium} />}
        </View>
        {/* Insured Person Details */}
        {insuredPerson && (
          <>
            <View style={styles.divider} />
            <View style={styles.content}>
              <Text style={styles.sectionTitle}>Thông tin chi tiết NĐBH</Text>
              <InfoRow label="Họ và tên NĐBH" value={insuredPerson.name} />
              <InfoRow label="Ngày sinh" value={insuredPerson.birthDate} />
              <InfoRow label="Số giấy tờ tùy thân" value={insuredPerson.idNumber} />
              <InfoRow label="Giới tính" value={insuredPerson.gender} />
              <InfoRow label="Số điện thoại" value={insuredPerson.phoneNumber} />
              <InfoRow label="Địa chỉ" value={insuredPerson.address} />
            </View>
          </>
        )}

        {/* Hide Button */}
        <TouchableOpacity style={styles.hideButton} onPress={onHide}>
          <Icon name="ArrowUp2" size={16} color={colors.green} />
          <Text style={styles.hideButtonText}>Ẩn chi tiết</Text>
        </TouchableOpacity>
      </Card>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#f6f6f6ff',
    marginHorizontal: spacing.sm,
    padding: spacing.md,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginVertical: spacing.sm,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  content: {
    gap: spacing.sm,
  },
  packageName: {
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
  },
  benefitsSection: {
    marginTop: spacing.sm,
  },
  benefitsTitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  benefitItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: spacing.md,
    paddingVertical: spacing.xs,
  },
  benefitLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    flex: 1,
    fontFamily: typography.fontFamily.regular,
    maxWidth: '60%',
  },
  benefitValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'right',
  },
  hideButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.xs,
    marginTop: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  hideButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
  },
  totalPremium: {
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginVertical: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
    marginBottom: spacing.sm,
  },
});
