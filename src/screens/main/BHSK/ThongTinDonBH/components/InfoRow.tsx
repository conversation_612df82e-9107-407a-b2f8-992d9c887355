import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors, spacing, typography} from '@constants/theme';

interface InfoRowProps {
  label: string;
  value: string | number;
  valueStyle?: any;
  style?: any;
  labelStyle?: any;
}

export const InfoRow: React.FC<InfoRowProps> = ({label, value, valueStyle, labelStyle, style}) => {
  const isEmpty = !value || value === '' || value === null || value === undefined;
  return (
    <View style={[styles.row, style]}>
      <Text style={[styles.label, labelStyle]}>{label}</Text>
      <Text style={[styles.value, valueStyle, isEmpty ? {color: colors.warning} : null]}>{isEmpty ? 'Chưa xác định' : value}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.xs,
  },
  label: {
    fontSize: typography.fontSize.base - 1,
    color: colors.gray[600],
    flex: 1,
    fontFamily: typography.fontFamily.regular,
    maxWidth: '45%',
  },
  value: {
    fontSize: typography.fontSize.base - 1,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
    flex: 1,
    textAlign: 'right',
  },
});
