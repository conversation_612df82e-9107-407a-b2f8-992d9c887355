import {Button, Card, Checkbox, ConfirmModal, createToastHelpers, CustomTouchableOpacity, Icon, IOSAlert, ScreenComponent, useIOSAlert, useToast} from '@components/common';
import {ACTION_CODE, CONFIG_SERVER} from '@constants/axios';
import {colors, spacing, typography} from '@constants/theme';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';
import {useFocusEffect} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {FlatList, Image, LayoutAnimation, Platform, ScrollView, Text, TouchableOpacity, UIManager, View} from 'react-native';
import {ExpandableCard} from './components/ExpandableCard';
import {InfoRow} from './components/InfoRow';
import {InsurancePackageCard} from './components/InsurancePackageCard';
import {styles} from './Styles';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// ============ HELPER FUNCTIONS ============
const formatDate = (dateNumber: number): string => {
  if (!dateNumber || dateNumber.toString().length !== 8) return '';
  const str = dateNumber.toString();
  return `${str.slice(6, 8)}/${str.slice(4, 6)}/${str.slice(0, 4)}`;
};

const formatCurrency = (amount: number): string => {
  return amount ? `${amount.toLocaleString()}đ` : '0đ';
};

const calculateAge = (birthDate: string): string => {
  try {
    const [day, month, year] = birthDate.split('/').map(Number);
    const birth = new Date(year, month - 1, day);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) age--;
    return age.toString();
  } catch {
    return '';
  }
};

const formatDateToYYYYMMDD = (date: Date | string | undefined | null): string => {
  if (!date) return '';

  let d: Date;
  if (date instanceof Date) {
    // Handle Date object - typeof Date is "object"
    d = date;
  } else if (typeof date === 'string') {
    // Handle ISO string like "1985-10-10T08:14:08.000Z"
    d = new Date(date);
  } else {
    // Fallback - try to convert anything else
    d = new Date(date as any);
  }

  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// ============ MAIN COMPONENT ============
export default function ThongTinDonBHScreen({navigation, route}: any) {
  console.log('ThongTinDonBHScreen');
  const {updatedBuyerData, insuranceOrderData, fromDanhSach, soID} = route?.params || {};
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const alert = useIOSAlert();

  // State management
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [expandedOrderIds, setExpandedOrderIds] = useState<number[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [orders, setOrders] = useState<any[]>([]);
  const [orderToDelete, setOrderToDelete] = useState<number | null>(null);
  const [buyerInfo, setBuyerInfo] = useState(updatedBuyerData || {});
  const [isUpdatingBuyer, setIsUpdatingBuyer] = useState(false);
  const [isUpdatingInsured, setIsUpdatingInsured] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [latestInsuranceData, setLatestInsuranceData] = useState(insuranceOrderData);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  // Lưu so_id để sử dụng cho việc load lại dữ liệu
  const [soId] = useState(insuranceOrderData?.so_id || soID);
  const [danhSachDonVi, setDanhSachDonVi] = useState<any[]>([]);
  const [donViDaChon, setDonViDaChon] = useState<string>('');

  const getDonViXuLyBoiThuong = async () => {
    try {
      const params = {
        so_id: soId,
        actionCode: ACTION_CODE.GET_DON_VI_XU_LY_BOi_THUONG,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        setDanhSachDonVi(response.data);
        // Tìm đơn vị đã được chọn (chon === 1)
        const selected = response.data.find((item: any) => item.chon === 1);
        if (selected) {
          setDonViDaChon(selected.ma);
        }
      }
    } catch (error) {
      console.log('🚀 ~ getDonViXuLyBoiThuong ~ error:', error);
    }
  };

  useEffect(() => {
    getDonViXuLyBoiThuong();
  }, []);

  // ============ API FUNCTIONS ============
  // Hàm load lại dữ liệu từ API
  const loadLatestInsuranceData = async () => {
    if (!soId) {
      console.log('Không có so_id để load dữ liệu. soId:', soId);
      return;
    }

    try {
      setIsRefreshing(true);
      const params = {
        so_id: soId,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_CHI_TIET_DON_BH,
      };
      const response = await getCommonExecute(params);
      console.log('🚀 ~ loadLatestInsuranceData ~ response:', response);
      if (response?.data) {
        setLatestInsuranceData(response.data);

        // Cập nhật orders từ dữ liệu mới
        const mappedOrders = mapInsuranceData(response.data);
        setOrders(mappedOrders);

        setBuyerInfo({
          name: response.data.ten_kh || '',
          birthDate: formatDate(response.data.ngay_sinh_kh),
          idNumber: response.data.cmt_kh || '',
          gender: response.data.gioi_tinh_kh === 'NAM' ? 'Nam' : response.data.gioi_tinh_kh === 'NU' ? 'Nữ' : '',
          address: response.data.dia_chi_kh || '',
          phoneNumber: response.data.dthoai_kh || '',
          email: response.data.email_kh || '',
          loai_kh: response.data.loai_kh || '',
          mst: response.data.mst_kh || '',
        });
      }
    } catch (error: any) {
      console.log('🚀 ~ loadLatestInsuranceData ~ error:', error);
      toast.error('Có lỗi xảy ra khi tải dữ liệu mới nhất', {
        position: 'top',
        duration: 2000,
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // ============ DATA MAPPING ============
  const mapInsuranceData = (data: any) => {
    if (!data?.ndbh?.length) return [];

    // Get dkbs array for mapping benefits
    const dkbsList = data.dkbs || [];

    return data.ndbh.map((item: any, index: number) => {
      // Find benefits for this insured person
      const personBenefits = dkbsList.filter((dkb: any) => dkb.so_id_dt === item.so_id_dt);

      // Calculate total premium from benefits
      const additional = item.phi_dkbs;
      const premium = item.phi_chinh;

      // Map benefits to UI format
      const benefits = personBenefits.map((dkb: any, idx: number) => ({
        label: `${idx + 1}. ${dkb.ten_qloi}`,
        value: formatCurrency(dkb.phi_bh),
      }));

      const totalPrice = item.phi_bh;

      return {
        id: item.so_id_dt || index + 1,
        insuredName: item.ten || '',
        idNumber: item.so_cmt || '',
        startDate: formatDate(item.ngay_hl),
        expiryDate: formatDate(item.ngay_kt),
        premiumFee: formatCurrency(totalPrice),
        packageDetails: {
          packageName: item.ten_goi_bh || 'Chưa xác định',
          benefits,
          mainBenefitFee: formatCurrency(Number(premium)),
          additionalFee: formatCurrency(Number(additional)),
          totalPremium: formatCurrency(Number(totalPrice)),
        },
        packageInfo: {
          name: item?.ten_goi_bh,
          validityPeriod: `${formatDate(item.ngay_hl)} - ${formatDate(item.ngay_kt)}`,
          premium,
          additionalFee: formatCurrency(Number(additional)),
          totalPremium: formatCurrency(Number(totalPrice)),
        },
        insuredPersonDetails: {
          name: item.ten || '',
          birthDate: formatDate(item.ngay_sinh),
          idNumber: item.so_cmt || '',
          gender: item.gioi_tinh === 'NAM' ? 'Nam' : item.gioi_tinh === 'NU' ? 'Nữ' : '',
          phoneNumber: item.dthoai || '',
          email: item.email || '',
          address: item.dia_chi || '',
          ngdpt_so_id_dt: item.so_id_ngdpt || '',
          mqh_ngdpt: item.mqh_ngdpt || '',
          relationship: item.mqh_nguoi_mua || '',
        },
      };
    });
  };

  // ============ EFFECTS ============
  // Load dữ liệu khi navigate từ DanhSachBHSK (lần đầu mount)
  useEffect(() => {
    loadLatestInsuranceData();
  }, []); // Chỉ chạy 1 lần khi mount

  // Use useFocusEffect to load latest data when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      // Chỉ load dữ liệu khi không phải lần mount đầu tiên VÀ cần refresh
      if (!isInitialMount && needsRefresh) {
        console.log('useFocusEffect: Loading latest data...');
        loadLatestInsuranceData();
        setNeedsRefresh(false); // Reset flag sau khi refresh
      }
    }, [isInitialMount, needsRefresh, soId]),
  );

  // Initialize orders from API data and buyer info
  useEffect(() => {
    if (!latestInsuranceData) return;

    const mappedOrders = mapInsuranceData(latestInsuranceData);
    setOrders(mappedOrders);

    // Map buyer info from API if available
    if (latestInsuranceData.cmt_kh || latestInsuranceData.mst_kh) {
      setBuyerInfo({
        name: latestInsuranceData.ten_kh || '',
        birthDate: formatDate(latestInsuranceData.ngay_sinh_kh),
        idNumber: latestInsuranceData.cmt_kh || '',
        gender: latestInsuranceData.gioi_tinh_kh === 'NAM' ? 'Nam' : latestInsuranceData.gioi_tinh_kh === 'NU' ? 'Nữ' : '',
        address: latestInsuranceData.dia_chi_kh || '',
        phoneNumber: latestInsuranceData.dthoai_kh || '',
        email: latestInsuranceData.email_kh || '',
        mst: latestInsuranceData.mst_kh || '',
        loai_kh: latestInsuranceData.loai_kh || '',
      });
    }
    // Đánh dấu đã mount xong sau lần render đầu tiên
    if (isInitialMount) {
      setIsInitialMount(false);
    }
  }, [latestInsuranceData, isInitialMount]);

  // Handle buyer data updates
  useEffect(() => {
    const {updatedBuyerData: newData, refresh} = route?.params || {};
    if (!newData || !refresh) return;

    setIsUpdatingBuyer(true);
    setTimeout(() => {
      setBuyerInfo({
        name: newData.fullName || buyerInfo.name,
        birthDate: newData.birthDate?.toLocaleDateString('vi-VN') || buyerInfo.birthDate,
        idNumber: newData.idNumber || buyerInfo.idNumber,
        gender: newData.gender === 'male' ? 'Nam' : newData.gender === 'female' ? 'Nữ' : buyerInfo.gender,
        address: newData.address || buyerInfo.address,
        phoneNumber: newData.phoneNumber || buyerInfo.phoneNumber,
        email: newData.email || buyerInfo.email,
        mst: newData.mst || buyerInfo.mst,
        loai_kh: newData.loai_kh || buyerInfo.loai_kh,
      });
      setIsUpdatingBuyer(false);
    }, 1000);
  }, [route?.params]);

  // Handle insured data updates
  useEffect(() => {
    const {updatedInsuredData: newData, refresh} = route?.params || {};
    if (!newData || !refresh) return;

    setIsUpdatingInsured(true);
    setTimeout(() => {
      setOrders(prev =>
        prev.map(order =>
          order.id === newData.orderId
            ? {
                ...order,
                insuredName: newData.fullName || order.insuredName,
                idNumber: newData.idNumber || order.idNumber,
                insuredPersonDetails: {
                  ...order.insuredPersonDetails,
                  name: newData.fullName || order.insuredPersonDetails.name,
                  birthDate: newData.dateOfBirth ? new Date(newData.dateOfBirth).toLocaleDateString('vi-VN') : order.insuredPersonDetails.birthDate,
                  idNumber: newData.idNumber || order.insuredPersonDetails.idNumber,
                  gender: newData.gender === 'male' ? 'Nam' : newData.gender === 'female' ? 'Nữ' : order.insuredPersonDetails.gender,
                  address: newData.address || order.insuredPersonDetails.address,
                },
              }
            : order,
        ),
      );
      setIsUpdatingInsured(false);
    }, 1000);
  }, [route?.params]);

  // ============ EVENT HANDLERS ============
  const toggleOrderDetails = (orderId: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpandedOrderIds(prev => (prev.includes(orderId) ? prev.filter(id => id !== orderId) : [...prev, orderId]));
  };

  const handleAddInsured = () => {
    // Set flag để refresh khi quay lại màn hình này
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA, {
      fromAddInsured: true,
      editData: null,
      thongTinDonBH: latestInsuranceData,
    });
  };

  const handlePayment = async () => {
    try {
      if (!agreedToTerms) {
        alert.show({
          title: 'Thông báo',
          message: 'Vui lòng đồng ý với điều khoản của chúng tôi',
          buttons: [{text: 'Đóng', style: 'cancel'}],
        });
        return;
      } else if (!donViDaChon) {
        alert.show({
          title: 'Thông báo',
          message: 'Vui lòng chọn đơn vị xử lý bổi thường',
          buttons: [{text: 'Ok', style: 'cancel'}],
        });
        return;
      }

      setIsSubmitting(true);
      const params = {
        so_id: soId,
        actionCode: ACTION_CODE.INIT_THANH_TOAN,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        navigation.navigate(MAIN_SCREENS.THANH_TOAN, {
          nv: latestInsuranceData?.nv,
          soId: soId,
        });
      }
    } catch (error) {
      console.log('🚀 ~ handlePayment ~ error:', error);
      toast.error('Có lỗi xảy ra khi khởi tạo thanh toán');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditBuyer = () => {
    // Set flag để refresh khi quay lại màn hình này
    setNeedsRefresh(true);
    navigation.navigate(MAIN_SCREENS.THONG_TIN_NGUOI_MUA, {
      editData: latestInsuranceData,
      updatedBuyerData,
    });
  };

  const handleEditOrder = (orderId: number) => {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    const details = order.insuredPersonDetails;
    // Tìm ndbhItem theo so_id_dt khớp với orderId
    const ndbhItem = latestInsuranceData?.ndbh?.find((item: any) => item.so_id_dt === orderId);
    console.log('🚀 ~ handleEditOrder ~ ndbhItem:', ndbhItem);

    // Lấy các quyền lợi bổ sung (dkbs) của người này (theo so_id_dt)
    const mappedDkbs =
      latestInsuranceData?.dkbs
        ?.filter((item: any) => item.so_id_dt === orderId) // Chỉ lấy dkbs của người đang edit
        ?.map((item: any) => ({
          ma: item.ma_qloi, // Sửa từ item.id thành item.ma_qloi
        })) || [];
    console.log('🚀 ~ handleEditOrder ~ mappedDkbs:', mappedDkbs);
    // Set flag để refresh khi quay lại màn hình này
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA, {
      editData: {
        orderId: ndbhItem.so_id,
        orderIdDt: ndbhItem.so_id_dt,
        fullName: ndbhItem.ten,
        gender: ndbhItem.gioi_tinh,
        dateOfBirth: ndbhItem.ngay_sinh,
        idNumber: ndbhItem.so_cmt,
        address: ndbhItem.dia_chi,
        age: calculateAge(details.birthDate),
        phone: ndbhItem.dthoai,
        email: ndbhItem.email,
        relationshipWithBuyer: ndbhItem.mqh_nguoi_mua,
        ngdpt_so_id_dt: ndbhItem.so_id_ngdpt,
        ngdpt_moi_qhe: ndbhItem.mqh_ngdpt,
        dkbs: mappedDkbs,
        soIdGoi: ndbhItem.so_id_goi_bh,
        phiBH: ndbhItem.phi_bh,
        ngay_hl: ndbhItem.ngay_hl,
        ngay_kt: ndbhItem.ngay_kt,
      },
      thongTinDonBH: latestInsuranceData,
    });
  };

  const handleDeleteOrder = (orderId: number) => {
    setOrderToDelete(orderId);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!orderToDelete) return;

    try {
      setIsDeleting(true);
      setShowDeleteModal(false);

      // Tìm NDBH cụ thể trong latestInsuranceData.ndbh dựa trên orderToDelete (so_id_dt)
      const ndbhItem = latestInsuranceData?.ndbh?.find((item: any) => item.so_id_dt === orderToDelete);

      if (!ndbhItem) {
        throw new Error('Không tìm thấy thông tin NDBH');
      }

      const params = {
        so_id: ndbhItem.so_id,
        so_id_dt: ndbhItem.so_id_dt,
        actionCode: ACTION_CODE.XOA_NDBH,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        // Xóa thành công - load lại dữ liệu mới nhất
        await loadLatestInsuranceData();
        setOrderToDelete(null);

        toast.success('Xóa NDBH thành công!', {
          position: 'top',
          duration: 2000,
        });
      } else {
        throw new Error(response?.error_message || 'Xóa NDBH thất bại');
      }
    } catch (error: any) {
      console.log('🚀 ~ confirmDelete ~ error:', error);
      toast.error(error?.response?.data?.error_message || 'Có lỗi xảy ra khi xóa NDBH', {
        position: 'top',
        duration: 3000,
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleChonDonVi = async (maDonVi: string) => {
    const ngaySinhFormatted = formatDateToYYYYMMDD(latestInsuranceData.ndbh[0].ngay_sinh);
    const mappedDkbs =
      latestInsuranceData?.dkbs
        ?.filter((item: any) => item.so_id_dt === latestInsuranceData.ndbh[0].so_id_dt) // Chỉ lấy dkbs của người đang edit
        ?.map((item: any) => ({
          ma: item.ma_qloi, // Sửa từ item.id thành item.ma_qloi
        })) || [];
    try {
      const params = {
        ...latestInsuranceData,
        so_id: latestInsuranceData.so_id,
        so_id_dt: latestInsuranceData.ndbh[0].so_id_dt,
        ten: latestInsuranceData.ndbh[0].ten,
        so_cmt: latestInsuranceData.ndbh[0].so_cmt,
        dia_chi: latestInsuranceData.ndbh[0].dia_chi,
        dkbs: mappedDkbs,
        ma_doi_tac_ql: latestInsuranceData.ma_doi_tac_ql,
        mqh_nguoi_mua: latestInsuranceData.ndbh[0].mqh_nguoi_mua || '', // Gửi mã
        so_id_goi_bh: latestInsuranceData.ndbh[0].so_id_goi_bh || '',
        ngay_hl: latestInsuranceData.ndbh[0].ngay_hl || '',
        ngay_kt: latestInsuranceData.ndbh[0].ngay_kt || '',
        gioi_tinh: latestInsuranceData.ndbh[0].gioi_tinh || '',
        ngay_sinh: ngaySinhFormatted,
        dthoai: latestInsuranceData.ndbh[0].dthoai || '',
        email: latestInsuranceData.ndbh[0].email || '',
        ngdpt_so_id_dt: latestInsuranceData.ndbh[0].ngdpt_so_id_dt || '', // Gửi mã
        ngdpt_moi_qhe: latestInsuranceData.ndbh[0].ngdpt_moi_qhe || '', // Gửi mã
        ma_tpa: maDonVi,
        so_cmt_kh: latestInsuranceData.cmt_kh,
        dchi_kh: latestInsuranceData.dia_chi_kh,
        actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN,
      };
      console.log('🚀 ~ handleChonDonVi ~ params:', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ handleChonDonVi ~ response:', response);

      if (response?.data) {
        setDonViDaChon(maDonVi);
        toast.success('Chọn đơn vị xử lý bồi thường thành công', {
          position: 'top',
          duration: 2000,
        });
      } else {
        toast.error('Chọn đơn vị xử lý bồi thường thất bại', {
          position: 'top',
          duration: 2000,
        });
      }
    } catch (error: any) {
      console.log('🚀 ~ handleChonDonVi ~ error:', error);
      toast.error(error?.response?.data?.error_message || 'Có lỗi xảy ra khi chọn đơn vị', {
        position: 'top',
        duration: 2000,
      });
    }
  };

  // ============ RENDER HELPERS ============
  const renderDonViItem = ({id, uriLogo, tenDayDu, ten, selected, onPress}: any) => (
    <CustomTouchableOpacity onPress={onPress}>
      <View style={[styles.itemContainer, selected && styles.selectedItem]}>
        {uriLogo ? (
          <View style={styles.logoContainer}>
            <Image source={{uri: uriLogo}} style={styles.logoItem} />
          </View>
        ) : (
          <View style={styles.logoPlaceholder}>
            <Text style={styles.textPlaceholder}>{id}</Text>
          </View>
        )}
        <View style={styles.itemContent}>
          <Text style={styles.tenNhaBH} numberOfLines={2}>
            {ten}
          </Text>
          <CustomTouchableOpacity onPress={() => handleXemChiTiet(tenDayDu)}>
            <Text style={styles.subText}>Xem chi tiết</Text>
          </CustomTouchableOpacity>
        </View>
      </View>
    </CustomTouchableOpacity>
  );

  const donViList = useMemo(
    () =>
      (danhSachDonVi || [])
        .filter((dv: any) => dv.ma !== donViDaChon) // Loại bỏ đơn vị đã chọn khỏi danh sách
        .map((dv: any) => ({
          id: dv.ma,
          ten: dv.ma,
          tenDayDu: dv.ten,
          uriLogo: dv.logo ? (dv.logo.startsWith('http') ? dv.logo : `${CONFIG_SERVER.BASE_URL_API}${dv.logo}`) : null,
        })),
    [danhSachDonVi, donViDaChon],
  );

  const handleXemChiTiet = useCallback((ten: string) => {
    alert.show({
      title: 'Đơn vị xử lý bồi thường',
      message: ten,
      buttons: [{text: 'Đóng', style: 'cancel'}],
    });
  }, []);

  // ============ RENDER ============
  return (
    <ScreenComponent
      dialogLoading={isUpdatingBuyer || isUpdatingInsured || isDeleting || isRefreshing}
      showHeader
      headerTitle="Thông tin đơn bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        <View style={styles.footer}>
          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>Tổng phí bảo hiểm</Text>
            <Text style={styles.totalValue}>{formatCurrency(latestInsuranceData?.tong_phi) || '0đ'}</Text>
          </View>
          <View style={styles.footerButtons}>
            <Button title="Thêm NĐBH" onPress={handleAddInsured} style={styles.addButton} variant={'outline'} disabled={isSubmitting} />
            <Button title="Thanh toán" onPress={handlePayment} style={styles.payButton} loading={isSubmitting} />
          </View>
        </View>
      }>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Buyer Information */}
        <View style={styles.titleContainer}>
          <Icon name="Profile" color={colors.green} size={20} />
          <Text style={styles.title}>Thông tin người mua bảo hiểm</Text>
        </View>
        {buyerInfo?.idNumber || buyerInfo.mst ? (
          <ExpandableCard defaultExpanded showEdit onEdit={handleEditBuyer}>
            <View>
              {buyerInfo?.loai_kh === 'C' ? (
                <View>
                  <InfoRow label="Loại khách hàng" value={latestInsuranceData?.loai_kh_ten} />
                  <InfoRow label="Họ và tên" value={latestInsuranceData?.ten_kh || buyerInfo.name} />
                  <InfoRow label="Ngày sinh" value={buyerInfo.birthDate} />
                  <InfoRow label="Số giấy tờ tùy thân" value={buyerInfo.idNumber} />
                  <InfoRow label="Giới tính" value={buyerInfo.gender} />
                  <InfoRow label="Địa chỉ" value={buyerInfo.address} />
                  <InfoRow label="Số điện thoại" value={buyerInfo.phoneNumber} />
                  <InfoRow label="Email" value={buyerInfo.email} />
                </View>
              ) : (
                <View>
                  <InfoRow label="Loại khách hàng" value={latestInsuranceData?.loai_kh_ten} />
                  <InfoRow label="Tên tổ chức" value={latestInsuranceData?.ten_kh || buyerInfo.name} />
                  <InfoRow label="Mã số thuế" value={buyerInfo.mst} />
                  <InfoRow label="Địa chỉ" value={buyerInfo.address} />
                  <InfoRow label="Số điện thoại" value={buyerInfo.phoneNumber} />
                  <InfoRow label="Email" value={buyerInfo.email} />
                </View>
              )}
            </View>
          </ExpandableCard>
        ) : (
          <Card style={styles.emptyCard}>
            <Text style={{color: colors.danger, fontFamily: typography.fontFamily.regular}}>Chưa có thông tin người mua</Text>
            <TouchableOpacity style={styles.actionButton} onPress={handleEditBuyer}>
              <Icon name="Edit2" size={16} color={colors.green} />
              <Text style={styles.actionText}>Sửa</Text>
            </TouchableOpacity>
          </Card>
        )}

        {/* Insurance Orders */}
        <View style={styles.titleContainer}>
          <Icon name="DocumentText" color={colors.green} size={20} />
          <Text style={styles.title}>Thông tin người được bảo hiểm</Text>
        </View>

        {orders.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Chưa có đơn bảo hiểm nào</Text>
            <Text style={styles.emptySubText}>Vui lòng thêm NĐBH để tiếp tục</Text>
          </View>
        ) : (
          orders.map(order => (
            <React.Fragment key={order.id}>
              <ExpandableCard
                defaultExpanded
                showViewDetails
                showEdit
                showDelete
                onViewDetails={() => toggleOrderDetails(order.id)}
                onEdit={() => handleEditOrder(order.id)}
                onDelete={() => handleDeleteOrder(order.id)}>
                <InfoRow label="Họ và tên NĐBH" value={order.insuredName} />
                <InfoRow label="Số giấy tờ tùy thân" value={order.idNumber} />
                <InfoRow label="Phí gói bảo hiểm" value={order.premiumFee} />
                <InfoRow label="Hiệu lực" value={`${order.startDate} - ${order.expiryDate}`} />
              </ExpandableCard>

              {expandedOrderIds.includes(order.id) && (
                <InsurancePackageCard data={order.packageDetails} insuredPerson={order.insuredPersonDetails} visible onHide={() => toggleOrderDetails(order.id)} />
              )}
            </React.Fragment>
          ))
        )}

        <Card title="Chọn đơn vị xử lý bồi thường">
          {/* Đơn vị đã chọn */}
          {donViDaChon && (
            <View style={{marginBottom: spacing.md}}>
              <Text style={styles.selectedUnitLabel}>Đơn vị bảo hiểm gốc</Text>
              <View style={{marginTop: spacing.xs}}>
                <View style={styles.donViItem}>
                  {renderDonViItem({
                    id: donViDaChon,
                    tenDayDu: danhSachDonVi.find(dv => dv.ma === donViDaChon)?.ten || '',
                    ten: danhSachDonVi.find(dv => dv.ma === donViDaChon)?.ma || '',
                    uriLogo: danhSachDonVi.find(dv => dv.ma === donViDaChon)?.logo
                      ? danhSachDonVi.find(dv => dv.ma === donViDaChon)?.logo.startsWith('http')
                        ? danhSachDonVi.find(dv => dv.ma === donViDaChon)?.logo
                        : `${CONFIG_SERVER.BASE_URL_API}${danhSachDonVi.find(dv => dv.ma === donViDaChon)?.logo}`
                      : null,
                    selected: true,
                    onPress: () => {},
                  })}
                </View>
              </View>
            </View>
          )}

          {/* Danh sách đơn vị */}
          {donViDaChon && <Text style={styles.selectedUnitLabel}>Đơn vị xử lý bồi thường bên thứ 3</Text>}
          <FlatList
            data={donViList}
            numColumns={2}
            scrollEnabled={false}
            keyExtractor={item => item.id}
            ItemSeparatorComponent={() => <View style={{height: spacing.sm}} />}
            columnWrapperStyle={styles.donViContainer}
            contentContainerStyle={{paddingBottom: 4}}
            renderItem={({item}) => (
              <View style={styles.donViItem}>
                {renderDonViItem({
                  id: item.id,
                  ten: item.ten,
                  tenDayDu: item.tenDayDu,
                  uriLogo: item.uriLogo,
                  selected: donViDaChon === item.id,
                  onPress: () => handleChonDonVi(item.id),
                })}
              </View>
            )}
          />
        </Card>

        {/* Terms Agreement */}
        <Card style={styles.noteContainer}>
          <Checkbox value={agreedToTerms} onValueChange={setAgreedToTerms} />
          <Text style={styles.noteText}>
            Bên mua bảo hiểm/người được bảo hiểm cam kết các thông tin khai báo là chính xác, trung thực và hoàn toàn chịu trách nhiệm về các thông tin đã khai báo. Đồng thời tôi đã đọc, hiểu và đồng
            ý với{' '}
            <Text style={styles.noteLink} onPress={() => console.log('View terms')}>
              điều kiện, điều khoản, quy tắc của Sàn Bảo Hiểm.
            </Text>
          </Text>
        </Card>
      </ScrollView>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        type="danger"
        visible={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Thông báo"
        message={`Bạn có chắc chắn muốn xóa NĐBH ${orderToDelete ? orders.find(o => o.id === orderToDelete)?.insuredName : ''} không?`}
        onConfirm={confirmDelete}
        cancelText="Để sau"
      />
      <IOSAlert visible={alert.visible} title={alert.title} message={alert.message} buttons={alert.buttons} onClose={alert.hide} />
    </ScreenComponent>
  );
}
