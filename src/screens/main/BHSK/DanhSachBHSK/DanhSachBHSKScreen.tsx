import {Card, createToastHelpers, CustomTouchableOpacity, Icon, Loading, ScreenComponent, TextField, useToast} from '@components/common';
import {colors, spacing} from '@constants/theme';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {FlatList, Platform, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useFocusEffect} from '@react-navigation/native';
import {styles} from '@screens/main/BHSK/DanhSachBHSK/Styles';
import {MAIN_SCREENS} from '@navigation/routes';
import {PAGE_SIZE} from '@commons/Constant';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';

interface LoadMoreState {
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

// Interface for API response data
interface InsuranceItemAPI {
  so_id?: number; // ID
  so_hd?: string; // Số hợp đồng
  ten_kh?: string; // Tên khách hàng
  ma_sp?: string; // Mã sản phẩm
  sl_doi_tuong?: number; // Số lượng đối tượng
  tong_phi?: number; // Tổng phí
  hieu_luc?: string; // Hiệu lực (format: "dd/mm/yyyy - dd/mm/yyyy")
  trang_thai?: string; // Trạng thái code (C, D, etc.)
  trang_thai_ten?: string; // Tên trạng thái
  ten_doi_tac_ql?: string; // Tên đối tác quản lý
  sott?: number; // Số thứ tự từ API
  // Add other API fields as needed
  [key: string]: any;
}

interface InsuranceItem {
  so_id: string;
  so_hd: string; // Số hợp đồng
  ten_doi_tac_ql: string; // Công ty bảo hiểm
  ten_kh: string; // Tên khách hàng
  sl_doi_tuong: string; // Số lượng đối tượng
  hieu_luc: string; // Hiệu lực
  tong_phi: string; // Tổng phí
  trang_thai: string; // Trạng thái code (C, D, etc.)
  trang_thai_ten: string; // Tên trạng thái
}

// Helper function to transform API data to UI format
const transformApiDataToUI = (apiData: InsuranceItemAPI): InsuranceItem => {
  return {
    so_id: apiData.so_id?.toString() || '',
    so_hd: apiData.so_hd || 'N/A',
    ten_doi_tac_ql: apiData.ten_doi_tac_ql || 'N/A',
    ten_kh: apiData.ten_kh || 'N/A',
    sl_doi_tuong: apiData.sl_doi_tuong?.toString() || '0',
    hieu_luc: apiData.hieu_luc || 'N/A',
    tong_phi: typeof apiData.tong_phi === 'number' ? `${apiData.tong_phi.toLocaleString()}đ` : 'N/A',
    trang_thai: apiData.trang_thai || '',
    trang_thai_ten: apiData.trang_thai_ten || 'N/A',
  };
};

export default function DanhSachBhskScreen({navigation}: any) {
  console.log('DanhSachBhskScreen');

  const insets = useSafeAreaInsets();
  const [searchValue, setSearchValue] = useState('');
  const [danhSachDonBH, setDanhSachDonBH] = useState<InsuranceItem[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);

  // LoadMoreState for insurance list
  const [loadMoreState, setLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: PAGE_SIZE,
  });

  // Ref để track xem searchValue đã từng có giá trị hay chưa
  const hasSearchedBefore = useRef(false);
  const listRef = useRef<FlatList>(null);

  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // API function to fetch insurance data
  const getDanhSachDonBH = useCallback(
    async (isLoadMore: boolean = false, customSearchValue?: string) => {
      try {
        // Get current values from state
        let currentPage: number = 1;
        let pageSize: number = PAGE_SIZE;

        setLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize;
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        // Use searchLoading for search operations
        if (customSearchValue !== undefined && !isLoadMore) {
          setSearchLoading(true);
        }

        const searchTerm = customSearchValue !== undefined ? customSearchValue : searchValue;

        const params = {
          nd_tim: searchTerm || '',
          nv: 'NG',
          trang: currentPage,
          so_dong: pageSize,
          actionCode: ACTION_CODE.GET_DS_DON_BH,
        };

        const response = await getCommonExecute(params);
        console.log('🚀 ~ DanhSachBhskScreen ~ response: ', response);

        const listDonBH: InsuranceItemAPI[] = response.data.data || [];
        const totalItems = response.data.tong_so_dong || listDonBH.length;

        // Transform API data to UI format
        const transformedData = listDonBH.map(transformApiDataToUI);

        setDanhSachDonBH(prev => {
          let result: InsuranceItem[];
          if (isLoadMore) {
            // Merge data for load more
            result = [...prev, ...transformedData];
          } else {
            // Replace data for search/refresh
            result = transformedData;
          }
          return result;
        });

        setLoadMoreState(prev => {
          const hasMoreItems = currentPage * pageSize < totalItems;

          return {
            ...prev,
            isLoading: false,
            isLoadingMore: false,
            currentPage: currentPage,
            totalItems: totalItems,
            hasMore: hasMoreItems,
          };
        });
      } catch (error) {
        console.log('getDanhSachDonBH ~ error:', error);
        toast.error('Lỗi tải danh sách đơn bảo hiểm');
        setLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      } finally {
        setSearchLoading(false);
      }
    },
    [toast], // Removed searchValue dependency
  );

  // Load more handler
  const handleLoadMore = useCallback(() => {
    if (!loadMoreState.isLoadingMore && loadMoreState.hasMore && !loadMoreState.isLoading) {
      getDanhSachDonBH(true, searchValue);
    }
  }, [loadMoreState.isLoadingMore, loadMoreState.hasMore, loadMoreState.isLoading, searchValue, getDanhSachDonBH]);

  // Refresh handler
  const onRefreshData = useCallback(() => {
    setIsPullToRefresh(true);
    getDanhSachDonBH(false, searchValue).then(() => {
      setIsPullToRefresh(false);
    });
  }, [searchValue, getDanhSachDonBH]);

  // Search handler
  const handleSearch = useCallback(() => {
    getDanhSachDonBH(false, searchValue);
  }, [searchValue, getDanhSachDonBH]);

  const handleAddNew = () => {
    // console.log('Add new insurance');
    // Set flag to refresh data when returning
    setNeedsRefresh(true);
    navigation.navigate(MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA, {thongTinDonBH: null});
  };

  const handleItemPress = async (item: InsuranceItem) => {
    const params = {
      so_id: item.so_id,
      so_id_dt: 0,
      actionCode: ACTION_CODE.GET_CHI_TIET_DON_BH,
    };

    const response = await getCommonExecute(params);
    // console.log('🚀 ~ handleItemPress ~ response:', response);
    // Set flag to refresh data when returning
    setNeedsRefresh(true);

    // Navigate to ThongTinDonBH with the insurance order data
    navigation.navigate(MAIN_SCREENS.THONG_TIN_DON_BH, {
      insuranceOrderData: response.data,
      updatedBuyerData: response.data,
      fromDanhSach: true,
    });
  };

  // Auto search when searchValue becomes empty and has searched before
  useEffect(() => {
    // Track khi searchValue có giá trị
    if (searchValue !== '') {
      hasSearchedBefore.current = true;
      return; // Don't auto search when typing
    }

    // Chỉ tự động load khi searchValue rỗng và đã từng search trước đó
    if (searchValue === '' && hasSearchedBefore.current) {
      getDanhSachDonBH(false, '');
    }
  }, [searchValue]); // Removed getDanhSachDonBH dependency

  // Initial data load
  useEffect(() => {
    getDanhSachDonBH(false).then(() => {
      // Mark as not initial mount after first load
      setTimeout(() => setIsInitialMount(false), 1000);
    });
  }, []); // Empty dependency array for initial load only

  // Use useFocusEffect to refresh data when returning from other screens
  useFocusEffect(
    React.useCallback(() => {
      // Only refresh if not on initial mount and we need to refresh
      if (!isInitialMount && needsRefresh) {
        getDanhSachDonBH(false, searchValue);
        setNeedsRefresh(false); // Reset flag after refreshing
      }
    }, [isInitialMount, needsRefresh, searchValue]),
  );

  const renderItem = ({item}: {item: InsuranceItem}) => {
    const statusColor = item.trang_thai === 'D' ? colors.success : colors.warning;
    const statusText = item.trang_thai_ten;

    const renderInfoRow = (title: string, value: string, style?: object) => {
      return (
        <View style={styles.cardRow}>
          <Text style={styles.label}>{title}</Text>
          <Text style={[styles.value, style, value === 'Chưa xác định' && {color: colors.warning}]}>{value}</Text>
        </View>
      );
    };

    return (
      <CustomTouchableOpacity onPress={() => handleItemPress(item)}>
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.contractNumber}>Số hợp đồng {item.so_hd}</Text>
          </View>
          {renderInfoRow('Công ty bảo hiểm', item.ten_doi_tac_ql)}
          {renderInfoRow('Tên khách hàng', item.ten_kh)}
          {renderInfoRow('Số người tham gia bảo hiểm', item.sl_doi_tuong + ' người')}
          {renderInfoRow('Hiệu lực hợp đồng', item.hieu_luc)}
          {renderInfoRow('Phí bảo hiểm', item.tong_phi, styles.premium)}
          {renderInfoRow('Trạng thái', statusText, {...styles.status, color: statusColor})}
        </Card>
      </CustomTouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="DocumentText" size={64} color={colors.gray[400]} variant="Bulk" />
      <Text style={styles.emptyText}>{searchValue ? 'Không tìm thấy đơn bảo hiểm' : 'Chưa có đơn bảo hiểm nào'}</Text>
      <Text style={styles.emptySubText}>{searchValue ? 'Không có đơn bảo hiểm nào phù hợp với từ khóa tìm kiếm của bạn' : 'Chưa có đơn bảo hiểm nào trong hệ thống. Hãy thêm đơn bảo hiểm mới.'}</Text>
    </View>
  );

  return (
    <ScreenComponent dialogLoading={loadMoreState.isLoading && !loadMoreState.isLoadingMore} showHeader headerTitle="Danh sách đơn bảo hiểm" showBackButton onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextField
            placeholder="Nhập thông tin tìm kiếm..."
            value={searchValue}
            onChangeText={setSearchValue}
            onSubmitEditing={handleSearch}
            showPlaceholderWhenEmpty={true}
            containerStyle={styles.searchInputContainer}
            rightIconType={<Icon name="SearchNormal" color={colors.gray[600]} size={20} variant="Linear" />}
            onRightIconPress={handleSearch}
            autoCorrect={false}
            autoCapitalize="none"
            returnKeyType="search"
            blurOnSubmit={false}
            enablesReturnKeyAutomatically={false}
          />
        </View>

        {/* List */}
        <FlatList
          ref={listRef}
          data={danhSachDonBH}
          renderItem={renderItem}
          keyExtractor={(_, index) => String(index)}
          contentContainerStyle={danhSachDonBH.length === 0 ? styles.emptyContentContainer : styles.listContent}
          showsVerticalScrollIndicator={false}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={10}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl refreshing={isPullToRefresh && loadMoreState.isLoading && !loadMoreState.isLoadingMore} onRefresh={onRefreshData} colors={[colors.primary]} tintColor={colors.primary} />
          }
          onEndReachedThreshold={0.1}
          onEndReached={handleLoadMore}
          ListFooterComponent={() => {
            if (loadMoreState.isLoadingMore) {
              return (
                <View style={styles.loadMoreContainer}>
                  <Loading size="small" message={'Đang tải thêm...'} />
                </View>
              );
            }
            if (!loadMoreState.hasMore && danhSachDonBH.length > 0) {
              return (
                <View style={styles.endDataContainer}>
                  <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
                  <Text style={styles.endDataText}>Bạn đã xem tất cả {danhSachDonBH.length} đơn bảo hiểm</Text>
                </View>
              );
            }
            return null;
          }}
        />

        {/* Floating Action Button */}
        <TouchableOpacity style={[styles.fab, {bottom: Platform.OS === 'android' ? insets.bottom + spacing['2xl'] || spacing.xl : spacing['2xl']}]} activeOpacity={0.8} onPress={handleAddNew}>
          <Icon name="Add" size={32} color={colors.white} variant="Linear" />
        </TouchableOpacity>
      </View>
    </ScreenComponent>
  );
}
