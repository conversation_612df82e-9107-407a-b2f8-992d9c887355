import { StyleSheet } from 'react-native';
import { borderRadius, colors, shadows, spacing, typography } from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },

  // Search
  searchContainer: {
    paddingHorizontal: spacing.sm,
    paddingBottom: spacing.sm,
  },
  searchInputContainer: {
    marginBottom: 0,
  },

  // List
  listContent: {
    paddingTop: spacing.sm,
    paddingBottom: spacing.xl,
  },

  // Card
  card: {
    padding: spacing.md,
  },
  cardHeader: {
    marginBottom: spacing.sm,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  contractNumber: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: spacing.sm,
  },
  label: {
    fontSize: typography.fontSize.base - 1,
    color: colors.gray[600],
    flex: 1,
    fontFamily: typography.fontFamily.regular,

  },
  value: {
    fontSize: typography.fontSize.base - 1,
    color: colors.gray[800],
    fontFamily: typography.fontFamily.medium,
    flex: 1,
    textAlign: 'right',
  },
  premium: {
    color: colors.gray[800],
    fontWeight: typography.fontWeight.bold as any,
    fontFamily: typography.fontFamily.semibold,
  },
  status: {
    fontFamily: typography.fontFamily.semibold,
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing['3xl'],
  },
  emptyContentContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingTop: spacing.sm,
    paddingBottom: spacing.xl,
  },
  emptyText: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.gray[700],
    marginTop: spacing.md,
  },
  emptySubText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    marginTop: spacing.xs,
    textAlign: 'center',
    paddingHorizontal: spacing.md,
    lineHeight: 20,
    fontFamily: typography.fontFamily.regular,
  },

  // Loading State
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing['3xl'],
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginTop: spacing.md,
    fontFamily: typography.fontFamily.regular,
  },

  // Load More State
  loadMoreContainer: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },

  // End Data State
  endDataContainer: {
    paddingBottom: spacing.xl,
    alignItems: 'center',
    marginTop: spacing.md,
  },
  endDataTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  endDataText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },

  // FAB
  fab: {
    position: 'absolute',
    right: spacing.md,
    width: 56,
    height: 56,
    borderRadius: borderRadius.full,
    backgroundColor: colors.green,
    justifyContent: 'center',
    alignItems: 'center',
    // ...shadows.lg,
    // elevation: 6,
  },
});
