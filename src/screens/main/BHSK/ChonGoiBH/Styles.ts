import {borderRadius, colors, spacing, typography} from '../../../../constants/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width: SCREEN_WIDTH} = Dimensions.get('window');
const CARD_WIDTH = Math.min(SCREEN_WIDTH - spacing.sm) / 2.6;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    paddingHorizontal: spacing.sm,
  },

  // Section styles
  sectionCard: {
    marginBottom: spacing.md,
  },

  section: {
    marginBottom: spacing.md,
    paddingHorizontal: spacing.xs,
  },

  card: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    backgroundColor: '#E3EAD8',
    borderWidth: 1,
    borderColor: colors.green,
  },

  cardTitle: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },

  // Header columns để thẳng hàng với content
  headerColumn1: {
    flex: 3,
    paddingRight: spacing.sm,
  },

  headerColumn2: {
    flex: 2,
    textAlign: 'center',
    paddingRight: spacing.sm,
  },

  headerColumn3: {
    flex: 1,
    textAlign: 'center',
  },

  // Checkbox column
  checkboxColumn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },

  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  sectionSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.regular,
  },

  // Date picker styles
  dateRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },

  dateField: {
    flex: 1,
  },

  // Package cards styles
  packagesScroll: {
    paddingBottom: spacing.md,
  },

  packageCard: {
    width: CARD_WIDTH,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.sm,
    marginHorizontal: spacing.sm - 2,
    borderWidth: 1,
    borderColor: colors.gray[400],
  },

  logoPlaceholder: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[300],
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
  },

  logoPlaceholderOther: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[300],
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginVertical: spacing.sm,
  },
  textPlaceholder: {
    color: colors.gray[700],
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
  },

  selectedPackageCard: {
    borderColor: colors.green,
    borderWidth: 2,
    borderRadius: borderRadius.xl,
    // backgroundColor removed - using LinearGradient instead
  },

  firstPackageCard: {
    marginLeft: spacing.sm,
  },

  gradientWrapper: {
    flex: 1,
    paddingHorizontal: spacing.sm,
    marginHorizontal: -spacing.sm,
    borderRadius: borderRadius.xl - 2,
  },

  packageHeader: {
    alignItems: 'flex-start',
  },

  micLogo: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },

  logoContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginVertical: spacing.sm,
    padding: spacing.sm,
  },

  micText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },

  packageTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs,
  },

  packageDescription: {
    fontSize: typography.fontSize.xs,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
    lineHeight: typography.fontSize.sm * 1.4,
  },

  originalPrice: {
    fontSize: typography.fontSize.sm + 2,
    color: colors.green,
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },

  packagePrice: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginTop: spacing.sm,
    marginBottom: spacing.md,
  },

  // Old selected package info styles (now replaced by new DSGoiBH-style)
  selectedPackageInfoOld: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    borderRadius: borderRadius.xl,
    borderWidth: 2,
    borderColor: colors.green,
    paddingHorizontal: spacing.sm,
  },

  micLogoLarge: {
    backgroundColor: colors.green,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.xl,
    minWidth: 60,
    alignItems: 'center',
  },

  micTextLarge: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },

  selectedPackageDetails: {
    flex: 1,
  },

  selectedPackageTitle: {
    fontSize: typography.fontSize.sm + 2,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
  },

  selectedPackageSubtext: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    color: colors.yellow,
    marginBottom: spacing.xs / 2,
  },

  selectedPackageOriginalPrice: {
    fontSize: typography.fontSize.sm + 2,
    color: colors.yellow,
    fontWeight: typography.fontWeight.semibold,
  },

  selectedPackagePrice: {
    fontSize: typography.fontSize.sm + 2,
    fontWeight: typography.fontWeight.bold,
    color: colors.yellow,
  },

  // Benefits styles
  benefitRow: {
    flexDirection: 'row',
    alignItems: 'center', // Thay đổi từ 'center' thành 'flex-start'
    paddingVertical: spacing.sm,
    minHeight: 44, // Tăng chiều cao để align tốt hơn
    paddingHorizontal: 0,
  },

  benefitInfo: {
    // Không set flex ở đây nữa, sẽ set inline
    paddingRight: spacing.sm,
  },

  benefitLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    lineHeight: typography.fontSize.base * 1.4, // Consistent line height
    fontFamily: typography.fontFamily.regular,
  },

  benefitNumber: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
  },

  benefitPrice: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
    lineHeight: typography.fontSize.base * 1.4, // Consistent line height
  },

  benefitAmount: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },

  // Additional benefits section
  additionalBenefitText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    textAlign: 'right',
  },

  // Total section styles
  totalSection: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    marginHorizontal: -spacing.md,
  },

  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
    marginHorizontal: spacing.sm,
  },

  totalMainRow: {
    marginBottom: spacing.lg,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },

  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  totalSubLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },

  totalPrice: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  totalSubPrice: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },

  submitButton: {
    marginHorizontal: 0,
  },

  // Selected Package Card Styles (matching DSGoiBHScreen)
  selectedPackageContainer: {
    borderRadius: borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.green,
  },

  selectedPackageGradient: {
    flex: 1,
  },

  selectedPackageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    padding: spacing.sm,
  },

  // Selected Package Logo
  selectedLogoContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    padding: spacing.sm,
  },

  selectedLogo: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },

  // Selected Package Info
  selectedPackageInfo: {
    flex: 1,
  },

  selectedPackageName: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
  },

  selectedTotalBenefit: {
    fontSize: typography.fontSize.xs,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
    fontFamily: typography.fontFamily.regular,
  },

  selectedBenefitAmount: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
  },

  // Selected Package Premium
  selectedPremiumContainer: {
    alignItems: 'flex-end',
  },

  selectedPremiumLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
    fontFamily: typography.fontFamily.regular,
  },

  selectedPremiumAmount: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  // Footer Buttons
  footerContainer: {},
  footerButtonContainer: {
    flexDirection: 'row',
    marginTop: spacing.md,
  },
  secondaryButton: {
    flex: 1,
    marginRight: 4,
  },
  secondaryButtonText: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
  primaryButton: {
    flex: 1,
    marginLeft: 4,
  },

  // Insurer info styles (matching ThongTinXeScreen)
  insurerItemContainer: {
    flexDirection: 'row',
    padding: spacing.sm + 3,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.xl,
    gap: spacing.sm,
    maxWidth: '49%',
  },
  insurerSelectedItem: {
    borderWidth: 1,
    borderColor: colors.green,
    backgroundColor: '#edf8eb',
  },
  insurerLogoWrapper: {
    width: 48,
    height: 48,
    padding: spacing.sm,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  insurerLogoImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  insurerLogoPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.gray[300],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
  },
  insurerLogoText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  insurerItemContent: {
    flex: 1,
    gap: spacing.xs,
  },
  insurerTenNhaBH: {
    color: colors.dark,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
  insurerStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  insurerStatusText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.regular,
  },
  secondaryButton1: {
    flex: 1,
    marginLeft: 4,
  },
  primaryButton1: {
    flex: 1.1,
    marginRight: 4,
  },
});
