import {PAGE_SIZE} from '@commons/Constant';
import {ConfirmModal, createToastHelpers, Icon, ScreenComponent, useToast} from '@components/common';
import Button from '@components/common/Button';
import Card from '@components/common/Card';
import DateTimePickerComponent from '@components/common/DateTimePickerComponent';
import {ACTION_CODE, CONFIG_SERVER} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import {MAIN_SCREENS} from '@navigation/routes';
import {getCommonExecute} from '@services/endpoints/commonEndpoints';
import {insurancePackageSelectionValidation, validateEndDate} from '@utils/validationSchemas';
import React, {useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {Image, Pressable, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Svg, {Path} from 'react-native-svg';
import {styles} from './Styles';
import {formatCurrency} from '@utils/formatters';

// Utility function để format date thành YYYYMMDD string
const formatDateToYYYYMMDD = (date: Date | string | undefined | null): string => {
  if (!date) return '';

  // Nếu đã là string YYYYMMDD format rồi thì return luôn
  if (typeof date === 'string' && /^\d{8}$/.test(date)) {
    return date;
  }

  let d: Date;
  if (date instanceof Date) {
    d = date;
  } else if (typeof date === 'string') {
    d = new Date(date);
  } else {
    d = new Date(date as any);
  }

  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

interface FormData {
  startDate: Date;
  endDate: Date;
  suicide: boolean;
  family_illness: boolean;
  accident_support: boolean;
  natural_death: boolean;
  accident_death: boolean;
  // Dynamic fields for additional benefits from API
  [key: string]: boolean | Date;
}

interface InsurancePackage {
  id: string;
  title: string;
  price: number;
  originalPrice: number;
  discount?: string;
  description: string;
  mua_nhieu?: string;
  logo: string;
  maQL: string;
}

// Pentagon Badge Component (pointed bottom)
const PentagonBadge = ({text, backgroundColor, textColor}: {text: string; backgroundColor: string; textColor: string}) => {
  return (
    <View style={{position: 'relative', width: 70, height: 26}}>
      <Svg height="26" width="70" style={{position: 'absolute', top: 0, left: 0}}>
        {/* Pentagon shape: top-left corner, top-right corner, bottom-right, bottom-center point, bottom-left */}
        <Path d="M4,0 L66,0 L66,18 L35,26 L4,18 Z" fill={backgroundColor} />
      </Svg>
      <View style={{alignItems: 'center', justifyContent: 'center', flex: 1, paddingBottom: 4}}>
        <Text style={{color: textColor, fontSize: 11}}>{text}</Text>
      </View>
    </View>
  );
};

export default function ChonGoiBHScreen({navigation, route}: any) {
  const fromAddInsured = route?.params?.fromAddInsured || false;
  const {insuredPersonData, formData, thongTinDonBH, editData} = route?.params;
  logger.log('🚀 ~ ChonGoiBHScreen ~ formData:', formData);
  logger.log('🚀 ~ ChonGoiBHScreen ~ insuredPersonData:', insuredPersonData);
  const isEditMode = !!editData;
  logger.log('🚀 ~ ChonGoiBHScreen ~ editData:', editData);
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  const {control, watch, setValue, getValues, handleSubmit} = useForm<FormData>({
    defaultValues: {
      startDate: new Date(),
      endDate: new Date(new Date().getTime() + 365 * 24 * 60 * 60 * 1000), // 1 year later
      suicide: false,
      family_illness: false,
      accident_support: false,
      natural_death: false,
      accident_death: false,
    },
  });

  const [selectedPackage, setSelectedPackage] = useState<string>('');
  const [showModalBack, setShowModalBack] = useState(false);
  const [insurancePackages, setInsurancePackages] = useState<InsurancePackage[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingPackageDetail, setLoadingPackageDetail] = useState(false);
  const [additionalBenefitsFromAPI, setAdditionalBenefitsFromAPI] = useState<any[]>([]);
  const [mainBenefitsFromAPI, setMainBenefitsFromAPI] = useState<any[]>([]);
  const [selectedAdditionalBenefits, setSelectedAdditionalBenefits] = useState<Set<string>>(new Set());
  const [hasLoadedPackages, setHasLoadedPackages] = useState(false);
  const [hasRestoredBenefits, setHasRestoredBenefits] = useState(false);

  // Watch all form values to trigger re-calculation
  const formValues = watch();
  const startDate = watch('startDate');

  // Effect: Tự động tính ngày kết thúc khi ngày bắt đầu thay đổi
  useEffect(() => {
    if (startDate && startDate instanceof Date && !isNaN(startDate.getTime())) {
      // Tính ngày kết thúc = ngày bắt đầu + 1 năm
      const endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1);
      setValue('endDate', endDate, {shouldValidate: true});
    }
  }, [startDate, setValue]);

  // Fetch danh sách gói bảo hiểm khi component mount hoặc khi đổi nhà bảo hiểm
  useEffect(() => {
    if (insuredPersonData || (thongTinDonBH && !hasLoadedPackages)) {
      getDanhSachGoiBaoHiem();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [insuredPersonData, thongTinDonBH, hasLoadedPackages, editData?.ma_doi_tac_ql, thongTinDonBH?.ma_doi_tac_ql]);

  // Fetch chi tiết gói khi selectedPackage thay đổi
  useEffect(() => {
    if ((selectedPackage && insuredPersonData) || thongTinDonBH) {
      // Reset selected benefits khi đổi gói (trừ khi đang ở edit mode lần đầu load)
      if (!isEditMode || hasLoadedPackages) {
        setSelectedAdditionalBenefits(new Set());
      }
      getChiTietGoiBaoHiemChon();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPackage]);

  // Set quyền lợi bổ sung đã chọn từ editData khi load chi tiết gói xong - CHỈ MỘT LẦN
  useEffect(() => {
    if (isEditMode && !hasRestoredBenefits && editData?.dkbs && Array.isArray(editData.dkbs) && additionalBenefitsFromAPI.length > 0) {
      logger.log('🚀 ~ Restoring selected benefits from editData.dkbs:', editData.dkbs);
      logger.log('🚀 ~ Available additionalBenefitsFromAPI:', additionalBenefitsFromAPI);
      // Tạo Set từ danh sách dkbs đã chọn trước đó
      const selectedIds = new Set<string>(editData.dkbs.map((item: any) => String(item.ma || item.id || item)));
      logger.log('🚀 ~ Setting selectedAdditionalBenefits to:', Array.from(selectedIds));
      setSelectedAdditionalBenefits(selectedIds);
      setHasRestoredBenefits(true); // Đánh dấu đã restore, không restore lại nữa
    }
  }, [isEditMode, hasRestoredBenefits, editData?.dkbs, additionalBenefitsFromAPI]);

  // Function để toggle additional benefit selection
  const toggleAdditionalBenefit = (benefitId: string) => {
    setSelectedAdditionalBenefits(prev => {
      const newSet = new Set(prev);
      if (newSet.has(benefitId)) {
        newSet.delete(benefitId);
      } else {
        newSet.add(benefitId);
      }
      return newSet;
    });
  };

  const serializeFormDataForNavigation = (data: FormData) => {
    return {
      ...data,
      ngay_sinh: editData.ngay_sinh ? formatDateToYYYYMMDD(editData.ngay_sinh) : undefined,
      ngay_hl: formatDateToYYYYMMDD(data.startDate),
      ngay_kt: formatDateToYYYYMMDD(data.endDate),
    };
  };

  const getDanhSachGoiBaoHiem = async () => {
    try {
      setLoading(true);
      let currentPage: number = 1;
      let pageSize: number = PAGE_SIZE;

      // Format ngày sinh từ data - có thể là Date object hoặc string YYYYMMDD
      const ngaySinhFormatted = formData?.ngay_sinh ? formatDateToYYYYMMDD(formData.ngay_sinh) : '';

      // Format ngày bắt đầu từ form
      const startDate = getValues('startDate');
      const startYear = startDate.getFullYear();
      const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');
      const startDay = String(startDate.getDate()).padStart(2, '0');
      const ngayBatDauFormatted = `${startYear}${startMonth}${startDay}`;

      const params = {
        ma_doi_tac_ql: editData?.ma_doi_tac_ql || thongTinDonBH?.ma_doi_tac_ql,
        so_id: formData?.so_id || insuredPersonData?.so_id || editData?.orderId || thongTinDonBH?.so_id,
        ma_sp: '',
        nv: 'NG',
        ngay_sinh: ngaySinhFormatted,
        gioi_tinh: formData?.gioi_tinh || '',
        ngay_hl: ngayBatDauFormatted,
        trang: currentPage,
        so_dong: pageSize,
        actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN_NGUOI_GOI_BH_PTRANG,
      };
      logger.log('🚀 ~ getDanhSachGoiBaoHiem ~ params:', params);

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ getDanhSachGoiBaoHiem ~ response:', response);

      if (response?.data?.data && Array.isArray(response.data?.data)) {
        const rows = response.data.data;
        const goiBaoHiem: InsurancePackage[] = [];

        // Tìm item có mua_nhieu = 'C' đầu tiên
        const muaNhieuIndex = rows.findIndex((item: any) => item.mua_nhieu === 'C');

        // Nếu có item mua_nhieu = 'C', đưa lên đầu
        if (muaNhieuIndex !== -1) {
          const muaNhieuItem = rows[muaNhieuIndex];
          // Xử lý logo URL cho item mua nhiều
          const muaNhieuLogoUrl = muaNhieuItem.logo ? (muaNhieuItem.logo.startsWith('http') ? muaNhieuItem.logo : `${CONFIG_SERVER.BASE_URL_API}${muaNhieuItem.logo}`) : null;

          goiBaoHiem.push({
            id: muaNhieuItem.id?.toString() || muaNhieuItem.ma || '1', // Dùng id hoặc ma của gói
            title: muaNhieuItem.ten || '',
            price: muaNhieuItem.tong_phi || 0, // Đúng field name từ API
            description: 'Tổng quyền lợi lên tới',
            originalPrice: muaNhieuItem.muc_tn_toi || '',
            mua_nhieu: muaNhieuItem.mua_nhieu || '',
            logo: muaNhieuLogoUrl,
            maQL: muaNhieuItem.ma_doi_tac_ql,
          });
        }

        // Thêm các item còn lại (không phải mua_nhieu = 'C')
        for (let i = 0; i < rows.length && goiBaoHiem.length < 3; i++) {
          if (i === muaNhieuIndex) continue; // Skip item đã thêm

          const item = rows[i];
          logger.log('🚀 ~ getDanhSachGoiBaoHiem ~ item: ', item);
          const logoUrl = item.logo ? (item.logo.startsWith('http') ? item.logo : `${CONFIG_SERVER.BASE_URL_API}${item.logo}`) : null;
          logger.log('🚀 ~ getDanhSachGoiBaoHiem ~ logoUrl: ', logoUrl);

          goiBaoHiem.push({
            id: item.id?.toString() || item.ma || String(goiBaoHiem.length + 1), // Dùng id hoặc ma của gói
            title: item.ten || `Gói ${goiBaoHiem.length + 1}`,
            price: item.tong_phi || 0, // Đúng field name từ API
            description: 'Tổng quyền lợi lên tới',
            originalPrice: item.muc_tn_toi || '',
            mua_nhieu: item.mua_nhieu || '',
            logo: logoUrl,
            maQL: item.ma_doi_tac_ql,
          });
        }

        if (goiBaoHiem.length > 0) {
          setInsurancePackages(goiBaoHiem);
          // Nếu ở edit mode và có soIdGoi thì chọn gói đó, không thì chọn gói đầu tiên
          let packageToSelect = goiBaoHiem[0].id;

          if (isEditMode && editData?.soIdGoi) {
            const foundPackage = goiBaoHiem.find(pkg => pkg.id === editData.soIdGoi?.toString());
            if (foundPackage) {
              packageToSelect = foundPackage.id;
            }
          }

          setSelectedPackage(packageToSelect);
          logger.log('🚀 ~ Selected package:', packageToSelect, 'from editData.soIdGoi:', editData?.soIdGoi);
          // Đánh dấu đã load xong
          setHasLoadedPackages(true);
        }
      }
    } catch (error) {
      console.error('Error getDanhSachGoiBaoHiem:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChiTietGoiBaoHiemChon = async () => {
    try {
      setLoadingPackageDetail(true);
      const params = {
        so_id: insuredPersonData?.so_id || thongTinDonBH?.so_id || 0,
        so_id_dt: insuredPersonData?.so_id_dt || thongTinDonBH?.so_id_dt || 0,
        id_goi_bh: selectedPackage, // ID của gói bảo hiểm đã chọn
        actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN_NGUOI_GOI_BH_LKE_CT,
      };

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ getChiTietGoiBaoHiemChon ~ response:', response);

      if (response?.data) {
        // Parse quyền lợi bổ sung từ data.dkbs
        const quyenLoiBoSung = response.data.dkbs || [];

        const additionalBenefitsData = quyenLoiBoSung.map((item: any) => ({
          id: item.ma_qloi || item.ma_quyen_loi || `additional_${item.ma_qloi}`,
          label: item.ten_qloi || item.ten_quyen_loi || '',
          price: item.phi_bh || 0,
          amount: item.gh_tien_nam || 0,
          originalSelected: item.qloi_chon || 0,
        }));
        setAdditionalBenefitsFromAPI(additionalBenefitsData);

        // Parse quyền lợi chính từ data.dk
        const quyenLoiChinh = response.data.dk || [];
        const mainBenefitsData = quyenLoiChinh.map((item: any) => ({
          id: item.ma_qloi || item.ma_quyen_loi || `main_${item.ma_qloi}`,
          label: item.ten_qloi || item.ten_quyen_loi || '',
          amount: item.gh_tien_nam || item.so_tien_bh || 0,
        }));
        setMainBenefitsFromAPI(mainBenefitsData);
      }

      return response;
    } catch (error) {
      console.error('Error getChiTietGoiBaoHiemChon:', error);
    } finally {
      setLoadingPackageDetail(false);
    }
  };

  // Function để lấy danh sách quyền lợi bổ sung được chọn
  const getSelectedAdditionalBenefits = () => {
    return additionalBenefitsFromAPI
      .filter(benefit => selectedAdditionalBenefits.has(benefit.id))
      .map(benefit => ({
        id: benefit.id,
        label: benefit.label,
        price: benefit.price,
        amount: benefit.amount,
        originalSelected: benefit.originalSelected,
      }));
  };

  // Function để xem tất cả những gì đang được chọn (có thể gọi bất cứ lúc nào)
  const getCurrentSelections = () => {
    const selectedPkg = insurancePackages.find(pkg => pkg.id === selectedPackage);
    const selectedAdditionalBenefits = getSelectedAdditionalBenefits();
    const formData = getValues();

    const currentSelections = {
      // Gói bảo hiểm chính
      mainPackage: selectedPkg
        ? {
            id: selectedPkg.id,
            name: selectedPkg.title,
            basePrice: selectedPkg.price,
            description: selectedPkg.description,
            originalPrice: selectedPkg.originalPrice,
          }
        : null,

      // Quyền lợi bổ sung được chọn
      additionalBenefits: selectedAdditionalBenefits,

      // Tổng số quyền lợi bổ sung được chọn
      totalAdditionalBenefitsCount: selectedAdditionalBenefits.length,

      // Tổng phí quyền lợi bổ sung
      totalAdditionalPrice: selectedAdditionalBenefits.reduce((sum, benefit) => sum + benefit.price, 0),

      // Thời gian tham gia
      duration: {
        startDate: formData.startDate,
        endDate: formData.endDate,
        startDateFormatted: formData.startDate.toLocaleDateString('vi-VN'),
        endDateFormatted: formData.endDate.toLocaleDateString('vi-VN'),
      },

      // Tổng phí bảo hiểm
      totalPrice: calculateTotalPrice(),
    };

    return currentSelections;
  };

  const getChiTietDonBH = async () => {
    try {
      const params = {
        so_id: insuredPersonData?.so_id || thongTinDonBH?.so_id,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_CHI_TIET_DON_BH,
      };

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ getChiTietDonBH ~ response:', response);
    } catch (error) {
      console.error('Error getChiTietDonBH:', error);
    }
  };

  useEffect(() => {
    getChiTietDonBH();
  }, []);

  const calculateTotalPrice = () => {
    const selectedPkg = insurancePackages.find(pkg => pkg.id === selectedPackage);
    const basePrice = selectedPkg ? selectedPkg.price : 0;

    const additionalPrice = additionalBenefitsFromAPI.reduce((sum, benefit) => {
      return selectedAdditionalBenefits.has(benefit.id) ? sum + benefit.price : sum;
    }, 0);

    return basePrice + additionalPrice;
  };

  const handleDetailBenefit = () => {
    navigation.navigate(MAIN_SCREENS.TOM_TAT_QUYEN_LOI, {
      mainBenefits: mainBenefitsFromAPI,
      additionalBenefits: additionalBenefitsFromAPI,
    });
  };

  const handleViewAll = () => {
    const data = getValues();
    navigation.navigate(MAIN_SCREENS.DS_GOI_BH, {
      so_id: formData.so_id,
      ngayBatDau: data.startDate,
      ngayKetThuc: data.endDate,
      ngaySinh: formData.ngay_sinh,
      gioiTinh: formData.gioi_tinh,
      ma_doi_tac_ql: '', // Không pass mã nhà bảo hiểm để hiển thị tất cả gói
      currentSelectedPackageId: selectedPackage, // Truyền ID gói đang chọn
      canSelectInsurer: false, // Không cho phép chọn nhà bảo hiểm khi xem tất cả
      onSelectPackage: (selectedPkg: InsurancePackage) => {
        // Cập nhật gói được chọn từ màn DSGoiBH
        // Tìm gói trong list hiện tại, nếu không có thì thêm vào
        const existingPkgIndex = insurancePackages.findIndex(pkg => pkg.id === selectedPkg.id);
        if (existingPkgIndex !== -1) {
          // Gói đã có trong list, chỉ cần set selected
          setSelectedPackage(selectedPkg.id);
        } else {
          // Gói mới, thêm vào list (giữ max 3 gói)
          const updatedPackages = [selectedPkg, ...insurancePackages.slice(0, 2)];
          setInsurancePackages(updatedPackages);
          setSelectedPackage(selectedPkg.id);
        }
      },
    });
  };

  const handleGoToOrderInfo = () => {
    // Navigate back to order information screen
    setShowModalBack(true);
  };

  const handleCloseBackModal = () => {
    setShowModalBack(false);
  };

  const handleConfirmBackModal = () => {
    // Navigate back to main screen
    navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH);
  };

  const onSubmit = async () => {
    try {
      // Lấy giá trị từ form
      const formValues = getValues();

      // Sử dụng giá trị từ form hoặc default
      const startDate = formValues.startDate || new Date();
      const endDate = formValues.endDate || new Date(new Date().getTime() + 365 * 24 * 60 * 60 * 1000);

      // Format ngày hiệu lực và ngày kết thúc
      const ngayHieuLuc = formatDateToYYYYMMDD(startDate);
      const ngayKetThuc = formatDateToYYYYMMDD(endDate);
      const selectedPkg = insurancePackages.find(pkg => pkg.id === selectedPackage);
      const phiBaoHiemChinh = selectedPkg ? selectedPkg.price : 0;

      // Chuẩn bị dkbs (quyền lợi bổ sung) đã chọn - chỉ cần mã trong object
      const dkbsMaArray = Array.from(selectedAdditionalBenefits).map(benefitId => ({
        ma: benefitId,
      }));

      // Nếu ở edit mode, dùng thông tin từ editData, nếu không dùng từ formData và insuredPersonData
      const baseData = isEditMode ? {...thongTinDonBH, ...formData} : formData;
      const soId = isEditMode ? editData?.orderId || insuredPersonData?.so_id || 0 : insuredPersonData?.so_id || 0;
      const soIdDt = isEditMode ? editData?.orderIdDt || insuredPersonData?.so_id_dt || 0 : insuredPersonData?.so_id_dt || 0;

      // Prepare params matching API
      const params = {
        ...baseData,
        // Override các field quan trọng sau để đảm bảo đúng
        so_id: soId,
        dchi_kh: thongTinDonBH?.dia_chi_kh,
        so_cmt_kh: thongTinDonBH?.cmt_kh,
        so_id_dt: soIdDt,
        ngay_hl: ngayHieuLuc, // YYYYMMDD format string
        ngay_kt: ngayKetThuc, // YYYYMMDD format string
        phi_bh: phiBaoHiemChinh,
        so_id_goi_bh: Number(selectedPackage), // Convert to number
        dkbs: dkbsMaArray, // Chỉ gửi array mã DKBS
        actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN,
      };
      logger.log('🚀 ~ onSubmit ~ params:', params);
      logger.log('🚀 ~ onSubmit ~ isEditMode:', isEditMode, 'soId:', soId, 'soIdDt:', soIdDt);

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ onSubmit ~ response:', response);

      if (response?.data) {
        toast.success('Lưu thông tin thành công');

        // Nếu từ chế độ edit, quay về màn ThongTinDonBH
        if (isEditMode) {
          setTimeout(() => {
            navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH, {
              needsRefresh: true,
              soID: editData.orderId,
            });
          }, 1000);
        } else {
          // Chuẩn bị thông tin đầy đủ để truyền sang màn hình tiếp theo
          const selectedBenefits = getSelectedAdditionalBenefits();

          navigation.popTo(MAIN_SCREENS.CAU_HOI_SK, {
            data: response?.output, // so_id, so_id_dt từ API response
            insuredPersonData: {
              ...formData, // Thông tin người tham gia (họ tên, ngày sinh, giới tính, etc.)
              ...insuredPersonData, // so_id, so_id_dt gốc (nếu có)
              // Thông tin gói bảo hiểm đã chọn
              so_id_goi_bh: Number(selectedPackage),
              // Thông tin quyền lợi bổ sung đã chọn
              dkbs: selectedBenefits,
              dchi_kh: thongTinDonBH?.dia_chi_kh || '',
              so_cmt_kh: thongTinDonBH?.cmt_kh || '',
              // Thông tin thời gian
              ngay_hl: formatDateToYYYYMMDD(formValues.startDate),
              ngay_kt: formatDateToYYYYMMDD(formValues.endDate),
              // Tổng phí
              phi_bh: calculateTotalPrice(),
            },
            thongTinDonBH: {
              ...thongTinDonBH,
              so_id_goi_bh: Number(selectedPackage),
              dkbs: selectedBenefits,
            },
            fromAddInsured,
            // Lưu trữ dữ liệu từ màn trước
            originalFormData: formData, // Dữ liệu gốc từ ThongTinNguoiThamGia
          });
        }
      } else {
        toast.error(response?.error_message || 'Có lỗi xảy ra', {
          position: 'top',
          duration: 3000,
        });
      }
    } catch (error: any) {
      console.error('❌ API error:', error);
      toast.error(error?.response?.data?.error_message || 'Có lỗi xảy ra khi lưu thông tin', {
        position: 'top',
        duration: 3000,
      });
    }
  };

  const handleEditCauHoi = () => {
    const data = getValues();
    const serializedData = serializeFormDataForNavigation(data);

    const dkbsMaArray = Array.from(selectedAdditionalBenefits).map(benefitId => ({
      ma: benefitId,
    }));

    const selectedBenefits = getSelectedAdditionalBenefits();

    navigation.navigate(MAIN_SCREENS.CAU_HOI_SK, {
      fromAddInsured,
      thongTinDonBH: {
        ...thongTinDonBH,
        so_id_goi_bh: Number(selectedPackage),
        dkbs: selectedBenefits,
      },
      formDataGoi: formData,
      data: {
        so_id: editData?.orderId || insuredPersonData?.so_id || thongTinDonBH?.so_id || 0,
        so_id_dt: editData?.orderIdDt || insuredPersonData?.so_id_dt || thongTinDonBH?.so_id_dt || 0,
      },
      editData: {
        ...editData,
        so_id_goi_bh: Number(selectedPackage),
        dkbs: dkbsMaArray, // Chỉ gửi array mã DKBS
        ma_doi_tac_ql: thongTinDonBH?.ma_doi_tac_ql,
      }, // Truyền editData có soIdGoi và dkbs
    });
  };

  const handleEditNhaBaoHiem = () => {
    const data = getValues();
    navigation.navigate(MAIN_SCREENS.DS_GOI_BH, {
      so_id: formData.so_id,
      ngayBatDau: data.startDate,
      ngayKetThuc: data.endDate,
      ngaySinh: formData.ngay_sinh,
      gioiTinh: formData.gioi_tinh,
      ma_doi_tac_ql: editData?.ma_doi_tac_ql || thongTinDonBH?.ma_doi_tac_ql,
      currentSelectedPackageId: selectedPackage,
      canSelectInsurer: true, // Flag để cho phép chọn nhà bảo hiểm
      onSelectPackage: async (selectedPkg: InsurancePackage) => {
        // Kiểm tra xem gói mới có thuộc nhà bảo hiểm khác không
        const currentInsurer = insurancePackages.find(pkg => pkg.id === selectedPackage)?.maQL;
        const newInsurer = selectedPkg.maQL;

        if (currentInsurer !== newInsurer) {
          // Đổi nhà bảo hiểm - cần reload tất cả gói của nhà bảo hiểm mới
          // Cập nhật ma_doi_tac_ql trong editData hoặc thongTinDonBH
          if (editData) {
            editData.ma_doi_tac_ql = newInsurer;
          }
          if (thongTinDonBH) {
            thongTinDonBH.ma_doi_tac_ql = newInsurer;
          }

          // Reset và load lại danh sách gói của nhà bảo hiểm mới
          setHasLoadedPackages(false);
          setSelectedPackage(selectedPkg.id);

          // Gọi lại API để lấy tất cả gói của nhà bảo hiểm mới
          await getDanhSachGoiBaoHiem();
        } else {
          // Cùng nhà bảo hiểm - chỉ cập nhật selectedPackage
          const existingPkgIndex = insurancePackages.findIndex(pkg => pkg.id === selectedPkg.id);
          if (existingPkgIndex !== -1) {
            setSelectedPackage(selectedPkg.id);
          } else {
            const updatedPackages = [selectedPkg, ...insurancePackages.slice(0, 2)];
            setInsurancePackages(updatedPackages);
            setSelectedPackage(selectedPkg.id);
          }
        }
      },
    });
  };

  return (
    <ScreenComponent
      dialogLoading={loading}
      showHeader
      headerTitle="Chọn gói bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        <View style={styles.footerContainer}>
          {/* Breakdown phí */}
          <View style={styles.totalRow}>
            <Text style={styles.totalSubLabel}>Phí bảo hiểm chính</Text>
            <Text style={styles.totalSubPrice}>{formatCurrency(insurancePackages.find(pkg => pkg.id === selectedPackage)?.price || 0)}</Text>
          </View>
          {/* Chỉ hiển thị dòng Quyền lợi bổ sung nếu có data */}
          {additionalBenefitsFromAPI.length > 0 && (
            <View style={styles.totalRow}>
              <Text style={styles.totalSubLabel}>Quyền lợi bổ sung</Text>
              <Text style={styles.totalSubPrice}>
                {formatCurrency(
                  additionalBenefitsFromAPI.reduce((sum, benefit) => {
                    return selectedAdditionalBenefits.has(benefit.id) ? sum + benefit.price : sum;
                  }, 0),
                )}
              </Text>
            </View>
          )}
          {/* Tổng cộng */}
          <View style={[styles.totalRow, styles.totalMainRow]}>
            <Text style={styles.totalLabel}>Tổng phí bảo hiểm</Text>
            <Text style={styles.totalPrice}>{formatCurrency(calculateTotalPrice())}</Text>
          </View>
          {fromAddInsured ? (
            <View style={styles.footerButtonContainer}>
              <Button style={styles.secondaryButton} onPress={handleGoToOrderInfo} title={'Đơn bảo hiểm'} variant={'outline'} />
              <Button title="Chọn gói" onPress={handleSubmit(onSubmit)} style={styles.primaryButton} />
            </View>
          ) : (
            <View style={styles.footerButtonContainer}>
              <Button title="Chọn gói" onPress={handleSubmit(onSubmit)} style={styles.primaryButton1} />
              {isEditMode && <Button style={styles.secondaryButton1} onPress={handleEditCauHoi} title={'Tiếp tục'} variant={'outline'} />}
            </View>
          )}
        </View>
      }>
      <ScrollView style={styles.container} contentContainerStyle={{paddingBottom: spacing.sm}} showsVerticalScrollIndicator={false}>
        {/* Thời gian tham gia */}
        <Card title="Thời gian tham gia">
          <View style={styles.dateRow}>
            <View style={styles.dateField}>
              <DateTimePickerComponent control={control} name="startDate" label="Ngày bắt đầu" required rules={insurancePackageSelectionValidation.startDate} />
            </View>
            <View style={styles.dateField}>
              <DateTimePickerComponent
                control={control}
                name="endDate"
                label="Ngày kết thúc"
                disabled
                required
                rules={{
                  ...insurancePackageSelectionValidation.endDate,
                  validate: validateEndDate(getValues),
                }}
              />
            </View>
          </View>
        </Card>

        {/* Các gói phù hợp */}
        <Card>
          <View style={styles.sectionHeader}>
            <Text style={styles.cardTitle}>Các gói phù hợp</Text>
            <TouchableOpacity onPress={handleViewAll}>
              <Text style={styles.sectionSubtitle}>Xem tất cả</Text>
            </TouchableOpacity>
          </View>
          {loading ? (
            <View style={{padding: spacing.md, alignItems: 'center'}}>
              <Text>Đang tải danh sách gói bảo hiểm...</Text>
            </View>
          ) : insurancePackages.length === 0 ? (
            <View style={{padding: spacing.md, alignItems: 'center'}}>
              <Text style={{color: colors.gray[500]}}>Chưa có gói bảo hiểm phù hợp</Text>
            </View>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.packagesScroll}>
              {insurancePackages.map((pkg, index) => (
                <TouchableOpacity
                  key={pkg.id}
                  style={[styles.packageCard, selectedPackage === pkg.id && styles.selectedPackageCard, index === 0 && styles.firstPackageCard]}
                  onPress={() => setSelectedPackage(pkg.id)}>
                  {selectedPackage === pkg.id ? (
                    <LinearGradient
                      colors={['#D9EAC5', '#96BF49']}
                      start={{x: 0.5, y: 0}} // giữa-trên
                      end={{x: 0.5, y: 1}}
                      style={styles.gradientWrapper}>
                      <View style={styles.packageHeader}>
                        {pkg.logo ? (
                          <View style={styles.logoContainer}>
                            <Image source={{uri: pkg.logo}} style={styles.selectedLogo} />
                          </View>
                        ) : (
                          <View style={styles.logoPlaceholderOther}>
                            <Text style={styles.textPlaceholder}>{pkg?.maQL}</Text>
                          </View>
                        )}

                        {pkg.mua_nhieu === 'C' && (
                          <View style={{position: 'absolute', top: 0, right: 0, marginRight: -4}}>
                            <PentagonBadge text="Mua nhiều" backgroundColor={colors.yellow} textColor={colors.danger} />
                          </View>
                        )}
                      </View>
                      <Text style={[styles.packageTitle]}>{pkg.title}</Text>
                      <Text style={[styles.packageDescription, {color: colors.danger}]}>{pkg.description}</Text>
                      <Text style={[styles.originalPrice, {color: colors.danger}]}>{formatCurrency(pkg.originalPrice)}</Text>

                      <Text style={[styles.packagePrice]}>{formatCurrency(pkg.price)}</Text>
                    </LinearGradient>
                  ) : (
                    <View>
                      <View style={styles.packageHeader}>
                        {pkg.logo ? (
                          <View style={styles.logoContainer}>
                            <Image source={{uri: pkg.logo}} style={styles.selectedLogo} resizeMode="contain" />
                          </View>
                        ) : (
                          <View style={styles.logoPlaceholderOther}>
                            <Text style={styles.textPlaceholder}>{pkg?.maQL}</Text>
                          </View>
                        )}

                        {pkg.mua_nhieu === 'C' && (
                          <View style={{position: 'absolute', top: 0, right: 0, marginRight: -4}}>
                            <PentagonBadge text="Mua nhiều" backgroundColor={colors.yellow} textColor={colors.danger} />
                          </View>
                        )}
                      </View>
                      <Text style={styles.packageTitle}>{pkg.title}</Text>
                      <Text style={styles.packageDescription}>{pkg.description}</Text>
                      <Text style={styles.originalPrice}>{formatCurrency(pkg.originalPrice)}</Text>
                      <Text style={styles.packagePrice}>{formatCurrency(pkg.price)}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </Card>

        {/* Nhà bảo hiểm - Chỉ hiển thị ở chế độ edit */}
        {isEditMode && (
          <Card>
            <View style={styles.sectionHeader}>
              <Text style={styles.cardTitle}>Nhà bảo hiểm</Text>
              <TouchableOpacity onPress={handleEditNhaBaoHiem}>
                <Text style={styles.sectionSubtitle}>Thay đổi</Text>
              </TouchableOpacity>
            </View>
            <View style={[styles.insurerItemContainer, styles.insurerSelectedItem]}>
              {insurancePackages.find(pkg => pkg.id === selectedPackage)?.logo ? (
                <View style={styles.insurerLogoWrapper}>
                  <Image source={{uri: insurancePackages.find(pkg => pkg.id === selectedPackage)?.logo}} style={styles.insurerLogoImage} resizeMode="contain" />
                </View>
              ) : (
                <View style={styles.insurerLogoPlaceholder}>
                  <Text style={styles.insurerLogoText}>{insurancePackages.find(pkg => pkg.id === selectedPackage)?.maQL || editData?.ma_doi_tac_ql || thongTinDonBH?.ma_doi_tac_ql}</Text>
                </View>
              )}
              <View style={styles.insurerItemContent}>
                <Text style={styles.insurerTenNhaBH}>{insurancePackages.find(pkg => pkg.id === selectedPackage)?.maQL || editData?.ma_doi_tac_ql || thongTinDonBH?.ma_doi_tac_ql}</Text>
                <View style={styles.insurerStatusBadge}>
                  <Text style={styles.insurerStatusText}>Xem chi tiết</Text>
                </View>
              </View>
            </View>
          </Card>
        )}

        {/* Gói đang chọn */}
        <Card>
          <View style={styles.sectionHeader}>
            <Text style={styles.cardTitle}>Gói đang chọn</Text>
          </View>
          {insurancePackages.length === 0 ? (
            <View style={{padding: spacing.md, alignItems: 'center'}}>
              <Text style={{color: colors.gray[500]}}>Chưa có gói bảo hiểm được chọn</Text>
            </View>
          ) : (
            <View style={styles.selectedPackageContainer}>
              <LinearGradient colors={['#D9EAC5', '#96BF49']} start={{x: 0, y: 0.5}} end={{x: 1, y: 0.5}} style={styles.selectedPackageGradient}>
                <View style={styles.selectedPackageContent}>
                  {/* Logo */}
                  {insurancePackages.find(pkg => pkg.id === selectedPackage)?.logo ? (
                    <View style={styles.selectedLogoContainer}>
                      <Image source={{uri: insurancePackages.find(pkg => pkg.id === selectedPackage)?.logo}} style={styles.selectedLogo} resizeMode="contain" />
                    </View>
                  ) : (
                    <View style={styles.logoPlaceholder}>
                      <Text style={styles.textPlaceholder}>{insurancePackages.find(pkg => pkg.id === selectedPackage)?.maQL}</Text>
                    </View>
                  )}

                  {/* Info */}
                  <View style={styles.selectedPackageInfo}>
                    <Text style={styles.selectedPackageName}>{insurancePackages.find(pkg => pkg.id === selectedPackage)?.title}</Text>
                    <Text style={styles.selectedTotalBenefit}>{insurancePackages.find(pkg => pkg.id === selectedPackage)?.description}</Text>
                    <Text style={styles.selectedBenefitAmount}>{formatCurrency(insurancePackages.find(pkg => pkg.id === selectedPackage)?.originalPrice || 0)}</Text>
                  </View>

                  {/* Premium */}
                  <View style={styles.selectedPremiumContainer}>
                    <Text style={styles.selectedPremiumLabel}>Phí bảo hiểm</Text>
                    <Text style={styles.selectedPremiumAmount}>{formatCurrency(insurancePackages.find(pkg => pkg.id === selectedPackage)?.price || 0)}</Text>
                  </View>
                </View>
              </LinearGradient>
            </View>
          )}
        </Card>

        {/* Tóm tắt quyền lợi */}
        <Pressable onPress={handleDetailBenefit}>
          <Card style={styles.card}>
            <Icon name={'Book'} color={colors.green} size={20} variant={'Bold'} />
            <Text style={styles.cardTitle}>Tóm tắt quyền lợi</Text>
          </Card>
        </Pressable>

        {/* Quyền lợi bổ sung - Chỉ hiển thị khi có data */}
        {!loadingPackageDetail && additionalBenefitsFromAPI.length > 0 && (
          <Card>
            <View style={styles.sectionHeader}>
              <Text style={[styles.cardTitle, {flex: 3}]}>Quyền lợi bổ sung</Text>
              <Text style={[styles.cardTitle, {flex: 2, textAlign: 'center'}]}>Phí bảo hiểm</Text>
              <Text style={[styles.cardTitle, {flex: 1, textAlign: 'center'}]}>Chọn</Text>
            </View>
            {additionalBenefitsFromAPI.map((benefit, index) => (
              <View key={benefit.id} style={styles.benefitRow}>
                <View style={{flex: 3, paddingRight: spacing.sm}}>
                  <Text style={styles.benefitLabel}>
                    {index + 1}. {benefit.label}
                  </Text>
                </View>
                <View style={{flex: 2, alignItems: 'center', paddingRight: spacing.sm}}>
                  <Text style={[styles.benefitPrice, {marginTop: 1}]}>{formatCurrency(benefit.price)}</Text>
                </View>
                <View style={{flex: 1, alignItems: 'center', marginRight: -3}}>
                  <TouchableOpacity
                    onPress={() => toggleAdditionalBenefit(benefit.id)}
                    style={{
                      width: 22,
                      height: 22,
                      borderRadius: 8,
                      borderWidth: 1,
                      borderColor: selectedAdditionalBenefits.has(benefit.id) ? colors.green : colors.gray[400],
                      backgroundColor: selectedAdditionalBenefits.has(benefit.id) ? colors.green : colors.white,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginTop: -3,
                    }}
                    activeOpacity={0.7}>
                    {selectedAdditionalBenefits.has(benefit.id) && (
                      <View
                        style={{
                          width: 8,
                          height: 4,
                          borderLeftWidth: 2,
                          borderBottomWidth: 2,
                          borderColor: colors.white,
                          transform: [{rotate: '-45deg'}],
                          marginTop: -1,
                        }}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </Card>
        )}

        {/* Quyền lợi chính */}
        <Card>
          <View style={[styles.sectionHeader, {justifyContent: 'space-between'}]}>
            <Text style={styles.cardTitle}>Quyền lợi chính</Text>
            <Text style={styles.cardTitle}>Hạn mức bảo vệ</Text>
          </View>
          {loadingPackageDetail ? (
            <View style={{padding: spacing.md, alignItems: 'center'}}>
              <Text style={{color: colors.gray[500]}}>Đang tải thông tin quyền lợi...</Text>
            </View>
          ) : mainBenefitsFromAPI.length === 0 ? (
            <View style={{padding: spacing.md, alignItems: 'center'}}>
              <Text style={{color: colors.gray[500]}}>Chưa có quyền lợi chính</Text>
            </View>
          ) : (
            mainBenefitsFromAPI.map((benefit, index) => (
              <View key={benefit.id} style={[styles.benefitRow, {justifyContent: 'space-between'}]}>
                <Text style={[styles.benefitLabel, {maxWidth: '50%'}]}>
                  {index + 1}. {benefit.label}
                </Text>
                <Text style={[styles.benefitAmount, {marginTop: 1}]}>{formatCurrency(benefit.amount)}</Text>
              </View>
            ))
          )}
        </Card>
      </ScrollView>

      <ConfirmModal
        visible={showModalBack}
        onClose={handleCloseBackModal}
        title={'Thông báo'}
        message={'Bạn có quay trở lại đơn bảo hiểm?'}
        onConfirm={handleConfirmBackModal}
        onCancel={handleCloseBackModal}
        confirmText={'Đồng ý'}
        cancelText={'Không'}
      />
    </ScreenComponent>
  );
}
