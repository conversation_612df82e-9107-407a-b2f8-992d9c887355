import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {FlatList, Image, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import {Button, Card, ConfirmModal, Loading, ScreenComponent, TextField} from '@components/common';
import Icon from '@components/common/Icon';
import LinearGradient from 'react-native-linear-gradient';
import {colors, spacing} from '@constants/theme';
import {styles} from './Styles';
import {MAIN_SCREENS} from '@navigation/routes';
import {ACTION_CODE, CONFIG_SERVER} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints/commonEndpoints';
import {PAGE_SIZE} from '@commons/Constant';
import {formatCurrencyPlain} from '@utils/currencyFormatter';
import {useSelector} from 'react-redux';
import {RootState} from '@store/index';

interface InsurancePackage {
  id: string;
  provider: string;
  logo: any;
  packageName: string;
  totalBenefit: string;
  premium: number;
  badge?: 'popular' | 'hot';
  mua_nhieu?: string;
  maQL?: string;
}

interface LoadMoreState {
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

// Utility function để format date thành YYYYMMDD string
const formatDateToYYYYMMDD = (date: Date | string | undefined | null): string => {
  if (!date) return '';

  // Nếu đã là string YYYYMMDD format rồi thì return luôn
  if (typeof date === 'string' && /^\d{8}$/.test(date)) {
    return date;
  }

  let d: Date;
  if (date instanceof Date) {
    d = date;
  } else if (typeof date === 'string') {
    d = new Date(date);
  } else {
    d = new Date(date as any);
  }

  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

export default function DSGoiBHScreen({navigation, route}: any) {
  const {so_id, ngayBatDau, ngaySinh, gioiTinh, onSelectPackage, currentSelectedPackageId, insuredPersonData, ma_doi_tac_ql, canSelectInsurer} = route?.params || {};
  console.log('🚀 ~ DSGoiBHScreen ~ ma_doi_tac_ql:', ma_doi_tac_ql);
  console.log('🚀 ~ DSGoiBHScreen ~ canSelectInsurer:', canSelectInsurer);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPackageId, setSelectedPackageId] = useState<string>('');
  const [selectedInsurerId, setSelectedInsurerId] = useState<string>(ma_doi_tac_ql || '');
  const [showFilter, setShowFilter] = useState(false);
  const [packages, setPackages] = useState<InsurancePackage[]>([]);
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const [loadingDetail, setLoadingDetail] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const danhSachNhaBaoHiemSucKhoe = useSelector((state: RootState) => state.commonCategories.danhSachNhaBaoHiemSucKhoe);
  console.log('🚀 ~ DSGoiBHScreen ~ danhSachNhaBaoHiemSucKhoe:', danhSachNhaBaoHiemSucKhoe);

  // LoadMoreState for package list
  const [loadMoreState, setLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: PAGE_SIZE,
  });

  // Fetch danh sách gói bảo hiểm khi component mount hoặc khi selectedInsurerId thay đổi
  useEffect(() => {
    // Load gói bảo hiểm ngay cả khi selectedInsurerId rỗng (để hiển thị tất cả)
    getDanhSachGoiBaoHiem(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedInsurerId]);

  // Set selected package khi có currentSelectedPackageId từ ChonGoiBH
  useEffect(() => {
    if (currentSelectedPackageId && packages.length > 0) {
      // Kiểm tra xem gói đang chọn có trong danh sách không
      const packageExists = packages.some(pkg => pkg.id === currentSelectedPackageId);
      if (packageExists) {
        setSelectedPackageId(currentSelectedPackageId);
      } else if (packages.length > 0) {
        // Nếu không có, chọn gói đầu tiên
        setSelectedPackageId(packages[0].id);
      }
    }
  }, [currentSelectedPackageId, packages]);

  const handleSelectInsurer = useCallback((insurerId: string) => {
    setSelectedInsurerId(insurerId);
    // Reset selected package khi đổi nhà bảo hiểm
    setSelectedPackageId('');
  }, []);

  const renderNhaBaoHiemItem = useCallback(
    ({uriLogo, ten, ma_doi_tac_ql, selected, onPress}: any) => (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        <View style={[styles.itemContainer, selected && styles.selectedItem]}>
          {uriLogo ? (
            <View style={styles.logoContainer}>
              <Image source={{uri: uriLogo}} style={styles.logoItem} />
            </View>
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.textPlaceholder}>{ma_doi_tac_ql}</Text>
            </View>
          )}
          <View style={styles.itemContent}>
            <Text style={styles.tenNhaBH}>{ten}</Text>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Text style={styles.textChiTiet}>Xem chi tiết</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    ),
    [],
  );

  const nhaBaoHiemList = useMemo(
    () =>
      (danhSachNhaBaoHiemSucKhoe || []).map((it: any) => ({
        id: it.ma,
        ma_doi_tac_ql: it.ma,
        uriLogo: it.logo ? (it.logo.startsWith('http') ? it.logo : `${CONFIG_SERVER.BASE_URL_API}${it.logo}`) : null,
        ten: it.ma,
      })),
    [danhSachNhaBaoHiemSucKhoe],
  );

  const getDanhSachGoiBaoHiem = useCallback(
    async (isLoadMore: boolean = false) => {
      try {
        // Get current values from state
        let currentPage: number = 1;
        let pageSize: number = PAGE_SIZE;

        setLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize;
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        // Format ngày sinh từ params
        const ngaySinhFormatted = ngaySinh ? formatDateToYYYYMMDD(ngaySinh) : '';

        // Format ngày bắt đầu từ params
        const ngayBatDauFormatted = ngayBatDau ? formatDateToYYYYMMDD(ngayBatDau) : '';

        const params = {
          ma_doi_tac_ql: selectedInsurerId || '', // Nếu empty thì API sẽ trả về tất cả gói
          so_id: so_id,
          ma_sp: '',
          nv: 'NG',
          ngay_sinh: ngaySinhFormatted,
          gioi_tinh: gioiTinh || '',
          ngay_hl: ngayBatDauFormatted,
          trang: currentPage,
          so_dong: pageSize,
          actionCode: ACTION_CODE.PTVV_BH_HD_TIEP_NHAN_NGUOI_GOI_BH_PTRANG,
        };

        const response = await getCommonExecute(params);
        console.log('🚀 ~ getDanhSachGoiBaoHiem ~ response:', response);

        if (response?.data?.data && Array.isArray(response.data?.data)) {
          const rows = response.data.data;
          const totalItems = response.data.tong_so_dong || rows.length;

          const goiBaoHiem: InsurancePackage[] = rows.map((item: any) => ({
            id: item.id?.toString() || item.ma || '',
            maQL: item?.ma_doi_tac_ql,
            logo: item?.logo,
            packageName: item.ten || '',
            totalBenefit: item.muc_tn_toi || '',
            premium: item.tong_phi || 0,
            badge: item.mua_nhieu === 'C' ? 'popular' : undefined,
            mua_nhieu: item.mua_nhieu || '',
          }));

          setPackages(prev => {
            if (isLoadMore) {
              // Merge data for load more
              return [...prev, ...goiBaoHiem];
            } else {
              // Replace data for refresh
              return goiBaoHiem;
            }
          });

          // Set selected package dựa vào currentSelectedPackageId hoặc gói đầu tiên (chỉ khi không loadMore)
          if (!isLoadMore && goiBaoHiem.length > 0) {
            if (currentSelectedPackageId) {
              const packageExists = goiBaoHiem.some(pkg => pkg.id === currentSelectedPackageId);
              setSelectedPackageId(packageExists ? currentSelectedPackageId : goiBaoHiem[0].id);
            } else {
              setSelectedPackageId(goiBaoHiem[0].id);
            }
          }

          setLoadMoreState(prev => {
            const hasMoreItems = currentPage * pageSize < totalItems;

            return {
              ...prev,
              isLoading: false,
              isLoadingMore: false,
              currentPage: currentPage,
              totalItems: totalItems,
              hasMore: hasMoreItems,
            };
          });
        }
      } catch (error) {
        console.error('Error getDanhSachGoiBaoHiem:', error);
        setLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      }
    },
    [ngaySinh, ngayBatDau, gioiTinh, currentSelectedPackageId, selectedInsurerId],
  );

  // Load more handler
  const handleLoadMore = useCallback(() => {
    if (!loadMoreState.isLoadingMore && loadMoreState.hasMore && !loadMoreState.isLoading) {
      getDanhSachGoiBaoHiem(true);
    }
  }, [loadMoreState.isLoadingMore, loadMoreState.hasMore, loadMoreState.isLoading, getDanhSachGoiBaoHiem]);

  // Refresh handler
  const onRefreshData = useCallback(() => {
    setIsPullToRefresh(true);
    getDanhSachGoiBaoHiem(false).then(() => {
      setIsPullToRefresh(false);
    });
  }, [getDanhSachGoiBaoHiem]);

  const handleSelectPackage = (packageId: string) => {
    setSelectedPackageId(packageId);
  };

  const handleViewDetails = () => {
    if (selectedPackageId) {
      console.log('View details for package:', selectedPackageId);
      // Navigate to details screen
      navigation.navigate(MAIN_SCREENS.THONG_TIN_SAN_PHAM);
    }
  };

  const handleChoosePackage = () => {
    if (selectedPackageId) {
      // Kiểm tra xem có đang thay đổi nhà bảo hiểm không
      // Chỉ check khi canSelectInsurer === true và có cả ma_doi_tac_ql (không phải xem tất cả)
      const isChangingInsurer = canSelectInsurer && ma_doi_tac_ql && selectedInsurerId && selectedInsurerId !== ma_doi_tac_ql;

      if (isChangingInsurer) {
        // Hiển thị confirm modal nếu đang thay đổi nhà bảo hiểm
        setShowConfirmModal(true);
      } else {
        // Nếu không thay đổi nhà bảo hiểm, thực hiện chọn gói luôn
        confirmChoosePackage();
      }
    }
  };

  const confirmChoosePackage = () => {
    const selectedPkg = packages.find(pkg => pkg.id === selectedPackageId);
    if (selectedPkg && onSelectPackage) {
      // Xử lý logo URL
      const logoUrl = selectedPkg.logo ? (selectedPkg.logo.startsWith('http') ? selectedPkg.logo : `${CONFIG_SERVER.BASE_URL_API}${selectedPkg.logo}`) : null;

      // Callback để cập nhật gói trong màn ChonGoiBH
      onSelectPackage({
        id: selectedPkg.id,
        title: selectedPkg.packageName,
        price: selectedPkg.premium,
        description: 'Tổng quyền lợi lên tới',
        originalPrice: selectedPkg.totalBenefit,
        mua_nhieu: selectedPkg.mua_nhieu,
        logo: logoUrl,
        maQL: selectedPkg.maQL,
      });
    }
    // Đóng modal và quay về màn trước
    setShowConfirmModal(false);
    navigation.goBack();
  };

  const renderPackageItem = ({item}: {item: InsurancePackage}) => {
    console.log('🚀 ~ renderPackageItem ~ item:', item);
    const isSelected = selectedPackageId === item.id;

    // Tạo full URL cho logo
    const logoUrl = item.logo ? (item.logo.startsWith('http') ? item.logo : `${CONFIG_SERVER.BASE_URL_API}${item.logo}`) : null;

    return (
      <TouchableOpacity style={styles.packageItem} onPress={() => handleSelectPackage(item.id)} activeOpacity={0.7}>
        {/* Content Wrapper */}
        {isSelected ? (
          <View style={styles.gradientContainer}>
            <LinearGradient colors={['#D9EAC5', '#96BF49']} start={{x: 0, y: 0.5}} end={{x: 1, y: 0.5}} style={styles.gradientWrapper}>
              <View style={styles.packageContent}>
                {/* Logo */}
                {logoUrl ? (
                  <View style={[styles.logoContainer, styles.logoContainerSelected]}>
                    <Image source={{uri: logoUrl}} style={styles.logo} resizeMode="contain" />
                  </View>
                ) : (
                  <View style={styles.logoPlaceholder}>
                    <Text style={styles.textPlaceholder}>{item?.maQL}</Text>
                  </View>
                )}

                {/* Info */}
                <View style={styles.packageInfo}>
                  <Text style={[styles.packageName, styles.packageNameSelected]}>{item.packageName}</Text>
                  <Text style={[styles.totalBenefit, styles.textSelected]}>Tổng quyền lợi tối đa</Text>
                  <Text style={[styles.benefitAmount, styles.textSelected]}>{item.totalBenefit}</Text>
                </View>

                {/* Premium */}
                <View style={styles.premiumContainer}>
                  <Text style={[styles.premiumLabel, styles.textSelected]}>Phí bảo hiểm</Text>
                  <Text style={[styles.premiumAmount, styles.premiumAmountSelected]}>{formatCurrencyPlain(item.premium)}đ</Text>
                </View>
              </View>
            </LinearGradient>
          </View>
        ) : (
          <View style={styles.packageContent}>
            {/* Logo */}
            {item.logo ? (
              <View style={styles.logoContainer}>
                <Image source={{uri: logoUrl}} style={styles.logo} resizeMode="contain" />
              </View>
            ) : (
              <View style={styles.logoPlaceholder}>
                <Text style={styles.textPlaceholder}>{item?.maQL}</Text>
              </View>
            )}

            {/* Info */}
            <View style={styles.packageInfo}>
              <Text style={styles.packageName}>{item.packageName}</Text>
              <Text style={styles.totalBenefit}>Tổng quyền lợi tối đa</Text>
              <Text style={styles.benefitAmount}>{item.totalBenefit}</Text>
            </View>

            {/* Premium */}
            <View style={styles.premiumContainer}>
              <Text style={styles.premiumLabel}>Phí bảo hiểm</Text>
              <Text style={styles.premiumAmount}>{formatCurrencyPlain(item.premium)}đ</Text>
            </View>
          </View>
        )}

        {/* Badge - Render after content to ensure it's on top */}
        {item.badge && (
          <View style={styles.badgeContainer}>
            <View style={[styles.badge, item.badge === 'popular' ? styles.badgePopular : styles.badgeHot]}>
              <Text style={styles.badgeText}>{item.badge === 'popular' ? 'Mua nhiều' : 'Hot'}</Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={loadMoreState.isLoading && !loadMoreState.isLoadingMore}
      showHeader
      bodyStyle={styles.container}
      headerTitle="Danh sách gói bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={
        <View style={styles.footer}>
          <TouchableOpacity style={styles.detailButton} onPress={handleViewDetails} disabled={!selectedPackageId}>
            <Text style={[styles.detailButtonText, !selectedPackageId && styles.buttonTextDisabled]}>Xem chi tiết</Text>
          </TouchableOpacity>
          <Button title="Chọn gói" onPress={handleChoosePackage} disabled={!selectedPackageId} style={styles.chooseButton} />
        </View>
      }>
      <View>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextField
            placeholder="Tìm kiếm"
            showPlaceholderWhenEmpty={true}
            value={searchQuery}
            onChangeText={setSearchQuery}
            containerStyle={styles.searchInputContainer}
            rightIconType={'search'}
            inputContainerStyle={styles.searchInput}
          />

          <TouchableOpacity style={styles.filterButton} onPress={() => setShowFilter(!showFilter)}>
            <Icon name="Filter" size={24} color={colors.green} />
          </TouchableOpacity>
        </View>

        {/* Card Nhà bảo hiểm - Chỉ hiển thị khi canSelectInsurer === true */}
        {canSelectInsurer && (
          <Card title={'Nhà bảo hiểm'} style={styles.cardNhaBH}>
            <FlatList
              data={nhaBaoHiemList}
              numColumns={2}
              scrollEnabled={false}
              keyExtractor={item => item.id}
              ItemSeparatorComponent={() => <View style={{height: spacing.sm}} />}
              columnWrapperStyle={styles.nhaBHContainer}
              contentContainerStyle={{paddingBottom: 4}}
              renderItem={({item}) => (
                <View style={styles.nhaBHItem}>
                  {renderNhaBaoHiemItem({
                    uriLogo: item.uriLogo,
                    ten: item.ten,
                    ma_doi_tac_ql: item.ma_doi_tac_ql,
                    selected: selectedInsurerId === item.id,
                    onPress: () => handleSelectInsurer(item.id),
                  })}
                </View>
              )}
            />
          </Card>
        )}

        {/* Package List */}
        <FlatList
          data={packages}
          renderItem={renderPackageItem}
          keyExtractor={item => item.id}
          contentContainerStyle={packages.length === 0 ? {paddingTop: 20} : styles.listContent}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          ListEmptyComponent={() => (
            <View style={styles.listContent}>
              <Text style={{textAlign: 'center', marginTop: 20, color: colors.gray[500]}}>Không tìm thấy gói bảo hiểm phù hợp</Text>
            </View>
          )}
          refreshControl={
            <RefreshControl refreshing={isPullToRefresh && loadMoreState.isLoading && !loadMoreState.isLoadingMore} onRefresh={onRefreshData} colors={[colors.primary]} tintColor={colors.primary} />
          }
          onEndReachedThreshold={0.1}
          onEndReached={handleLoadMore}
          ListFooterComponent={() => {
            if (loadMoreState.isLoadingMore) {
              return (
                <View style={{paddingVertical: 20, alignItems: 'center'}}>
                  <Loading size="small" message={'Đang tải thêm...'} />
                </View>
              );
            }
            if (!loadMoreState.hasMore && packages.length > 0) {
              return (
                <View style={{paddingVertical: 20, alignItems: 'center'}}>
                  <Text style={{fontSize: 14, fontWeight: '600', color: colors.gray[700]}}>Đã hiển thị hết</Text>
                  <Text style={{fontSize: 12, color: colors.gray[500], marginTop: 4}}>Bạn đã xem tất cả {packages.length} gói bảo hiểm</Text>
                </View>
              );
            }
            return null;
          }}
        />
      </View>

      <ConfirmModal
        visible={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Thông báo"
        message="Thay đổi nhà bảo hiểm sẽ phải chọn lại
        toàn bộ các gói của NĐBH trong đơn."
        confirmText="Đồng ý"
        cancelText="Hủy"
        onConfirm={confirmChoosePackage}
        onCancel={() => setShowConfirmModal(false)}
      />
    </ScreenComponent>
  );
}
