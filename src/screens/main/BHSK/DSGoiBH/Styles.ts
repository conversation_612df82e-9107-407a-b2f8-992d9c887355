import {StyleSheet} from 'react-native';
import {borderRadius, colors, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  logoPlaceholder: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[400],
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    padding: spacing.xs,
  },
  textPlaceholder: {
    color: colors.gray[600],
    fontSize: typography.fontSize.sm,
  },
  // Search Bar
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
  },
  searchInputContainer: {
    flex: 1,
  },
  searchInput: {
    height: 50,
  },
  searchIcon: {
    padding: spacing.xs,
    position: 'absolute',
    right: 60, // Position to the right of TextField
    top: '50%',
    transform: [{translateY: -10}],
  },
  filterButton: {
    padding: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: colors.gray[400],
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  // List
  listContent: {
    paddingHorizontal: spacing.sm,
    paddingTop: spacing.md,
    paddingBottom: spacing['3xl'],
  },
  separator: {
    height: spacing.sm,
  },
  // Package Item
  packageItem: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[200],
    position: 'relative',
    marginHorizontal: spacing.xs,
    marginBottom: 6,
    // Remove overflow hidden from main container to show badge
  },
  gradientContainer: {
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.green,
  },
  gradientWrapper: {
    flex: 1,
  },
  packageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    padding: spacing.sm,
  },
  // Badge
  badgeContainer: {
    position: 'absolute',
    top: -8,
    right: 12,
    zIndex: 999,
    elevation: 999,
  },
  badge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.base,
  },
  badgePopular: {
    backgroundColor: colors.warning,
  },
  badgeHot: {
    backgroundColor: colors.danger,
  },
  badgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.white,
    fontWeight: typography.fontWeight.bold as any,
  },
  // Logo
  logoContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    padding: spacing.xs,
  },
  logo: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  logoContainerSelected: {
    backgroundColor: colors.white,
  },
  // Package Info
  packageInfo: {
    flex: 1,
  },
  packageName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
  },
  packageNameSelected: {
    color: colors.dark,
  },
  totalBenefit: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[600],
    marginBottom: spacing.xs / 2,
  },
  benefitAmount: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
  },
  textSelected: {
    color: colors.dark,
  },
  // Premium
  premiumContainer: {
    alignItems: 'flex-end',
  },
  premiumLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[600],
    marginBottom: spacing.xs / 2,
  },
  premiumAmount: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
  },
  premiumAmountSelected: {
    color: colors.dark,
  },
  // Footer
  footer: {
    flexDirection: 'row',
  },
  detailButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1.5,
    borderColor: colors.green,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
  },
  detailButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.green,
  },
  buttonTextDisabled: {
    color: colors.gray[400],
  },
  chooseButton: {
    flex: 1,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[300],
    gap: spacing.sm,
  },
  selectedItem: {
    borderColor: colors.green,
    backgroundColor: colors.green + '08',
  },
  logoItem: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  itemContent: {
    flex: 1,
    gap: spacing.xs,
  },
  tenNhaBH: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
  },
  textChiTiet: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontWeight: typography.fontWeight.medium as '500',
  },
  nhaBHContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  nhaBHItem: {
    flex: 1,
    maxWidth: '49%',
  },
  cardNhaBH: {
    marginHorizontal: spacing.md - 2,
  },
});
