import {StyleSheet} from 'react-native';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  paymentCard: {
    marginBottom: spacing.lg,
  },
  paymentOptionsContainer: {
    gap: spacing.md,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderWidth: 2,
    borderColor: colors.gray[200],
    borderRadius: borderRadius.lg,
    padding: spacing.md,
  },
  paymentOptionSelected: {
    borderColor: colors.green,
    backgroundColor: colors.green + '08',
  },
  paymentOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    backgroundColor: '#FEF3C7',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  iconContainerSelected: {
    backgroundColor: colors.green,
  },
  paymentOptionDisabled: {
    opacity: 0.5,
    borderColor: colors.gray[200],
    backgroundColor: colors.gray[100],
  },
  paymentOptionText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.dark,
    flex: 1,
  },
  paymentOptionTextSelected: {
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as any,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.gray[300],
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: colors.green,
    backgroundColor: colors.white,
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.green,
  },
  footer: {
    backgroundColor: colors.white,
    padding: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    ...shadows.sm,
  },
  paymentButton: {
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
  },
});
