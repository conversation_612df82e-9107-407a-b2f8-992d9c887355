import {Al<PERSON><PERSON>oda<PERSON>, Button, createToast<PERSON><PERSON><PERSON>, ScreenComponent, useIOSAlert, useToast} from '@components/common';
import Card from '@components/common/Card';
import Icon from '@components/common/Icon';
import VNPayWebView from '@components/VNPay/VNPayWebView';
import {ACTION_CODE} from '@constants/axios';
import NavigationUtil from '@navigation/NavigationUtil';
import {styles} from '@screens/main/BHSK/ThanhToan/Styles';
import {getCommonExecute} from '@services/endpoints';
import React, {useState} from 'react';
import {Alert, Modal, ScrollView, Text, TouchableOpacity, View} from 'react-native';

type PaymentMethod = 'visa' | 'qrcode';

interface PaymentOption {
  id: PaymentMethod;
  title: string;
  icon: keyof typeof import('iconsax-react-native');
  disabled?: boolean;
}

const paymentOptions: PaymentOption[] = [
  {
    id: 'visa',
    title: 'Thẻ quốc tế Visa',
    icon: 'Card',
    disabled: true,
  },
  {
    id: 'qrcode',
    title: 'Thanh toán QR Code',
    icon: 'ScanBarcode',
  },
];

export default function ThanhToanScreen({navigation, route}: any) {
  const [selectedPayment, setSelectedPayment] = useState<PaymentMethod>('qrcode');
  const [loading, setLoading] = useState(false);
  const [showVNPay, setShowVNPay] = useState(false);
  const [paymentData, setPaymentData] = useState<{ma_gd: string; so_tien: number} | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const alert = useIOSAlert();
  const handlePaymentSelect = (method: PaymentMethod) => {
    setSelectedPayment(method);
  };

  const handlePayment = async () => {
    if (!selectedPayment) {
      Alert.alert('Vui lòng chọn phương thức thanh toán');
      return;
    }

    try {
      setLoading(true);
      const params = {
        nv: route.params?.nv,
        so_id: route.params?.soId,
        actionCode: ACTION_CODE.GET_THONG_TIN_THANH_TOAN,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        // Lưu thông tin thanh toán và hiển thị VNPay WebView
        setPaymentData({
          ma_gd: response.output.ma_gd,
          so_tien: response.output.so_tien,
        });
        setShowVNPay(true);
      } else {
        alert.show({
          title: 'Thông báo',
          message: response?.message || 'Có lỗi xảy ra khi tạo mã QR thanh toán',
          buttons: [{text: 'Đóng', style: 'cancel'}],
        });
      }
    } catch (error) {
      console.error('Error generating QR payment:', error);
      alert.show({
        title: 'Thông báo',
        message: 'Có lỗi xảy ra khi tạo mã QR thanh toán',
        buttons: [{text: 'Đóng', style: 'cancel'}],
      });
    } finally {
      setLoading(false);
    }
  };

  const renderPaymentOption = (option: PaymentOption) => {
    const isSelected = selectedPayment === option.id;

    return (
      <TouchableOpacity
        key={option.id}
        style={[styles.paymentOption, isSelected && styles.paymentOptionSelected, option.disabled && styles.paymentOptionDisabled]}
        onPress={() => !option.disabled && handlePaymentSelect(option.id)}
        activeOpacity={option.disabled ? 1 : 0.7}>
        <View style={styles.paymentOptionContent}>
          <View style={[styles.iconContainer, isSelected && styles.iconContainerSelected]}>
            <Icon name={option.icon} size={24} color={isSelected ? '#FFFFFF' : '#F59E0B'} />
          </View>
          <Text style={[styles.paymentOptionText, isSelected && styles.paymentOptionTextSelected]}>{option.title}</Text>
        </View>
        <View style={[styles.radioButton, isSelected && styles.radioButtonSelected]}>{isSelected && <View style={styles.radioButtonInner} />}</View>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      showHeader
      headerTitle="Thanh toán"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      dialogLoading={loading}
      footer={<Button title="Lấy mã QR thanh toán" onPress={handlePayment} style={styles.paymentButton} disabled={!selectedPayment || loading} />}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* VNPay WebView Modal */}
        <Modal visible={showVNPay} animationType="slide">
          {paymentData && (
            <VNPayWebView
              amount={paymentData.so_tien}
              orderId={paymentData.ma_gd}
              onSuccess={() => {
                setShowVNPay(false);
                setShowSuccessModal(true);
              }}
              onClose={() => {
                setShowVNPay(false);
              }}
            />
          )}
        </Modal>

        {/* Success Modal */}
        <AlertModal
          visible={showSuccessModal}
          onClose={() => {
            setShowSuccessModal(false);
            // Điều hướng về màn hình danh sách đơn bảo hiểm
            // navigation.navigate(MAIN_SCREENS.DANH_SACH_BHSK, {refresh: true});
          }}
          title="Thanh toán thành công"
          message="Giao dịch thanh toán của bạn đã hoàn tất thành công."
          type="success"
          confirmText="OK"
          onConfirm={() => {
            setShowSuccessModal(false);
            // Điều hướng về màn hình danh sách đơn bảo hiểm
            NavigationUtil.pop(2);
          }}
        />
        <Card title="Chọn phương thức thanh toán" style={styles.paymentCard}>
          <View style={styles.paymentOptionsContainer}>{paymentOptions.map(renderPaymentOption)}</View>
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
