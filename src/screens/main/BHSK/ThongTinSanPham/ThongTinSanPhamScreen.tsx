import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {Button, Card, Icon, ScreenComponent} from '@components/common';
import {styles} from '@screens/main/BHSK/ThongTinSanPham/Styles';
import {MAIN_SCREENS} from '@navigation/routes';

import {colors} from '@constants/theme';

// Product advantages data
const productAdvantages = [
  {
    id: 1,
    icon: 'UsdCoin',
    iconColor: colors.green,
    title: '<PERSON><PERSON><PERSON> phí ưu đãi',
    subtitle: 'chỉ 999.999đ',
    backgroundColor: colors.gray[100],
  },
  {
    id: 2,
    icon: 'Briefcase',
    iconColor: colors.green,
    title: 'Quyền lợi hưởng',
    subtitle: 'tới 150.000.000đ',
    backgroundColor: colors.gray[100],
  },
  {
    id: 3,
    icon: 'Moneys',
    iconColor: colors.green,
    title: 'Bồi thường nhanh chóng',
    subtitle: '',
    backgroundColor: colors.gray[100],
  },
];

// Information items data
const informationItems = [
  {
    id: 1,
    icon: 'DocumentText',
    iconColor: colors.green,
    title: 'Chi tiết quyền lợi',
    backgroundColor: colors.gray[100],
  },
  {
    id: 2,
    icon: 'Shield',
    iconColor: colors.green,
    title: 'Quy tắc bảo hiểm',
    backgroundColor: colors.gray[100],
  },
];

export default function ThongTinSanPhamScreen({navigation}: any) {
  const handleAdvantagePress = (item: any) => {
    console.log('Advantage pressed:', item.title);
  };

  const handleInformationPress = (item: any) => {
    console.log('Information pressed:', item.title);

    // Navigate to detail screens based on item
    if (item.id === 1) {
      navigation.navigate(MAIN_SCREENS.TOM_TAT_QUYEN_LOI);
    } else if (item.title === 2) {
      // TODO: Navigate to insurance rules screen
      console.log('Navigate to insurance rules');
    }
  };

  const handleChoosePackage = () => {
    console.log('Choose package pressed');
    navigation.popTo(MAIN_SCREENS.CHON_GOI_BH);
  };

  const renderAdvantageItem = (item: any, index: number) => (
    <TouchableOpacity key={item.id} style={styles.advantageItem} onPress={() => handleAdvantagePress(item)} activeOpacity={0.7}>
      <View style={[styles.advantageIconContainer, {backgroundColor: item.backgroundColor}]}>
        <Icon name={item.icon} size={28} color={item.iconColor} />
      </View>
      <Text style={styles.advantageTitle}>{item.title}</Text>
      <Text style={styles.advantageSubtitle}>{item.subtitle}</Text>
    </TouchableOpacity>
  );

  const renderInformationItem = (item: any, index: number) => (
    <TouchableOpacity key={item.id} style={styles.informationItem} onPress={() => handleInformationPress(item)} activeOpacity={0.7}>
      <View style={styles.informationItemLeft}>
        <View style={[styles.informationIconContainer]}>
          <Icon name={item.icon} size={28} color={item.iconColor} />
        </View>
        <Text style={styles.informationTitle}>{item.title}</Text>
      </View>
      <Icon name="ArrowRight" size={20} color={colors.gray[500]} />
    </TouchableOpacity>
  );

  return (
    <ScreenComponent
      showHeader
      headerTitle="Thông tin chi tiết sản phẩm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title="Chọn gói" onPress={handleChoosePackage} style={styles.chooseButton} />}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} bounces={false}>
        {/* Product Banner */}
        <Card style={styles.bannerPlaceholder}>
          <Text style={styles.bannerText}>Banner của sản phẩm</Text>
        </Card>

        {/* Product Advantages Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ưu điểm của sản phẩm</Text>
          <View style={styles.advantagesGrid}>{productAdvantages.map((item, index) => renderAdvantageItem(item, index))}</View>
        </View>

        {/* Information You Need to Know Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Thông tin bạn cần biết</Text>
          <Card style={styles.informationCard}>
            {informationItems.map((item, index) => (
              <View key={item.id}>
                {renderInformationItem(item, index)}
                {index < informationItems.length - 1 && <View style={styles.informationDivider} />}
              </View>
            ))}
          </Card>
        </View>
      </ScrollView>
    </ScreenComponent>
  );
}
