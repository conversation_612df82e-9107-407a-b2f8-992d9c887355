import {StyleSheet} from 'react-native';
import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },

  // Footer styles
  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.light,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },

  chooseButton: {
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    ...shadows.base,
  },

  // Banner styles
  bannerCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.white,
    ...shadows.sm,
  },

  bannerPlaceholder: {
    height: 140,
    backgroundColor: colors.gray[200],
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginBottom: spacing.xl,
  },

  bannerText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontWeight: typography.fontWeight.medium,
  },

  // Section styles
  section: {
    marginBottom: 0,
  },

  sectionTitle: {
    fontSize: typography.fontSize.lg,
    color: colors.dark,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.xs,
  },

  // Product advantages styles
  advantagesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },

  advantageItem: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xs,
  },

  advantageIconContainer: {
    width: 56,
    height: 56,
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
    ...shadows.sm,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },

  advantageTitle: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    fontWeight: typography.fontWeight.semibold,
    textAlign: 'center',
    marginBottom: 2,
  },

  advantageSubtitle: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[600],
    textAlign: 'center',
  },

  // Information section styles
  informationCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.xs,
    ...shadows.sm,
  },

  informationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
  },

  informationItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  informationIconContainer: {
    width: 50,
    height: 50,
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
    backgroundColor: colors.green + '15',
  },

  informationTitle: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium,
    flex: 1,
  },

  informationDivider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginHorizontal: spacing.sm,
  },
});
