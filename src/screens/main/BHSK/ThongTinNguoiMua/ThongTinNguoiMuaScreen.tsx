import React, {useCallback, useEffect, useState} from 'react';
import {Image, KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {Controller, useForm, useWatch} from 'react-hook-form';
import {Button, Card, ConfirmModal, createToastHelpers, DateTimePickerComponent, ScreenComponent, TextField, useToast} from '@components/common';
import DocumentScanner from '@components/common/DocumentScanner';
import Radio from '@components/common/Radio';
import Icon from '@components/common/Icon';
import {styles} from '@screens/main/BHSK/ThongTinNguoiMua/Styles';
import {buyerInformationFormValidation} from '@utils/validationSchemas';
import {MAIN_SCREENS} from '@navigation/routes';
import {colors, spacing} from '@constants/theme';
import R from '@assets/R';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import NavigationUtil from '@navigation/NavigationUtil';

type BuyerType = 'individual' | 'organization';
type CustomerType = 'C' | 'T'; // C = Cá nhân, T = Tổ chức

// Form data interface (for react-hook-form)
interface BuyerFormData {
  // Common fields
  loai_kh?: CustomerType; // C = Cá nhân, T = Tổ chức
  ten_kh: string; // Name (fullName or organizationName)
  dchi_kh: string; // Address
  dthoai_kh: string; // Phone
  email_kh: string; // Email

  // Individual specific fields
  gioi_tinh_kh?: string; // Gender (NAM/NU)
  ngay_sinh_kh: string; // Birth date (YYYYMMDD)
  so_cmt_kh?: string; // ID number

  // Organization specific fields
  mst_kh?: string; // Tax code

  // UI specific fields (for form display)
  fullName?: string; // For individual display
  gender?: string; // For UI gender selection
  birthDate?: Date | null; // For date picker
  idNumber?: string; // For individual ID input
  address?: string; // For UI address input
  phoneNumber?: string; // For UI phone input
  email?: string; // For UI email input
  organizationName?: string; // For organization display
  taxCode?: string; // For UI tax code input
  orgAddress?: string; // For UI org address
  orgPhoneNumber?: string; // For UI org phone
  orgEmail?: string; // For UI org email
}

interface LoadingState {
  isSubmitting: boolean;
}

// Utility functions (moved outside component for proper scope)
const formatDateToYYYYMMDD = (date: Date | null): string => {
  if (!date) return '';
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// Convert YYYYMMDD to Date object
const convertYYYYMMDDToDate = (dateNum: number | string | null | undefined): Date | null => {
  if (!dateNum) return null;
  const dateStr = dateNum.toString();
  if (dateStr.length !== 8) return null;

  const year = parseInt(dateStr.substring(0, 4));
  const month = parseInt(dateStr.substring(4, 6)) - 1;
  const day = parseInt(dateStr.substring(6, 8));
  return new Date(year, month, day);
};

export default function ThongTinNguoiMuaScreen({navigation, route}: any) {
  logger.log('ThongTinNguoiMuaScreen');
  const {heathData, editData, insuredPersonData, thongTinDonBH} = route?.params || null;
  const [chiTietDon, setChiTietDon] = useState([]);
  const isEditMode = !!editData;

  // Determine initial buyer type based on loai_kh or buyerType
  const getInitialBuyerType = (): BuyerType => {
    if (editData?.loai_kh) {
      return editData.loai_kh === 'C' ? 'individual' : 'organization';
    }
    return editData?.buyerType || 'individual';
  };

  const [buyerType, setBuyerType] = useState<BuyerType>(getInitialBuyerType());
  const [frontIdImage, setFrontIdImage] = useState<string | null>(editData?.frontIdImage || null);
  const [backIdImage, setBackIdImage] = useState<string | null>(editData?.backIdImage || null);
  const [isFrontScannerVisible, setFrontScannerVisible] = useState(false);
  const [isBackScannerVisible, setBackScannerVisible] = useState(false);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isSubmitting: false,
  });
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showAddMoreModal, setShowAddMoreModal] = useState<boolean>(false);
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // Check if buyer is same as insured person (BAN_THAN)
  const isBuyerSameAsInsured = insuredPersonData?.mqh_nguoi_mua === 'BAN_THAN';

  // Single form instance following HouseholdFormScreen pattern
  const {
    control,
    handleSubmit,
    formState: {errors, isValid},
    setValue,
    getValues,
    trigger,
  } = useForm<BuyerFormData>({
    mode: 'onChange',
    defaultValues: {
      // API format fields
      ten_kh: editData?.ten_kh || (isBuyerSameAsInsured ? insuredPersonData?.ten : ''),
      dchi_kh: editData?.dia_chi_kh || editData?.orgAddress || (isBuyerSameAsInsured ? insuredPersonData?.dia_chi : ''),
      dthoai_kh: editData?.dthoai_kh || editData?.orgPhoneNumber || (isBuyerSameAsInsured ? insuredPersonData?.dthoai : ''),
      email_kh: editData?.email_kh || editData?.orgEmail || (isBuyerSameAsInsured ? insuredPersonData?.email : ''),
      loai_kh: buyerType === 'individual' ? 'C' : 'T',

      // Individual fields
      gioi_tinh_kh: editData?.gioi_tinh_kh || (isBuyerSameAsInsured ? insuredPersonData?.gioi_tinh : 'NAM'),
      ngay_sinh_kh: editData?.ngay_sinh_kh || (isBuyerSameAsInsured ? insuredPersonData?.ngay_sinh : undefined),
      so_cmt_kh: editData?.cmt_kh || (isBuyerSameAsInsured ? insuredPersonData?.so_cmt : ''),

      // Organization fields
      mst_kh: editData?.mst_kh || '',

      // UI display fields (backward compatibility)
      fullName: editData?.ten_kh || (isBuyerSameAsInsured ? insuredPersonData?.ten : ''),
      birthDate: convertYYYYMMDDToDate(editData?.ngay_sinh_kh || (isBuyerSameAsInsured ? insuredPersonData?.ngay_sinh : null)),
      idNumber: editData?.cmt_kh || (isBuyerSameAsInsured ? insuredPersonData?.so_cmt : ''),
      address: editData?.dia_chi_kh || (isBuyerSameAsInsured ? insuredPersonData?.dia_chi : ''),
      phoneNumber: editData?.dthoai_kh || (isBuyerSameAsInsured ? insuredPersonData?.dthoai : ''),
      email: editData?.email_kh || (isBuyerSameAsInsured ? insuredPersonData?.email : ''),
      organizationName: editData?.ten_kh || '',
      taxCode: editData?.mst_kh || '',
      orgAddress: editData?.dia_chi_kh || '',
      orgPhoneNumber: editData?.dthoai_kh || '',
      orgEmail: editData?.email_kh || '',
    },
  });

  // Watch for reactive values
  const watchedBirthDate = useWatch({control, name: 'birthDate'});

  // Effect to populate form when in edit mode
  useEffect(() => {
    if (isEditMode && editData) {
      // Set form values manually to ensure they're properly set
      Object.keys(editData).forEach(key => {
        const value = editData[key];
        if (value !== null && value !== undefined && value !== '') {
          setValue(key as keyof BuyerFormData, value, {shouldValidate: false});
        }
      });

      // Also set state values
      if (editData.buyerType) setBuyerType(editData.buyerType);
      if (editData.frontIdImage) setFrontIdImage(editData.frontIdImage);
      if (editData.backIdImage) setBackIdImage(editData.backIdImage);
    }
  }, [isEditMode, editData, setValue]);

  const handleBuyerTypeChange = useCallback((type: BuyerType) => {
    setBuyerType(type);
  }, []);

  const handlePickFrontImage = useCallback(() => {
    setFrontScannerVisible(true);
  }, []);

  const handlePickBackImage = useCallback(() => {
    setBackScannerVisible(true);
  }, []);

  const handleFrontOcrComplete = useCallback(
    (info: any, imageUri: string) => {
      // Auto-fill form data from OCR result
      if (buyerType === 'individual') {
        if (info.id) setValue('idNumber', info.id, {shouldValidate: true});
        if (info.name) setValue('fullName', info.name, {shouldValidate: true});
        if (info.address) setValue('address', info.address, {shouldValidate: true});
        if (info.dob) {
          // Convert dd/mm/yyyy to Date object
          const parts = info.dob.split('/');
          if (parts.length === 3) {
            const [day, month, year] = parts.map(Number);
            const date = new Date(year, month - 1, day);
            setValue('birthDate', date, {shouldValidate: true});
          }
        }
      }
      setFrontIdImage(imageUri);
      setFrontScannerVisible(false);
    },
    [buyerType, setValue],
  );

  const handleBackOcrComplete = useCallback((info: any, imageUri: string) => {
    setBackIdImage(imageUri);
    setBackScannerVisible(false);
  }, []);

  // Render ID card placeholder giống ThongTinNguoiThamGiaScreen
  const renderIdCardPlaceholder = useCallback((type: 'front' | 'back', imageUri: string | null, onPress: () => void) => {
    const title = type === 'front' ? 'Mặt trước' : 'Mặt sau';
    return (
      <View style={styles.idCardContainer}>
        <TouchableOpacity style={styles.idCardPlaceholder} onPress={onPress} activeOpacity={0.7}>
          {imageUri ? (
            <>
              <Image source={{uri: imageUri}} style={styles.idCardImage} resizeMode="cover" />
              <View style={styles.scanCorners}>
                <View style={[styles.corner, styles.cornerTopLeft]} />
                <View style={[styles.corner, styles.cornerTopRight]} />
                <View style={[styles.corner, styles.cornerBottomLeft]} />
                <View style={[styles.corner, styles.cornerBottomRight]} />
              </View>
            </>
          ) : (
            <>
              <Image source={R.images.img_cccd} style={styles.idCardIcon} resizeMode="contain" />
              <Text style={styles.idCardLabel}>{title}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    );
  }, []);

  // Single photo upload like ThongTinNguoiThamGia for simplified UX
  const renderSinglePhotoUpload = useCallback(() => {
    return (
      <TouchableOpacity onPress={handlePickFrontImage} style={{flexDirection: 'row', flex: 1, alignItems: 'center', gap: spacing.sm}} activeOpacity={0.7}>
        <Image source={frontIdImage ? {uri: frontIdImage} : R.images.img_cccd} style={styles.idCardIcon} resizeMode="contain" />
        <Text style={styles.idCardLabel}>Chụp hoặc tải ảnh căn cước công dân (không bắt buộc)</Text>
      </TouchableOpacity>
    );
  }, [frontIdImage, handlePickFrontImage]);

  // Note: Utility functions moved outside component for proper scope

  const getChiTietDonBH = async () => {
    try {
      const params = {
        so_id: insuredPersonData?.so_id,
        so_id_dt: 0,
        actionCode: ACTION_CODE.GET_CHI_TIET_DON_BH,
      };
      const response = await getCommonExecute(params);
      if (response.data) {
        setChiTietDon(response.data);
      } else {
        logger.log(response.error);
      }
    } catch (error) {
      logger.log('🚀 ~ getChiTietDonBH ~ error:', error);
    }
  };

  // Simple submit handler like other screens
  const onSubmit = useCallback(
    async (data: BuyerFormData) => {
      if (loadingState.isSubmitting) {
        return;
      }

      setLoadingState(prev => ({...prev, isSubmitting: true}));

      try {
        if (buyerType === 'individual') {
          const params = {
            ...insuredPersonData,
            so_id: editData?.so_id || heathData?.so_id || 0,
            loai_kh: 'C',
            ten_kh: data.fullName,
            gioi_tinh_kh: data.gioi_tinh_kh || '',
            ngay_sinh_kh: formatDateToYYYYMMDD(data.birthDate ?? null),
            so_cmt_kh: data.idNumber,
            dchi_kh: data.address,
            dthoai_kh: data.phoneNumber,
            email_kh: data.email,
            actionCode: ACTION_CODE.LUU_THONG_TIN_NGUOI_MUA,
          };

          const response = await getCommonExecute(params);

          if (response?.data) {
            toast.success('Lưu thông tin người mua thành công!', {
              position: 'top',
              duration: 2000,
            });

            setTimeout(() => {
              if (isEditMode) {
                navigation.goBack();
              } else {
                getChiTietDonBH();
                // Hiện modal hỏi có muốn mua thêm NĐBH không
                setShowAddMoreModal(true);
              }
            }, 2000);
          } else {
            toast.error(response?.error || 'Có lỗi xảy ra', {
              position: 'top',
              duration: 3000,
            });
          }
        } else {
          const params = {
            ...insuredPersonData,
            so_id: editData?.so_id || heathData?.so_id || 0,
            loai_kh: 'T',
            ten_kh: data.organizationName,
            mst_kh: data.taxCode,
            dchi_kh: data.orgAddress,
            dthoai_kh: data.orgPhoneNumber,
            email_kh: data.orgEmail,
            actionCode: ACTION_CODE.LUU_THONG_TIN_NGUOI_MUA,
          };

          const response = await getCommonExecute(params);

          if (response?.data) {
            toast.success('Lưu thông tin người mua thành công!', {
              position: 'top',
              duration: 2000,
            });

            setTimeout(() => {
              if (isEditMode) {
                navigation.goBack();
              } else {
                // Hiện modal hỏi có muốn mua thêm NĐBH không
                setShowAddMoreModal(true);
              }
            }, 2000);
          } else {
            toast.error(response?.error_message || 'Có lỗi xảy ra', {
              position: 'top',
              duration: 3000,
            });
          }
        }
      } catch (error: any) {
        console.error('Error submitting buyer info:', error);
        toast.error(error?.response?.data?.error_message || 'Có lỗi xảy ra khi lưu thông tin', {
          position: 'top',
          duration: 3000,
        });
      } finally {
        setLoadingState(prev => ({...prev, isSubmitting: false}));
      }
    },
    [buyerType, frontIdImage, backIdImage, loadingState.isSubmitting, isEditMode, navigation, toast, insuredPersonData],
  );

  const handleAddMoreNDBH = () => {
    setShowAddMoreModal(false);
    // Lấy thông tin đơn BH từ response để pass sang màn ThongTinNguoiThamGia
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_NGUOI_THAM_GIA, {
      fromAddInsured: true,
      editData: null,
      thongTinDonBH: {
        ...chiTietDon,
      },
    });
  };

  const handleGoToDonBH = () => {
    setShowAddMoreModal(false);
    navigation.popTo(MAIN_SCREENS.THONG_TIN_DON_BH, {
      soID: insuredPersonData?.so_id,
    });
  };

  const handleShowModal = async () => {
    // If in edit mode, directly submit without showing modal
    if (isEditMode) {
      return handleSubmit(onSubmit)();
    }

    // For create mode, validate form trước khi hiện modal
    let isFormValid = false;

    if (buyerType === 'individual') {
      isFormValid = await trigger(['fullName', 'birthDate', 'idNumber', 'address', 'phoneNumber', 'email']);
    } else {
      isFormValid = await trigger(['organizationName', 'taxCode', 'orgAddress', 'orgPhoneNumber', 'orgEmail']);
    }

    if (isFormValid) {
      setShowModal(true);
    }
  };

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle={'Thông tin người mua'}
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title={isEditMode ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} loading={loadingState.isSubmitting} disabled={loadingState.isSubmitting} />}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        {/* <Text style={styles.subtitle}>Thông tin người được bảo hiểm (NDBH)</Text> */}

        {/* Buyer Type Toggle */}
        <View style={styles.toggleContainer}>
          <TouchableOpacity style={[styles.toggleButton, buyerType === 'individual' && styles.activeToggle]} onPress={() => handleBuyerTypeChange('individual')}>
            <Icon name="Profile" size={20} color={buyerType === 'individual' ? '#FFF' : colors.green} variant={buyerType === 'individual' ? 'Bold' : 'Linear'} />
            <Text style={[styles.toggleText, buyerType === 'individual' && styles.activeToggleText]}>Cá nhân</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.toggleButton, buyerType === 'organization' && styles.activeToggle]} onPress={() => handleBuyerTypeChange('organization')}>
            <Icon name="Building" size={20} color={buyerType === 'organization' ? '#FFF' : colors.green} variant={buyerType === 'organization' ? 'Bold' : 'Linear'} />
            <Text style={[styles.toggleText, buyerType === 'organization' && styles.activeToggleText]}>Tổ chức</Text>
          </TouchableOpacity>
        </View>
      </View>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.lg}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          {/* Individual Form */}
          {buyerType === 'individual' && (
            <>
              {/* Chụp/Tải ảnh giấy tờ tùy thân */}
              <Card title="Chụp/Tải ảnh giấy tờ tùy thân">{renderSinglePhotoUpload()}</Card>

              <Card title={'Thông tin người mua'} style={styles.formCard}>
                {/* Personal Form Fields */}
                <View style={styles.formSection}>
                  <TextField
                    control={control}
                    name="fullName"
                    label="Họ và tên"
                    placeholder="Nhập họ và tên"
                    required
                    rules={buyerInformationFormValidation.personal.fullName as any}
                    error={errors.fullName?.message}
                    containerStyle={styles.formField}
                  />
                  {/* Gender Selection */}
                  <Controller
                    control={control}
                    name="gioi_tinh_kh"
                    rules={buyerInformationFormValidation.personal.gender as any}
                    render={({field: {value, onChange}}) => (
                      <Radio.Group
                        label="Giới tính"
                        required
                        value={value}
                        onChange={onChange}
                        orientation="horizontal"
                        containerStyle={styles.genderSection}
                        error={(errors as any).gioi_tinh_kh?.message}>
                        <Radio.Button label="Nam" value="NAM" />
                        <Radio.Button label="Nữ" value="NU" />
                      </Radio.Group>
                    )}
                  />
                  <DateTimePickerComponent
                    control={control}
                    name="birthDate"
                    label="Ngày sinh"
                    placeholder="Chọn ngày sinh"
                    rules={buyerInformationFormValidation.personal.birthDate as any}
                    error={errors.birthDate?.message}
                    required
                    containerStyle={styles.formField}
                  />
                  <TextField
                    control={control}
                    name="idNumber"
                    label="Số giấy tờ tùy thân"
                    placeholder="Nhập số CCCD/CMND"
                    rules={buyerInformationFormValidation.personal.idNumber as any}
                    error={errors.idNumber?.message}
                    required
                    keyboardType="numeric"
                    containerStyle={styles.formField}
                  />
                  <TextField
                    control={control}
                    name="address"
                    label="Địa chỉ"
                    placeholder="Nhập địa chỉ"
                    rules={buyerInformationFormValidation.personal.address as any}
                    error={errors.address?.message}
                    numberOfLines={3}
                    required
                    containerStyle={styles.formField}
                  />
                  <TextField
                    control={control}
                    name="phoneNumber"
                    label="Điện thoại"
                    placeholder="Nhập số điện thoại"
                    rules={buyerInformationFormValidation.personal.phoneNumber as any}
                    error={errors.phoneNumber?.message}
                    keyboardType="phone-pad"
                    required
                    containerStyle={styles.formField}
                  />
                  <TextField
                    control={control}
                    name="email"
                    label="Email"
                    placeholder="Nhập email"
                    rules={buyerInformationFormValidation.personal.email as any}
                    error={errors.email?.message}
                    keyboardType="email-address"
                    containerStyle={styles.formField}
                  />
                </View>
              </Card>
            </>
          )}

          {/* Organization Form */}
          {buyerType === 'organization' && (
            <Card title={'Thông tin người mua'} style={styles.formCard}>
              <View style={styles.formSection}>
                <TextField
                  control={control}
                  name="organizationName"
                  label="Tên tổ chức"
                  placeholder="Nhập tên tổ chức"
                  rules={buyerInformationFormValidation.organization.organizationName as any}
                  error={errors.organizationName?.message}
                  required
                  containerStyle={styles.formField}
                />
                <TextField
                  control={control}
                  name="taxCode"
                  label="Mã số thuế"
                  placeholder="Nhập mã số thuế"
                  rules={buyerInformationFormValidation.organization.taxCode as any}
                  error={errors.taxCode?.message}
                  required
                  keyboardType="numeric"
                  containerStyle={styles.formField}
                />
                <TextField
                  control={control}
                  name="orgAddress"
                  label="Địa chỉ"
                  placeholder="Nhập địa chỉ"
                  rules={buyerInformationFormValidation.organization.address as any}
                  error={errors.orgAddress?.message}
                  numberOfLines={3}
                  required
                  containerStyle={styles.formField}
                />
                <TextField
                  control={control}
                  name="orgPhoneNumber"
                  label="Điện thoại"
                  placeholder="Nhập số điện thoại"
                  rules={buyerInformationFormValidation.organization.phoneNumber as any}
                  error={errors.orgPhoneNumber?.message}
                  keyboardType="phone-pad"
                  required
                  containerStyle={styles.formField}
                />
                <TextField
                  control={control}
                  name="orgEmail"
                  label="Email"
                  placeholder="Nhập email"
                  rules={buyerInformationFormValidation.organization.email as any}
                  error={errors.orgEmail?.message}
                  keyboardType="email-address"
                  required
                  containerStyle={styles.formField}
                />
              </View>
            </Card>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Document Scanner Modals */}
      <DocumentScanner isVisible={isFrontScannerVisible} onClose={() => setFrontScannerVisible(false)} initialImageUri={frontIdImage} onOcrComplete={handleFrontOcrComplete} />

      <DocumentScanner isVisible={isBackScannerVisible} onClose={() => setBackScannerVisible(false)} initialImageUri={backIdImage} onOcrComplete={handleBackOcrComplete} />

      {/* Add More NDBH Confirmation Modal */}
      <ConfirmModal
        visible={showAddMoreModal}
        onClose={() => setShowAddMoreModal(false)}
        title="Thông báo"
        message="Bạn có muốn mua thêm cho NĐBH khác không?"
        onConfirm={handleAddMoreNDBH}
        onCancel={handleGoToDonBH}
        confirmText="Đồng ý"
        cancelText="Không"
      />
    </ScreenComponent>
  );
}
