import { StyleSheet } from 'react-native';
import { borderRadius, colors, shadows, spacing, typography } from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: spacing.xl,
  },
  headerSection: {
  },
  subtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginHorizontal: spacing.sm,
    marginBottom: spacing.sm
  },
  toggleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm + 5,
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
  },
  activeToggle: {
    backgroundColor: colors.green,
    borderWidth: 1,
    borderColor: colors.green,
  },
  toggleText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.green,
    marginLeft: spacing.xs,
  },
  activeToggleText: {
    color: colors.white,
  },
  formCard: {},
  photoSection: {
    marginBottom: spacing.lg,
  },
  photoTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  photoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.md,
  },
  imageUploader: {
    flex: 1,
    height: 120,
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginTop: spacing.xs,
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },
  imageSubtext: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[500],
    marginTop: spacing.xs / 2,
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },
  // Updated gender section for Radio component
  genderSection: {
    marginBottom: spacing.md,
  },

  // Keep old gender styles for reference/fallback
  oldGenderSection: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: borderRadius.base,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginBottom: spacing.md,
  },
  genderButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    backgroundColor: colors.white,
    borderRadius: borderRadius.base,
  },
  activeGender: {
    backgroundColor: colors.green,
  },
  genderText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.green,
    marginLeft: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  activeGenderText: {
    color: colors.white,
  },
  formSection: {},
  formField: {},
  submitButton: {
    marginTop: spacing.xl,
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
  },

  // ID Cards styles từ ThongTinNguoiThamGiaScreen
  idCardsRow: {
    flexDirection: 'row',
    gap: spacing.sm,
    justifyContent: 'space-between',
  },
  idCardContainer: {
    flex: 1,
    alignItems: 'center',
  },
  idCardPlaceholder: {
    width: '100%',
    aspectRatio: 1.586, // Standard ID card ratio (85.6mm x 54mm)
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.sm,
    position: 'relative',
    overflow: 'hidden',
  },
  idCardImage: {
    width: '100%',
    height: '100%',
    borderRadius: borderRadius.lg - 2,
  },
  idCardIcon: {
    width: 70,
    height: 50,
  },
  scanCorners: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  corner: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderColor: colors.green,
  },
  cornerTopLeft: {
    top: 8,
    left: 8,
    borderTopWidth: 2,
    borderLeftWidth: 2,
    borderTopLeftRadius: 2,
  },
  cornerTopRight: {
    top: 8,
    right: 8,
    borderTopWidth: 2,
    borderRightWidth: 2,
    borderTopRightRadius: 2,
  },
  cornerBottomLeft: {
    bottom: 8,
    left: 8,
    borderBottomWidth: 2,
    borderLeftWidth: 2,
    borderBottomLeftRadius: 2,
  },
  cornerBottomRight: {
    bottom: 8,
    right: 8,
    borderBottomWidth: 2,
    borderRightWidth: 2,
    borderBottomRightRadius: 2,
  },
  idCardLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    marginTop: spacing.sm,
    marginBottom: spacing.sm,
    flex: 1,
    lineHeight: 21,
    fontFamily: typography.fontFamily.regular,
  },
});
