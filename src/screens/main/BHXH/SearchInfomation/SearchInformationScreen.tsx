import R from '@assets/R';
import {PAGE_SIZE} from '@commons/Constant';
import {Card, createToastHelpers, Icon, ScreenComponent, useToast} from '@components/common';
import Button from '@components/common/Button';
import TextField from '@components/common/TextField';
import {ACTION_CODE} from '@constants/axios';
import {colors} from '@constants/theme';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp} from '@navigation/types';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import React, {useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {ActivityIndicator, FlatList, Image, Pressable, Text, View} from 'react-native';
import {styles} from './searchinfomation.style';

// Type cho route params
type SearchInformationRouteProp = RouteProp<
  {
    SearchInformation: {
      mode?: 'search' | 'change';
      chiTietBienLai?: any;
      currentParticipant?: any;
      bt_ho_gia_dinh?: string | number; // Thêm bt_ho_gia_dinh để load members của hộ
    };
  },
  'SearchInformation'
>;

export default function SearchInformationScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const route = useRoute<SearchInformationRouteProp>();

  // Lấy params từ navigation
  const {mode = 'search', chiTietBienLai, currentParticipant, bt_ho_gia_dinh} = route.params || {};

  console.log('🔍 SearchInformationScreen params:', {mode, bt_ho_gia_dinh, hasCurrentParticipant: !!currentParticipant});

  const [loading, setLoading] = useState(false);
  const [loadingMemberDetail, setLoadingMemberDetail] = useState(false);
  const [danhSachThanhVien, setDanhSachThanhVien] = useState<any[]>([]);
  const [selectedPerson, setSelectedPerson] = useState<any>(currentParticipant || null);

  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm({
    defaultValues: {
      nd_tim: '',
    },
    mode: 'onChange',
  });

  const getDanhSachThanhVien = async (page: number, searchValue?: string, householdId?: string | number) => {
    try {
      setLoading(true);

      const params = {
        bt: '',
        bt_ho_gia_dinh: householdId ? String(householdId) : '', // Nếu có bt_ho_gia_dinh thì filter theo hộ
        tinh_thanh: '',
        phuong_xa: '',
        loai: '',
        cccd: '',
        dthoai: '',
        so_bhxh: '',
        ten: '',
        ngay_sinh: '',
        gioi_tinh: '',
        nd_tim: searchValue || '', // truyền vào nội dung tìm kiếm
        moi_qhe_ten: '',
        trang: page, // xử lý loadmore
        so_dong: PAGE_SIZE, //fix cứng ko cần thay đổi
        actionCode: ACTION_CODE.GET_DS_THANH_VIEN,
      };

      console.log('📡 getDanhSachThanhVien params:', params);
      const response = await getCommonExecute(params);

      // Kiểm tra response structure
      let listThanhVien: any[] = [];
      if (response?.data?.data) {
        listThanhVien = response.data.data;
        console.log('✅ getDanhSachThanhVien success - found', listThanhVien.length, 'members');
        setDanhSachThanhVien(listThanhVien);

        // Maintain selection if current participant is in the new list
        if (mode === 'change' && selectedPerson?.bt) {
          const foundParticipant = listThanhVien.find(item => item.bt === selectedPerson.bt);
          if (foundParticipant) {
            setSelectedPerson(foundParticipant); // Update with fresh data from search
          }
        }
      } else {
        console.log('⚠️ getDanhSachThanhVien - no data in response');
        setDanhSachThanhVien([]);
      }
    } catch (error) {
      console.log('getDanhSachThanhVien ~ error:', error);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = (data: any) => {
    getDanhSachThanhVien(1, data.nd_tim, bt_ho_gia_dinh);
  };

  // Auto-load members khi có bt_ho_gia_dinh (mode change từ DetailCollection)
  useEffect(() => {
    if (mode === 'change' && bt_ho_gia_dinh) {
      console.log('🔄 Auto-loading household members for bt_ho_gia_dinh:', bt_ho_gia_dinh);
      getDanhSachThanhVien(1, '', bt_ho_gia_dinh);
    }
  }, [mode, bt_ho_gia_dinh]);

  const handleApplyChange = async () => {
    if (!selectedPerson) {
      toast.error('Vui lòng chọn người tham gia');
      return;
    }

    try {
      const params = {
        ...chiTietBienLai,
        actionCode: ACTION_CODE.TAO_PHIEU_THU_HO,
        bt: chiTietBienLai.bt,
        bt_tvien: selectedPerson.bt,
      };

      const response = await getCommonExecute(params);

      if (response?.data) {
        toast.success('Tạo phiếu thu hộ thành công!', {
          position: 'top',
          duration: 3000,
        });
        setTimeout(() => {
          navigation.goBack();
        }, 300);
      } else {
        toast.error('Tạo phiếu thu hộ thất bại. Vui lòng thử lại.', {
          position: 'top',
          duration: 3000,
        });
      }
    } catch (error) {
      console.log('🚀 ~ onSubmit ~ error:', error);
      toast.error('Đã xảy ra lỗi khi tạo phiếu thu hộ. Vui lòng thử lại.', {
        position: 'top',
        duration: 3000,
      });
    }
  };

  // Copy function getMemberData từ HouseholdMemberFormScreen
  const getMemberData = async (itemBt: string) => {
    if (!itemBt) {
      return null;
    }
    setLoadingMemberDetail(true);
    try {
      const params = {
        ma: itemBt, // Thay đổi từ memberId thành itemBt
        actionCode: ACTION_CODE.GET_CHI_TIET_THANH_VIEN,
      };
      const response = await getCommonExecute(params);
      if (response?.data && response.data.thanh_vien.length > 0) {
        return response.data.thanh_vien[0]; // Return data thay vì setMemberData
      } else {
        return null;
      }
    } catch (error) {
      console.log('🚀 ~ getMemberData ~ error:', error);
      return null;
    } finally {
      setLoadingMemberDetail(false);
    }
  };

  const renderLoading = () => {
    return (
      <View style={{padding: 20, alignItems: 'center'}}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{marginTop: 8, color: colors.dark}}>Đang tải dữ liệu...</Text>
      </View>
    );
  };

  const renderEmptyState = () => {
    return (
      <View style={{padding: 20, alignItems: 'center'}}>
        <Text style={styles.description}>Chưa có dữ liệu</Text>
        <Text style={{marginTop: 8, color: colors.gray[700], fontSize: 14}}>Nhập từ khóa và bấm "Tìm kiếm" để hiển thị kết quả. Tìm kiếm theo tên, CCCD, mã BHXH, địa chỉ...</Text>
      </View>
    );
  };

  const renderItem = ({item}: {item: any}) => {
    const handleItemPress = async () => {
      if (!item?.bt) {
        return;
      }

      // Nếu là mode change, toggle selection
      if (mode === 'change') {
        // Nếu item đã được chọn, bỏ chọn (deselect)
        if (selectedPerson?.bt === item.bt) {
          setSelectedPerson(null);
        } else {
          // Nếu chưa được chọn, chọn item này
          setSelectedPerson(item);
        }
        return;
      }
      // Mode search - navigate như cũ
      try {
        const memberData = await getMemberData(item.bt);

        if (memberData) {
          navigation.navigate(MAIN_SCREENS.INFORMATION_PARTICIPANT, {
            memberData: memberData,
          });
        } else {
          toast.error('Không thể lấy thông tin chi tiết thành viên');
        }
      } catch (error) {
        console.log('🚀 ~ handleItemPress ~ error:', error);
        toast.error('Đã xảy ra lỗi khi lấy thông tin chi tiết');
      }
    };

    const isSelected = mode === 'change' && selectedPerson?.bt === item.bt;

    return (
      <Card style={isSelected ? styles.selectedCard : undefined}>
        <Pressable style={[styles.contentRow, loadingMemberDetail && {opacity: 0.6}]} onPress={handleItemPress} disabled={loadingMemberDetail}>
          <View style={styles.titleRow}>
            <Text style={styles.titleResult}>Mã số BHXH</Text>
            <Text style={styles.titleResult}>Họ và tên</Text>
            <Text style={styles.titleResult}>Số CCCD</Text>
            <Text style={styles.titleResult}>Ngày sinh</Text>
            <Text style={styles.titleResult}>Tên chủ hộ</Text>
          </View>
          <View style={styles.valueRow}>
            <Text style={styles.value}>{item?.so_bhxh || 'N/A'}</Text>
            <Text style={[styles.value, styles.nameValue]}>{item?.ten || 'N/A'}</Text>
            <Text style={styles.value}>{item?.cmt || item?.cccd || 'N/A'}</Text>
            <Text style={styles.value}>{item?.ngay_sinh || 'N/A'}</Text>
            <Text style={styles.value}>{item?.chu_ho || item?.ten_chu_ho || 'N/A'}</Text>
          </View>
          <View style={styles.buttonRow}>
            {mode === 'change' ? (
              isSelected ? (
                <Icon name="TickCircle" size={24} color={colors.green} variant="Bold" />
              ) : null
            ) : loadingMemberDetail ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Image source={R.icons.ic_arrow_right} style={styles.arrowRightIcon} resizeMode="contain" />
            )}
          </View>
        </Pressable>
      </Card>
    );
  };

  return (
    <ScreenComponent
      showHeader
      headerTitle="Người tham gia bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter={mode === 'change'}
      footer={mode === 'change' ? <Button title="Áp dụng" onPress={handleApplyChange} disabled={!selectedPerson || loadingMemberDetail} /> : undefined}>
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <Controller
            control={control}
            name="nd_tim"
            rules={{
              required: 'Vui lòng nhập từ khóa tìm kiếm',
              // minLength: {
              //   value: 2,
              //   message: 'Từ khóa tìm kiếm phải có ít nhất 2 ký tự',
              // },
            }}
            render={({field: {onChange, value}}) => (
              <TextField
                placeholder={'Tìm kiếm theo tên, CCCD, mã BHXH, địa chỉ...'}
                value={value}
                onChangeText={onChange}
                showPlaceholderWhenEmpty={true}
                containerStyle={styles.searchInputContainer}
                rightIconType={value ? 'clear' : 'search'}
                onRightIconPress={value ? () => onChange('') : undefined}
                error={errors.nd_tim?.message}
              />
            )}
          />
        </View>
        <Button title={loading ? 'Đang tìm kiếm...' : 'Tìm kiếm'} onPress={handleSubmit(onSubmit)} disabled={loading} />
        <View style={styles.result}>
          {loading ? (
            renderLoading()
          ) : danhSachThanhVien?.length > 0 ? (
            <FlatList data={danhSachThanhVien} renderItem={renderItem} keyExtractor={(_, index) => String(index)} showsVerticalScrollIndicator={false} />
          ) : (
            renderEmptyState()
          )}
        </View>
      </View>
    </ScreenComponent>
  );
}
