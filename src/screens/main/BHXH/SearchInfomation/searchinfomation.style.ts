import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    marginHorizontal: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
    marginBottom: spacing.md,
  },
  searchForm: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.base,
    marginVertical: spacing.md,
    shadowColor: colors.gray[500],
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 2,
    flex: 1,
  },
  result: {
    marginTop: spacing.md,
    flex: 1,
  },
  inputRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: typography.fontSize.base,
  },
  item: {
    padding: spacing.md,
    backgroundColor: colors.white,
    marginBottom: spacing.md,
    borderRadius: borderRadius.base,
    shadowColor: colors.gray[500],
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 2,
  },
  contentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.xl,
  },
  titleRow: {
    gap: spacing.sm,
  },
  titleResult: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  valueRow: {
    flex: 1,
    gap: spacing.sm,
  },
  value: {
    color: colors.black,
    fontSize: typography.fontSize.base,
  },
  nameValue: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  arrowRightIcon: {
    width: 18,
    height: 18,
  },
  buttonRow: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchInputContainer: {
    flex: 1,
  },
  // Search Bar
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'stretch',
    gap: spacing.sm,
    paddingHorizontal: spacing.sm,
    marginTop: spacing.md,
    width: '100%',
    alignSelf: 'center',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  selectedCard: {
    borderColor: colors.green,
    borderWidth: 2,
  },
});
