import Icon from '@components/common/Icon';
import {colors} from '@constants/theme';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Camera, PhotoFile, useCameraDevice} from 'react-native-vision-camera';

interface CameraModalProps {
  visible: boolean;
  title: string;
  onClose: () => void;
  setVisibale: () => void;
  onCapture: (photos: PhotoFile[]) => void;
}

const CameraModal: React.FC<CameraModalProps> = ({visible, title, onClose, onCapture, setVisibale}) => {
  const camera = useRef<Camera>(null);
  const [photos, setPhotos] = useState<PhotoFile[]>([]);
  const device = useCameraDevice('back');
  const insets = useSafeAreaInsets();

  useEffect(() => {
    if (visible) {
      // Reset state when modal opens
      setPhotos([]);
    }
  }, [visible]);

  const handleTakePhoto = useCallback(async () => {
    try {
      if (camera.current) {
        const photo = await camera.current.takePhoto({
          flash: 'off',
          enableShutterSound: false,
        });
        setPhotos(prev => [...prev, photo]);
      }
    } catch (error) {
      console.error('Failed to take photo:', error);
    }
  }, []);

  const handleSave = useCallback(() => {
    onCapture(photos);
    setPhotos([]);
    onClose();
  }, [photos, onCapture, onClose]);

  const handleCancel = useCallback(() => {
    setPhotos([]);
    onClose();
  }, [onClose]);

  return (
    <Modal isVisible={visible} swipeDirection={'down'} onBackButtonPress={setVisibale} onSwipeComplete={setVisibale} style={styles.modal}>
      <View style={styles.container}>
        <View style={styles.cameraContainer}>{device && <Camera ref={camera} style={StyleSheet.absoluteFill} device={device} isActive={visible} photo={true} />}</View>

        <View style={[styles.header, {paddingTop: insets.top}]}>
          <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
            <Icon name="CloseCircle" size={32} color={'white'} />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Icon name="TickCircle" size={32} color={'white'} />
          </TouchableOpacity>
        </View>

        <View style={[styles.footer, {paddingBottom: insets.bottom > 0 ? insets.bottom : 15}]}>
          {photos.length > 0 && (
            <View style={styles.photoCount}>
              <Text style={styles.photoCountText}>{photos.length} ảnh</Text>
            </View>
          )}
          <TouchableOpacity onPress={handleTakePhoto} style={styles.captureButton}>
            <View style={styles.captureButtonInner} />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    // paddingVertical: 16,
    backgroundColor: 'black',
    zIndex: 1,
    height: 120,
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 24,
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    color: '#007AFF',
    fontSize: 24,
  },
  cameraContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    zIndex: 1,
    height: 120,
  },
  photoCount: {
    position: 'absolute',
    top: 10,
    right: 16,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  photoCountText: {
    color: 'white',
    fontSize: 14,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 62,
    height: 62,
    borderRadius: 31,
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: 'black',
  },
});

export default CameraModal;
