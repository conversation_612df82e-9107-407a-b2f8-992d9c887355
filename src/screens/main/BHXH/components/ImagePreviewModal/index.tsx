import {Icon} from '@components/common';
import {colors} from '@constants/theme';
import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, FlatList, Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import AntDesign from 'react-native-vector-icons/AntDesign';

interface ImageItem {
  uri: string;
  isTemp?: boolean;
}

interface ImagePreviewModalProps {
  visible: boolean;
  images: ImageItem[];
  initialIndex?: number;
  onClose: () => void;
}

const {width: screenWidth} = Dimensions.get('window');
const THUMBNAIL_SIZE = 70;

const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({visible, images, initialIndex = 0, onClose}) => {
  const insets = useSafeAreaInsets();
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const swiperRef = useRef<any>(null);
  const thumbnailListRef = useRef<FlatList<ImageItem>>(null);

  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
      setTimeout(() => {
        swiperRef.current?.scrollToIndex({index: initialIndex, animated: false});
        thumbnailListRef.current?.scrollToIndex({index: initialIndex, animated: true, viewPosition: 0.5});
      }, 150); // Delay to ensure modal is fully visible
    }
  }, [visible, initialIndex]);

  const handleThumbnailPress = (index: number) => {
    if (swiperRef.current) {
      swiperRef.current.scrollToIndex({index});
    }
  };

  const onMomentumScrollEnd = ({index}: {index: number}) => {
    setCurrentIndex(index);
    thumbnailListRef.current?.scrollToIndex({index, animated: true, viewPosition: 0.5});
  };

  if (!images || images.length === 0) {
    return null;
  }

  const renderMainImage = ({item}: {item: ImageItem}) => (
    <View style={styles.mainImageWrapper}>
      <Image source={{uri: item.uri}} style={styles.mainImage} resizeMode="contain" />
    </View>
  );

  const renderThumbnail = ({item, index}: {item: ImageItem; index: number}) => (
    <TouchableOpacity onPress={() => handleThumbnailPress(index)} style={[styles.thumbnailWrapper, currentIndex === index && styles.thumbnailSelected]}>
      <Image source={{uri: item.uri}} style={styles.thumbnailImage} />
    </TouchableOpacity>
  );

  return (
    <Modal isVisible={visible} onBackButtonPress={onClose} onBackdropPress={onClose} style={styles.modal} animationIn="fadeIn" animationOut="fadeOut" useNativeDriverForBackdrop>
      <View style={[styles.container, {paddingBottom: insets.bottom}]}>
        <TouchableOpacity style={[styles.closeButton, {top: insets.top + 10}]} onPress={onClose}>
          <Icon name="CloseCircle" size={32} color={'white'} />
        </TouchableOpacity>

        <View style={styles.mainImageContainer}>
          <SwiperFlatList ref={swiperRef} data={images} renderItem={renderMainImage} index={initialIndex} onChangeIndex={onMomentumScrollEnd} showPagination={false} />
        </View>

        <View style={styles.thumbnailContainer}>
          <FlatList
            ref={thumbnailListRef}
            data={images}
            renderItem={renderThumbnail}
            keyExtractor={(item, index) => `${item.uri}-${index}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.thumbnailList}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'space-between',
  },
  closeButton: {
    position: 'absolute',
    left: 15,
    zIndex: 10,
  },
  mainImageContainer: {
    flex: 1,
  },
  mainImageWrapper: {
    width: screenWidth,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainImage: {
    width: '100%',
    height: '100%',
  },
  thumbnailContainer: {
    height: 120,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
  },
  thumbnailList: {
    paddingHorizontal: 10,
    alignItems: 'center',
  },
  thumbnailWrapper: {
    width: THUMBNAIL_SIZE,
    height: THUMBNAIL_SIZE,
    borderRadius: 8,
    marginHorizontal: 5,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  thumbnailSelected: {
    borderColor: colors.white,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
});

export default ImagePreviewModal;
