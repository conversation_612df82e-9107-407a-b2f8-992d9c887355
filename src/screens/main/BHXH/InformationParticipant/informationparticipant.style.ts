import {colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  infoContainer: {
    backgroundColor: colors.white,
    marginBottom: spacing.md,
    paddingHorizontal: 0,
  },
  valueRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    gap: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontWeight: typography.fontWeight.bold as any,
  },
  textEdit: {
    fontSize: typography.fontSize.base,
    color: '#0033FF',
    fontWeight: typography.fontWeight.medium as any,
  },
  titleValue: {
    color: colors.gray[600],
    fontSize: typography.fontSize.base,
  },
  value: {
    color: colors.gray[800],
    fontSize: typography.fontSize.base,
  },
  editIcon: {
    width: 18,
    height: 18,
    marginRight: spacing.sm,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  line: {
    width: '100%',
    height: 1,
    backgroundColor: colors.gray[200],
  },
  infoRow: {
    marginTop: spacing.sm,
    gap: spacing.sm,
  },
  relationshipValue: {
    color: colors.green,
    fontWeight: typography.fontWeight.medium as any,
  },
  typeHouseholdValue: {
    color: colors.danger,
    fontWeight: typography.fontWeight.medium as any,
  },
  historyContainer: {
    padding: spacing.md,
    backgroundColor: '#C8E1DA',
    marginBottom: spacing.sm,
    marginHorizontal: spacing.sm,
  },
  titleHistory: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontWeight: typography.fontWeight.bold as any,
  },
  itemHistory: {
    flexDirection: 'row',

    gap: spacing.xl,
    backgroundColor: colors.white,
  },
  rowHistory: {
    gap: spacing.sm,
  },
  descriptionHistory: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },

  buttonContainer: {
    marginTop: spacing.xl,
    marginBottom: spacing.xl,
  },
  textHistory: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
});
