import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>, ScreenComponent} from '@components/common';
import {Image, RefreshControl, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {styles} from './informationparticipant.style';
import {useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';
import R from '@assets/R';
import {MAIN_SCREENS} from '@navigation/routes';
import {spacing} from '@constants/theme';
import {GIOI_TINH, MOI_QUAN_HE_VOI_CHU_HO} from '@commons/Constant';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '@store/index';
import {fetchLoaiHoGiaDinh} from '@store/slices/commonSlice';
import {getCommonExecute} from '@services/endpoints';
import {ACTION_CODE} from '@constants/axios';
import {setDialogLoading} from '@store/slices/uiSlice';

interface InformationValueProps {
  bt?: number;
  bt_ho_gia_dinh?: number;
  cmt?: string;
  dia_chi?: string;
  dia_chi_full?: string;
  dthoai?: string;
  email?: string;
  gioi_tinh?: string;
  loai?: string;
  loai_ho_gia_dinh?: string; // Alternative field name for household type
  ma_doi_tac?: string;
  moi_qhe?: string;
  ngay_cap_nhat?: string;
  ngay_sinh?: string;
  ngay_tao?: string;
  nguoi_cap_nhat?: string;
  nguoi_tao?: string;
  phuong_xa?: string;
  so_bhxh?: string;
  sott?: number;
  stt?: number;
  ten?: string;
  tinh_thanh?: string;
  trang_thai_ten?: string;
  cccd?: number | string;
  ten_chu_ho?: string;
}

interface HistoryValueProps {
  receiptNumber: string;
  date: string;
  amount: number; // Số tiền đã được định dạng là number để dễ format
  unit: string;
  collector: string;
}

interface BienLaiItem {
  so_bien_lai: string;
  ngay_lap_bien_lai: string;
  so_tien: string;
  don_vi_bhxh_ten: string;
  nguoi_tao: string;
  [key: string]: any;
}

export default function InformationParticipantScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const route = useRoute();
  const dispatch = useDispatch<AppDispatch>();

  const [informationParticipant, setInformationParticipant] = useState<InformationValueProps>({});
  const [historyParticipant, setHistoryParticipant] = useState<HistoryValueProps[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const {danhSachLoaiHoGiaDinh} = useSelector((state: RootState) => state.commonCategories);
  const {isDialogLoading} = useSelector((state: RootState) => state.ui);

  // Extract memberData từ navigation params
  const memberData = (route.params as any)?.memberData;
  console.log('Member Data:', memberData);
  const householdDetailData = (route.params as any)?.householdDetailData;
  console.log('🚀 ~ InformationParticipantScreen ~ householdDetailData:', householdDetailData);

  // State for refresh control
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Function để map relationship value thành label
  const getRelationshipLabel = (relationshipValue: string | undefined) => {
    if (!relationshipValue) return;
    const relationship = MOI_QUAN_HE_VOI_CHU_HO.find(item => item.value === relationshipValue);
    const label = relationship?.label || 'N/A';

    return label;
  };
  const getGenderLabel = (gender: string | undefined) => {
    if (!gender) return;
    const relationship = GIOI_TINH.find(item => item.value === gender);
    const label = relationship?.label || 'N/A';

    return label;
  };
  const getTenLoaiHoGiaDinh = (loai: string | undefined) => {
    if (!loai) {
      return 'N/A';
    }
    const loaiHoGiaDinh = danhSachLoaiHoGiaDinh.find(item => item.ma === loai);
    const label = loaiHoGiaDinh?.ten || 'N/A';

    return label;
  };

  // Lấy thông tin chi tiết thành viên từ API
  const getMemberData = useCallback(async (memberId: string) => {
    try {
      const params = {
        ma: memberId,
        actionCode: ACTION_CODE.GET_CHI_TIET_THANH_VIEN,
      };
      const response = await getCommonExecute(params);
      if (response?.data && response.data.thanh_vien.length > 0) {
        return response.data.thanh_vien[0];
      }
    } catch (error) {
      console.log('🚀 ~ getMemberData ~ error:', error);
    }
    return null;
  }, []);

  // Lấy lịch sử nộp tiền BHXH của thành viên
  const getPaymentHistory = useCallback(async (bt_tvien: string) => {
    if (!bt_tvien) {
      console.log('⚠️ getPaymentHistory: bt_tvien is empty');
      return;
    }

    try {
      setLoadingHistory(true);
      console.log('🔄 Loading payment history for bt_tvien:', bt_tvien);

      const params = {
        loai: '', // Để trống để lấy cả BHXH và BHYT
        nsd_lap_bien_lai: '',
        trang_thai: '',
        so_bhxh: '',
        ten: '',
        cmt: '',
        nd_tim: '',
        bt_tvien: bt_tvien,
        trang: 1,
        so_dong: 1000, // Lấy tất cả lịch sử
        actionCode: ACTION_CODE.DANH_SACH_BIEN_LAI,
      };

      const response = await getCommonExecute(params);
      console.log('🚀 ~ getPaymentHistory ~ response:', response);

      if (response?.data?.data) {
        const bienLaiList: BienLaiItem[] = response.data.data;

        // Map dữ liệu từ API sang HistoryValueProps
        const mappedHistory: HistoryValueProps[] = bienLaiList.map(item => {
          // Parse số tiền: loại bỏ dấu phẩy và các ký tự không phải số
          const cleanAmount = (item.so_tien || '0').toString().replace(/[,\.]/g, '');
          const amount = parseFloat(cleanAmount) || 0;

          console.log('💰 Parsing amount:', item.so_tien, '→', cleanAmount, '→', amount);

          return {
            receiptNumber: item.so_bien_lai || 'N/A',
            date: item.ngay_lap_bien_lai || 'N/A',
            amount: amount,
            unit: item.don_vi_bhxh_ten || 'N/A',
            collector: item.nguoi_tao || 'N/A',
          };
        });

        setHistoryParticipant(mappedHistory);
        console.log('✅ Payment history loaded:', mappedHistory.length, 'items');
      } else {
        setHistoryParticipant([]);
        console.log('ℹ️ No payment history found');
      }
    } catch (error) {
      console.log('❌ ~ getPaymentHistory ~ error:', error);
      setHistoryParticipant([]);
    } finally {
      setLoadingHistory(false);
    }
  }, []);

  // Refresh data khi quay lại màn hình sau khi cập nhật
  useFocusEffect(
    useCallback(() => {
      // Only refresh if:
      // 1. Not on initial mount
      // 2. We need to refresh (flag was set)
      if (!isInitialMount && needsRefresh && memberData?.bt) {
        console.log('🔄 Refreshing member data after successful update');

        // Show loading dialog
        dispatch(setDialogLoading(true));

        // Lấy dữ liệu mới nhất từ API
        getMemberData(memberData.bt.toString()).then(freshData => {
          if (freshData) {
            // Merge with householdDetailData to get complete information
            const completeData = {
              ...freshData,
              // Fill household information from householdDetailData if available
              ten_chu_ho: householdDetailData?.ten || freshData?.ten,
              loai: householdDetailData?.loai || freshData?.loai,
            };

            setInformationParticipant(completeData);
            console.log('📋 ~ InformationParticipant ~ refreshed completeData:', completeData);
          }
          // Hide loading dialog
          dispatch(setDialogLoading(false));
        });

        setNeedsRefresh(false); // Reset flag after refreshing
      }
    }, [isInitialMount, needsRefresh, memberData, householdDetailData, dispatch]),
  );

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    if (!memberData?.bt) return;

    setRefreshing(true);
    try {
      const freshData = await getMemberData(memberData.bt.toString());
      if (freshData) {
        // Merge with householdDetailData to get complete information
        const completeData = {
          ...freshData,
          // Fill household information from householdDetailData if available
          ten_chu_ho: householdDetailData?.ten || freshData?.ten,
          loai: householdDetailData?.loai || freshData?.loai,
        };

        setInformationParticipant(completeData);
        console.log('📋 ~ InformationParticipant ~ refreshed completeData:', completeData);
      }

      // Refresh payment history
      await getPaymentHistory(memberData.bt.toString());
    } catch (error) {
      console.log('Error during refresh:', error);
    } finally {
      setRefreshing(false);
    }
  }, [memberData, householdDetailData, getMemberData, getPaymentHistory]);

  // useEffect để update informationParticipant khi có memberData
  useEffect(() => {
    if (memberData && typeof memberData === 'object') {
      if (danhSachLoaiHoGiaDinh.length === 0) {
        dispatch(fetchLoaiHoGiaDinh());
      }
      try {
        // Merge memberData with householdDetailData to get complete information
        const completeData = {
          ...memberData,
          // Fill household information from householdDetailData if available
          ten_chu_ho: householdDetailData?.ten || memberData?.ten,
          loai: householdDetailData?.loai || memberData?.loai,
        };

        setInformationParticipant(completeData);
        console.log('📋 ~ InformationParticipant ~ completeData:', completeData);

        // Lấy lịch sử nộp tiền BHXH khi có bt (mã thành viên)
        if (memberData.bt) {
          console.log('🔄 Fetching payment history for member bt:', memberData.bt);
          getPaymentHistory(memberData.bt.toString());
        }
      } catch (error) {
        console.log('❌ ~ useEffect ~ Error mapping memberData:', error);
      }
    }

    // Mark as not initial mount after first load
    setTimeout(() => setIsInitialMount(false), 1000);
  }, [memberData, householdDetailData, dispatch, getPaymentHistory]);

  const handleNext = () => {
    (navigation as any).navigate(MAIN_SCREENS.DETAIL_PRODUCT, {memberData: informationParticipant, mode: 'create'});
  };

  const handleEdit = () => {
    // Set flag to refresh data when returning
    setNeedsRefresh(true);

    navigation.navigate(MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM, {
      memberId: memberData?.id || memberData?.bt,
      bt_ho_gia_dinh: memberData?.bt_ho_gia_dinh,
    });
  };

  const renderLabelContent = (title?: string, value?: number | string) => {
    return (
      <View style={styles.infoRow}>
        <View style={styles.valueRow}>
          <Text style={styles.titleValue}>{title}</Text>
          <Text style={styles.value}>{value}</Text>
        </View>
        <View style={styles.line}></View>
      </View>
    );
  };

  const renderHistoryParticipant = ({item}: {item: HistoryValueProps}) => {
    return (
      <Card style={styles.itemHistory}>
        <View style={styles.rowHistory}>
          <Text style={styles.textHistory}>Số biên lai</Text>
          <Text style={styles.textHistory}>Ngày thu tiền</Text>
          <Text style={styles.textHistory}>Số tiền</Text>
          <Text style={styles.textHistory}>Đơn vị</Text>
          <Text style={styles.textHistory}>Cán bộ thu</Text>
        </View>

        <View style={styles.rowHistory}>
          <Text style={[styles.value, styles.relationshipValue]}>{item.receiptNumber}</Text>
          <Text style={styles.value}>{item.date}</Text>
          <Text style={[styles.value, styles.typeHouseholdValue]}>{item.amount?.toLocaleString('vi-VN')}đ</Text>
          <Text style={styles.value}>{item.unit}</Text>
          <Text style={styles.value}>{item.collector}</Text>
        </View>
      </Card>
    );
  };

  return (
    <ScreenComponent
      showHeader
      headerTitle="Thông tin người tham gia"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      dialogLoading={isDialogLoading}
      footer={<Button title="Tiếp theo" onPress={() => handleNext()} />}>
      <View style={{flex: 1}}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: spacing.lg}}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}>
          {/* Thông tin người tham gia bảo hiểm */}
          {informationParticipant && (
            <Card style={styles.infoContainer}>
              <View style={styles.infoRow}>
                <View style={styles.valueRow}>
                  <Text style={styles.title}>Thông tin chung</Text>
                  <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
                    <Image source={R.icons.ic_edit} style={styles.editIcon} resizeMode="contain" />
                    <Text style={styles.textEdit}>Điều chỉnh thông tin</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.line}></View>
              </View>
              {/* {renderLabelContent('Số tiếp nhận', '')} */}
              {renderLabelContent('Mã số BHXH', informationParticipant?.so_bhxh)}
              {renderLabelContent('Họ và tên', informationParticipant?.ten)}
              {renderLabelContent('Số CCCD', informationParticipant?.cccd || informationParticipant?.cmt)}
              {renderLabelContent('Ngày sinh', informationParticipant?.ngay_sinh)}
              {renderLabelContent('Giới tính', getGenderLabel(informationParticipant?.gioi_tinh))}
              {renderLabelContent('Địa chỉ', informationParticipant?.dia_chi || informationParticipant?.dia_chi_full)}

              {/* Thông tin hộ gia đình */}
              {renderLabelContent('Tên chủ hộ', informationParticipant?.ten_chu_ho)}
              {renderLabelContent('Mối q.hệ với chủ hộ', getRelationshipLabel(informationParticipant?.moi_qhe))}
              {renderLabelContent('Loại hộ gia đình', getTenLoaiHoGiaDinh(informationParticipant?.loai))}
            </Card>
          )}
          {/* Lịch sử nộp tiền Bảo hiểm xã hội */}
          {/*<View>*/}
          {/*  <View style={styles.historyContainer}>*/}
          {/*    <Text style={styles.titleHistory}>Lịch sử nộp tiền Bảo hiểm xã hội</Text>*/}
          {/*  </View>*/}
          {/*  <FlatList*/}
          {/*    data={historyParticipant}*/}
          {/*    renderItem={renderHistoryParticipant}*/}
          {/*    keyExtractor={item => `${item.receiptNumber}-${item.date}`}*/}
          {/*    ListEmptyComponent={*/}
          {/*      loadingHistory ? (*/}
          {/*        <Text style={styles.descriptionHistory}>Đang tải dữ liệu...</Text>*/}
          {/*      ) : (*/}
          {/*        <Text style={styles.descriptionHistory}>Không có dữ liệu</Text>*/}
          {/*      )*/}
          {/*    }*/}
          {/*    showsVerticalScrollIndicator={false}*/}
          {/*    scrollEnabled={false}*/}
          {/*  />*/}
          {/*</View>*/}
        </ScrollView>
      </View>
    </ScreenComponent>
  );
}
