import R from '@assets/R';
import {<PERSON><PERSON>, Card, ConfirmModal, createToastHelpers, CustomModal, ScreenComponent, useToast} from '@components/common';
import {ACTION_CODE, CONFIG_SERVER} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import {MainNavigationProp} from '@navigation/types';
import Clipboard from '@react-native-clipboard/clipboard';
import {useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints/commonEndpoints';
import {uploadImage} from '@services/uploadService';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ActivityIndicator, Alert, Image, Linking, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {launchImageLibrary} from 'react-native-image-picker';
import {Camera, PhotoFile} from 'react-native-vision-camera';
import {ChiTietBienLaiData, ChiTietBienLaiParams} from '../Collection/types';
import CameraModal from '../components/CameraModal';
import ImagePreviewModal from '../components/ImagePreviewModal';
import {styles} from './payment.style';

type PaymentMethod = 'Chuyển khoản' | 'Tiền mặt';

interface BankTransferInfo {
  accountNumber: string;
  accountName: string;
  bankName: string;
  qrCode: string; // Đường dẫn ảnh mã QR hoặc base64
}

interface ImageItem {
  uri: string;
  isTemp?: boolean; // true if not yet uploaded
  uploadStatus?: 'pending' | 'success' | 'error'; // Add upload status
  uploadResponse?: any; // Store upload response
}

interface ImageData {
  id: string;
  title: string;
  images: ImageItem[];
}

// Interface cho params API xác nhận thu tiền
interface ConfirmPaymentParams {
  actionCode: string;
  bt: string | number;
}

export default function PaymentScreen({route}: {route: {params: {chiTietBienLai: any; chiTietDonViThuHo: any}}}) {
  const {chiTietBienLai: initialChiTietBienLai, chiTietDonViThuHo} = route?.params;
  logger.log('🚀 ~ PaymentScreen ~ chiTietDonViThuHo:', chiTietDonViThuHo);
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  const navigation = useNavigation<MainNavigationProp>();

  // State mới để quản lý dữ liệu chi tiết biên lai
  const [chiTietBienLai, setChiTietBienLai] = useState<ChiTietBienLaiData | null>(initialChiTietBienLai || null);
  const [isLoadingDetail, setIsLoadingDetail] = useState<boolean>(false);

  // Ref để track API calls và tránh duplicate calls
  const isApiCallingRef = useRef<boolean>(false);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('Chuyển khoản');
  const [imageData, setImageData] = useState<ImageData[]>([
    {id: '2', title: 'Ảnh biên lai', images: []},
    {id: '3', title: 'Ảnh thanh toán', images: []},
  ]);
  const [isShowQRCodeModal, setIsShowQRCodeModal] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [selectedImageType, setSelectedImageType] = useState<{id: string; title: string} | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);
  const [qrError, setQrError] = useState<string>('');
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [previewImages, setPreviewImages] = useState<ImageItem[]>([]);
  const [previewInitialIndex, setPreviewInitialIndex] = useState(0);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isConfirming, setIsConfirming] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'confirm' | 'remove'>('confirm');

  // Hàm gọi API chi tiết biên lai để refresh dữ liệu
  const getChiTietBienLai = useCallback(async () => {
    // Tránh duplicate API calls
    if (isApiCallingRef.current) return;
    try {
      isApiCallingRef.current = true;
      setIsLoadingDetail(true);

      const params: ChiTietBienLaiParams = {
        ma: chiTietBienLai?.bt,
        actionCode: ACTION_CODE.CHI_TIET_BIEN_LAI,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        setChiTietBienLai(response.data);
      }
    } catch (error: any) {
      const errorMsg = error?.message || 'Đã xảy ra lỗi khi gọi API chi tiết biên lai';
      console.error('🚀 ~ getChiTietBienLai ~ catch error:', error);
      toast.error(errorMsg);
    } finally {
      setIsLoadingDetail(false);
      isApiCallingRef.current = false;
    }
  }, [chiTietBienLai?.bt]);

  // Gọi API khi component mount
  useEffect(() => {
    getChiTietBienLai();
  }, [getChiTietBienLai]);

  const handleShowQRCodeModal = async () => {
    setIsShowQRCodeModal(true);
    // await generateVietQR();
  };

  const handleCloseQRCodeModal = () => {
    setIsShowQRCodeModal(false);
    setQrError('');
  };

  // const generateVietQR = async () => {
  //   const bankTransferInfo = paymentInfoData[0]?.bankTransferInfo;
  //   if (!bankTransferInfo) {
  //     setQrError('Không tìm thấy thông tin chuyển khoản');
  //     return;
  //   }

  //   setIsGeneratingQR(true);
  //   setQrError('');

  //   try {
  //     // Lấy mã ngân hàng từ tên ngân hàng
  //     const bankCode = getBankCodeFromName(bankTransferInfo.bankName);
  //     if (!bankCode) {
  //       throw new Error(`Không tìm thấy mã ngân hàng cho: ${bankTransferInfo.bankName}`);
  //     }

  //     // Tạo nội dung chuyển tiền
  //     const transferContent = `Thanh toan BHXH ${paymentInfoData[0].codeBHXH} - ${paymentInfoData[0].name}`;

  //     // Gọi VietQR API
  //     const result = await vietQRService.generateQR({
  //       accountNo: bankTransferInfo.accountNumber,
  //       accountName: bankTransferInfo.accountName,
  //       acqId: bankCode,
  //       amount: paymentInfoData[0].amount,
  //       addInfo: transferContent,
  //       template: 'compact',
  //     });

  //     if (result.code === '00' && result.data) {
  //       setQrCodeUrl(result.data.qrDataURL);
  //     } else {
  //       throw new Error(result.desc || 'Không thể tạo mã QR');
  //     }
  //   } catch (error) {
  //     console.error('Error generating VietQR:', error);
  //     const errorMessage = error instanceof Error ? error.message : 'Không thể tạo mã QR. Vui lòng thử lại.';
  //     setQrError(errorMessage);
  //     Alert.alert('Lỗi', errorMessage);
  //   } finally {
  //     setIsGeneratingQR(false);
  //   }
  // };

  const handleCameraPress = useCallback(
    async (id: string) => {
      const imageType = imageData.find(item => item.id === id);
      if (!imageType) return;

      let status = Camera.getCameraPermissionStatus();
      if (status === 'not-determined') {
        status = await Camera.requestCameraPermission();
      }

      if (status === 'granted') {
        setSelectedImageType(imageType);
        setShowCamera(true);
      } else if (status === 'denied') {
        Alert.alert('Thông báo', 'Vui lòng cấp quyền truy cập camera để sử dụng tính năng này.', [
          {text: 'Hủy', style: 'cancel'},
          {text: 'Mở cài đặt', onPress: () => Linking.openSettings()},
        ]);
      } else if (status === 'restricted') {
        Alert.alert('Thông báo', 'Quyền truy cập camera đã bị hạn chế bởi chính sách thiết bị.', [{text: 'OK'}]);
      }
    },
    [imageData],
  );

  const handleCameraCapture = useCallback(
    (photos: PhotoFile[]) => {
      if (!selectedImageType) return;

      setImageData(prev => {
        const newData = [...prev];
        const index = newData.findIndex(item => item.id === selectedImageType.id);
        if (index !== -1) {
          const newImages = photos.map(photo => ({
            uri: `file://${photo.path}`,
            isTemp: true,
          }));
          newData[index] = {
            ...newData[index],
            images: [...newData[index].images, ...newImages],
          };
        }
        return newData;
      });
    },
    [selectedImageType],
  );
  const handleDeleteSingleImage = useCallback((id: string, index: number) => {
    setImageData(prev => {
      const newData = [...prev];
      const itemIndex = newData.findIndex(item => item.id === id);
      if (itemIndex !== -1) {
        newData[itemIndex] = {
          ...newData[itemIndex],
          images: newData[itemIndex].images.filter((_, idx) => idx !== index),
        };
      }
      return newData;
    });
  }, []);

  const handleGallery = useCallback(
    async (id: string) => {
      const imageType = imageData.find(item => item.id === id);
      if (!imageType) return;

      try {
        const result = await launchImageLibrary({
          mediaType: 'photo',
          quality: 0.8,
          selectionLimit: 0, // 0 means no limit
        });

        if (result.assets && result.assets.length > 0) {
          setImageData(prev => {
            const newData = [...prev];
            const index = newData.findIndex(item => item.id === id);
            if (index !== -1) {
              const newImages =
                result.assets?.map(asset => ({
                  uri: asset.uri || '',
                  isTemp: true,
                })) || [];

              newData[index] = {
                ...newData[index],
                images: [...newData[index].images, ...newImages],
              };
            }
            return newData;
          });
        }
      } catch (error) {
        console.error('Error picking image:', error);
      }
    },
    [imageData],
  );

  // Hàm xử lý chung cho cả xác nhận và gỡ xác nhận
  const handleModalConfirm = async () => {
    setIsConfirming(true);
    const actionCode = confirmAction === 'remove' ? ACTION_CODE.GO_XAC_NHAN_THU_TIEN : ACTION_CODE.XAC_NHAN_THU_TIEN;
    try {
      const isRemoveAction = confirmAction === 'remove';
      const params: ConfirmPaymentParams = {
        actionCode: actionCode,
        bt: chiTietBienLai?.bt || '',
      };
      const response = await getCommonExecute(params);
      // Xử lý response thành công
      if (response?.status === 200 || response?.data) {
        const successMessage = isRemoveAction ? 'Gỡ xác nhận thu tiền thành công!' : 'Xác nhận thu tiền thành công!';

        toast.success(successMessage, {
          position: 'top',
          duration: 3000,
        });
        setShowConfirmModal(false);

        // Gọi lại API để refresh dữ liệu và cập nhật trạng thái button
        await getChiTietBienLai();
      } else {
        throw new Error('Phản hồi không hợp lệ từ server');
      }
    } catch (error) {
      console.error(`${confirmAction} payment error:`, error);
    } finally {
      setIsConfirming(false);
    }
  };

  // Function to handle upload images for a specific category
  // Function to upload all images from all categories
  const handleUploadAllImages = async () => {
    // Get all temp images from all categories
    const allTempImages: Array<{image: ImageItem; categoryId: string; loai: 'bien_lai' | 'thanh_toan'}> = [];

    imageData.forEach(category => {
      const tempImages = category.images.filter(img => img.isTemp);
      const loai = category.id === '2' ? 'bien_lai' : 'thanh_toan';
      tempImages.forEach(image => {
        allTempImages.push({image, categoryId: category.id, loai});
      });
    });

    if (allTempImages.length === 0) {
      toast.info('Vui lòng chụp hoặc chọn ảnh trước khi tải lên');
      return;
    }

    setIsUploading(true);
    try {
      // Update UI to show upload in progress for all temp images
      setImageData(prev => {
        const updatedData = [...prev];
        updatedData.forEach(category => {
          category.images = category.images.map(img => ({
            ...img,
            uploadStatus: img.isTemp ? 'pending' : img.uploadStatus,
          }));
        });
        return updatedData;
      });

      // Upload all images in parallel
      const uploadPromises = allTempImages.map(async ({image, categoryId, loai}, index) => {
        try {
          const result = await uploadAndMapImage(image.uri, loai);

          // Update the specific image's status
          setImageData(prev => {
            const updatedData = [...prev];
            const categoryIndex = updatedData.findIndex(item => item.id === categoryId);
            if (categoryIndex !== -1) {
              const imageToUpdate = updatedData[categoryIndex].images.find(i => i.uri === image.uri);
              if (imageToUpdate) {
                imageToUpdate.uploadStatus = result.success ? 'success' : 'error';
                imageToUpdate.uploadResponse = result.success ? result.response : result.error;
                imageToUpdate.isTemp = false;
              }
            }
            return updatedData;
          });

          return result;
        } catch (error) {
          console.error(`🚨 Error uploading image ${index + 1}:`, error);

          // Update status to error
          setImageData(prev => {
            const updatedData = [...prev];
            const categoryIndex = updatedData.findIndex(item => item.id === categoryId);
            if (categoryIndex !== -1) {
              const imageToUpdate = updatedData[categoryIndex].images.find(i => i.uri === image.uri);
              if (imageToUpdate) {
                imageToUpdate.uploadStatus = 'error';
                imageToUpdate.isTemp = false;
              }
            }
            return updatedData;
          });

          return {success: false, error};
        }
      });

      // Wait for all uploads to complete using Promise.allSettled
      const results = await Promise.allSettled(uploadPromises);

      // Process results
      const successfulResults = results.filter(result => result.status === 'fulfilled' && result.value.success).map(result => (result as PromiseFulfilledResult<any>).value);

      const failedResults = results.filter(result => result.status === 'rejected' || (result.status === 'fulfilled' && !result.value.success));

      const successCount = successfulResults.length;
      const failCount = failedResults.length;

      // Show appropriate toast messages
      if (successCount > 0 && failCount === 0) {
        toast.success(`Đã tải lên thành công tất cả ${successCount} ảnh`);
      } else if (successCount > 0 && failCount > 0) {
        toast.success(`Đã tải lên thành công ${successCount}/${successCount + failCount} ảnh`);
      } else {
        toast.error('Tất cả ảnh tải lên thất bại. Vui lòng kiểm tra kết nối mạng và thử lại.');
      }
    } catch (error) {
      console.error('🚨 Error in handleUploadAllImages:', error);
      toast.error('Đã xảy ra lỗi khi tải ảnh lên');
    } finally {
      setIsUploading(false);
    }
  };

  const handleUploadImages = async (category: ImageData) => {
    // Check if category has any temp images
    const tempImages = category.images.filter(img => img.isTemp);

    if (tempImages.length === 0) {
      toast.info('Vui lòng chụp hoặc chọn ảnh trước khi tải lên');
      return;
    }

    setIsUploading(true);
    try {
      const loai = category.id === '2' ? 'bien_lai' : 'thanh_toan';

      // Update UI to show upload in progress for temp images
      setImageData(prev => {
        const updatedData = [...prev];
        const categoryIndex = updatedData.findIndex(item => item.id === category.id);
        if (categoryIndex !== -1) {
          updatedData[categoryIndex].images = updatedData[categoryIndex].images.map(img => ({
            ...img,
            uploadStatus: img.isTemp ? 'pending' : img.uploadStatus,
          }));
        }
        return updatedData;
      });

      // Upload images in parallel using Promise.allSettled for better error handling
      const uploadPromises = tempImages.map(async (img, index) => {
        try {
          const result = await uploadAndMapImage(img.uri, loai);

          // Update the specific image's status
          setImageData(prev => {
            const updatedData = [...prev];
            const categoryIndex = updatedData.findIndex(item => item.id === category.id);
            if (categoryIndex !== -1) {
              const imageToUpdate = updatedData[categoryIndex].images.find(i => i.uri === img.uri);
              if (imageToUpdate) {
                imageToUpdate.uploadStatus = result.success ? 'success' : 'error';
                imageToUpdate.uploadResponse = result.success ? result.response : result.error;
                imageToUpdate.isTemp = false;
              }
            }
            return updatedData;
          });

          return result;
        } catch (error) {
          console.error(`🚨 Error uploading image ${index + 1}:`, error);

          // Update status to error
          setImageData(prev => {
            const updatedData = [...prev];
            const categoryIndex = updatedData.findIndex(item => item.id === category.id);
            if (categoryIndex !== -1) {
              const imageToUpdate = updatedData[categoryIndex].images.find(i => i.uri === img.uri);
              if (imageToUpdate) {
                imageToUpdate.uploadStatus = 'error';
                imageToUpdate.isTemp = false;
              }
            }
            return updatedData;
          });

          return {success: false, error};
        }
      });

      // Wait for all uploads to complete using Promise.allSettled
      const results = await Promise.allSettled(uploadPromises);

      // Process results
      const successfulResults = results.filter(result => result.status === 'fulfilled' && result.value.success).map(result => (result as PromiseFulfilledResult<any>).value);

      const failedResults = results.filter(result => result.status === 'rejected' || (result.status === 'fulfilled' && !result.value.success));

      const successCount = successfulResults.length;
      const failCount = failedResults.length;

      // Show appropriate toast messages
      if (successCount > 0 && failCount === 0) {
        toast.success(`Đã tải lên thành công ${successCount} ảnh`);
      } else if (successCount > 0 && failCount > 0) {
        toast.success(`Đã tải lên thành công ${successCount} ảnh, ${failCount} ảnh thất bại`);
      } else {
        toast.error('Tất cả ảnh tải lên thất bại. Vui lòng kiểm tra kết nối mạng và thử lại.');
      }
    } catch (error) {
      console.error('Error in handleUploadImages:', error);
      toast.error('Đã xảy ra lỗi khi tải ảnh lên');
    } finally {
      setIsUploading(false);
    }
  };

  // Function to upload a single image and map it to the receipt
  const uploadAndMapImage = async (imageUri: string, loai: 'bien_lai' | 'thanh_toan') => {
    try {
      // Upload the image
      const fileName = `${loai}_${Date.now()}.jpg`;
      const uploadResponse = await uploadImage({path: imageUri.replace('file://', '')}, fileName);

      // Map the image to the receipt
      const mappingParams = {
        actionCode: ACTION_CODE.MAPPING_FILE_BIEN_LAI,
        bt_bien_lai: chiTietBienLai?.bt || '',
        bt_file: uploadResponse.bt_file,
        url: uploadResponse.url_file,
        loai: loai,
        trang_thai: 'D',
      };
      // Call the API to map the image to the receipt
      const mappingResponse = await getCommonExecute(mappingParams);
      return {
        success: true,
        response: uploadResponse,
        mappingResponse: mappingResponse,
      };
    } catch (error) {
      console.error(`🚨 Error uploading/mapping image:`, error);

      // Log chi tiết lỗi để debug
      if (error && typeof error === 'object') {
        console.error('🚨 Error details:', {
          message: (error as any).message,
          status: (error as any).status,
          response: (error as any).response?.data,
          stack: (error as any).stack,
        });
      }

      return {
        success: false,
        error: error,
      };
    }
  };

  const handlePayment = async () => {
    // Check if there are any temp images that haven't been uploaded
    const hasTempImages = imageData.some(category => category.images.some(img => img.isTemp));

    if (hasTempImages) {
      Alert.alert('Thông báo', 'Vui lòng tải ảnh lên trước khi xác nhận thu tiền. Sử dụng nút "Tải ảnh lên" bên cạnh tiêu đề.', [{text: 'OK'}]);
      return;
    }

    // Hiển thị modal xác nhận
    setConfirmAction('confirm');
    setShowConfirmModal(true);
  };

  const handleCopyAction = (title: string, value: string | number) => {
    const bankInfo = chiTietDonViThuHo.ten_tk && chiTietDonViThuHo.so_tk && chiTietDonViThuHo.ten_nh;
    if (bankInfo) {
      Clipboard.setString(String(value));
      toast.success(`Đã sao chép ${title}`);
    } else {
      console.warn('No account name to copy');
      toast.error('Không có tên tài khoản để sao chép');
    }
  };

  //RENDER

  const ImageCategory = ({item}: {item: ImageData}) => {
    const soLuongAnh = item?.images?.length > 0 ? `(${item.images.length})` : null;
    const handleImagePress = (tappedIndex: number) => {
      setPreviewImages(item.images);
      setPreviewInitialIndex(tappedIndex);
      setIsPreviewVisible(true);
    };
    return (
      <View key={item.id} style={styles.imageCategory}>
        <View style={styles.imageCategoryHeader}>
          <Text style={styles.imageCategoryTitle}>
            {item.title} {soLuongAnh}
          </Text>
          <TouchableOpacity style={styles.uploadButton} onPress={() => handleUploadImages(item)} activeOpacity={0.7}>
            <Text style={styles.uploadButtonText}>Tải ảnh lên</Text>
          </TouchableOpacity>
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.imageScrollContainer}>
          {/* Always show empty state with upload buttons */}
          <View style={styles.uploadContainer}>
            <View style={styles.uploadArea}>
              <View style={styles.uploadContent}>
                <View style={styles.uploadIconContainer}>
                  <Image source={R.icons.ic_image} style={{width: 32, height: 32, tintColor: colors.gray[400], opacity: 0.7}} resizeMode="contain" />
                </View>
                <Text style={styles.uploadText}>Thêm ảnh</Text>
                <Text style={styles.uploadSubText}>Chạm vào nút bên dưới để thêm</Text>
              </View>
            </View>
            <View style={styles.actionContainer}>
              <TouchableOpacity style={styles.actionButton} onPress={() => handleCameraPress(item.id)} activeOpacity={0.8}>
                <Image source={R.icons.ic_camera} style={{width: 20, height: 20, tintColor: colors.white}} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={() => handleGallery(item.id)} activeOpacity={0.8}>
                <Image source={R.icons.ic_image} style={{width: 20, height: 20, tintColor: colors.white}} />
              </TouchableOpacity>
            </View>
          </View>

          {/* All captured/selected images */}
          {item.images.map((image, index) => (
            <TouchableOpacity key={index} activeOpacity={0.9} onPress={() => handleImagePress(index)}>
              <View style={styles.additionalImageContainer}>
                <Image source={{uri: image.uri}} style={{width: '100%', height: '100%'}} resizeMode="cover" />
                {image.uploadStatus && (
                  <View
                    style={[
                      styles.uploadStatusOverlay,
                      image.uploadStatus === 'pending' && styles.uploadStatusPending,
                      image.uploadStatus === 'success' && styles.uploadStatusSuccess,
                      image.uploadStatus === 'error' && styles.uploadStatusError,
                    ]}>
                    {image.uploadStatus === 'pending' && <ActivityIndicator size="small" color={colors.white} />}
                    {image.uploadStatus === 'success' && <Text style={styles.uploadStatusIcon}>✓</Text>}
                    {image.uploadStatus === 'error' && <Text style={styles.uploadStatusIcon}>✗</Text>}
                  </View>
                )}
                {(image.isTemp || image.uploadStatus === 'error') && (
                  <TouchableOpacity style={styles.deleteButton} onPress={() => handleDeleteSingleImage(item.id, index)} activeOpacity={0.8}>
                    <Text style={styles.deleteButtonText}>×</Text>
                  </TouchableOpacity>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderContentLabel = (title: string, value: string | number, style?: any) => {
    return (
      <View style={styles.info}>
        <Text style={styles.infoTitle}>{title}</Text>
        <Text style={[styles.infoValue, {...style}]}>{value}</Text>
      </View>
    );
  };

  // Hàm hiển thị modal xác nhận gỡ
  const showRemoveConfirmModal = () => {
    setConfirmAction('remove');
    setShowConfirmModal(true);
  };

  // Hàm render footer button dựa trên trạng thái
  const renderFooterButton = () => {
    const trangThai = chiTietBienLai?.trang_thai;

    switch (trangThai) {
      case 'DA_THU':
        return (
          <Button
            title="Gỡ xác nhận đã thu"
            onPress={showRemoveConfirmModal}
            loading={isUploading || isConfirming || isLoadingDetail}
            variant="secondary" // Sử dụng variant khác để phân biệt
          />
        );
      case 'DANG_XU_LY':
        return <Button title="Xác nhận đã thu tiền" onPress={handlePayment} loading={isUploading || isConfirming || isLoadingDetail} />;
      case 'DA_HUY':
      case 'DA_CHUYEN':
        return null; // Không hiển thị button
      default:
        return null;
    }
  };

  const renderLabelBankInfo = (title: string, value: string | number) => {
    return (
      <View style={styles.infoRow}>
        <Text style={styles.bankInfoLabel}>{title}</Text>
        <View style={styles.bankAccountRow}>
          <Text style={styles.infoValue}>{value}</Text>
          <TouchableOpacity style={styles.copyButton} activeOpacity={0.7} onPress={() => handleCopyAction(title, value)}>
            <Image source={R.icons.ic_copy} style={{width: 16, height: 16, tintColor: colors.green}} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent showHeader headerTitle="Thu hộ" showBackButton onPressBack={() => navigation.goBack()} showFooter={renderFooterButton() !== null} footer={renderFooterButton()}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.sm}}>
        {/* Thông tin đơn */}
        <View>
          <Text style={styles.title}>Thông tin đơn</Text>
          <Card style={styles.infoContainer}>
            {renderContentLabel('Mã số BHXH', chiTietBienLai?.ma_so_bhxh || chiTietBienLai?.so_bhxh || '', {color: colors.primary})}
            {renderContentLabel('Họ và tên', chiTietBienLai?.ten || '')}
            {renderContentLabel('Số CCCD', chiTietBienLai?.cccd || chiTietBienLai?.cmt || '')}
            {renderContentLabel('Ngày sinh', chiTietBienLai?.ngay_sinh || '')}
            {renderContentLabel('Chi tiết', chiTietBienLai?.detail || '')}
            {renderContentLabel('Biên lai', chiTietBienLai?.so_bien_lai || '', {color: colors.primary})}
            {renderContentLabel('Cần thanh toán', `${(chiTietBienLai?.so_tien || 0).toLocaleString('vi-VN')}đ`, {color: colors.danger})}
            <Text style={styles.noteText}>Lưu ý: Kiểm tra lại thông tin trước khi thanh toán</Text>
          </Card>
        </View>
        {/* Phương thức thanh toán */}
        <View>
          <Text style={styles.title}>Phương thức thanh toán</Text>

          {/* Tab chọn phương thức */}
          <View style={styles.paymentTabs}>
            <TouchableOpacity
              style={[styles.paymentTab, styles.paymentTabFirst, paymentMethod === 'Chuyển khoản' && styles.paymentTabActive]}
              onPress={() => setPaymentMethod('Chuyển khoản')}
              activeOpacity={0.7}>
              <Text style={[styles.paymentTabText, paymentMethod === 'Chuyển khoản' && styles.paymentTabTextActive]}>Chuyển khoản</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.paymentTab, styles.paymentTabLast, paymentMethod === 'Tiền mặt' && styles.paymentTabActive]}
              onPress={() => setPaymentMethod('Tiền mặt')}
              activeOpacity={0.7}>
              <Text style={[styles.paymentTabText, paymentMethod === 'Tiền mặt' && styles.paymentTabTextActive]}>Tiền mặt</Text>
            </TouchableOpacity>
          </View>

          {/* Hiển thị thông tin chuyển khoản nếu chọn */}
          {paymentMethod === 'Chuyển khoản' && chiTietDonViThuHo && (
            <View>
              <Card style={styles.bankInfoContainer}>
                <View style={styles.bankInfo}>
                  <View style={styles.bankInfoContent}>
                    {renderLabelBankInfo('Tên chủ tài khoản:', chiTietDonViThuHo?.ten_tk?.toUpperCase() ?? '')}
                    {renderLabelBankInfo('Số tài khoản:', chiTietDonViThuHo?.so_tk ?? '')}
                    {renderLabelBankInfo('Tên ngân hàng', chiTietDonViThuHo?.ten_nh ?? '')}
                  </View>

                  {/* QR Code */}
                  <TouchableOpacity style={styles.qrContainer} onPress={handleShowQRCodeModal} activeOpacity={0.7}>
                    <Image source={R.icons.ic_qrcode} style={{width: 48, height: 48}} resizeMode="contain" />
                  </TouchableOpacity>
                </View>
              </Card>
              <Card style={styles.bankPaymentInfoContainer}>
                <Text style={styles.bankInfoText}>
                  Vui lòng thanh toán <Text style={styles.bankInfoTextAmount}>{chiTietBienLai?.so_tien?.toLocaleString('vi-VN')}đ</Text> và gửi ảnh vào ô bên dưới để hoàn tất thanh toán
                </Text>
              </Card>
            </View>
          )}

          {paymentMethod === 'Tiền mặt' && (
            <Card style={styles.cashInfoContainer}>
              <View style={styles.cashInfo}>
                <Image source={R.icons.ic_location} style={{width: 48, height: 48, tintColor: colors.green}} />
                <Text style={styles.cashInfoText}>Vui lòng đến điểm thu BHXH gần nhất để thanh toán</Text>
                <TouchableOpacity style={styles.findLocationButton} onPress={() => toast.info('Chức năng đang phát triển')} activeOpacity={0.7}>
                  <Text style={styles.findLocationText}>Tìm điểm thu gần nhất</Text>
                </TouchableOpacity>
              </View>
            </Card>
          )}
        </View>
        {/* Ảnh biên lai và thanh toán */}
        <View>
          <View style={styles.imageSectionHeader}>
            <Text style={styles.title}>Ảnh biên lai và thanh toán</Text>
            {/* Upload All Images Button */}
            {imageData.some(category => category.images.some(img => img.isTemp)) && (
              <TouchableOpacity style={styles.uploadAllButton} onPress={handleUploadAllImages} disabled={isUploading} activeOpacity={0.7}>
                <Text style={styles.uploadAllButtonText}>{isUploading ? 'Đang tải...' : 'Tải tất cả ảnh lên'}</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.imageSection}>
          {imageData
            .filter(
              item =>
                paymentMethod === 'Tiền mặt'
                  ? item.id === '2' // Chỉ hiển thị ảnh biên lai
                  : true, // Hiển thị tất cả
            )
            .map(item => (
              <ImageCategory key={item.id} item={item} />
            ))}
        </View>

        {/* Button thanh toán */}
        {/* <View style={styles.buttonWrapper}>
          <Button title={isUploading ? 'Đang tải ảnh lên...' : 'Thanh toán'} onPress={handlePayment} disabled={isUploading} />
        </View> */}
        <CustomModal isVisible={isShowQRCodeModal} onClose={handleCloseQRCodeModal} showCloseButton={true} title="Quét mã QR để thanh toán">
          <View style={styles.qrModalContainer}>
            {chiTietDonViThuHo?.file_qrcode && <Image source={{uri: `${CONFIG_SERVER.BASE_URL_API}` + chiTietDonViThuHo?.file_qrcode}} style={{width: 300, height: 300}} resizeMode="contain" />}

            <Text style={[styles.qrModalSubText, styles.qrModalSubTextAccountName]}>{chiTietDonViThuHo?.ten_tk?.toUpperCase() ?? ''}</Text>
            <Text style={styles.qrModalSubText}>{chiTietDonViThuHo?.so_tk ?? ''}</Text>
            <Text style={styles.qrModalSubText}>{chiTietDonViThuHo?.ten_nh ?? ''}</Text>
            {/* <Text style={styles.qrModalAmountText}>Số tiền: {paymentInfoData?.[0]?.amount?.toLocaleString('vi-VN')}đ</Text> */}

            <View style={styles.qrModalButtonContainer}>
              {/* <TouchableOpacity style={styles.qrModalButton} onPress={handleCopyAccountName} activeOpacity={0.7}>
                <Text style={styles.qrModalButtonText}>Lưu mã QR</Text>
              </TouchableOpacity> */}
              {/* <TouchableOpacity style={[styles.qrModalButton, styles.qrModalButtonSecondary]} onPress={handleCopyAccountNumber} activeOpacity={0.7}>
                <Text style={[styles.qrModalButtonText, styles.qrModalButtonTextSecondary]}>Sao chép STK</Text>
              </TouchableOpacity> */}
            </View>
          </View>
        </CustomModal>
      </ScrollView>

      <CameraModal visible={showCamera} setVisibale={() => setShowCamera(false)} title={selectedImageType?.title || ''} onClose={() => setShowCamera(false)} onCapture={handleCameraCapture} />

      <ImagePreviewModal visible={isPreviewVisible} images={previewImages} initialIndex={previewInitialIndex} onClose={() => setIsPreviewVisible(false)} />

      <ConfirmModal
        visible={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title={confirmAction === 'remove' ? 'Gỡ xác nhận thu tiền' : 'Xác nhận thu tiền'}
        message={confirmAction === 'remove' ? 'Bạn có chắc chắn muốn gỡ xác nhận đã thu tiền hay không?' : 'Bạn có chắc chắn xác nhận đã thu tiền hay không?'}
        confirmText="Đồng ý"
        cancelText="Để sau"
        onConfirm={handleModalConfirm}
        onCancel={() => setShowConfirmModal(false)}
        confirmVariant={confirmAction === 'remove' ? 'warning' : 'success'}
        type={confirmAction === 'remove' ? 'warning' : 'info'}
        loading={isConfirming}
      />
    </ScreenComponent>
  );
}
