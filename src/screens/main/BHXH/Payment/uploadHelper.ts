const uploadAllImages = async () => {
  setIsUploading(true);
  try {
    const newImageData = [...imageData];

    // Upload all temp images
    for (let categoryIndex = 0; categoryIndex < newImageData.length; categoryIndex++) {
      const category = newImageData[categoryIndex];
      const tempImages = category.images.filter(img => img.isTemp);

      if (tempImages.length > 0) {
        const uploadedUrls = await Promise.all(tempImages.map(img => uploadImage({path: img.uri.replace('file://', '')})));

        // Replace temp images with uploaded ones
        const permanentImages = category.images.filter(img => !img.isTemp).map(img => ({uri: img.uri, isTemp: false}));

        newImageData[categoryIndex].images = [...permanentImages, ...uploadedUrls.map(url => ({uri: url, isTemp: false}))];
      }
    }

    setImageData(newImageData);
    // Proceed with payment
    handlePaymentSubmit();
  } catch (error) {
    console.error('Error uploading images:', error);
    // TODO: Show error message
  } finally {
    setIsUploading(false);
  }
};
