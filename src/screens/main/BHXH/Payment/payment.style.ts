import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
    marginHorizontal: spacing.sm,
  },
  titleImage: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
  },
  infoContainer: {
    backgroundColor: colors.white,
    marginBottom: spacing.md,
    ...shadows.base,
  },
  info: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  infoRow: {
    gap: spacing.sm,
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  infoTitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  infoValue: {
    flexShrink: 1,
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
  },
  amountValue: {
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as any,
  },
  noteText: {
    fontSize: typography.fontSize.base,
    color: '#FF4800',
    marginTop: spacing.sm,
    fontWeight: typography.fontWeight.medium as any,
  },
  cardImage: {
    borderRadius: borderRadius.xl,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },

  imageCount: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    backgroundColor: colors.green + '15',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.full,
  },

  // Upload area styles
  uploadContainer: {
    position: 'relative',
    marginBottom: spacing.xl,
    width: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm, // Half width with spacing
    height: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm, // Square
  },
  uploadArea: {
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    borderRadius: borderRadius.lg,
    backgroundColor: '#F9FAFB',
    width: '100%',
    height: '100%',
    marginBottom: 0,
  },
  uploadContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: spacing.md,
    paddingBottom: spacing.xl + spacing.md, // Extra padding for button space
  },
  uploadIconContainer: {
    marginBottom: spacing.xs,
  },
  uploadText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
    fontWeight: typography.fontWeight.semibold as any,
    marginBottom: spacing.xs / 2,
  },
  uploadSubText: {
    fontSize: 10,
    color: colors.gray[500],
    textAlign: 'center',
    opacity: 0.8,
    paddingHorizontal: spacing.xs,
  },

  // Action buttons
  actionContainer: {
    position: 'absolute',
    bottom: -20, // Position overlapping the border
    left: 0,
    right: 0,
    flexDirection: 'row',
    gap: spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: colors.green,
    padding: 12,
    borderRadius: borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.base,
    borderWidth: 2,
    borderColor: colors.white,
  },

  // Image square styles (when images exist)
  imageSquare: {
    width: '100%',
    height: '100%',
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: colors.gray[100],
    borderWidth: 2,
    borderColor: colors.gray[300],
  },
  imageSquareFull: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageCountBadge: {
    position: 'absolute',
    bottom: spacing.sm,
    right: spacing.sm,
    backgroundColor: colors.dark + 'CC',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
  },
  imageCountBadgeText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold as any,
  },

  // Delete action button
  deleteActionButton: {
    backgroundColor: colors.danger,
  },
  deleteIcon: {
    color: colors.white,
    fontSize: 24,
    fontWeight: typography.fontWeight.bold as any,
    lineHeight: 24,
  },
  imageContainer: {
    gap: spacing.sm,
  },
  imageSection: {
    gap: spacing.sm,
    marginLeft: spacing.sm,
  },
  imageCategory: {
    marginBottom: spacing.md,
  },
  imageCategoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  imageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  imageList: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  imageCategoryTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
  },
  uploadButton: {
    backgroundColor: colors.green,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.base,
    marginRight: spacing.sm,
  },
  uploadButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.white,
    fontWeight: typography.fontWeight.medium as any,
  },
  imageScrollContainer: {
    paddingRight: spacing.md,
    gap: spacing.sm,
  },
  additionalImageContainer: {
    width: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm,
    height: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: colors.gray[100],
    position: 'relative', // Để hỗ trợ vị trí tuyệt đối của nút xóa
  },

  deleteButton: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
    width: 24,
    height: 24,
    backgroundColor: colors.danger + 'CC',
    borderRadius: borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  deleteButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold as any,
    lineHeight: 24,
  },
  imageGridRow: {
    flexDirection: 'row',
    gap: spacing.sm,
    justifyContent: 'center',
  },
  imageGridRowSingle: {
    justifyContent: 'flex-start',
    paddingHorizontal: spacing.sm,
  },
  buttonWrapper: {
    paddingVertical: spacing.xl,
  },

  // Payment method tabs
  paymentTabs: {
    flexDirection: 'row',
    marginHorizontal: spacing.sm,
    marginVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.lg,
    backgroundColor: colors.white,
    overflow: 'hidden',
  },
  paymentTab: {
    flex: 1,
    paddingVertical: spacing.sm + 2,
    backgroundColor: colors.white,
    alignItems: 'center',
  },
  paymentTabFirst: {
    borderRightWidth: 1,
    borderRightColor: colors.gray[300],
  },
  paymentTabLast: {
    // No border for last tab
  },
  paymentTabActive: {
    backgroundColor: colors.green,
  },
  paymentTabText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontWeight: typography.fontWeight.medium as any,
  },
  paymentTabTextActive: {
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as any,
  },

  // Bank transfer info
  bankInfoContainer: {
    backgroundColor: colors.white,
    marginHorizontal: spacing.sm,
    ...shadows.base,
  },
  bankInfo: {
    flexDirection: 'row',
    gap: spacing.sm,
    // justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: spacing.xs,
  },
  bankInfoContent: {
    flex: 1,
    gap: spacing.sm,
  },
  bankInfoTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  bankInfoRow: {
    gap: spacing.xs,
  },
  bankInfoLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  bankInfoValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
  },
  bankAccountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  bankAccountNumber: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontWeight: typography.fontWeight.bold as any,
    flex: 1,
  },
  bankTransferContent: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
    flex: 1,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.base,
  },

  copyText: {
    fontSize: typography.fontSize.xs,
    color: colors.green,
    fontWeight: typography.fontWeight.medium as any,
  },
  qrContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrCodeContainer: {
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.sm,
  },
  qrText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
  },

  // Cash payment info
  cashInfoContainer: {
    backgroundColor: colors.white,
    marginBottom: spacing.md,
    marginHorizontal: spacing.sm,
    ...shadows.base,
  },
  cashInfo: {
    alignItems: 'center',
    gap: spacing.md,
    paddingVertical: spacing.lg,
  },

  cashInfoText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
  },
  findLocationButton: {
    backgroundColor: colors.green,
    paddingHorizontal: spacing.lg,
    paddingVertical: 12,
    borderRadius: borderRadius.lg,
  },
  findLocationText: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    fontWeight: typography.fontWeight.medium as any,
  },

  // QR Modal styles
  qrModalContainer: {
    alignItems: 'center',
    gap: spacing.sm,
  },
  qrModalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    textAlign: 'center',
  },
  qrModalImage: {
    width: 300,
    height: 300,
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.lg,
  },
  qrModalSubText: {
    fontSize: typography.fontSize.base,
  },
  qrModalNote: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: spacing.md,
  },
  qrModalButtonContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginTop: spacing.md,
  },
  qrModalButton: {
    backgroundColor: colors.green,
    paddingHorizontal: spacing.lg,
    paddingVertical: 12,
    borderRadius: borderRadius.lg,
  },
  qrModalButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as any,
  },
  qrModalButtonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.green,
  },
  qrModalButtonTextSecondary: {
    color: colors.green,
  },
  qrModalSubTextAccountName: {
    fontWeight: typography.fontWeight.bold as any,
  },
  qrModalAmountText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
    marginTop: spacing.sm,
  },

  // QR Loading states
  qrLoadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 300,
    height: 300,
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.lg,
    gap: spacing.md,
  },
  qrLoadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
  },

  // QR Error states
  qrErrorContainer: {
    alignItems: 'center',
    gap: spacing.md,
  },
  qrErrorText: {
    fontSize: typography.fontSize.base,
    color: colors.danger,
    textAlign: 'center',
    paddingHorizontal: spacing.md,
  },
  retryButton: {
    backgroundColor: colors.green,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.lg,
  },
  retryButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as any,
  },

  bankPaymentInfoContainer: {
    backgroundColor: '#c0e3d2',
    marginBottom: spacing.md,
  },
  bankInfoText: {
    color: colors.black,
    fontSize: typography.fontSize.base,
    textAlign: 'center',
    lineHeight: 24,
  },
  bankInfoTextAmount: {
    color: colors.green,
    fontWeight: typography.fontWeight.bold as any,
  },

  // Upload status overlay styles
  uploadStatusOverlay: {
    position: 'absolute',
    top: spacing.xs,
    left: spacing.xs,
    width: 24,
    height: 24,
    borderRadius: borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  uploadStatusPending: {
    backgroundColor: colors.gray[500] + 'CC',
  },
  uploadStatusSuccess: {
    backgroundColor: colors.green + 'CC',
  },
  uploadStatusError: {
    backgroundColor: colors.gray[400] + 'CC',
  },
  uploadStatusIcon: {
    color: colors.white,
    fontSize: 14,
    fontWeight: typography.fontWeight.bold as any,
    lineHeight: 14,
  },

  // Image section header styles
  imageSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  uploadAllButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.base,
    minWidth: 120,
    marginRight: spacing.sm,
  },
  uploadAllButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    textAlign: 'center',
  },

  uploadIcon: {
    width: 32,
    height: 32,
    tintColor: colors.gray[400],
    opacity: 0.7,
  },
});
