import R from '@assets/R';
import {Card, TextField} from '@components/common';
import ScreenComponent from '@components/common/ScreenComponent';
import {ACTION_CODE} from '@constants/axios';
import {colors} from '@constants/theme';
import {MainNavigationProp} from '@navigation/types';
import {useFocusEffect} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ActivityIndicator, FlatList, Image, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import {styles} from './paymenthistory.style';
import {BienLaiItem, DanhSachBienLaiResponse, LoadMoreState} from './types';

export default function PaymentHistoryScreen({navigation, route}: {navigation: MainNavigationProp; route: any}) {
  console.log('PaymentHistoryScreen ');

  const {bt_tvien} = route?.params;
  const [searchValue, setSearchValue] = useState('');
  const [bienLaiList, setBienLaiList] = useState<BienLaiItem[]>([]);
  const [loadMoreState, setLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: 10,
  });

  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [isRefreshingFromFocus, setIsRefreshingFromFocus] = useState(false);
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const isFirstMount = useRef(true);
  const apiCallCount = useRef(0);
  const isInitialLoadDone = useRef(false);
  // FlatList ref để reset layout
  const flatListRef = useRef<FlatList>(null);

  const getDanhSachBienLai = useCallback(
    async (isLoadMore: boolean = false, customSearchValue?: string) => {
      apiCallCount.current += 1;

      try {
        // Get current values from state
        let currentPage: number = 1;
        let pageSize: number = 10;

        setLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize;
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        const searchTerm = customSearchValue !== undefined ? customSearchValue : searchValue;

        const params = {
          nsd_lap_bien_lai: '',
          trang_thai: '',
          so_bhxh: '',
          ten: '',
          cmt: '',
          nd_tim: searchTerm,
          bt_tvien: bt_tvien ? bt_tvien : '',
          trang: currentPage!,
          so_dong: pageSize!,
          actionCode: ACTION_CODE.DANH_SACH_BIEN_LAI,
        };

        const response = await getCommonExecute(params);
        console.log('🚀 ~ PaymentHistoryScreen ~ response:', response);

        if (response?.data) {
          const responseData: DanhSachBienLaiResponse = response.data;
          const newItems = responseData.data || [];
          const totalItems = responseData.tong_so_dong || 0;

          setBienLaiList(prev => {
            let result: BienLaiItem[];
            if (isLoadMore) {
              // Merge data cho loadmore
              result = [...prev, ...newItems];
            } else {
              // Replace data cho search/refresh
              result = newItems;
            }
            return result;
          });

          setLoadMoreState(prev => {
            const hasMoreItems = currentPage * prev.pageSize < totalItems;

            return {
              ...prev,
              isLoading: false,
              isLoadingMore: false,
              currentPage: currentPage,
              totalItems: totalItems,
              hasMore: hasMoreItems,
            };
          });

          // Reset FlatList layout sau khi load data - chỉ khi pull-to-refresh
          if (!isLoadMore && isPullToRefresh && flatListRef.current) {
            setTimeout(() => {
              flatListRef.current?.scrollToOffset({offset: 0, animated: false});
            }, 100);
          }
        }
      } catch (error) {
        console.log('🚀 ~ getDanhSachBienLai ~ error:', error);
        setLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      }
    },
    [searchValue, bt_tvien, isPullToRefresh], // Thêm dependencies cần thiết
  );

  // Handle load more khi scroll đến cuối danh sách
  const handleLoadMore = useCallback(() => {
    if (loadMoreState.hasMore && !loadMoreState.isLoadingMore && !loadMoreState.isLoading) {
      console.log('🔄 Loading more data...');
      getDanhSachBienLai(true);
    }
  }, [loadMoreState.hasMore, loadMoreState.isLoadingMore, loadMoreState.isLoading, getDanhSachBienLai]);

  // Handle pull to refresh
  const handleRefresh = useCallback(() => {
    console.log('🔄 Pull to refresh...');
    setIsPullToRefresh(true);
    getDanhSachBienLai(false).finally(() => {
      setIsPullToRefresh(false);
    });
  }, [getDanhSachBienLai]);

  // Handle search khi bấm icon search
  const handleSearchPress = useCallback(() => {
    console.log('🔍 Search pressed with text:', searchValue);
    // Reset về trang đầu khi search
    setLoadMoreState(prev => ({
      ...prev,
      currentPage: 1,
      hasMore: true,
    }));
    // Gọi API search
    getDanhSachBienLai(false, searchValue);
  }, [searchValue, getDanhSachBienLai]);

  // Handle thay đổi text input (chỉ update state, không gọi API)
  const handleSearchTextChange = useCallback((text: string) => {
    setSearchValue(text);
  }, []);

  // Load data lần đầu
  useEffect(() => {
    if (!isInitialLoadDone.current) {
      isInitialLoadDone.current = true;
      getDanhSachBienLai();
    }
  }, []); // Chỉ chạy 1 lần khi component mount

  // Reset initial mount flag after a delay
  useEffect(() => {
    setTimeout(() => setIsInitialMount(false), 1000);
  }, []);

  // Track loading state để reset layout khi loading finish - chỉ khi pull-to-refresh
  useEffect(() => {
    // Chỉ reset layout khi pull-to-refresh xong, không reset khi loading từ focus/back
    if (!loadMoreState.isLoading && isPullToRefresh && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({offset: 0, animated: false});
      }, 150);
    }
  }, [loadMoreState.isLoading, isPullToRefresh]);

  // Use useFocusEffect to refresh data when returning from other screens
  useFocusEffect(
    React.useCallback(() => {
      // Only refresh if:
      // 1. Not on initial mount
      // 2. We need to refresh (flag was set)
      if (!isInitialMount && needsRefresh) {
        console.log('🔄 Refreshing biên lai data after returning from other screen');
        setIsRefreshingFromFocus(true); // Flag để disable dialog loading
        getDanhSachBienLai(false, searchValue).then(() => {
          // Reset FlatList layout after data refresh để fix spacing issue
          // Tăng timeout để đảm bảo dialog loading đã disappear
          setTimeout(() => {
            flatListRef.current?.scrollToOffset({offset: 0, animated: false});
            setIsRefreshingFromFocus(false); // Reset flag
          }, 300);
        });
        setNeedsRefresh(false); // Reset flag immediately
      }
    }, [isInitialMount, needsRefresh, searchValue]),
  );

  // Component hiển thị loading footer khi load more
  const renderLoadingFooter = () => {
    // Hiển thị loading khi đang load more
    if (loadMoreState.isLoadingMore) {
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Đang tải thêm...</Text>
        </View>
      );
    }

    // Hiển thị thông báo "Đã hiển thị hết" khi không còn dữ liệu và có ít nhất 1 item
    if (!loadMoreState.hasMore && bienLaiList.length > 0) {
      return (
        <View style={styles.endDataContainer}>
          <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
          <Text style={styles.endDataText}>Bạn đã xem tất cả {bienLaiList.length} biên lai</Text>
        </View>
      );
    }

    return null;
  };

  const renderPaymentHistory = ({item}: {item: BienLaiItem}) => {
    const renderLabel = (title: string, value: any, style?: any) => {
      return (
        <View style={styles.paymentHistoryRow}>
          <Text style={styles.title}>{title}</Text>
          <Text style={[styles.value, style]}>{value}</Text>
        </View>
      );
    };
    return (
      <Card>
        {renderLabel('Số biên lai', item.so_bien_lai, {fontWeight: 'bold', color: colors.primary})}
        {renderLabel('Ngày thu tiền', item.ngay_lap_bien_lai)}
        {renderLabel('Số tiền', item.so_tien, {fontWeight: 'bold', color: colors.warning})}
        {renderLabel('Đơn vị', item.don_vi_bhxh_ten)}
        {renderLabel('Cán bộ thu', item.nguoi_tao)}
      </Card>
    );
  };
  return (
    <ScreenComponent showHeader headerTitle="Lịch sử nộp tiền" showBackButton onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        {/* TextField tìm kiếm */}
        <TextField
          placeholder="Nhập thông tin tìm kiếm..."
          value={searchValue}
          onChangeText={handleSearchTextChange}
          onSubmitEditing={handleSearchPress}
          returnKeyType="search"
          showPlaceholderWhenEmpty={true}
          rightIconType={
            <TouchableOpacity onPress={handleSearchPress} style={styles.searchIconContainer}>
              <Image source={R.icons.ic_search} style={styles.searchIcon} />
            </TouchableOpacity>
          }
          inputContainerStyle={styles.searchInputContainer}
        />
        {/* Danh sách lịch sử nộp tiền */}
        <FlatList
          ref={flatListRef}
          data={bienLaiList}
          renderItem={renderPaymentHistory}
          keyExtractor={(_, index) => String(index)}
          ListEmptyComponent={
            loadMoreState.isLoading ? (
              <View style={{padding: 20, alignItems: 'center'}}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.description, {marginTop: 10}]}>Đang tải dữ liệu...</Text>
              </View>
            ) : (
              <Text style={styles.description}>Không có dữ liệu</Text>
            )
          }
          ListFooterComponent={renderLoadingFooter}
          showsVerticalScrollIndicator={false}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={<RefreshControl refreshing={isPullToRefresh} onRefresh={handleRefresh} colors={[colors.primary]} tintColor={colors.primary} />}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={10}
        />
      </View>
    </ScreenComponent>
  );
}
