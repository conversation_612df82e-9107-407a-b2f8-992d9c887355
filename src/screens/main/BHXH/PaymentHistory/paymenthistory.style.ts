import {colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  searchIcon: {
    width: 24,
    height: 24,
  },
  searchIconContainer: {
    padding: spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentHistoryItem: {
    flexDirection: 'row',
    gap: spacing.xl,
  },
  paymentHistoryRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  value: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  searchInputContainer: {
    marginHorizontal: spacing.sm,
  },
  // Styles cho load more
  loadingFooter: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: spacing.sm,
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  // End of Data State
  endDataContainer: {
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: spacing.sm,
    marginTop: spacing.md,
    marginBottom: spacing.xl,
  },
  endDataTitle: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.green,
    textAlign: 'center',
  },
  endDataText: {
    marginTop: spacing.xs,
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 20,
  },
});
