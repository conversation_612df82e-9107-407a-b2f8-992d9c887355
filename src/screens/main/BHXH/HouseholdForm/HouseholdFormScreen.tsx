import {ActionSheetModal, ActionSheetOption, <PERSON>ton, Card, createToastHelpers, DateTimePickerComponent, Icon, Radio, ScreenComponent, TextField, useToast} from '@components/common';
import {ACTION_CODE} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp, MainStackParamList} from '@navigation/types';
import {RouteProp, useFocusEffect, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import {AppDispatch, RootState} from '@store/index';
import {householdFormValidation} from '@utils/validationSchemas';
import moment from 'moment';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Controller, useForm, useWatch} from 'react-hook-form';
import {<PERSON><PERSON>, BackHandler, Image, KeyboardAvoidingView, Linking, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {formatDateForAPI, toApiGender} from '@utils/formatters';
import {useDispatch, useSelector} from 'react-redux';
import {styles} from './householdform.style';
import DocumentScanner, {ScanDocumentOptions} from 'react-native-document-scanner-plugin';
import {Camera} from 'react-native-vision-camera';
import R from '@assets/R';
import {dimensions} from '@constants/dimensions';

interface LoadingState {
  isSubmitting: boolean;
}

type HouseHoldFormData = {
  typeHousehold: string;
  province: string; // Mã tỉnh
  provinceName: string; // Tên tỉnh để hiển thị
  ward: string; // Mã xã
  wardName: string; // Tên xã để hiển thị
  address: string;
};

interface AddHouseholdFormData {
  ma_dvi: string;
  bt: string;
  ten: string;
  cmt: string;
  ngay_sinh: string;
  gioi_tinh: string;
  tinh_thanh: string;
  dia_chi: string; // Thêm trường địa chỉ
  phuong_xa: string;
  email: string;
  dthoai: string;
  so_bhxh: string;
  loai: string;
  stt: number;
  actionCode: string;
}

type HouseholdOwnerData = {
  householdOwnerCodeBHXH: string;
  householdOwnerName: string;
  householdOwnerCCCD: string;
  householdOwnerGender: string;
  householdOwnerDateOfBirth: Date;
  householdOwnerPhone: string;
  householdOwnerEmail: string;
};

export default function HouseholdFormScreen({route}: {route: RouteProp<MainStackParamList, typeof MAIN_SCREENS.HOUSEHOLD_FORM>}) {
  logger.log('HouseholdFormScreen');
  const params = route?.params as any;

  // Function để handle back button - gọi callback nếu có update thành công
  const handleBackPress = () => {
    logger.log('🔙 Back button pressed from HouseholdForm - hasSuccessfulUpdate:', hasSuccessfulUpdate);
    logger.log('🔙 params?.onUpdateInfo:', !!params?.onUpdateInfo);

    // Nếu có update thành công, gọi callback để refresh dữ liệu ở màn trước
    if (hasSuccessfulUpdate && params?.onUpdateInfo) {
      logger.log('🔄 Calling onUpdateInfo callback');
      params.onUpdateInfo({});
    } else {
      logger.log('ℹ️ No successful update or no callback available');
    }

    navigation.goBack();
  };
  const isCreateMode = !params?.infoData && !params?.chiTietBienLai && params?.mode !== 'edit';

  const isEditHouseholdOnly = params?.isEditHouseholdOnly || false;
  const isEditMode = params?.mode === 'edit'; // Chế độ chỉnh sửa từ HouseholdInfo

  const headerTitle = isCreateMode ? 'Thêm hộ gia đình mới' : isEditHouseholdOnly ? 'Điều chỉnh thông tin hộ' : 'Thông tin hộ gia đình';
  const buttonTitle = isCreateMode ? 'Tạo hộ gia đình' : 'Cập nhật';

  const navigation = useNavigation<MainNavigationProp>();
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  const {provinces, wards, loadingProvinces} = useSelector((state: RootState) => state.address);
  const [showHouseholdTypeModal, setShowHouseholdTypeModal] = useState(false);
  const [showProvinceModal, setShowProvinceModal] = useState(false);
  const [showWardModal, setShowWardModal] = useState(false);
  const [householdTypes, setHouseholdTypes] = useState<any[]>([]);
  const [frontCccdImage, setFrontCccdImage] = useState<string | null>(null);
  const [backCccdImage, setBackCccdImage] = useState<string | null>(null);
  const {danhSachLoaiHoGiaDinh} = useSelector((state: RootState) => state.commonCategories);
  const dispatch = useDispatch<AppDispatch>();

  // LoadingState management
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isSubmitting: false,
  });
  const [hasSuccessfulUpdate, setHasSuccessfulUpdate] = useState(false); // Track khi có update thành công

  // Helper function to get default values
  const getDefaultValues = () => {
    const infoData = params?.infoData;
    const chiTietBienLai = params?.chiTietBienLai;

    return {
      // Household information
      typeHousehold: infoData?.loai || chiTietBienLai?.loai_ho_gia_dinh || '',
      province: infoData?.tinh_thanh || chiTietBienLai?.tinh_thanh || '',
      provinceName: infoData?.tinh_thanh_ten || '',
      ward: infoData?.phuong_xa || chiTietBienLai?.phuong_xa || '',
      wardName: infoData?.phuong_xa_ten || '',
      address: infoData?.dia_chi || chiTietBienLai?.dia_chi || '',

      // Household owner information
      householdOwnerCodeBHXH: infoData?.so_bhxh || chiTietBienLai?.chu_ho_so_bhxh || '',
      householdOwnerName: infoData?.ten || chiTietBienLai?.ten_chu_ho || '',
      householdOwnerCCCD: infoData?.cmt || chiTietBienLai?.chu_ho_cccd || '',
      householdOwnerGender: infoData?.gioi_tinh || chiTietBienLai?.chu_ho_gioi_tinh || 'NAM',
      householdOwnerDateOfBirth: infoData?.ngay_sinh ? moment(infoData.ngay_sinh, 'YYYYMMDD').toDate() : chiTietBienLai?.chu_ho_ngay_sinh || '',
      householdOwnerPhone: infoData?.dthoai || chiTietBienLai?.chu_ho_dien_thoai || '',
      householdOwnerEmail: infoData?.email || chiTietBienLai?.chu_ho_email || '',
    };
  };

  const {
    control,
    setValue,
    handleSubmit,
    formState: {errors, isSubmitting},
    trigger,
    setError,
    clearErrors,
    getValues,
  } = useForm<HouseHoldFormData & HouseholdOwnerData>({
    mode: 'onChange',
    defaultValues: getDefaultValues(),
  });

  const selectedHouseholdType = useWatch({control, name: 'typeHousehold'});
  const selectedProvince = useWatch({control, name: 'province'});
  const selectedWard = useWatch({control, name: 'ward'});
  const provinceName = useWatch({control, name: 'provinceName'});
  const wardName = useWatch({control, name: 'wardName'});
  const selectedHouseholdOwnerGender = useWatch({control, name: 'householdOwnerGender'});

  useEffect(() => {
    if (selectedProvince && !provinceName && provinces.length > 0) {
      const provinceData = provinces.find(p => p.ma === selectedProvince);
      if (provinceData) {
        setValue('provinceName', provinceData.ten);
      }
    }
    if (selectedWard && !wardName && wards.length > 0) {
      const wardData = wards.find((w: any) => w.ma === selectedWard);
      if (wardData) {
        setValue('wardName', wardData.ten);
      }
    }
  }, [selectedProvince, provinceName, selectedWard, wardName, provinces, wards, setValue]);

  const getHouseholdType = useCallback(async () => {
    try {
      const response = await getCommonExecute({
        actionCode: ACTION_CODE.GET_LOAI_HO_GIA_DINH,
      });
      const listHouseholdType = response.data;

      if (listHouseholdType && Array.isArray(listHouseholdType)) {
        setHouseholdTypes(listHouseholdType);
      } else {
        setHouseholdTypes([]);
        toast.warning('Không tải được danh sách loại hộ gia đình', {
          duration: 3000,
          position: 'top',
        });
      }
    } catch (error) {
      console.error('Error fetching household types:', error);
      setHouseholdTypes([]);
      toast.error('Lỗi khi tải danh sách loại hộ gia đình', {
        duration: 3000,
        position: 'top',
      });
    }
  }, []);

  const getTenLoaiHoGiaDinh = useCallback(
    (loai: string | undefined) => {
      if (!loai) {
        return '';
      }

      // First try to find in householdTypes (from API)
      const householdType = householdTypes.find(type => (type.ma || type.id || type.value) === loai);
      if (householdType) {
        return householdType.ten || householdType.name || householdType.label || '';
      }

      // Fallback to Redux store data
      const loaiHoGiaDinh = danhSachLoaiHoGiaDinh.find(item => item.ma === loai);
      return loaiHoGiaDinh?.ten || '';
    },
    [householdTypes, danhSachLoaiHoGiaDinh],
  );

  useEffect(() => {
    getHouseholdType();
  }, [getHouseholdType]);

  // Load initial data from chiTietBienLai (similar to ParticipantFormScreen)
  useEffect(() => {
    if (params?.chiTietBienLai) {
      const chiTietBienLai = params.chiTietBienLai;

      // Populate form with existing data from chiTietBienLai
      setValue('typeHousehold', chiTietBienLai.loai_ho_gia_dinh || '');
      setValue('province', chiTietBienLai.tinh_thanh || '');
      setValue('ward', chiTietBienLai.phuong_xa || '');
      setValue('address', chiTietBienLai.dia_chi || '');

      // Household owner information
      setValue('householdOwnerCodeBHXH', chiTietBienLai.chu_ho_so_bhxh || '');
      setValue('householdOwnerName', chiTietBienLai.ten_chu_ho || '');
      setValue('householdOwnerCCCD', chiTietBienLai.chu_ho_cccd || '');
      setValue('householdOwnerPhone', chiTietBienLai.chu_ho_dien_thoai || '');
      setValue('householdOwnerEmail', chiTietBienLai.chu_ho_email || '');

      // Set gender as boolean based on API value
      setValue('householdOwnerGender', chiTietBienLai.chu_ho_gioi_tinh || '');

      // Parse ngày sinh thành Date object if available
      if (chiTietBienLai.chu_ho_ngay_sinh) {
        const parsedDate = moment(chiTietBienLai.chu_ho_ngay_sinh, 'YYYYMMDD').toDate();
        setValue('householdOwnerDateOfBirth', parsedDate);
      }
    }
  }, [params?.chiTietBienLai, setValue]);

  // Handle Android back button
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        logger.log('📱 Android back button pressed in HouseholdForm');
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [handleBackPress]),
  );

  const householdTypeOptions: ActionSheetOption[] = useMemo(
    () =>
      householdTypes.map(type => ({
        id: type.ma || type.id || type.value,
        title: type.ten || type.name || type.label || 'Không xác định',
        onPress: () => handleHouseholdTypeSelect(type.ma || type.id || type.value, type.ten || type.name || type.label || 'Không xác định'),
      })),
    [householdTypes],
  );

  // Use getTenLoaiHoGiaDinh function to display household type name
  const selectedTypeHouseholdName = useMemo(() => {
    return getTenLoaiHoGiaDinh(selectedHouseholdType);
  }, [selectedHouseholdType, getTenLoaiHoGiaDinh]);

  const provinceOptions: ActionSheetOption[] = useMemo(
    () =>
      provinces.map(province => ({
        id: province.ma,
        title: province.ten,
        onPress: () => handleProvinceSelect(province.ma, province.ten),
      })),
    [provinces],
  );

  const wardOptions: ActionSheetOption[] = useMemo(() => {
    if (!selectedProvince) {
      return [];
    }
    // Lọc phường/xã theo mã tỉnh
    return wards
      .filter((w: any) => w.ma_tinh === selectedProvince)
      .map((w: any) => ({
        id: w.ma,
        title: w.ten,
        onPress: () => handleWardSelect(w.ma, w.ten),
      }));
  }, [selectedProvince, wards]);

  // Dropdown handlers
  const handleHouseholdTypeSelect = (value: string, label: string) => {
    setValue('typeHousehold', value, {shouldValidate: true}); // Lưu mã và trigger validation
    clearErrors('typeHousehold'); // Clear error when selected
    setShowHouseholdTypeModal(false);
  };

  const handleProvinceSelect = (value: string, label: string) => {
    setValue('province', value);
    setValue('provinceName', label);
    setValue('ward', '');
    setValue('wardName', '');
    clearErrors('provinceName'); // Clear error when selected
    setShowProvinceModal(false);
  };

  const handleWardSelect = (value: string, label: string) => {
    setValue('ward', value);
    setValue('wardName', label);
    clearErrors('wardName'); // Clear error when selected
    setShowWardModal(false);
  };

  const handleScanImage = async (setImageCallback: (uri: string) => void) => {
    const requestCameraPermission = async () => {
      let status = Camera.getCameraPermissionStatus();

      // Nếu chưa được hỏi quyền, yêu cầu quyền và đợi user chọn
      if (status === 'not-determined') {
        status = await Camera.requestCameraPermission();

        // Sau khi user chọn từ dialog hệ thống, kiểm tra lại status
        if (status === 'granted') {
          return true;
        } else if (status === 'denied') {
          // User đã từ chối quyền từ dialog hệ thống
          return false;
        }
      }

      // Nếu đã có quyền
      if (status === 'granted') {
        return true;
      }

      // Nếu user đã từ chối quyền trước đó, hiển thị dialog để mở cài đặt
      if (status === 'denied') {
        Alert.alert('Cho phép Sàn Bảo Hiểm chụp ảnh và quay video?', 'Vui lòng cấp quyền truy cập camera để sử dụng tính năng này.', [
          {text: 'Không cho phép', style: 'cancel'},
          {text: 'Mở cài đặt', onPress: () => Linking.openSettings()},
        ]);
        return false;
      }

      // Nếu quyền bị hạn chế
      if (status === 'restricted') {
        Alert.alert('Thông báo', 'Quyền truy cập camera đã bị hạn chế bởi chính sách thiết bị.', [{text: 'OK'}]);
        return false;
      }

      return false;
    };

    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      return;
    }

    try {
      const {scannedImages} = await DocumentScanner.scanDocument({
        maxNumDocuments: 1,
      });

      if (scannedImages && scannedImages.length > 0) {
        const imageUri = scannedImages[0];
        // On Android, the URI might not have a file:// prefix, which is required for the Image component.
        const correctedUri = Platform.OS === 'android' && !imageUri.startsWith('file://') ? `file://${imageUri}` : imageUri;
        setImageCallback(correctedUri);
      }
    } catch (error) {
      console.error('Error scanning document:', error);
    }
  };

  const addHousehold = useCallback(
    async (formData: HouseHoldFormData & HouseholdOwnerData) => {
      // Prevent concurrent submissions
      if (loadingState.isSubmitting) {
        return;
      }

      setLoadingState(prev => ({...prev, isSubmitting: true}));
      try {
        // Tìm thông tin loại hộ gia đình từ mã được chọn
        const selectedHouseholdTypeData = householdTypes.find(type => (type.ma || type.id || type.value) === selectedHouseholdType);

        // Sử dụng interface AddHouseholdFormData để type safety
        const householdData: AddHouseholdFormData = {
          ma_dvi: '',
          bt: '',

          ten: formData.householdOwnerName || '',
          cmt: formData.householdOwnerCCCD || '',
          ngay_sinh: formData.householdOwnerDateOfBirth ? formatDateForAPI(formData.householdOwnerDateOfBirth) : '',
          gioi_tinh: toApiGender(formData.householdOwnerGender),
          tinh_thanh: formData.province || '',
          dia_chi: formData.address || '',
          phuong_xa: formData.ward || '',
          email: formData.householdOwnerEmail || '',
          dthoai: formData.householdOwnerPhone || '',
          so_bhxh: formData.householdOwnerCodeBHXH || '',
          loai: selectedHouseholdTypeData?.ma || selectedHouseholdTypeData?.id || selectedHouseholdType || '',
          stt: 0,
          actionCode: ACTION_CODE.ADD_HO_GIA_DINH,
        };

        const response = await getCommonExecute(householdData);

        if (response?.data) {
          // Gọi callback để refresh danh sách
          if (params?.onUpdateInfo) {
            params.onUpdateInfo(response.data);
          }

          toast.success('Thêm hộ gia đình thành công!', {
            duration: 3000,
            position: 'top',
          });

          setTimeout(() => {
            navigation.goBack();
          }, 500);
        } else {
          toast.error('Thêm hộ gia đình thất bại', {
            duration: 3000,
            position: 'top',
          });
        }

        return response;
      } catch (error) {
        console.error('Error adding household:', error);
        toast.error('Có lỗi xảy ra khi thêm hộ gia đình. Vui lòng thử lại.', {
          duration: 3000,
          position: 'top',
        });
        throw error;
      } finally {
        setLoadingState(prev => ({...prev, isSubmitting: false}));
      }
    },
    [householdTypes, selectedHouseholdType, params, navigation, loadingState.isSubmitting],
  );

  const updateHouseholdInfo = useCallback(
    async (formData: HouseHoldFormData & HouseholdOwnerData) => {
      // Prevent concurrent submissions
      if (loadingState.isSubmitting) {
        return;
      }

      setLoadingState(prev => ({...prev, isSubmitting: true}));
      try {
        const infoData = params?.infoData;
        // Validate that we have household data to update
        if (!infoData?.bt) {
          toast.error('Không tìm thấy thông tin hộ gia đình để cập nhật', {
            position: 'top',
            duration: 3000,
          });
          return;
        }
        // Follow ParticipantFormScreen pattern: spread existing data and update specific fields
        const updateParams: any = {
          // Spread existing household data to preserve all fields
          ...infoData,
          // API parameters
          actionCode: ACTION_CODE.ADD_HO_GIA_DINH,
          bt: infoData.bt,
          // Update household information from form
          loai: formData.typeHousehold,
          tinh_thanh: formData.province,
          phuong_xa: formData.ward,
          dia_chi: formData.address,
        };

        logger.log('🚀 ~ updateHouseholdInfo ~ updateParams:', updateParams);

        const response = await getCommonExecute(updateParams);
        logger.log('🚀 ~ updateHouseholdInfo ~ response:', response);

        if (response?.success || response?.data) {
          toast.success('Cập nhật thông tin hộ gia đình thành công!', {
            position: 'top',
            duration: 2000,
          });

          // Set flag để biết có update thành công, callback sẽ được gọi khi user ấn back
          logger.log('✅ Update successful - setting hasSuccessfulUpdate to true');
          setHasSuccessfulUpdate(true);
        } else {
          toast.error('Cập nhật thất bại. Vui lòng thử lại.', {
            position: 'top',
            duration: 3000,
          });
        }
      } catch (error: any) {
        logger.log('🚀 ~ updateHouseholdInfo ~ error:', error);
        toast.error('Đã xảy ra lỗi khi cập nhật. Vui lòng thử lại.', {
          position: 'top',
          duration: 3000,
        });
      } finally {
        setLoadingState(prev => ({...prev, isSubmitting: false}));
      }
    },
    [householdTypes, selectedHouseholdType, params, navigation, toast, isEditHouseholdOnly, loadingState.isSubmitting],
  );

  const updateHousehold = useCallback(
    async (formData: HouseHoldFormData & HouseholdOwnerData) => {
      // Prevent concurrent submissions
      if (loadingState.isSubmitting) {
        return;
      }

      setLoadingState(prev => ({...prev, isSubmitting: true}));
      try {
        const chiTietBienLai = params?.chiTietBienLai;

        // Tìm thông tin loại hộ gia đình từ mã được chọn
        const selectedHouseholdTypeData = householdTypes.find(type => (type.ma || type.id || type.value) === selectedHouseholdType);

        const updateParams: any = {
          // API parameters - spread existing data from chiTietBienLai
          ...chiTietBienLai,
          actionCode: ACTION_CODE.TAO_PHIEU_THU_HO,
          bt: chiTietBienLai?.bt,
          bt_tvien: chiTietBienLai?.bt_tvien,

          // Household information
          loai_ho_gia_dinh: selectedHouseholdTypeData?.ma || selectedHouseholdTypeData?.id || selectedHouseholdType || '',
          tinh_thanh: formData.province || '',
          phuong_xa: formData.ward || '',
          dia_chi: formData.address || '',

          // Household owner information
          ten_chu_ho: formData.householdOwnerName || '',
          chu_ho_cccd: formData.householdOwnerCCCD || '',
          chu_ho_gioi_tinh: toApiGender(formData.householdOwnerGender),
          chu_ho_ngay_sinh: formData.householdOwnerDateOfBirth ? moment(formData.householdOwnerDateOfBirth).format('YYYYMMDD') : '',
          chu_ho_dien_thoai: formData.householdOwnerPhone || '',
          chu_ho_email: formData.householdOwnerEmail || '',
          chu_ho_so_bhxh: formData.householdOwnerCodeBHXH || '',
        };
        const response = await getCommonExecute(updateParams);
        if (response?.success || response?.data) {
          toast.success('Cập nhật thông tin hộ gia đình thành công!', {
            position: 'top',
            duration: 3000,
          });

          // Notify parent component if callback exists
          if (params?.onUpdateInfo) {
            params.onUpdateInfo(formData);
          }

          setTimeout(() => {
            navigation.goBack();
          }, 300);
        } else {
          toast.error('Cập nhật thất bại. Vui lòng thử lại.', {
            position: 'top',
            duration: 3000,
          });
        }
      } catch (error: any) {
        logger.log('🚀 ~ updateHousehold ~ error:', error);
        toast.error('Đã xảy ra lỗi khi cập nhật. Vui lòng thử lại.', {
          position: 'top',
          duration: 3000,
        });
      } finally {
        setLoadingState(prev => ({...prev, isSubmitting: false}));
      }
    },
    [householdTypes, selectedHouseholdType, params, navigation, toast, loadingState.isSubmitting],
  );

  const onSubmit = useCallback(
    async (data: HouseHoldFormData & HouseholdOwnerData) => {
      logger.log('Household form data:', data);

      if (isCreateMode) {
        // Gọi API thêm hộ gia đình mới
        await addHousehold(data);
      } else if (params?.chiTietBienLai) {
        // Gọi API cập nhật hộ gia đình với action code TAO_PHIEU_THU_HO
        await updateHousehold(data);
      } else if (isEditMode && params?.infoData) {
        // Gọi API cập nhật thông tin hộ gia đình từ HouseholdInfoScreen sử dụng ADD_HO_GIA_DINH
        await updateHouseholdInfo(data);
      }
    },
    [isCreateMode, isEditMode, addHousehold, updateHousehold, updateHouseholdInfo, selectedHouseholdType, setError, householdTypes, params, navigation, toast],
  );

  return (
    <ScreenComponent
      dialogLoading={false}
      showHeader
      headerTitle={headerTitle}
      showBackButton
      onPressBack={handleBackPress}
      showFooter
      footer={<Button title={buttonTitle} onPress={handleSubmit(onSubmit)} loading={loadingState.isSubmitting} />}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 60} enabled={true}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: spacing.lg}}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}>
          {/* Thông tin hộ gia đình */}
          <Card title="Thông tin hộ gia đình">
            <View style={styles.formContainer}>
              {/* Loại hộ gia đình Dropdown */}
              <TouchableOpacity onPress={() => setShowHouseholdTypeModal(true)} activeOpacity={1}>
                <View pointerEvents="none">
                  <TextField
                    label="Loại hộ gia đình"
                    inputContainerStyle={errors.typeHousehold ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                    labelStyle={errors.typeHousehold ? {color: colors.danger} : undefined}
                    required
                    value={selectedTypeHouseholdName}
                    onChangeText={() => {}} // Read-only dropdown, không cần xử lý onChange
                    editable={false}
                    rightIconType={'dropdown'}
                    placeholder="Chọn loại hộ gia đình"
                    error={errors.typeHousehold?.message}
                  />
                </View>
              </TouchableOpacity>

              {/* Hidden field to store the actual value for form validation */}
              <View style={{height: 0, overflow: 'hidden'}}>
                <TextField control={control} name="typeHousehold" rules={householdFormValidation.typeHousehold as any} />
              </View>
              <View style={styles.formRow}>
                {/* Tỉnh/Thành phố Dropdown */}
                <TouchableOpacity onPress={() => setShowProvinceModal(true)} activeOpacity={1} style={{flex: 1}} disabled={loadingProvinces}>
                  <View pointerEvents="none">
                    <TextField
                      label="Tỉnh/Thành phố"
                      inputContainerStyle={errors.provinceName ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                      labelStyle={errors.provinceName && {color: colors.danger}}
                      required
                      control={control}
                      name="provinceName"
                      editable={false}
                      rightIconType={'dropdown'}
                      placeholder="Chọn tỉnh/thành phố"
                      rules={householdFormValidation.provinceName as any}
                      error={errors.provinceName?.message}
                    />
                  </View>
                </TouchableOpacity>

                {/* Phường/Xã Dropdown */}
                <TouchableOpacity onPress={() => setShowWardModal(true)} activeOpacity={1} style={{flex: 1}}>
                  <View pointerEvents="none">
                    <TextField
                      label="Phường/Xã"
                      inputContainerStyle={errors.wardName ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                      labelStyle={errors.wardName ? {color: colors.danger} : undefined}
                      required
                      control={control}
                      name="wardName"
                      editable={false}
                      rightIconType={'dropdown'}
                      placeholder="Chọn phường/xã"
                      rules={householdFormValidation.wardName as any}
                      error={errors.wardName?.message}
                    />
                  </View>
                </TouchableOpacity>
              </View>
              <TextField
                label="Địa chỉ"
                placeholder="Nhập địa chỉ"
                inputContainerStyle={styles.input}
                required
                control={control}
                name="address"
                rules={householdFormValidation.address as any}
                error={errors.address?.message}
              />
            </View>
          </Card>
          {/* Thông tin chủ hộ - Chỉ hiển thị khi không phải là chế độ chỉnh sửa thông tin hộ */}
          {!isEditHouseholdOnly && (
            <Card title="Thông tin chủ hộ">
              {/* <View style={styles.noteContainer}>
                <Text style={styles.noteText}>Chụp/tải ảnh giấy tờ tùy thân để tự động nhập thông tin</Text>
              </View>
              <View style={styles.imageGroup}>
                <TouchableOpacity style={styles.imageContainer} onPress={() => handleScanImage(setFrontCccdImage)}>
                  <Image source={frontCccdImage ? {uri: frontCccdImage} : R.images.img_cccd} style={styles.image} />
                  <Text style={styles.imageText}>Mặt trước</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.imageContainer} onPress={() => handleScanImage(setBackCccdImage)}>
                  <Image source={backCccdImage ? {uri: backCccdImage} : R.images.img_cccd} style={styles.image} />
                  <Text style={styles.imageText}>Mặt sau</Text>
                </TouchableOpacity>
              </View> */}
              <View style={styles.formContainer}>
                <View style={styles.formRow}>
                  <TextField
                    label="Mã BHXH"
                    placeholder="Nhập mã BHXH"
                    containerStyle={styles.input}
                    control={control}
                    name="householdOwnerCodeBHXH"
                    required
                    rules={householdFormValidation.householdOwnerCodeBHXH as any}
                    error={errors.householdOwnerCodeBHXH?.message}
                  />
                  <TextField
                    label="Số CCCD"
                    placeholder="Nhập số CCCD"
                    containerStyle={styles.input}
                    required
                    control={control}
                    name="householdOwnerCCCD"
                    rules={householdFormValidation.householdOwnerCCCD as any}
                    error={errors.householdOwnerCCCD?.message}
                  />
                </View>
                <TextField
                  label="Họ và tên chủ hộ"
                  placeholder="Nhập họ và tên chủ hộ"
                  containerStyle={styles.input}
                  required
                  control={control}
                  name="householdOwnerName"
                  rules={householdFormValidation.householdOwnerName as any}
                  error={errors.householdOwnerName?.message}
                />
                <View style={styles.formRow}>
                  <Controller
                    control={control}
                    name="householdOwnerGender"
                    rules={householdFormValidation.householdOwnerGender as any}
                    render={({field: {value, onChange}}) => (
                      <Radio.Group required label="Giới tính" orientation="horizontal" containerStyle={styles.input} value={value} onChange={onChange}>
                        <Radio value="NAM" label="Nam" />
                        <Radio value="NU" label="Nữ" />
                      </Radio.Group>
                    )}
                  />

                  <DateTimePickerComponent
                    label="Ngày sinh"
                    placeholder="Nhập ngày sinh"
                    showPlaceholder={true}
                    control={control}
                    name="householdOwnerDateOfBirth"
                    required
                    rules={householdFormValidation.householdOwnerDateOfBirth as any}
                    error={errors.householdOwnerDateOfBirth?.message}
                    containerStyle={styles.input}
                    maximumDate={new Date()}
                    minimumDate={new Date(1900, 0, 1)}
                  />
                </View>
                <View style={styles.formRow}>
                  <TextField
                    label="Điện thoại"
                    placeholder="Nhập số điện thoại"
                    containerStyle={styles.input}
                    control={control}
                    name="householdOwnerPhone"
                    rules={householdFormValidation.householdOwnerPhone as any}
                    error={errors.householdOwnerPhone?.message}
                    required
                  />
                  <TextField
                    label="Email"
                    placeholder="Nhập email"
                    containerStyle={styles.input}
                    control={control}
                    name="householdOwnerEmail"
                    rules={householdFormValidation.householdOwnerEmail as any}
                  />
                </View>
              </View>
            </Card>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Action Sheet Modals */}
      <ActionSheetModal
        isVisible={showHouseholdTypeModal}
        onClose={() => setShowHouseholdTypeModal(false)}
        title="Chọn loại hộ gia đình"
        subtitle="Vui lòng chọn loại hộ gia đình phù hợp"
        options={householdTypeOptions}
        selectedValue={selectedHouseholdType}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm loại hộ gia đình..."
        cancelButtonText="Hủy"
      />

      <ActionSheetModal
        isVisible={showProvinceModal}
        onClose={() => setShowProvinceModal(false)}
        title="Chọn tỉnh/thành phố"
        subtitle="Vui lòng chọn tỉnh/thành phố nơi bạn sinh sống"
        options={provinceOptions}
        selectedValue={selectedProvince}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm tỉnh/thành phố..."
        cancelButtonText="Hủy"
        containerStyle={{height: dimensions.height * 0.75}}
      />

      <ActionSheetModal
        isVisible={showWardModal}
        onClose={() => setShowWardModal(false)}
        title="Chọn phường/xã"
        subtitle="Vui lòng chọn phường/xã nơi bạn sinh sống"
        options={wardOptions}
        selectedValue={selectedWard}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm phường/xã..."
        cancelButtonText="Hủy"
      />
    </ScreenComponent>
  );
}
