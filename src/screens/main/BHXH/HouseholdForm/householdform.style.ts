import {StyleSheet} from 'react-native';
import {borderRadius, colors, spacing, typography} from '@constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  noteContainer: {
    backgroundColor: '#C8E1DA',
    padding: spacing.sm,
    borderRadius: borderRadius.lg,
    marginVertical: spacing.sm,
  },
  noteText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
  },
  imageGroup: {
    flexDirection: 'row',
    gap: spacing.md,
    marginVertical: spacing.md,
  },
  imageContainer: {
    flex: 1,
    gap: spacing.sm,
  },
  image: {
    width: '100%',
    height: 100,
  },
  imageText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    textAlign: 'center',
  },
  formContainer: {},
  formRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  input: {
    flex: 1,
  },
  genderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.base,
    backgroundColor: colors.white,
    height: 56,
  },
  genderButton: {
    flexDirection: 'row',
    gap: spacing.sm,
    padding: spacing.sm,
    borderRadius: borderRadius.base,
    height: '100%',
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  genderButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.green,
  },
  genderIcon: {
    width: 18,
    height: 18,
    tintColor: colors.green,
  },
  footer: {
    paddingBottom: spacing.md,
    paddingTop: spacing.xl,
  },
  rightIcon: {
    width: 12,
    height: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  cancelButton: {
    padding: 15,
    backgroundColor: colors.gray[200],
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  cancelText: {
    textAlign: 'center',
    color: colors.dark,
    fontWeight: '500',
  },
  confirmButton: {
    padding: 15,
    backgroundColor: colors.green,
    borderRadius: 8,
    flex: 1,
    marginLeft: 10,
  },
  confirmText: {
    textAlign: 'center',
    color: colors.white,
    fontWeight: typography.fontWeight.medium as any,
    fontSize: typography.fontSize.base,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: spacing.xl,
  },
  modalHeader: {
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '500',
    color: colors.dark,
  },
  modalBody: {
    paddingVertical: 20,
  },
});
