import {colors, spacing, typography, borderRadius, shadows} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing.xl,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.gray[100],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  timeline: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: spacing.sm, // Tăng khoảng cách để cân đối
    alignItems: 'flex-start',
    width: '100%',
  },
  timelineLeft: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: 40,
    marginRight: spacing.md,
    position: 'relative',
  },
  avatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.info,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0,
    ...shadows.sm,
    marginTop: 4,
  },
  avatarCompleted: {
    backgroundColor: colors.green,
  },
  avatarCurrent: {
    backgroundColor: colors.info,
    ...shadows.base,
  },
  avatarPending: {
    backgroundColor: colors.gray[400],
  },
  avatarText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
  avatarTextCurrent: {
    color: colors.white,
  },
  avatarCheckmark: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
  timelineLine: {
    width: 2,
    height: 48, // Chiều cao tính toán: avatar(28) + marginBottom(spacing.lg=24) + padding(4)
    backgroundColor: colors.gray[300],
    position: 'absolute',
    left: 19, // Center chính xác của avatar (timelineLeft 40px - line 2px) / 2
    top: 32, // Bắt đầu từ bottom của avatar
  },
  timelineLineCompleted: {
    backgroundColor: colors.green,
  },
  timelineLineCurrent: {
    backgroundColor: colors.info,
    height: 84, // Dài hơn để thể hiện trạng thái đang xử lý
  },
  timelineContent: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacing.sm,
    borderRadius: borderRadius.base,
    borderWidth: 1,
    borderColor: colors.gray[200],
    marginTop: spacing.xs,
    minWidth: 0, // Prevent flex child from overflowing
  },
  timelineHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  userName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
    flex: 1,
  },
  timestamp: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    fontWeight: typography.fontWeight.normal,
    marginLeft: spacing.sm,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    lineHeight: typography.fontSize.sm * typography.lineHeight.relaxed,
    marginBottom: spacing.xs,
  },
  actionTextCurrent: {
    color: colors.gray[700],
    fontWeight: typography.fontWeight.normal,
  },
  processingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  processingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.info,
    marginRight: spacing.sm,
  },
  processingText: {
    fontSize: typography.fontSize.sm,
    color: colors.info,
    fontWeight: typography.fontWeight.medium,
    fontStyle: 'italic',
  },
  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  statusInfo: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: colors.gray[200],
    borderRadius: borderRadius.lg,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    ...shadows.sm,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: spacing.xs,
  },
  statusCompleted: {
    backgroundColor: colors.green,
  },
  statusCurrent: {
    backgroundColor: colors.info,
  },
  statusPending: {
    backgroundColor: colors.gray[400],
  },
  statusLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
    fontWeight: typography.fontWeight.medium,
  },
  timelineLinePending: {
    backgroundColor: colors.gray[300],
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginTop: spacing.md,
    textAlign: 'center',
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[700],
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: typography.fontSize.base,
    color: colors.gray[500],
    textAlign: 'center',
    lineHeight: typography.fontSize.base * typography.lineHeight.relaxed,
  },
  // Error state styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.danger,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  errorDescription: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.fontSize.base * typography.lineHeight.relaxed,
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
  },
});
