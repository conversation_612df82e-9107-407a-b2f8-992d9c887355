import {createToastHelpers, useToast} from '@components/common';
import ScreenComponent from '@components/common/ScreenComponent';
import {ACTION_CODE} from '@constants/axios';
import {MainNavigationProp} from '@navigation/types';
import {getCommonExecute} from '@services/endpoints';
import React, {useEffect, useState} from 'react';
import {FlatList, RefreshControl, Text, View} from 'react-native';
import {styles} from './processing.style';

interface ProcessingStep {
  id: string;
  status: 'completed' | 'current' | 'pending';
  ngay_hthi: string;
  noi_dung: string;
  ten_nsd: string;
}

// Interface cho dữ liệu raw từ API (trước khi transform)
interface ApiHistoryItem {
  bt?: string;
  nguoi_xu_ly?: string;
  hanh_dong?: string;
  thoi_gian?: string;
  trang_thai?: string;
  ghi_chu?: string;
  ten_nsd?: string;
  noi_dung?: string;
  ngay_hthi?: string;
  [key: string]: any; // Cho phép các field khác từ API
}

// Interface cho params API
interface ProcessingHistoryParams {
  actionCode: string;
  bt_bien_lai: string;
}

interface ProcessingScreenProps {
  navigation?: MainNavigationProp;
  route?: {
    params?: {
      title?: string;
      steps?: ProcessingStep[];
      autoProgress?: boolean;
      bt_bien_lai?: string; // ID biên lai từ màn hình trước
    };
  };
}

export default function ProcessingScreen({route, navigation}: ProcessingScreenProps) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // Animation states (removed unused animations)

  // API states
  const [isLoading, setIsLoading] = useState(false);
  const [dataQuaTrinhXuLy, setDataQuaTrinhXuLy] = useState<ProcessingStep[]>([]); // Steps từ API
  const [error, setError] = useState<string | null>(null);

  const params = route?.params || {};
  const {bt_bien_lai} = params;

  // Effect để fetch dữ liệu khi component mount
  useEffect(() => {
    // Nếu có bt_bien_lai từ params, fetch dữ liệu từ API
    if (bt_bien_lai) {
      getProcessingHistory();
    }
  }, [bt_bien_lai]);

  //GET QTXL
  const getProcessingHistory = async () => {
    // Kiểm tra bt_bien_lai có tồn tại không
    if (!bt_bien_lai) {
      console.warn('⚠️ Không có bt_bien_lai để fetch lịch sử xử lý');
      setError('Không có thông tin biên lai để lấy lịch sử xử lý');
      return;
    }
    try {
      setIsLoading(true);
      setError(null);

      // Tạo params cho API call
      const apiParams: ProcessingHistoryParams = {
        actionCode: ACTION_CODE.LICH_SU_XU_LY_BIEN_LAI, // Action code cho lịch sử xử lý
        bt_bien_lai: bt_bien_lai,
      };

      // Gọi API sử dụng getCommonExecute
      const response = await getCommonExecute(apiParams);

      // Xử lý response
      if (response?.data) {
        setDataQuaTrinhXuLy(response.data);
      } else {
        console.warn('⚠️ API response không có data:', response);
        setDataQuaTrinhXuLy([]); // Reset API steps
        setError('Không có dữ liệu lịch sử xử lý');
      }
    } catch (error: any) {
      const errorMessage = error?.message || 'Đã xảy ra lỗi khi tải lịch sử xử lý';
      console.error('❌ Lỗi khi fetch lịch sử xử lý:', error);

      setError(errorMessage);
      setDataQuaTrinhXuLy([]); // Reset API steps khi có lỗi

      // Hiển thị toast error
      toast.error(errorMessage, {
        duration: 3000,
        position: 'top',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderTimelineStep = ({item, index}: {item: ProcessingStep; index: number}) => {
    index = dataQuaTrinhXuLy.length - index;
    return (
      <View key={index} style={[styles.timelineItem]}>
        <View style={styles.timelineLeft}>
          <View style={[styles.avatar]}>
            <Text style={[styles.avatarText]}>{index}</Text>
          </View>
        </View>

        <View style={styles.timelineContent}>
          <View style={styles.timelineHeader}>
            <Text style={styles.userName}>{item.ten_nsd}</Text>
            <Text style={styles.timestamp}>{item.ngay_hthi}</Text>
          </View>
          <Text style={[styles.actionText]}>{item.noi_dung}</Text>
          {/* {status === 'current' && (
            <View style={styles.processingIndicator}>
              <View style={styles.processingDot} />
              <Text style={styles.processingText}>Đang xử lý...</Text>
            </View>
          )} */}
        </View>
      </View>
    );
  };

  //RENDER
  return (
    <ScreenComponent showHeader headerTitle={'Quá trình xử lý'} showBackButton onPressBack={() => navigation?.pop()}>
      <FlatList
        data={dataQuaTrinhXuLy}
        renderItem={renderTimelineStep}
        keyExtractor={(_, index) => String(index)}
        showsVerticalScrollIndicator={false}
        style={styles.timeline}
        refreshControl={<RefreshControl refreshing={isLoading} onRefresh={getProcessingHistory} />}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>{error || 'Chưa có dữ liệu quá trình xử lý'}</Text>
          </View>
        }
      />
    </ScreenComponent>
  );
}
