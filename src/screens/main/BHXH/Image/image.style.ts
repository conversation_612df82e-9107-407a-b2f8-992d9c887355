import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
    marginHorizontal: spacing.sm,
  },
  titleImage: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
  },
  infoContainer: {
    backgroundColor: colors.white,
    marginBottom: spacing.md,
    ...shadows.base,
  },
  info: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  infoRow: {
    gap: spacing.sm,
  },
  infoTitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  infoValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
  },
  amountValue: {
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as any,
  },
  noteText: {
    fontSize: typography.fontSize.base,
    color: '#FF4800',
    marginTop: spacing.sm,
    fontWeight: typography.fontWeight.medium as any,
  },
  cardImage: {
    borderRadius: borderRadius.xl,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },

  imageCount: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    backgroundColor: colors.green + '15',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.full,
  },

  // Upload area styles
  uploadContainer: {
    position: 'relative',
    marginBottom: spacing.xl,
    width: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm, // Half width with spacing
    height: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm, // Square
  },
  uploadArea: {
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    borderRadius: borderRadius.lg,
    backgroundColor: '#F9FAFB',
    width: '100%',
    height: '100%',
    marginBottom: 0,
  },
  uploadContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: spacing.md,
    paddingBottom: spacing.xl + spacing.md, // Extra padding for button space
  },
  uploadIcon: {
    width: 32,
    height: 32,
    tintColor: colors.gray[400],
    marginBottom: spacing.xs,
    opacity: 0.7,
  },
  uploadText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
    fontWeight: typography.fontWeight.semibold as any,
    marginBottom: spacing.xs / 2,
  },
  uploadSubText: {
    fontSize: 10,
    color: colors.gray[500],
    textAlign: 'center',
    opacity: 0.8,
    paddingHorizontal: spacing.xs,
  },

  // Action buttons
  actionContainer: {
    position: 'absolute',
    bottom: -20, // Position overlapping the border
    left: 0,
    right: 0,
    flexDirection: 'row',
    gap: spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: colors.green,
    padding: 12,
    borderRadius: borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.base,
    borderWidth: 2,
    borderColor: colors.white,
  },
  actionButtonIcon: {
    width: 20,
    height: 20,
    tintColor: colors.white,
  },

  // Image square styles (when images exist)
  imageSquare: {
    width: '100%',
    height: '100%',
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: colors.gray[100],
    borderWidth: 2,
    borderColor: colors.gray[300],
  },
  imageSquareFull: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageCountBadge: {
    position: 'absolute',
    bottom: spacing.sm,
    right: spacing.sm,
    backgroundColor: colors.dark + 'CC',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
  },
  imageCountBadgeText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold as any,
  },

  // Delete action button
  deleteActionButton: {
    backgroundColor: colors.danger,
  },
  deleteIcon: {
    color: colors.white,
    fontSize: 24,
    fontWeight: typography.fontWeight.bold as any,
    lineHeight: 24,
  },
  imageContainer: {
    gap: spacing.sm,
  },
  imageSection: {
    gap: spacing.sm,
    marginLeft: spacing.sm,
  },
  imageCategory: {
    marginBottom: spacing.md,
  },
  imageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  imageList: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  imageCategoryTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
    marginBottom: spacing.sm,
  },
  imageScrollContainer: {
    paddingRight: spacing.md,
    gap: spacing.sm,
  },
  additionalImageContainer: {
    width: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm,
    height: (width - spacing.sm * 2 - spacing.sm) / 2 - spacing.sm,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: colors.gray[100],
    position: 'relative', // Để hỗ trợ vị trí tuyệt đối của nút xóa
  },
  additionalImage: {
    width: '100%',
    height: '100%',
  },
  deleteButton: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
    width: 24,
    height: 24,
    backgroundColor: colors.danger + 'CC',
    borderRadius: borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  deleteButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold as any,
    lineHeight: 24,
  },
  imageGridRow: {
    flexDirection: 'row',
    gap: spacing.sm,
    justifyContent: 'center',
  },
  imageGridRowSingle: {
    justifyContent: 'flex-start',
    paddingHorizontal: spacing.sm,
  },
  buttonWrapper: {
    paddingVertical: spacing.xl,
  },

  // New styles for image display screen
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.sm,
  },

  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
    gap: spacing.sm,
  },

  imageItem: {
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: colors.gray[100],
    position: 'relative',
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[500],
  },

  gridImage: {
    width: '100%',
    height: '100%',
  },

  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: spacing.xs,
    paddingVertical: spacing.xs / 2,
  },

  imageInfo: {
    color: colors.white,
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium as any,
  },

  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 2,
    paddingHorizontal: spacing.lg,
  },

  emptyIcon: {
    width: 48,
    height: 48,
    tintColor: colors.gray[400],
    marginBottom: spacing.md,
    opacity: 0.6,
  },

  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[500],
    textAlign: 'center',
  },

  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xl * 3,
    paddingHorizontal: spacing.lg,
  },

  noDataIcon: {
    width: 64,
    height: 64,
    tintColor: colors.gray[300],
    marginBottom: spacing.lg,
    opacity: 0.5,
  },

  noDataTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.gray[600],
    marginBottom: spacing.sm,
    textAlign: 'center',
  },

  noDataSubtitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[500],
    textAlign: 'center',
    lineHeight: 22,
  },
});
