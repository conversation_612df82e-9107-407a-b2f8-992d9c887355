import React, {useState} from 'react';
import {Dimensions, Image, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import R from '@assets/R';
import {ScreenComponent} from '@components/common';
import {MainNavigationProp} from '@navigation/types';
import {useNavigation} from '@react-navigation/native';
import ImagePreviewModal from '@screens/main/BHXH/components/ImagePreviewModal';
import {CONFIG_SERVER} from '@constants/axios';
import {styles} from './image.style';
import {spacing} from '@constants/theme';

// Interface cho hình ảnh từ API
interface HinhAnhBienLai {
  bt_bien_lai?: number;
  bt_file?: number;
  url_file?: string;
  loai?: string; // "bien_lai" hoặc "thanh_toan"
  nguoi_tao?: string;
  trang_thai?: string;
  url?: string;
}

export default function ImageScreen({route}: {route: {params: {chiTietBienLai: any; ma: string; danhSachHinhAnh: HinhAnhBienLai[]}}}) {
  const {danhSachHinhAnh = []} = route?.params || {};
  const navigation = useNavigation<MainNavigationProp>();

  // Helper function để tạo URL hình ảnh đầy đủ
  const getFullImageUrl = (urlFile: string): string => {
    if (!urlFile) return '';

    // Nếu URL đã có protocol, return as is
    if (urlFile.startsWith('http://') || urlFile.startsWith('https://')) {
      return urlFile;
    }

    // Nếu URL bắt đầu bằng /, bỏ / đầu tiên
    const cleanUrl = urlFile.startsWith('/') ? urlFile.substring(1) : urlFile;

    // Kết hợp với base URL
    return `${CONFIG_SERVER.BASE_URL_API}/${cleanUrl}`;
  };

  // Phân nhóm hình ảnh theo loại
  const anhBienLai = danhSachHinhAnh.filter(item => item.loai === 'bien_lai');
  const anhThanhToan = danhSachHinhAnh.filter(item => item.loai === 'thanh_toan');

  // State cho preview modal
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [previewImages, setPreviewImages] = useState<{uri: string}[]>([]);
  const [previewInitialIndex, setPreviewInitialIndex] = useState(0);

  // Function để mở preview
  const handleImagePress = (images: HinhAnhBienLai[], index: number) => {
    const imageUris = images.filter(img => img.url_file).map(img => ({uri: getFullImageUrl(img.url_file!)}));

    setPreviewImages(imageUris);
    setPreviewInitialIndex(index);
    setIsPreviewVisible(true);
  };

  // Component hiển thị section hình ảnh
  const ImageSection = ({title, images, emptyMessage}: {title: string; images: HinhAnhBienLai[]; emptyMessage: string}) => {
    const screenWidth = Dimensions.get('window').width;
    const imageSize = (screenWidth - 60) / 2; // 2 cột với padding

    return (
      <View style={styles.imageSection}>
        <Text style={styles.sectionTitle}>
          {title} ({images.length})
        </Text>

        {images.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Image source={R.icons.ic_image} style={styles.emptyIcon} />
            <Text style={styles.emptyText}>{emptyMessage}</Text>
          </View>
        ) : (
          <View style={styles.imageGrid}>
            {images.map((image, index) => (
              <TouchableOpacity key={`${image.bt_file}-${index}`} style={[styles.imageItem, {width: imageSize, height: imageSize}]} onPress={() => handleImagePress(images, index)} activeOpacity={0.8}>
                <Image
                  source={{
                    uri: getFullImageUrl(image.url_file!),
                  }}
                  style={styles.gridImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <ScreenComponent showHeader headerTitle="Hình ảnh biên lai" showBackButton onPressBack={() => navigation.goBack()}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing['2xl']}}>
        {/* Section Ảnh biên lai */}
        <ImageSection title="Ảnh biên lai" images={anhBienLai} emptyMessage="Chưa có ảnh biên lai nào" />

        {/* Section Ảnh thanh toán */}
        <ImageSection title="Ảnh thanh toán" images={anhThanhToan} emptyMessage="Chưa có ảnh thanh toán nào" />

        {/* Hiển thị thông báo nếu không có ảnh nào */}
      </ScrollView>

      {/* Image Preview Modal */}
      <ImagePreviewModal visible={isPreviewVisible} images={previewImages} initialIndex={previewInitialIndex} onClose={() => setIsPreviewVisible(false)} />
    </ScreenComponent>
  );
}
