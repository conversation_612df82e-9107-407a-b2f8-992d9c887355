import R from '@assets/R';
import {But<PERSON>, Card, createToastHel<PERSON>, ScreenComponent, useToast} from '@components/common';
import {ACTION_CODE} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp} from '@navigation/types';
import Clipboard from '@react-native-clipboard/clipboard';
import {RouteProp, useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import {useAppSelector} from '@store/index';
import {formatGender} from '@utils/formatters';
import moment from 'moment';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Image, RefreshControl, ScrollView, StyleProp, Text, TextStyle, TouchableOpacity, View} from 'react-native';
import {ChiTietBienLaiData, ChiTietBienLaiParams} from '../Collection/types';
import {styles} from './detailcollection.style';

// Interface cho danh sách hình ảnh biên lai
interface DanhSachHinhAnhParams {
  bt_bien_lai: number;
  trang_thai: string;
  actionCode: string;
}

interface HinhAnhBienLai {
  bt?: number;
  url_file?: string;
  url_file_thumbnail?: string;
  ten_file?: string;
  ngay_tao?: string;
  // Thêm các field khác nếu cần
}

// Type cho route params
type DetailCollectionRouteProp = RouteProp<
  {
    DetailCollection: {
      ma: string;
    };
  },
  'DetailCollection'
>;

export default function DetailCollectionScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const route = useRoute<DetailCollectionRouteProp>();

  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const user = useAppSelector(state => state.auth.user);

  // Lấy tham số từ navigation
  const {ma, prevScreen} = route.params;

  // State cho dữ liệu chi tiết biên lai

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // State cũ (giữ lại để tương thích)

  const [thongTinNguoiThamGia, setThongTinNguoiThamGia] = useState<any>({});
  const [thongTinChuHo, setThongTinChuHo] = useState<any>({});
  const [thongTinBHXHTN, setThongTinBHXHTN] = useState<any>({});
  const [thongTinBHYT, setThongTinBHYT] = useState<any>({});
  const [chiTietBienLai, setChiTietBienLai] = useState<ChiTietBienLaiData | null>({});
  const [fullFieldData, setFullFieldData] = useState<ChiTietBienLaiData | null>({});
  const [chiTietDonViThuHo, setChiTietDonViThuHo] = useState<any>({});

  // State cho danh sách hình ảnh biên lai
  const [danhSachHinhAnh, setDanhSachHinhAnh] = useState<HinhAnhBienLai[]>([]);

  // Ref để track API calls và tránh duplicate calls
  const isApiCallingRef = useRef<boolean>(false);
  const hasDataLoadedRef = useRef<boolean>(false);
  const shouldRefreshOnFocusRef = useRef<boolean>(false);
  const isInitialMountRef = useRef<boolean>(true);

  useEffect(() => {
    getChiTietDoiTacThuHo();
  }, []);

  // Function gọi API danh sách hình ảnh biên lai
  const getDanhSachHinhAnhBienLai = useCallback(async (btBienLai: number, trangThai: string = 'D') => {
    if (!btBienLai) {
      logger.log('Không có bt biên lai để gọi API hình ảnh');
      return;
    }

    try {
      const params: DanhSachHinhAnhParams = {
        bt_bien_lai: btBienLai,
        trang_thai: trangThai,
        actionCode: ACTION_CODE.GET_LIST_FILE,
      };

      const response = await getCommonExecute(params);

      // Gán dữ liệu vào state (tạm thời chưa hiển thị)
      if (response?.data && Array.isArray(response.data)) {
        setDanhSachHinhAnh(response.data);
        // logger.log('✅ Đã lưu danh sách hình ảnh:', response.data.length, 'ảnh');
      } else if (response?.data) {
        // Nếu response.data không phải array, wrap nó trong array
        setDanhSachHinhAnh([response.data]);
        // logger.log('✅ Đã lưu hình ảnh đơn lẻ');
      } else {
        setDanhSachHinhAnh([]);
        // logger.log('⚠️ Không có dữ liệu hình ảnh');
      }
    } catch (error: any) {
      console.error('🚀 ~ getDanhSachHinhAnhBienLai ~ catch error:', error);
      // Không hiển thị toast error cho API này vì chỉ là tính năng phụ
      setDanhSachHinhAnh([]);
    }
  }, []);

  // Helper function để tối ưu việc set state từ API response
  const updateStateFromApiResponse = useCallback(
    (data: any, shouldCallImageAPI: boolean = true) => {
      // Destructuring tất cả fields cần thiết một lần
      const {
        // Thông tin biên lai
        so_bien_lai = '',
        noi_dung_thu = '',
        so_tien = 0,
        // Thông tin người tham gia
        ma_so_bhxh = '',
        ten = '',
        cccd = '',
        cmt = '',
        dien_thoai = '',
        email = '',
        dia_chi = '',
        ngay_sinh = '',
        gioi_tinh = '',
        //Thong tin chủ hộ
        ten_chu_ho = '',
        loai_ho_gia_dinh_ten = '',
        mqh_ten = '',
        // BHXH TN fields
        bhtn_so_thang_dong = 0,
        bhtn_dong_tu_thang = '',
        bhtn_muc_tien_dong = 0,
        bhtn_so_tien_dong = 0,
        // BHYT fields
        bhyt_so_thang_dong = 0,
        bhyt_dong_tu_thang = '',
        bhyt_noi_dang_ky_kcb_ten = '',
        bhyt_so_tien_dong = 0,
      } = data;

      setFullFieldData(data);

      // Batch update tất cả states
      setThongTinNguoiThamGia({
        ma_so_bhxh,
        ten,
        cccd: cccd || cmt,
        dien_thoai,
        email,
        dia_chi,
        ngay_sinh,
        gioi_tinh,
      });

      setThongTinChuHo({
        ten_chu_ho,
        loai_ho_gia_dinh_ten,
        mqh_ten,
      });

      setThongTinBHXHTN({
        bhtn_so_thang_dong,
        bhtn_dong_tu_thang,
        bhtn_muc_tien_dong,
        bhtn_so_tien_dong,
      });

      setThongTinBHYT({
        bhyt_so_thang_dong,
        bhyt_dong_tu_thang,
        bhyt_noi_dang_ky_kcb_ten,
        bhyt_so_tien_dong,
      });

      // Debug log để kiểm tra dữ liệu BHYT
      logger.log('🔍 BHYT Data updated:', {
        bhyt_so_thang_dong,
        bhyt_dong_tu_thang,
        bhyt_noi_dang_ky_kcb_ten,
        bhyt_so_tien_dong,
      });

      setChiTietBienLai({
        so_bien_lai,
        noi_dung_thu,
        so_tien,
      });

      // Gọi API danh sách hình ảnh nếu có bt biên lai và được yêu cầu
      if (shouldCallImageAPI && data.bt) {
        logger.log('🚀 Calling image API with bt:', data.bt);
        // Gọi API hình ảnh với delay nhỏ để tránh conflict
        setTimeout(() => {
          getDanhSachHinhAnhBienLai(data.bt, 'D');
        }, 100);
      }
    },
    [getDanhSachHinhAnhBienLai],
  );

  // Function gọi API chi tiết biên lai với duplicate call protection
  const getChiTietBienLai = useCallback(
    async (isRefreshAction = false) => {
      if (!ma) return;

      // Tránh duplicate API calls
      if (isApiCallingRef.current) {
        logger.log('🚀 ~ API call already in progress, skipping...');
        return;
      }

      try {
        isApiCallingRef.current = true;
        if (isRefreshAction) {
          setIsRefreshing(true);
        } else {
          setIsLoading(true);
        }

        const params: ChiTietBienLaiParams = {
          ma: ma,
          actionCode: ACTION_CODE.CHI_TIET_BIEN_LAI,
        };
        const response = await getCommonExecute(params);
        logger.log('🚀 ~ getChiTietBienLai ~ response:', response);
        if (response?.data) {
          updateStateFromApiResponse(response.data);
          hasDataLoadedRef.current = true;
        }
      } catch (error: any) {
        const errorMsg = error?.message || 'Đã xảy ra lỗi khi gọi API chi tiết biên lai';

        console.error('🚀 ~ getChiTietBienLai ~ catch error:', error);
        toast.error(errorMsg);
      } finally {
        if (isRefreshAction) {
          setIsRefreshing(false);
        } else {
          setIsLoading(false);
        }
        isApiCallingRef.current = false;
      }
    },
    [ma, updateStateFromApiResponse],
  );

  const getChiTietDoiTacThuHo = async () => {
    try {
      const params = {
        ma: user?.ma_chi_nhanh,
        actionCode: ACTION_CODE.CHI_TIET_DON_VI_THU_HO_BHXH,
      };
      const response = await getCommonExecute(params);
      if (response?.data?.lke && Array.isArray(response.data.lke) && response.data.lke.length > 0) {
        setChiTietDonViThuHo(response.data.lke[0]);
        // logger.log('✅ ~ Đã lưu chi tiết đơn vị thu hộ:', response.data.lke[0]);
      } else {
        logger.log('⚠️ ~ Không tìm thấy data trong response.data.lke');
      }
    } catch (error) {
      logger.log('getDoiTac ~ error:', error);
    }
  };

  // Gọi API khi component mount hoặc ma thay đổi
  useEffect(() => {
    getChiTietBienLai();
  }, [getChiTietBienLai]);

  // Debug effect để theo dõi thay đổi của thongTinBHYT
  useEffect(() => {
    logger.log('🔍 thongTinBHYT changed:', thongTinBHYT);
  }, [thongTinBHYT]);

  useFocusEffect(
    React.useCallback(() => {
      // Skip initial mount
      if (isInitialMountRef.current) {
        isInitialMountRef.current = false;
        return;
      }

      // Kiểm tra flag để refresh dữ liệu khi quay lại từ màn hình khác
      if (shouldRefreshOnFocusRef.current) {
        logger.log('🔄 Refreshing data with dialog loading after returning from other screen');
        shouldRefreshOnFocusRef.current = false; // Reset flag
        getChiTietBienLai(false); // Refresh dữ liệu chính với dialog loading
        return; // Return early để tránh gọi API hình ảnh với dữ liệu cũ
      }

      // Khi màn hình được focus, gọi lại API để lấy danh sách hình ảnh
      // Kiểm tra `fullFieldData.bt` để đảm bảo có thông tin biên lai trước khi gọi
      if (fullFieldData?.bt) {
        getDanhSachHinhAnhBienLai(fullFieldData.bt);
      }
    }, [fullFieldData?.bt, getDanhSachHinhAnhBienLai, getChiTietBienLai]),
  );

  // Handle pull-to-refresh
  const onRefresh = useCallback(() => {
    logger.log('Pull-to-refresh triggered');
    getChiTietBienLai(true); // Pass true to indicate this is a refresh action
  }, [getChiTietBienLai]);

  // Force refresh function - có thể gọi từ bên ngoài
  const forceRefresh = useCallback(() => {
    logger.log('🔄 Force refresh triggered');
    // Reset tất cả refs
    isApiCallingRef.current = false;
    hasDataLoadedRef.current = false;
    // Gọi API với loading dialog
    getChiTietBienLai(false);
  }, [getChiTietBienLai]);

  const handleEditProduct = () => {
    logger.log('🔧 Edit product pressed');
    NavigationUtil.push(MAIN_SCREENS.DETAIL_PRODUCT, {
      mode: 'edit',
      chiTietBienLai: fullFieldData,
      onUpdateInfo: () => {
        // Set flag to refresh data when returning to this screen
        logger.log('🔄 onUpdateInfo callback triggered - setting refresh flag');
        shouldRefreshOnFocusRef.current = true;
      },
      // Thêm callback để force refresh nếu cần
      onForceRefresh: forceRefresh,
    });
  };

  const handleCopyBHXH = () => {
    if (thongTinNguoiThamGia.ma_so_bhxh) {
      Clipboard.setString(thongTinNguoiThamGia.ma_so_bhxh);
      toast.info('Đã sao chép Mã số BHXH ', {
        duration: 2000,
        position: 'bottom',
      });
      logger.log('Copied BHXH code:', thongTinNguoiThamGia.ma_so_bhxh);
    }
  };

  const handleChangeNguoiThamGia = async () => {
    logger.log('🔄 Change person pressed');

    // Set flag to refresh data when returning from SearchInformationScreen
    shouldRefreshOnFocusRef.current = true;

    // Prepare current participant data for pre-selection
    const currentParticipant = {
      bt: fullFieldData?.bt_tvien || fullFieldData?.bt, // Use bt_tvien or bt as identifier

      so_bhxh: thongTinNguoiThamGia?.ma_so_bhxh,
      ten: thongTinNguoiThamGia?.ten,
      cccd: thongTinNguoiThamGia?.cccd,
      cmt: thongTinNguoiThamGia?.cccd,
      ngay_sinh: thongTinNguoiThamGia?.ngay_sinh,
      chu_ho: thongTinChuHo?.ten_chu_ho,
      ten_chu_ho: thongTinChuHo?.ten_chu_ho,
    };

    // Lấy bt_ho_gia_dinh từ chi tiết thành viên
    let bt_ho_gia_dinh = null;
    if (fullFieldData?.bt_tvien) {
      try {
        logger.log('🔍 Fetching member detail to get bt_ho_gia_dinh for bt_tvien:', fullFieldData.bt_tvien);
        const params = {
          ma: fullFieldData.bt_tvien,
          actionCode: ACTION_CODE.GET_CHI_TIET_THANH_VIEN,
        };
        const response = await getCommonExecute(params);
        if (response?.data && response.data.thanh_vien.length > 0) {
          bt_ho_gia_dinh = response.data.thanh_vien[0].bt_ho_gia_dinh;
          logger.log('✅ Got bt_ho_gia_dinh:', bt_ho_gia_dinh);
        }
      } catch (error) {
        logger.log('❌ Error fetching member detail:', error);
      }
    }

    logger.log('📤 Navigating to SearchInformation with bt_ho_gia_dinh:', bt_ho_gia_dinh);
    NavigationUtil.push(MAIN_SCREENS.SEARCH_INFORMATION, {
      mode: 'change',
      chiTietBienLai: fullFieldData,
      currentParticipant: currentParticipant,
      bt_ho_gia_dinh: bt_ho_gia_dinh, // Thêm bt_ho_gia_dinh để load members của hộ
    });
  };

  const handleImage = () => {
    logger.log('Image pressed');
    logger.log('🖼️ Current image list:', danhSachHinhAnh);
    NavigationUtil.push(MAIN_SCREENS.IMAGE, {danhSachHinhAnh});
  };

  const handleHistory = () => {
    logger.log('History pressed');
    navigation.navigate(MAIN_SCREENS.PAYMENT_HISTORY, {bt_tvien: fullFieldData?.bt_tvien});
  };

  const handleProcessing = () => {
    logger.log('Processing pressed');
    NavigationUtil.push(MAIN_SCREENS.PROCESSING, {bt_bien_lai: fullFieldData?.bt});
  };

  const renderInfoRow = (label: string, value: string, style?: StyleProp<TextStyle>, isHighlighted?: boolean) => (
    <View style={styles.infoRow}>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={[styles.infoValue, isHighlighted && styles.highlightedValue, style]}>{value}</Text>
    </View>
  );

  return (
    <ScreenComponent
      dialogLoading={isLoading}
      showHeader
      headerTitle="Chi tiết biên lai thu hộ"
      showBackButton
      onPressBack={() => {
        if (prevScreen && prevScreen === 'DetailProductScreen') {
          navigation.pop(4);
        } else {
          navigation.goBack();
        }
      }}
      showFooter
      footer={<Button title="Thu hộ" onPress={() => NavigationUtil.push(MAIN_SCREENS.PAYMENT, {chiTietBienLai: fullFieldData, chiTietDonViThuHo: chiTietDonViThuHo})} />}>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: spacing.lg}}
        refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} colors={[colors.primary]} tintColor={colors.primary} />}>
        {/* Thông tin chung */}
        <Card style={styles.cardContainer} title="Thông tin chung">
          <View style={styles.cardHeader}>
            <Text style={styles.title}>Người tham gia</Text>
          </View>

          <View style={styles.infoContainer}>
            {/* Mã số BHXH with Copy button */}
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Mã số BHXH</Text>
              <View style={styles.copyRow}>
                <Text style={styles.codeBHXHValue}>{thongTinNguoiThamGia.ma_so_bhxh}</Text>
                <TouchableOpacity onPress={handleCopyBHXH} style={styles.copyButton}>
                  <Image source={R.icons.ic_copy} style={styles.copyIcon} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Họ và tên with Đổi người button below */}
            <View style={styles.nameSection}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Họ và tên</Text>
                <Text style={[styles.infoValue, styles.highlightedValue]}>{thongTinNguoiThamGia.ten}</Text>
              </View>
              <View style={styles.changePersonRow}>
                <TouchableOpacity onPress={handleChangeNguoiThamGia} style={styles.editButton}>
                  <Image source={R.icons.ic_change} style={styles.editIcon} resizeMode="contain" />
                  <Text style={styles.editButtonText}>Đổi người</Text>
                </TouchableOpacity>
              </View>
            </View>

            {renderInfoRow('Số CCCD', thongTinNguoiThamGia.cccd)}
            {renderInfoRow('Ngày sinh', thongTinNguoiThamGia.ngay_sinh)}
            {renderInfoRow('Giới tính', formatGender(thongTinNguoiThamGia.gioi_tinh))}
            {renderInfoRow('Địa chỉ', thongTinNguoiThamGia.dia_chi)}
          </View>
          <View style={styles.cardHeader}>
            <Text style={styles.title}>Hộ gia đình</Text>
          </View>

          <View style={styles.infoContainer}>
            {renderInfoRow('Tên chủ hộ', thongTinChuHo.ten_chu_ho, styles.highlightedValue)}
            {renderInfoRow('Mối q.hệ với chủ hộ', thongTinChuHo.mqh_ten)}
            {renderInfoRow('Loại hộ gia đình', thongTinChuHo.loai_ho_gia_dinh_ten, styles.typeHouseholdValue)}
          </View>
        </Card>

        {/* Thông tin sản phẩm */}
        <Card style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Thông tin sản phẩm</Text>
            <TouchableOpacity onPress={handleEditProduct} style={styles.editButton}>
              <Image source={R.icons.ic_edit} style={styles.editIcon} resizeMode="contain" />
              <Text style={styles.editButtonText}>Điều chỉnh thông tin</Text>
            </TouchableOpacity>
          </View>

          {/* BHXH tự nguyện - Chỉ hiển thị khi có số tháng đóng > 0 */}
          {thongTinBHXHTN?.bhtn_so_thang_dong > 0 && (
            <View style={styles.productContainer}>
              <Text style={styles.productTitle}>BHXH tự nguyện</Text>
              <View style={styles.productInfo}>
                {renderInfoRow('Số tháng đóng', `${thongTinBHXHTN?.bhtn_so_thang_dong} tháng`, styles.productInfoValue)}
                {renderInfoRow('Từ tháng', moment(thongTinBHXHTN?.bhtn_dong_tu_thang, 'YYYYMM').format('MM/YYYY'))}
                {renderInfoRow('Mức tiền căn cứ', `${thongTinBHXHTN?.bhtn_muc_tien_dong?.toLocaleString('vi-VN')}đ`, styles.highlightedValue)}
                {renderInfoRow('Số tiền đóng', `${thongTinBHXHTN?.bhtn_so_tien_dong?.toLocaleString('vi-VN')}đ`, styles.highlightedValue)}
              </View>
            </View>
          )}

          {/* Bảo hiểm Y tế - Chỉ hiển thị khi có số tháng đóng > 0 */}
          {thongTinBHYT?.bhyt_so_thang_dong > 0 && (
            <View style={styles.productContainer}>
              <Text style={styles.productTitle}>Bảo hiểm y tế</Text>
              <View style={styles.productInfo}>
                {renderInfoRow('Số tháng đóng', `${thongTinBHYT?.bhyt_so_thang_dong} tháng`, styles.productInfoValue)}
                {renderInfoRow('Từ tháng', moment(thongTinBHYT?.bhyt_dong_tu_thang, 'YYYYMM').format('MM/YYYY'))}
                {/* Debug: Hiển thị rõ ràng nơi đăng ký KCB */}
                {renderInfoRow('Nơi đăng ký KCB', thongTinBHYT?.bhyt_noi_dang_ky_kcb_ten || 'Chưa có thông tin', styles.typeHouseholdValue)}
                {renderInfoRow('Số tiền đóng', `${thongTinBHYT?.bhyt_so_tien_dong?.toLocaleString('vi-VN')}đ`, styles.highlightedValue)}
              </View>
            </View>
          )}
        </Card>

        {/* Thông tin biên lai */}
        <Card style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Thông tin biên lai</Text>
            <TouchableOpacity onPress={handleEditProduct} style={styles.editButton}>
              <Image source={R.icons.ic_edit} style={styles.editIcon} resizeMode="contain" />
              <Text style={styles.editButtonText}>Điều chỉnh thông tin</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.receiptContainer}>
            {renderInfoRow('Biên lai', String(chiTietBienLai?.so_bien_lai || 'N/A'), styles.typeHouseholdValue)}
            {renderInfoRow('Nội dung thu', chiTietBienLai?.noi_dung_thu)}
            {renderInfoRow('Số tiền đóng', `${chiTietBienLai?.so_tien?.toLocaleString('vi-VN')}đ`, styles.highlightedValue)}
          </View>
        </Card>

        {/* Menu */}
        <Card style={styles.cardContainer}>
          <TouchableOpacity onPress={handleImage} style={styles.cardRow}>
            <Image source={R.icons.ic_green_camera} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Hình ảnh</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleProcessing} style={styles.cardRow}>
            <Image source={R.icons.ic_list} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Quá trình xử lý</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleHistory} style={[styles.cardRow, {borderBottomWidth: 0}]}>
            <Image source={R.icons.ic_history} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Lịch sử thu hộ</Text>
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
