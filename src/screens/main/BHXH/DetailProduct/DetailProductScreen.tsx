import {ActionSheetModal, ActionSheetOption, But<PERSON>, Card, createToastHel<PERSON>, DateTimePickerComponent, ScreenComponent, TextField, useToast} from '@components/common';
import {ACTION_CODE} from '@constants/axios'; // <PERSON><PERSON>m bảo đã import ACTION_CODE
import {colors, spacing} from '@constants/theme';
import {MainNavigationProp, MainStackParamList} from '@navigation/types';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import {formatCurrencyPlain, parseCurrency} from '@utils/currencyFormatter';
import {detailProductFormValidation} from '@utils/validationSchemas';
import moment from 'moment';
import React, {useCallback, useEffect, useState} from 'react';
import {Controller, useForm, useWatch} from 'react-hook-form';
import {KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {styles} from './detailproduct.style';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';

const productOptions = [
  {label: 'BHXH tự nguyện', value: 'bhxh'},
  {label: 'Bảo hiểm y tế', value: 'bhyt'},
];

const DATA_TAI_TUC__BH: ActionSheetOption[] = [
  {id: 'G', title: 'Bảo hiểm cấp mới', onPress: () => {}},
  {id: 'T', title: 'Bảo hiểm tái tục', onPress: () => {}},
];

// LoadingState interface theo pattern từ memory
interface LoadingState {
  isLoading: boolean;
  isSubmitting: boolean;
  isLoadingDonViBaoHiem: boolean;
  isLoadingBenhVien: boolean;
}

const FormData = {
  // BHXHTN
  bhtn_tai_tuc: '',
  bhtn_so_thang_dong: '',
  bhtn_dong_tu_thang: undefined,
  bhtn_muc_tien_dong: '',
  bhtn_muc_dong_thang: '', // Mức đóng tháng cho BHXH
  bhtn_so_tien_dong: '',
  bhtn_tien_hh: '',
  bhtn_tlhh: '',
  //BHYT
  bhyt_tai_tuc: '',
  bhyt_so_thang_dong: '',
  bhyt_dong_tu_thang: undefined,
  bhyt_noi_dang_ky_kcb: '',
  bhyt_muc_dong_thang: '',
  bhyt_so_tien_dong: '',
  bhyt_tien_hh: '',
  bhyt_tlhh: '',
  //TT BIÊN LAI
  bt: '',
  bt_tvien: '',
  so_bien_lai: '',
  mau_bien_lai: '',
  quyen_bien_lai: '',
  don_vi_bhxh: '',
  noi_dung_thu: '',
  ghi_chu: '',
  // Fields for calculation results
  bhtn_nsdp_ho_tro: '', // NSNN hỗ trợ
  bhtn_nsnn_ho_tro: '', // NSĐP hỗ trợ
  bhyt_nsnn_ho_tro: '',
  bhyt_nsdp_ho_tro: '',
};

type FormData = {
  //BHXHTN
  bhtn_tai_tuc: string;
  bhtn_so_thang_dong: string;
  bhtn_dong_tu_thang: Date;
  bhtn_muc_tien_dong: string;
  bhtn_muc_dong_thang: string; // Mức đóng tháng cho BHXH
  bhtn_so_tien_dong: string;
  bhtn_tien_hh: string;
  bhtn_tlhh: string;
  //BHYT
  bhyt_tai_tuc: string;
  bhyt_so_thang_dong: string;
  bhyt_dong_tu_thang: Date;
  bhyt_noi_dang_ky_kcb: string;
  bhyt_muc_dong_thang: string;
  bhyt_so_tien_dong: string;
  bhyt_tien_hh: string;
  bhyt_tlhh: string;
  //THÔNG TIN BIÊN LAI
  bt: string;
  bt_tvien: string;
  so_bien_lai: string;
  mau_bien_lai: string;
  quyen_bien_lai: string;
  don_vi_bhxh: string;
  noi_dung_thu: string;
  ghi_chu: string;
  // Fields for calculation results
  bhtn_nsnn_ho_tro: string; // NSNN hỗ trợ
  bhtn_nsdp_ho_tro: string; // NSĐP hỗ trợ
  bhyt_nsnn_ho_tro: string;
  bhyt_nsdp_ho_tro: string;
};

interface DetailProductScreenProps {
  route: RouteProp<MainStackParamList, 'DetailProductScreen'> & {
    params: {
      memberData?: any;
      productData?: any;
      receiptData?: any;
      mode?: 'create' | 'edit';
      chiTietBienLai?: any;
      onUpdateInfo?: (productData: any, receiptData: any) => void;
    };
  };
}

export default function DetailProductScreen({route}: DetailProductScreenProps) {
  logger.log('DetailProductScreen');
  const navigation = useNavigation<MainNavigationProp>();
  const params = route?.params as
    | {memberData?: any; productData?: any; receiptData?: any; mode?: 'create' | 'edit'; chiTietBienLai?: any; onUpdateInfo?: (productData: any, receiptData: any) => void}
    | undefined;
  const {memberData, productData, receiptData, mode = 'create', chiTietBienLai, onUpdateInfo} = params || {};
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  // const dispatch = useDispatch<AppDispatch>();

  const isEditMode = mode === 'edit';
  const [selectedProducts, setSelectedProducts] = useState<string[]>(isEditMode ? [] : ['bhxh']); // Mặc định chọn BHXH tự nguyện cho create mode
  const [isShowModalTaiTucBaoHiem, setIsShowModalTaiTucBaoHiem] = useState(false);
  const [isShowModalTaiTucBHYT, setIsShowModalTaiTucBHYT] = useState(false);
  const [donViBaoHiem, setDonViBaoHiem] = useState<ActionSheetOption[]>([]);
  const [isShowModalDonViBaoHiem, setIsShowModalDonViBaoHiem] = useState(false);
  const [selectedDonViBaoHiem, setSelectedDonViBaoHiem] = useState<{id: string | number; title: string} | null>(null);

  // New state for hospital list modal
  const [isShowModalBenhVien, setIsShowModalBenhVien] = useState(false);
  const [danhSachBenhVien, setDanhSachBenhVien] = useState<ActionSheetOption[]>([]);
  const [selectedBenhVien, setSelectedBenhVien] = useState<{id: string | number; title: string} | null>(null);

  // LoadingState theo pattern từ memory
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    isSubmitting: false,
    isLoadingDonViBaoHiem: false,
    isLoadingBenhVien: false,
  });

  const handleProductToggle = (value: string) => {
    setSelectedProducts(prev => {
      const newSelection = prev.includes(value) ? prev.filter(item => item !== value) : [...prev, value];

      return newSelection;
    });
  };

  // Helper function để convert title về id cho API
  const convertTaiTucTitleToId = (title: string) => {
    const option = DATA_TAI_TUC__BH.find(item => item.title === title);
    return option?.id?.toString() || '';
  };

  // Helper function để convert id về title để hiển thị
  const convertTaiTucIdToTitle = (id: string) => {
    const option = DATA_TAI_TUC__BH.find(item => item.id === id);
    return option?.title || '';
  };

  // Update getDefaultValues to include calculation results
  const getDefaultValues = () => {
    if (isEditMode && chiTietBienLai) {
      const defaultValues = {
        //BHXHTN
        bhtn_so_thang_dong: chiTietBienLai.bhtn_so_thang_dong?.toString() || '',
        bhtn_dong_tu_thang: chiTietBienLai.bhtn_dong_tu_thang ? moment(chiTietBienLai.bhtn_dong_tu_thang, 'YYYYMMDD').toDate() : undefined,
        bhtn_muc_tien_dong: chiTietBienLai.bhtn_muc_tien_dong?.toString() || '',
        bhtn_muc_dong_thang: chiTietBienLai.bhtn_muc_dong_thang?.toString() || '',
        bhtn_nsnn_ho_tro: chiTietBienLai.bhtn_nsnn_ho_tro?.toString() || '',
        bhtn_nsdp_ho_tro: chiTietBienLai.bhtn_nsdp_ho_tro?.toString() || '',
        bhtn_tai_tuc: convertTaiTucIdToTitle(chiTietBienLai.bhtn_tai_tuc || ''),
        bhtn_so_tien_dong: chiTietBienLai.bhtn_so_tien_dong?.toString() || '',
        bhtn_tien_hh: chiTietBienLai.bhtn_tien_hh?.toString() || '',
        bhtn_tlhh: chiTietBienLai.bhtn_tlhh?.toString() || '',
        // BHYT
        bhyt_tai_tuc: convertTaiTucIdToTitle(chiTietBienLai.bhyt_tai_tuc || ''),
        bhyt_so_thang_dong: chiTietBienLai.bhyt_so_thang_dong?.toString() || '',
        bhyt_dong_tu_thang: chiTietBienLai.bhyt_dong_tu_thang ? moment(chiTietBienLai.bhyt_dong_tu_thang, 'YYYYMMDD').toDate() : undefined,
        bhyt_noi_dang_ky_kcb: chiTietBienLai.bhyt_noi_dang_ky_kcb || '', // Chỉ lấy mã, không lấy tên
        bhyt_muc_dong_thang: chiTietBienLai.bhyt_muc_dong_thang?.toString() || chiTietBienLai.bhyt_muc_dong_thang?.toString() || '',
        bhyt_nsnn_ho_tro: chiTietBienLai.bhyt_nsnn_ho_tro?.toString() || '',
        bhyt_nsdp_ho_tro: chiTietBienLai.bhyt_nsdp_ho_tro?.toString() || '',
        bhyt_so_tien_dong: chiTietBienLai.bhyt_so_tien_dong?.toString() || '',
        bhyt_tien_hh: chiTietBienLai.bhyt_hh_hh?.toString() || '',
        bhyt_tlhh: chiTietBienLai.bhyt_tlhh?.toString() || '',
        //TT BIÊN LAI
        so_bien_lai: chiTietBienLai.so_bien_lai?.toString() || '',
        don_vi_bhxh: chiTietBienLai.don_vi_bhxh || '',
        mau_bien_lai: chiTietBienLai.mau_bien_lai?.toString() || '',
        quyen_bien_lai: chiTietBienLai.quyen_bien_lai?.toString() || '',
        noi_dung_thu: chiTietBienLai.noi_dung_thu || '',
        ghi_chu: chiTietBienLai.ghi_chu || '',
      };

      return defaultValues;
    } else if (isEditMode && productData && receiptData) {
      const bhxhProduct = productData.find((p: any) => p.product === 'BHXH tự nguyện');
      const bhytProduct = productData.find((p: any) => p.product === 'Bảo hiểm y tế');

      return {
        //BHXHTN
        bhtn_so_thang_dong: bhxhProduct?.bhtn_so_thang_dong?.toString() || '',
        bhtn_dong_tu_thang: bhxhProduct?.bhtn_dong_tu_thang ? new Date(bhxhProduct.bhtn_dong_tu_thang) : undefined,
        bhtn_muc_tien_dong: bhxhProduct?.bhtn_muc_tien_dong?.toString() || '',
        bhtn_muc_dong_thang: bhxhProduct?.bhtn_muc_dong_thang?.toString() || '',
        bhtn_nsnn_ho_tro: bhxhProduct?.bhtn_nsnn_ho_tro?.toString() || '',
        bhtn_nsdp_ho_tro: bhxhProduct?.bhtn_nsdp_ho_tro?.toString() || '',
        bhtn_tai_tuc: bhxhProduct?.bhtn_tai_tuc?.toString() || '',
        bhtn_so_tien_dong: bhxhProduct?.bhtn_so_tien_dong?.toString() || '',
        bhtn_tien_hh: bhxhProduct?.bhtn_tien_hh?.toString() || '',
        bhtn_tlhh: bhxhProduct?.bhtn_tlhh?.toString() || '',
        // BHYT
        bhyt_tai_tuc: bhytProduct ? String(bhytProduct.bhyt_tai_tuc) : '',
        bhyt_so_thang_dong: bhytProduct ? String(bhytProduct.bhyt_so_thang_dong) : '',
        bhyt_dong_tu_thang: bhytProduct?.bhyt_dong_tu_thang ? new Date(bhytProduct.bhyt_dong_tu_thang) : undefined,
        bhyt_noi_dang_ky_kcb: bhytProduct ? String(bhytProduct.bhyt_noi_dang_ky_kcb) : '',
        bhyt_muc_dong_thang: bhytProduct ? String(bhytProduct?.bhyt_muc_dong_thang) : '',
        bhyt_nsnn_ho_tro: bhytProduct?.bhyt_nsnn_ho_tro?.toString() || '',
        bhyt_nsdp_ho_tro: bhytProduct?.bhyt_nsdp_ho_tro?.toString() || '',
        bhyt_so_tien_dong: bhytProduct?.bhtn_so_tien_dong?.toString() || '',
        bhyt_tien_hh: bhytProduct?.bhtn_tien_hh?.toString() || '',
        bhyt_tlhh: bhytProduct?.bhtn_tlhh?.toString() || '',
        //TT BIÊN LAI
        so_bien_lai: receiptData ? String(receiptData.so_bien_lai) : '',
        don_vi_bhxh: receiptData?.don_vi_bhxh ? String(receiptData?.don_vi_bhxh) : '',
        mau_bien_lai: receiptData?.mau_bien_lai ? String(receiptData?.mau_bien_lai) : '',
        quyen_bien_lai: receiptData?.quyen_bien_lai ? String(receiptData?.quyen_bien_lai) : '',
        noi_dung_thu: receiptData?.noi_dung_thu ? String(receiptData?.noi_dung_thu) : '',
        ghi_chu: receiptData?.ghi_chu || '',
      };
    }
    return FormData;
  };

  const {
    control,
    handleSubmit,
    formState: {errors},
    reset,
    setValue,
    getValues,
    trigger,
    watch,
  } = useForm<FormData>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: getDefaultValues(),
  });

  const getDonViBaoHiem = useCallback(async () => {
    try {
      setLoadingState(prev => ({...prev, isLoadingDonViBaoHiem: true}));

      let currentPage = 1;
      let pageSize = 1000;
      const params = {
        ma: '',
        ma_khac: '',
        ten: '',
        trang_thai: '',
        trang: currentPage,
        so_dong: pageSize,
        actionCode: ACTION_CODE.GET_DON_VI_BAO_HIEM,
      };
      const response = await getCommonExecute(params);

      if (response?.data) {
        // Kiểm tra cấu trúc data và format cho ActionSheetModal
        let dataArray = [];
        if (Array.isArray(response.data)) {
          dataArray = response.data;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          dataArray = response.data.data;
        } else if (response.data.list && Array.isArray(response.data.list)) {
          dataArray = response.data.list;
        } else {
          dataArray = [];
        }

        // Map data thành format ActionSheetOption cho ActionSheetModal mới
        const formattedData: ActionSheetOption[] = dataArray.map((item: any) => ({
          id: item.ma || item.id || item.value || item.ten,
          title: item.ten || item.name || item.label || `Đơn vị ${item.ma || item.id}`,
          onPress: () => {},
        }));

        setDonViBaoHiem(formattedData);
      } else {
        setDonViBaoHiem([]);
      }
    } catch (error) {
      logger.log('🚀 ~ getDonViBaoHiem ~ error:', error);
      setTimeout(() => {
        toast.error('Không thể tải danh sách đơn vị bảo hiểm', {
          position: 'top',
          duration: 3000,
        });
      }, 100);
    } finally {
      setLoadingState(prev => ({...prev, isLoadingDonViBaoHiem: false}));
    }
  }, []);

  const getDanhSachBenhVien = useCallback(async () => {
    try {
      setLoadingState(prev => ({...prev, isLoadingDanhSachBenhVien: true}));

      const params = {
        actionCode: ACTION_CODE.GET_DANH_SACH_BENH_VIEN,
      };
      const response = await getCommonExecute(params);
      if (response?.data) {
        // Kiểm tra cấu trúc data và format cho ActionSheetModal
        let dataArray = [];
        if (Array.isArray(response.data)) {
          dataArray = response.data;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          dataArray = response.data.data;
        } else if (response.data.list && Array.isArray(response.data.list)) {
          dataArray = response.data.list;
        } else {
          dataArray = [];
        }

        // Map data thành format ActionSheetOption cho ActionSheetModal mới
        const formattedData: ActionSheetOption[] = dataArray.map((item: any) => ({
          id: item.ma || item.id || item.value || item.ten,
          title: item.ten || item.name || item.label || `Bệnh viện ${item.ma || item.id}`,
          onPress: () => {},
        }));

        setDanhSachBenhVien(formattedData);
      } else {
        setDanhSachBenhVien([]);
        toast.warning('Không tải được danh sách bệnh viện', {
          duration: 3000,
          position: 'top',
        });
      }
    } catch (error) {
      logger.log('🚀 ~ getDanhSachBenhVien ~ error:', error);
      setDanhSachBenhVien([]);
      toast.error('Lỗi khi tải danh sách bệnh viện', {
        duration: 3000,
        position: 'top',
      });
    } finally {
      setLoadingState(prev => ({...prev, isLoadingDanhSachBenhVien: false}));
    }
  }, []);

  useEffect(() => {
    getDonViBaoHiem();
    getDanhSachBenhVien();
  }, []);

  // Đồng bộ selectedDonViBaoHiem khi danh sách donViBaoHiem được load
  useEffect(() => {
    if (donViBaoHiem.length > 0 && !selectedDonViBaoHiem) {
      const currentDonViValue = getValues('don_vi_bhxh');
      if (currentDonViValue) {
        const found = donViBaoHiem.find(item => item.id.toString() === currentDonViValue.toString());
        if (found) {
          setSelectedDonViBaoHiem(found);
        }
      }
    }
  }, [donViBaoHiem, selectedDonViBaoHiem]);

  useEffect(() => {
    if (danhSachBenhVien.length > 0 && !selectedBenhVien) {
      const currentBenhVienValue = getValues('bhyt_noi_dang_ky_kcb');
      if (currentBenhVienValue) {
        // Tìm theo mã (id) thay vì tên (title)
        const found = danhSachBenhVien.find(item => item.id.toString() === currentBenhVienValue.toString());
        if (found) {
          setSelectedBenhVien(found);
        }
      }
    }
  }, [danhSachBenhVien, selectedBenhVien]);

  const onSelectedDonViBaoHiem = (id: string | number, title: string) => {
    setValue('don_vi_bhxh', id.toString(), {shouldValidate: true}); // Cập nhật form value với mã
    setSelectedDonViBaoHiem({id, title}); // Cập nhật state để hiển thị tên
    setIsShowModalDonViBaoHiem(false);
    // Clear validation error nếu có
    if (errors.don_vi_bhxh) {
      trigger('don_vi_bhxh');
    }
  };

  // New function to handle hospital selection
  const onSelectedBenhVien = (id: string | number, title: string) => {
    setValue('bhyt_noi_dang_ky_kcb', id.toString(), {shouldValidate: true});
    setSelectedBenhVien({id, title});
    setIsShowModalBenhVien(false);
    // Clear validation error nếu có
    if (errors.bhyt_noi_dang_ky_kcb) {
      trigger('bhyt_noi_dang_ky_kcb');
    }
  };

  // Function to fetch hospital list directly (similar to getDonViBaoHiem)

  // Reset form khi có dữ liệu mới và set selected products cho edit mode
  useEffect(() => {
    if (isEditMode && chiTietBienLai) {
      reset(getDefaultValues());

      // Determine which products are selected based on existing data from chiTietBienLai
      const selectedProductsFromData = [];

      // Check BHXH - nếu có số tháng đóng hoặc số tiền đóng > 0
      if (chiTietBienLai.bhtn_so_thang_dong > 0 || chiTietBienLai.bhtn_so_tien_dong > 0 || chiTietBienLai.bhtn_muc_tien_dong > 0) {
        selectedProductsFromData.push('bhxh');
      }

      // Check BHYT - nếu có số tháng đóng hoặc số tiền đóng > 0
      if (chiTietBienLai.bhyt_so_thang_dong > 0 || chiTietBienLai.bhyt_so_tien_dong > 0) {
        selectedProductsFromData.push('bhyt');
      }

      if (selectedProductsFromData.length > 0) {
        setSelectedProducts(selectedProductsFromData);
      }

      // Set selected đơn vị bảo hiểm nếu có
      if (chiTietBienLai.don_vi_bhxh) {
        // Tìm trong danh sách donViBaoHiem trước
        const foundInList = donViBaoHiem.find(item => item.id.toString() === chiTietBienLai.don_vi_bhxh.toString());

        if (foundInList) {
          setSelectedDonViBaoHiem(foundInList);
        } else if (chiTietBienLai.don_vi_bhxh_ten) {
          // Fallback: sử dụng dữ liệu từ chiTietBienLai
          const donViBaoHiemFromData = {
            id: chiTietBienLai.don_vi_bhxh,
            title: chiTietBienLai.don_vi_bhxh_ten,
          };
          setSelectedDonViBaoHiem(donViBaoHiemFromData);
        }
      }
      // Set selected bệnh viện nếu có
      if (chiTietBienLai.bhyt_noi_dang_ky_kcb) {
        // Tìm trong danh sách danhSachBenhVien theo mã (id)
        const foundInList = danhSachBenhVien.find(item => item.id.toString() === chiTietBienLai.bhyt_noi_dang_ky_kcb.toString());

        if (foundInList) {
          setSelectedBenhVien(foundInList);
        } else if (chiTietBienLai.bhyt_noi_dang_ky_kcb_ten) {
          // Fallback: tạo object từ dữ liệu có sẵn
          const benhVienFromData = {
            id: chiTietBienLai.bhyt_noi_dang_ky_kcb,
            title: chiTietBienLai.bhyt_noi_dang_ky_kcb_ten,
          };
          setSelectedBenhVien(benhVienFromData);
        }
      }
    } else if (isEditMode && productData) {
      reset(getDefaultValues());

      // Determine which products are selected based on existing data
      const selectedProductsFromData = [];
      const bhxhProduct = productData.find((p: any) => p.product === 'BHXH tự nguyện');
      const bhytProduct = productData.find((p: any) => p.product === 'Bảo hiểm y tế');

      if (bhxhProduct && (bhxhProduct.months > 0 || bhxhProduct.amount)) {
        selectedProductsFromData.push('bhxh');
      }
      if (bhytProduct && (bhytProduct.months > 0 || bhytProduct.amount)) {
        selectedProductsFromData.push('bhyt');
      }

      if (selectedProductsFromData.length > 0) {
        setSelectedProducts(selectedProductsFromData);
      }
    } else if (!isEditMode) {
      setSelectedProducts(['bhxh']);
      // Đảm bảo create mode có mặc định BHXH được chọn
      setSelectedProducts(['bhxh']);
    }
  }, [productData, receiptData, chiTietBienLai, isEditMode, reset]);

  // Watch for form changes to enable real-time calculations
  const watchBhtnSoThangDong = useWatch({control, name: 'bhtn_so_thang_dong'});
  const watchBhtnMucTienDong = useWatch({control, name: 'bhtn_muc_tien_dong'});
  const watchBhtnTaiTuc = useWatch({control, name: 'bhtn_tai_tuc'});
  const watchBhtnDongTuThang = useWatch({control, name: 'bhtn_dong_tu_thang'});

  // Watch BHYT fields for calculation
  const watchBhytSoThangDong = useWatch({control, name: 'bhyt_so_thang_dong'});
  const watchBhytTaiTuc = useWatch({control, name: 'bhyt_tai_tuc'});
  const watchBhytMucDongThang = useWatch({control, name: 'bhyt_muc_dong_thang'});
  const watchBhytDongTuThang = useWatch({control, name: 'bhyt_dong_tu_thang'});

  // Đồng bộ tên và mã cho đơn vị bảo hiểm
  const donViBhxhValue = useWatch({control, name: 'don_vi_bhxh'});
  useEffect(() => {
    if (donViBhxhValue && donViBaoHiem.length > 0 && !selectedDonViBaoHiem) {
      const selected = donViBaoHiem.find(item => item.id.toString() === donViBhxhValue.toString());
      if (selected) {
        setSelectedDonViBaoHiem(selected);
      }
    }
  }, [donViBhxhValue, donViBaoHiem]);

  const noiDangKyKCB = useWatch({control, name: 'bhyt_noi_dang_ky_kcb'});
  useEffect(() => {
    if (noiDangKyKCB && danhSachBenhVien.length > 0 && !selectedBenhVien) {
      // Tìm theo mã (id) thay vì tên (title)
      const selected = danhSachBenhVien.find(item => item.id.toString() === noiDangKyKCB.toString());
      if (selected) {
        setSelectedBenhVien(selected);
      }
    }
  }, [noiDangKyKCB, danhSachBenhVien, selectedBenhVien, setValue]);
  useEffect(() => {
    if (selectedDonViBaoHiem) {
      setValue('don_vi_bhxh', selectedDonViBaoHiem.id.toString(), {shouldValidate: true});
    }
  }, [selectedDonViBaoHiem, setValue]);

  // Initialize BHYT fields with default values when BHYT is selected
  useEffect(() => {
    if (selectedProducts.includes('bhyt')) {
      // Set default values for BHYT fields if they are empty
      const currentSoThangDong = getValues('bhyt_so_thang_dong');
      const currentMucDongThang = getValues('bhyt_muc_dong_thang');
      const currentTaiTuc = getValues('bhyt_tai_tuc');
      if (currentTaiTuc === undefined || currentTaiTuc === null || currentTaiTuc === '') {
        setValue('bhyt_tai_tuc', '', {shouldDirty: false, shouldValidate: false});
      }

      if (currentSoThangDong === undefined || currentSoThangDong === null || currentSoThangDong === '') {
        setValue('bhyt_so_thang_dong', '', {shouldDirty: false, shouldValidate: false});
      }

      if (currentMucDongThang === undefined || currentMucDongThang === null || currentMucDongThang === '') {
        setValue('bhyt_muc_dong_thang', '', {shouldDirty: false, shouldValidate: false});
      }
    }
  }, [selectedProducts, getValues, setValue]);

  // Auto-calculate BHXH contributions using API when required fields are filled
  useEffect(() => {
    // Chỉ tính toán khi BHXH được chọn
    if (!selectedProducts.includes('bhxh') && !selectedProducts.includes('bhyt')) {
      // Nếu BHXH không được chọn, xóa giá trị trong các TextField kết quả
      setValue('bhtn_muc_dong_thang', '');
      setValue('bhtn_nsnn_ho_tro', '');
      setValue('bhtn_nsdp_ho_tro', '');
      setValue('bhyt_nsnn_ho_tro', '');
      setValue('bhyt_nsdp_ho_tro', '');
      setValue('bhtn_so_tien_dong', '');
      setValue('bhtn_tien_hh', '');
      setValue('bhtn_tlhh', '');
      setValue('bhyt_so_tien_dong', '');
      setValue('bhyt_tien_hh', '');
      setValue('bhyt_tlhh', '');
      return;
    }

    // Kiểm tra đủ điều kiện cho BHXH riêng biệt
    const soThangDong = watchBhtnSoThangDong ? parseFloat(watchBhtnSoThangDong) : 0;
    const mucTienDong = watchBhtnMucTienDong ? parseCurrency(watchBhtnMucTienDong) : 0;
    const taiTuc = watchBhtnTaiTuc;

    // Kiểm tra đủ điều kiện cho BHYT riêng biệt
    const soThangDongBHYT = watchBhytSoThangDong ? parseFloat(watchBhytSoThangDong) : 0;
    const mucDongThangBHYT = watchBhytMucDongThang ? parseCurrency(watchBhytMucDongThang) : 0;
    const taiTucBHYT = watchBhytTaiTuc;

    // Kiểm tra điều kiện theo specification riêng biệt cho từng loại
    const isValidBHXH = selectedProducts.includes('bhxh') ? soThangDong > 0 && mucTienDong > 0 && taiTuc && taiTuc.trim() !== '' : false;
    const isValidBHYT = selectedProducts.includes('bhyt') ? soThangDongBHYT > 0 && mucDongThangBHYT > 0 && taiTucBHYT && taiTucBHYT.trim() !== '' : false;

    // Nếu ít nhất một loại hợp lệ thì thực hiện tính toán
    if (isValidBHXH || isValidBHYT) {
      const params = {
        actionCode: ACTION_CODE.TINH_TOAN_BHXH,
        // Chỉ thêm các trường BHXH nếu BHXH hợp lệ
        ...(isValidBHXH && {
          bhtn_tai_tuc: taiTuc ? convertTaiTucTitleToId(taiTuc) : '',
          bhtn_so_thang_dong: soThangDong.toString(),
          bhtn_dong_tu_thang: getValues('bhtn_dong_tu_thang') ? moment(getValues('bhtn_dong_tu_thang')).format('YYYYMM') : '',
          bhtn_muc_tien_dong: Math.round(mucTienDong).toString(),
        }),
        // Chỉ thêm các trường BHYT nếu BHYT hợp lệ
        ...(isValidBHYT && {
          bhyt_tai_tuc: taiTucBHYT ? convertTaiTucTitleToId(taiTucBHYT) : '',
          bhyt_so_thang_dong: soThangDongBHYT.toString(),
          bhyt_dong_tu_thang: getValues('bhyt_dong_tu_thang') ? moment(getValues('bhyt_dong_tu_thang')).format('YYYYMM') : '',
          bhyt_muc_dong_thang: Math.round(mucDongThangBHYT).toString(),
        }),
        // Thông tin thành viên
        bt_tvien: memberData?.bt?.toString() || chiTietBienLai?.bt_tvien?.toString() || '',
        bt: memberData?.bt?.toString() || chiTietBienLai?.bt?.toString() || '',
      };

      getCommonExecute(params)
        .then(res => {
          if (res?.data) {
            // Truy cập đúng cấu trúc dữ liệu từ API response
            const bhtnData = res.data.bhtn || {};
            const bhytData = res.data.bhyt || {};

            // Cập nhật các trường kết quả BHYT nếu BHYT hợp lệ
            if (isValidBHYT) {
              const bhytNsnnHoTro = formatCurrencyPlain(bhytData.nsnn_ho_tro) || 0;
              const bhytNsdpHoTro = formatCurrencyPlain(bhytData.nsdp_ho_tro) || 0;
              const bhytSoTienDong = formatCurrencyPlain(bhytData.so_tien_dong) || 0;
              const bhytTienHH = formatCurrencyPlain(bhytData.tien_hh) || 0;
              const bhytTLHH = bhytData.tlhh || 0;

              setValue('bhyt_nsnn_ho_tro', bhytNsnnHoTro.toString());
              setValue('bhyt_nsdp_ho_tro', bhytNsdpHoTro.toString());
              setValue('bhyt_so_tien_dong', bhytSoTienDong.toString());
              setValue('bhyt_tien_hh', bhytTienHH.toString());
              setValue('bhyt_tlhh', String(bhytTLHH));
            } else if (selectedProducts.includes('bhyt')) {
              // Nếu BHYT được chọn nhưng không hợp lệ, xóa các trường kết quả
              setValue('bhyt_nsnn_ho_tro', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhyt_nsdp_ho_tro', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhyt_so_tien_dong', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhyt_tien_hh', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhyt_tlhh', '', {shouldDirty: true, shouldValidate: false});
            }

            // Cập nhật các trường kết quả BHXH nếu BHXH hợp lệ
            if (isValidBHXH) {
              // Đảm bảo giá trị được lấy đúng từ object bhtn
              const mucDongThang = formatCurrencyPlain(bhtnData.muc_dong_thang);
              const nsnnHoTro = formatCurrencyPlain(bhtnData.nsnn_ho_tro);
              const nsdpHoTro = formatCurrencyPlain(bhtnData.nsdp_ho_tro);
              const soTienDong = formatCurrencyPlain(bhtnData.so_tien_dong);
              const tienHH = formatCurrencyPlain(bhtnData.tien_hh);
              const tLHH = bhtnData.tlhh || 0;

              setValue('bhtn_muc_dong_thang', mucDongThang.toString());
              setValue('bhtn_nsnn_ho_tro', nsnnHoTro.toString());
              setValue('bhtn_nsdp_ho_tro', nsdpHoTro.toString());
              setValue('bhtn_so_tien_dong', soTienDong.toString());
              setValue('bhtn_tien_hh', tienHH.toString());
              setValue('bhtn_tlhh', String(tLHH));
            } else if (selectedProducts.includes('bhxh')) {
              // Nếu BHXH được chọn nhưng không hợp lệ, xóa các trường kết quả
              setValue('bhtn_muc_dong_thang', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhtn_nsnn_ho_tro', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhtn_nsdp_ho_tro', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhtn_so_tien_dong', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhtn_tien_hh', '', {shouldDirty: true, shouldValidate: false});
              setValue('bhtn_tlhh', '', {shouldDirty: true, shouldValidate: false});
            }
          }
        })
        .catch(error => {
          console.error('Lỗi tính toán BHXH:', error);
          // Xử lý lỗi riêng biệt cho từng loại
          if (selectedProducts.includes('bhxh')) {
            setValue('bhtn_muc_dong_thang', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhtn_nsnn_ho_tro', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhtn_nsdp_ho_tro', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhtn_so_tien_dong', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhtn_tien_hh', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhtn_tlhh', '', {shouldDirty: true, shouldValidate: false});
          }
          if (selectedProducts.includes('bhyt')) {
            setValue('bhyt_nsnn_ho_tro', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhyt_nsdp_ho_tro', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhyt_so_tien_dong', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhyt_tien_hh', '', {shouldDirty: true, shouldValidate: false});
            setValue('bhyt_tlhh', '', {shouldDirty: true, shouldValidate: false});
          }

          // Small delay to ensure toast is displayed properly
          setTimeout(() => {
            toast.error('Không thể tính toán BHXH vào lúc này. Vui lòng thử lại sau.', {
              position: 'top',
              duration: 3000,
            });
          }, 100);
        });
    } else {
      // Nếu không có loại nào hợp lệ, xóa giá trị trong các TextField kết quả theo specification
      if (selectedProducts.includes('bhxh')) {
        setValue('bhtn_muc_dong_thang', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhtn_nsnn_ho_tro', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhtn_nsdp_ho_tro', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhtn_so_tien_dong', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhtn_tien_hh', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhtn_tlhh', '', {shouldDirty: true, shouldValidate: false});
      }
      if (selectedProducts.includes('bhyt')) {
        setValue('bhyt_nsnn_ho_tro', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhyt_nsdp_ho_tro', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhyt_so_tien_dong', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhyt_tien_hh', '', {shouldDirty: true, shouldValidate: false});
        setValue('bhyt_tlhh', '', {shouldDirty: true, shouldValidate: false});
      }
    }
  }, [
    watchBhtnSoThangDong,
    watchBhtnMucTienDong,
    watchBhtnTaiTuc,
    watchBhytSoThangDong,
    watchBhytMucDongThang,
    watchBhytTaiTuc,
    selectedProducts,
    memberData,
    chiTietBienLai,
    getValues,
    setValue,
    watchBhtnDongTuThang,
    watchBhytDongTuThang,
  ]);

  // Validate form before submission
  const validateForm = useCallback(() => {
    // Check if at least one product is selected
    if (selectedProducts.length === 0) {
      setTimeout(() => {
        toast.error('Vui lòng chọn ít nhất một sản phẩm bảo hiểm', {
          position: 'top',
          duration: 3000,
        });
      }, 100);
      return false;
    }

    // Check member data or chiTietBienLai
    if (!memberData?.bt && !chiTietBienLai?.bt_tvien && !chiTietBienLai?.bt) {
      setTimeout(() => {
        toast.error('Không tìm thấy thông tin thành viên', {
          position: 'top',
          duration: 3000,
        });
      }, 100);
      return false;
    }

    return true;
  }, [selectedProducts, memberData, chiTietBienLai]);

  const onSubmit = useCallback(
    async (data: FormData) => {
      // Pre-validation before form submission
      if (!validateForm()) {
        return;
      }

      setLoadingState(prev => ({...prev, isSubmitting: true}));

      try {
        // Base params structure - use chiTietBienLai data if available for edit mode
        const baseParams =
          isEditMode && chiTietBienLai
            ? {
                ...chiTietBienLai, // Spread all existing data
              }
            : {};

        // Lấy cả ID và tên đơn vị bảo hiểm
        let donViBhxhId = selectedDonViBaoHiem?.id?.toString() || '';
        let donViBhxhTen = selectedDonViBaoHiem?.title || data.don_vi_bhxh || '';

        if (!donViBhxhId && isEditMode && chiTietBienLai?.don_vi_bhxh) {
          donViBhxhId = chiTietBienLai.don_vi_bhxh.toString();
          donViBhxhTen = chiTietBienLai.don_vi_bhxh_ten || data.don_vi_bhxh || '';
        }

        // Lấy mã bệnh viện từ selectedBenhVien
        let benhVienId = selectedBenhVien?.id?.toString() || data.bhyt_noi_dang_ky_kcb || '';

        // Nếu không có selectedBenhVien nhưng có dữ liệu từ form, tìm trong danh sách
        if (!benhVienId && data.bhyt_noi_dang_ky_kcb) {
          const foundBenhVien = danhSachBenhVien.find(item => item.id.toString() === data.bhyt_noi_dang_ky_kcb.toString() || item.title.toString() === data.bhyt_noi_dang_ky_kcb.toString());
          if (foundBenhVien) {
            benhVienId = foundBenhVien.id.toString();
          }
        }

        const params = {
          ...baseParams,
          actionCode: ACTION_CODE.TAO_PHIEU_THU_HO,
          bt: isEditMode ? chiTietBienLai?.bt?.toString() || '' : '',
          bt_tvien: isEditMode ? chiTietBienLai?.bt_tvien?.toString() || '' : memberData?.bt?.toString() || '',
          so_bien_lai: data.so_bien_lai || '',
          mau_bien_lai: data.mau_bien_lai || '',
          quyen_bien_lai: data.quyen_bien_lai || '',
          don_vi_bhxh: data.don_vi_bhxh || '', // Gửi mã (ID)
          noi_dung_thu: data.noi_dung_thu || '',
          ghi_chu: data.ghi_chu || '',

          // BHXH tự nguyện
          bhtn_tai_tuc: selectedProducts.includes('bhxh') ? convertTaiTucTitleToId(data.bhtn_tai_tuc || '') : '',
          bhtn_so_thang_dong: selectedProducts.includes('bhxh') ? data.bhtn_so_thang_dong || '' : '',
          bhtn_dong_tu_thang: selectedProducts.includes('bhxh') ? (data.bhtn_dong_tu_thang ? moment(data.bhtn_dong_tu_thang).format('YYYYMM') : '') : '',
          bhtn_muc_tien_dong: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_muc_tien_dong || '')).toString() : '',
          bhtn_muc_dong_thang: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_muc_dong_thang || '')).toString() : '',
          bhtn_nsnn_ho_tro: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_nsnn_ho_tro || '')).toString() : '',
          bhtn_nsdp_ho_tro: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_nsdp_ho_tro || '')).toString() : '',
          bhtn_so_tien_dong: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_so_tien_dong || '')).toString() : '',
          bhtn_tien_hh: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_tien_hh || '')).toString() : '',
          bhtn_tlhh: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhtn_tlhh || '')).toString() : '',

          // BHYT
          bhyt_tai_tuc: selectedProducts.includes('bhyt') ? convertTaiTucTitleToId(data.bhyt_tai_tuc || '') : '',
          bhyt_so_thang_dong: selectedProducts.includes('bhyt') ? data.bhyt_so_thang_dong || '' : '',
          bhyt_dong_tu_thang: selectedProducts.includes('bhyt') ? (data.bhyt_dong_tu_thang ? moment(data.bhyt_dong_tu_thang).format('YYYYMM') : '') : '',
          bhyt_noi_dang_ky_kcb: selectedProducts.includes('bhyt') ? benhVienId : '', // Gửi mã bệnh viện
          bhyt_muc_dong_thang: selectedProducts.includes('bhyt') ? Math.round(parseCurrency(data.bhyt_muc_dong_thang || '')).toString() : '',
          bhyt_nsnn_ho_tro: selectedProducts.includes('bhyt') ? Math.round(parseCurrency(data.bhyt_nsnn_ho_tro || '')).toString() : '',
          bhyt_nsdp_ho_tro: selectedProducts.includes('bhyt') ? Math.round(parseCurrency(data.bhyt_nsdp_ho_tro || '')).toString() : '',
          bhyt_so_tien_dong: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhyt_so_tien_dong || '')).toString() : '',
          bhyt_tien_hh: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhyt_tien_hh || '')).toString() : '',
          bhyt_tlhh: selectedProducts.includes('bhxh') ? Math.round(parseCurrency(data.bhyt_tlhh || '')).toString() : '',
        };

        const response = await getCommonExecute(params);

        if (response?.data) {
          const successMessage = isEditMode ? 'Cập nhật thông tin sản phẩm thành công!' : 'Tạo phiếu thu hộ thành công!';
          setTimeout(() => {
            toast.success(successMessage, {
              position: 'top',
            });
          }, 100);

          // Callback to parent screen if provided
          if (onUpdateInfo) {
            onUpdateInfo(data, {});
          }
          NavigationUtil.push(MAIN_SCREENS.DETAIL_COLLECTION, {ma: response.output.bt, prevScreen: route.name});
        } else {
          const errorMessage = isEditMode ? 'Cập nhật thông tin sản phẩm thất bại. Vui lòng thử lại.' : 'Tạo phiếu thu hộ thất bại. Vui lòng thử lại.';
          setTimeout(() => {
            toast.error(errorMessage, {
              position: 'top',
              duration: 2000,
            });
          }, 100);
        }
      } catch (error) {
        logger.log('🚀 ~ onSubmit ~ error:', error);
        const errorMessage = isEditMode ? 'Đã xảy ra lỗi khi cập nhật thông tin sản phẩm. Vui lòng thử lại.' : 'Đã xảy ra lỗi khi tạo phiếu thu hộ. Vui lòng thử lại.';
        setTimeout(() => {
          toast.error(errorMessage, {
            position: 'top',
            duration: 3000,
          });
        }, 100);
      } finally {
        setLoadingState(prev => ({...prev, isSubmitting: false}));
      }
    },
    [memberData, selectedProducts, onUpdateInfo, navigation, isEditMode, chiTietBienLai],
  );

  const onSelectedTaiTucBaoHiem = (id: string | number, title: string) => {
    setValue('bhtn_tai_tuc', title); // Hiển thị title thay vì id
    setIsShowModalTaiTucBaoHiem(false);
    // Trigger validation để clear error nếu có
    trigger('bhtn_tai_tuc');
  };

  const onSelectedTaiTucBHYT = (id: string | number, title: string) => {
    setValue('bhyt_tai_tuc', title); // Hiển thị title thay vì id
    setIsShowModalTaiTucBHYT(false);
    // Trigger validation để clear error nếu có
    trigger('bhyt_tai_tuc');
  };

  // Helper function để convert title về id cho selectedValue
  const getTitleToId = (title: string) => {
    const option = DATA_TAI_TUC__BH.find(item => item.title === title);
    return option?.id || '';
  };

  const buttonTitle = isEditMode ? 'Cập nhật' : 'Tạo phiếu thu';

  return (
    <ScreenComponent
      showHeader
      headerTitle="Thông tin chi tiết sản phẩm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title={buttonTitle} onPress={handleSubmit(onSubmit)} loading={loadingState.isSubmitting} disabled={loadingState.isSubmitting} />}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 130 : 60} enabled={true}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: spacing.lg}}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}>
          {/* Thông tin sản phẩm tham gia */}
          <Card title="Thông tin sản phẩm tham gia">
            <Text style={styles.helpText}>Vui lòng chọn ít nhất một sản phẩm bảo hiểm để tham gia</Text>
            <View style={styles.checkboxGroup}>
              {productOptions.map(option => (
                <TouchableOpacity key={option.value} style={styles.checkboxContainer} onPress={() => handleProductToggle(option.value)}>
                  <View style={[styles.checkbox, selectedProducts.includes(option.value) && styles.checkboxSelected]}>
                    {selectedProducts.includes(option.value) && <Text style={styles.checkboxText}>✓</Text>}
                  </View>
                  <Text style={styles.checkboxLabel}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>
          {/* Bảo hiểm xã hội tự nguyện */}
          {selectedProducts.includes('bhxh') && (
            <Card title="Bảo hiểm xã hội tự nguyện">
              <TouchableOpacity onPress={() => setIsShowModalTaiTucBaoHiem(true)}>
                <View pointerEvents="none">
                  <TextField
                    rightIconType={'dropdown'}
                    label="Chọn loại tái tục"
                    required
                    inputContainerStyle={errors.bhtn_tai_tuc ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                    labelStyle={errors.bhtn_tai_tuc ? {color: colors.danger} : undefined}
                    control={control}
                    name="bhtn_tai_tuc"
                    rules={selectedProducts.includes('bhxh') ? (detailProductFormValidation.bhtn_tai_tuc as any) : undefined}
                    error={errors.bhtn_tai_tuc?.message}
                  />
                </View>
              </TouchableOpacity>
              <View style={styles.inputGroup}>
                <TextField
                  placeholder="Nhập số tháng đóng"
                  label="Số tháng đóng"
                  required
                  keyboardType="numeric"
                  containerStyle={styles.input}
                  control={control}
                  name="bhtn_so_thang_dong"
                  rules={selectedProducts.includes('bhxh') ? (detailProductFormValidation.bhtn_so_thang_dong as any) : undefined}
                  error={errors.bhtn_so_thang_dong?.message}
                />
                <DateTimePickerComponent
                  showPlaceholder={true}
                  placeholder="Chọn tháng"
                  label="Từ tháng"
                  containerStyle={styles.input}
                  control={control}
                  name="bhtn_dong_tu_thang"
                  rules={selectedProducts.includes('bhxh') ? (detailProductFormValidation.bhtn_dong_tu_thang as any) : undefined}
                  required
                  error={errors.bhtn_dong_tu_thang?.message}
                />
              </View>
              <View style={styles.inputGroup}>
                <TextField
                  placeholder="Nhập mức tiền căn cứ"
                  label="Mức tiền căn cứ"
                  required
                  containerStyle={styles.input}
                  control={control}
                  name="bhtn_muc_tien_dong"
                  rules={selectedProducts.includes('bhxh') ? (detailProductFormValidation.bhtn_muc_tien_dong as any) : undefined}
                  isCurrency
                  error={errors.bhtn_muc_tien_dong?.message}
                />
                <TextField
                  label="Mức đóng/tháng"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhtn_muc_dong_thang"
                  // isCurrency8
                  editable={false}
                  placeholder="0"
                  error={errors.bhtn_muc_dong_thang?.message}
                />
              </View>
              <View style={styles.inputGroup}>
                <TextField
                  label="NSNN hỗ trợ"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhtn_nsnn_ho_tro"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhtn_nsnn_ho_tro?.message}
                />
                <TextField
                  label="NSĐP hỗ trợ"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhtn_nsdp_ho_tro"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhtn_nsdp_ho_tro?.message}
                />
              </View>
              <View style={styles.inputGroup}>
                <TextField
                  label="Tiền hoa hồng"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhtn_tien_hh"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhtn_nsnn_ho_tro?.message}
                />
                <TextField
                  label="Tỷ lệ hoa hồng"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhtn_tlhh"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhtn_nsdp_ho_tro?.message}
                />
              </View>

              <TextField
                label="Số tiền đóng"
                required
                containerStyle={styles.input}
                inputContainerStyle={{backgroundColor: colors.gray[100]}}
                labelStyle={{backgroundColor: colors.gray[100]}}
                control={control}
                name="bhtn_so_tien_dong"
                // isCurrency
                editable={false}
                placeholder="0"
                error={errors.bhtn_nsdp_ho_tro?.message}
              />

              {/* <TextField label="Người tham gia đóng" required style={styles.input} control= {control} name="nguoiThamGiaDong" isCurrency /> */}
            </Card>
          )}
          {/* Bảo hiểm y tế */}
          {selectedProducts.includes('bhyt') && (
            <Card title="Bảo hiểm y tế">
              <TouchableOpacity onPress={() => setIsShowModalTaiTucBHYT(true)}>
                <View pointerEvents="none">
                  <TextField
                    editable={false}
                    rightIconType={'dropdown'}
                    label="Chọn loại tái tục"
                    required
                    labelStyle={errors.bhyt_tai_tuc ? {color: colors.danger} : undefined}
                    inputContainerStyle={errors.bhyt_tai_tuc ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                    control={control}
                    name="bhyt_tai_tuc"
                    rules={selectedProducts.includes('bhyt') ? (detailProductFormValidation.bhyt_tai_tuc as any) : undefined}
                    error={errors.bhyt_tai_tuc?.message}
                  />
                </View>
              </TouchableOpacity>
              <View style={styles.inputGroup}>
                <TextField
                  label="Số tháng đóng"
                  placeholder="Nhập số tháng đóng"
                  required
                  containerStyle={styles.input}
                  control={control}
                  name="bhyt_so_thang_dong"
                  rules={selectedProducts.includes('bhyt') ? (detailProductFormValidation.bhyt_so_thang_dong as any) : undefined}
                  error={errors.bhyt_so_thang_dong?.message}
                />
                <DateTimePickerComponent
                  placeholder="Chọn tháng"
                  label="Từ tháng"
                  containerStyle={styles.input}
                  control={control}
                  name="bhyt_dong_tu_thang"
                  rules={selectedProducts.includes('bhyt') ? (detailProductFormValidation.bhyt_dong_tu_thang as any) : undefined}
                  required
                  error={errors.bhyt_dong_tu_thang?.message}
                />
              </View>

              <Controller
                control={control}
                name="bhyt_noi_dang_ky_kcb"
                rules={detailProductFormValidation.bhyt_noi_dang_ky_kcb as any}
                render={({field}) => (
                  <TouchableOpacity style={styles.input} onPress={() => setIsShowModalBenhVien(true)}>
                    <View pointerEvents="none">
                      <TextField
                        editable={false}
                        rightIconType={'dropdown'}
                        label="Nơi đăng ký KCB"
                        required
                        labelStyle={errors.bhyt_noi_dang_ky_kcb ? {color: colors.danger} : undefined}
                        inputContainerStyle={errors.bhyt_noi_dang_ky_kcb ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                        error={errors.bhyt_noi_dang_ky_kcb?.message}
                        value={selectedBenhVien?.title || ''} // Hiển thị tên thay vì mã
                        onChangeText={field.onChange}
                        placeholder="Chọn đơn vị bảo hiểm"
                      />
                    </View>
                  </TouchableOpacity>
                )}
              />

              <View style={styles.inputGroup}>
                <TextField
                  label="Mức đóng/tháng"
                  required
                  placeholder="Nhập mức đóng/tháng"
                  containerStyle={styles.input}
                  control={control}
                  name="bhyt_muc_dong_thang"
                  rules={selectedProducts.includes('bhyt') ? (detailProductFormValidation.bhyt_muc_dong_thang as any) : undefined}
                  isCurrency
                  error={errors.bhyt_muc_dong_thang?.message}
                />
                <TextField
                  label="NSNN hỗ trợ"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhyt_nsnn_ho_tro"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhyt_nsnn_ho_tro?.message}
                />
              </View>
              <View style={styles.inputGroup}>
                <TextField
                  label="NSĐP hỗ trợ"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhyt_nsdp_ho_tro"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhyt_nsdp_ho_tro?.message}
                />
                <TextField
                  label="Tiền hoa hồng"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhyt_tien_hh"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhyt_nsnn_ho_tro?.message}
                />
              </View>

              <View style={styles.inputGroup}>
                <TextField
                  label="Tỉ lệ hoa hồng"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhyt_tlhh"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhyt_nsdp_ho_tro?.message}
                />
                <TextField
                  label="Số tiền đóng"
                  required
                  containerStyle={styles.input}
                  inputContainerStyle={{backgroundColor: colors.gray[100]}}
                  labelStyle={{backgroundColor: colors.gray[100]}}
                  control={control}
                  name="bhyt_so_tien_dong"
                  // isCurrency
                  editable={false}
                  placeholder="0"
                  error={errors.bhtn_nsdp_ho_tro?.message}
                />
              </View>
            </Card>
          )}
          {/* Thông tin thu tiền */}
          <Card title="Thông tin thu tiền">
            <View style={styles.inputGroup}>
              <TextField
                label="Số biên lai"
                placeholder="Nhập số biên lai"
                required
                containerStyle={styles.input}
                control={control}
                name="so_bien_lai"
                rules={detailProductFormValidation.so_bien_lai as any}
                error={errors.so_bien_lai?.message}
              />
              <TextField
                label="Mẫu biên lai"
                placeholder="Nhập mẫu biên lai"
                containerStyle={styles.input}
                control={control}
                name="mau_bien_lai"
                rules={detailProductFormValidation.mau_bien_lai as any}
                error={errors.mau_bien_lai?.message}
              />
            </View>
            <TextField
              label="Quyển biên lai"
              placeholder="Nhập quyển biên lai"
              containerStyle={styles.input}
              control={control}
              name="quyen_bien_lai"
              rules={detailProductFormValidation.quyen_bien_lai as any}
              error={errors.quyen_bien_lai?.message}
            />
            <Controller
              control={control}
              name="don_vi_bhxh"
              rules={detailProductFormValidation.don_vi_bhxh as any}
              render={({field}) => (
                <TouchableOpacity onPress={() => setIsShowModalDonViBaoHiem(true)}>
                  <View pointerEvents="none">
                    <TextField
                      editable={false}
                      rightIconType={'dropdown'}
                      label="Đơn vị bảo hiểm"
                      required
                      labelStyle={errors.don_vi_bhxh ? {color: colors.danger} : undefined}
                      inputContainerStyle={errors.don_vi_bhxh ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                      error={errors.don_vi_bhxh?.message}
                      value={selectedDonViBaoHiem?.title || ''} // Hiển thị tên thay vì mã
                      onChangeText={field.onChange}
                      placeholder="Chọn đơn vị bảo hiểm"
                    />
                  </View>
                </TouchableOpacity>
              )}
            />
            <TextField
              label="Nội dung thu"
              placeholder="Nhập nội dung thu"
              required
              containerStyle={styles.input}
              control={control}
              name="noi_dung_thu"
              rules={detailProductFormValidation.noi_dung_thu as any}
              error={errors.noi_dung_thu?.message}
            />
            <TextField
              placeholder="Nhập ghi chú"
              label="Ghi chú"
              containerStyle={styles.input}
              control={control}
              name="ghi_chu"
              rules={detailProductFormValidation.ghi_chu as any}
              error={errors.ghi_chu?.message}
            />
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
      {/* Action Sheet Modals */}
      <ActionSheetModal
        isVisible={isShowModalTaiTucBaoHiem}
        onClose={() => setIsShowModalTaiTucBaoHiem(false)}
        title="Chọn loại tái tục"
        subtitle="Vui lòng chọn loại tái tục bảo hiểm"
        options={DATA_TAI_TUC__BH.map(option => ({
          ...option,
          onPress: () => onSelectedTaiTucBaoHiem(option.id, option.title),
        }))}
        selectedValue={getTitleToId(getValues('bhtn_tai_tuc') || '')}
        showSearchField={false}
        cancelButtonText="Hủy"
      />

      <ActionSheetModal
        isVisible={isShowModalTaiTucBHYT}
        onClose={() => setIsShowModalTaiTucBHYT(false)}
        title="Chọn loại tái tục"
        subtitle="Vui lòng chọn loại tái tục bảo hiểm y tế"
        options={DATA_TAI_TUC__BH.map(option => ({
          ...option,
          onPress: () => onSelectedTaiTucBHYT(option.id, option.title),
        }))}
        selectedValue={getTitleToId(getValues('bhyt_tai_tuc') || '')}
        showSearchField={false}
        cancelButtonText="Hủy"
      />

      <ActionSheetModal
        isVisible={isShowModalDonViBaoHiem}
        onClose={() => setIsShowModalDonViBaoHiem(false)}
        title="Chọn đơn vị bảo hiểm"
        subtitle="Vui lòng chọn đơn vị bảo hiểm phù hợp"
        options={donViBaoHiem.map(option => ({
          ...option,
          onPress: () => onSelectedDonViBaoHiem(option.id, option.title),
        }))}
        selectedValue={selectedDonViBaoHiem?.id}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm đơn vị bảo hiểm..."
        cancelButtonText="Hủy"
      />

      {/* New ActionSheetModal for hospital list */}
      <ActionSheetModal
        isVisible={isShowModalBenhVien}
        onClose={() => setIsShowModalBenhVien(false)}
        title="Nơi đăng ký KCB"
        subtitle="Vui lòng chọn bệnh viện đăng ký KCB"
        options={danhSachBenhVien.map(option => ({
          ...option,
          onPress: () => onSelectedBenhVien(option.id, option.title),
        }))}
        selectedValue={selectedBenhVien?.id}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm bệnh viện..."
        cancelButtonText="Hủy"
      />
    </ScreenComponent>
  );
}
