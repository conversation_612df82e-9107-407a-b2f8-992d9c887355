import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  searchInput: {
    marginHorizontal: spacing.sm,
  },
  copyRow: {
    flexDirection: 'row',
    gap: spacing.sm,
    alignItems: 'center',
  },
  copyIcon: {
    width: 20,
    height: 20,
  },
  content: {
    flex: 1,
    padding: spacing.lg,
  },
  nameValue: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  buttonContainer: {
    marginTop: spacing.xl,
    marginBottom: spacing.lg,
  },
  searchIcon: {
    width: 24,
    height: 24,
  },
  item: {
    padding: spacing.sm,
    backgroundColor: colors.white,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.xs,
    // gap: spacing.xl,
  },
  titleRow: {
    gap: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    width: 140,
  },
  valueRow: {
    gap: spacing.sm,
  },
  value: {
    color: colors.gray[800],
    fontSize: typography.fontSize.base,
  },
  pending: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  success: {
    color: colors.green,
    fontWeight: typography.fontWeight.medium as any,
  },
  floatButton: {
    position: 'absolute',
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    bottom: spacing.xl,
    right: spacing.xl,
    backgroundColor: colors.green,
    borderRadius: borderRadius.full,
  },
  fabIcon: {
    width: 20,
    height: 20,
    tintColor: colors.white,
  },
  danger: {
    color: colors.danger,
    fontWeight: typography.fontWeight.medium as any,
  },
  receiptValue: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
});
