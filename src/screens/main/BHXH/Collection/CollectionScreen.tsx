import {colors, spacing} from '@constants/theme';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {FlatList, Image, Platform, RefreshControl, Text, TouchableOpacity, View} from 'react-native';

import R from '@assets/R';
import {Card, createToastHelpers, Icon, Loading, ScreenComponent, TextField, useToast} from '@components/common';
import {ACTION_CODE} from '@constants/axios';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp} from '@navigation/types';
import Clipboard from '@react-native-clipboard/clipboard';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import {styles} from './collection.style';
import {BienLaiItem, DanhSachBienLaiResponse, getTrangThaiDisplay, LoadMoreState} from './types';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export default function CollectionScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const [searchValue, setSearchValue] = useState('');
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // State cho danh sách biên lai từ API
  const [bienLaiList, setBienLaiList] = useState<BienLaiItem[]>([]);
  const [loadMoreState, setLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: 10,
  });

  // Refresh flags theo pattern HouseholdListScreen
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const insets = useSafeAreaInsets();

  // FlatList ref để reset layout
  const flatListRef = useRef<FlatList>(null);

  // Ref để track xem searchValue đã từng có giá trị hay chưa
  const hasSearchedBefore = useRef(false);

  const getDanhSachBienLai = useCallback(
    async (isLoadMore: boolean = false, customSearchValue?: string) => {
      apiCallCount.current += 1;

      try {
        // Get current values from state
        let currentPage: number = 1;
        let pageSize: number = 10;

        setLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize;
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        const searchTerm = customSearchValue !== undefined ? customSearchValue : searchValue;

        const params = {
          nsd_lap_bien_lai: '',
          trang_thai: '',
          so_bhxh: '',
          ten: '',
          cmt: '',
          nd_tim: searchTerm,
          trang: currentPage!,
          so_dong: pageSize!,
          actionCode: ACTION_CODE.DANH_SACH_BIEN_LAI,
        };
        const response = await getCommonExecute(params);
        if (response?.data) {
          const responseData: DanhSachBienLaiResponse = response.data;
          const newItems = responseData.data || [];
          const totalItems = responseData.tong_so_dong || 0;

          setBienLaiList(prev => {
            let result: BienLaiItem[];
            if (isLoadMore) {
              // Merge data cho loadmore
              result = [...prev, ...newItems];
            } else {
              // Replace data cho search/refresh
              result = newItems;
            }
            return result;
          });

          setLoadMoreState(prev => {
            const hasMoreItems = currentPage * prev.pageSize < totalItems;

            return {
              ...prev,
              isLoading: false,
              isLoadingMore: false,
              currentPage: currentPage,
              totalItems: totalItems,
              hasMore: hasMoreItems,
            };
          });

          // Reset FlatList layout sau khi load data - chỉ khi pull-to-refresh
          if (!isLoadMore && isPullToRefresh && flatListRef.current) {
            setTimeout(() => {
              flatListRef.current?.scrollToOffset({offset: 0, animated: false});
            }, 100);
          }
        }
      } catch (error) {
        logger.log('🚀 ~ getDanhSachBienLai ~ error:', error);
        setLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      }
    },
    [], // Không có dependencies để tránh re-create function
  );

  // Load data lần đầu
  useEffect(() => {
    if (!isInitialLoadDone.current) {
      isInitialLoadDone.current = true;
      getDanhSachBienLai();
    }
  }, []); // Chỉ chạy 1 lần khi component mount

  // Reset initial mount flag after a delay
  useEffect(() => {
    setTimeout(() => setIsInitialMount(false), 1000);
  }, []);

  // Track loading state để reset layout khi loading finish - chỉ khi pull-to-refresh
  useEffect(() => {
    // Chỉ reset layout khi pull-to-refresh xong, không reset khi loading từ focus/back
    if (!loadMoreState.isLoading && isPullToRefresh && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({offset: 0, animated: false});
      }, 150);
    }
  }, [loadMoreState.isLoading, isPullToRefresh]);

  // Use useFocusEffect to refresh data when returning from other screens
  useFocusEffect(
    React.useCallback(() => {
      // Only refresh if:
      // 1. Not on initial mount
      // 2. We need to refresh (flag was set)
      if (!isInitialMount && needsRefresh) {
        logger.log('🔄 Refreshing biên lai data after returning from other screen');
        getDanhSachBienLai(false, searchValue).then(() => {
          // Reset FlatList layout after data refresh
          setTimeout(() => {
            flatListRef.current?.scrollToOffset({offset: 0, animated: false});
          }, 300);
        });
        setNeedsRefresh(false); // Reset flag immediately
      }
    }, [isInitialMount, needsRefresh, searchValue]),
  );

  // Sử dụng ref để track số lần gọi API và initial load
  const apiCallCount = useRef(0);
  const isInitialLoadDone = useRef(false);

  // Hàm xử lý tìm kiếm thủ công khi bấm icon search
  const handleSearch = useCallback(() => {
    getDanhSachBienLai(false, searchValue);
  }, [searchValue]);

  // Lắng nghe thay đổi của searchValue - tự động load data khi search text rỗng
  useEffect(() => {
    // Track khi searchValue có giá trị
    if (searchValue !== '') {
      hasSearchedBefore.current = true;
    }

    // Chỉ tự động load khi:
    // 1. searchValue rỗng (người dùng đã xóa hết text)
    // 2. Đã từng search trước đó (hasSearchedBefore = true)
    // 3. Đã load initial data
    if (searchValue === '' && hasSearchedBefore.current && isInitialLoadDone.current) {
      logger.log('🔄 Auto-loading data after clearing search text');
      getDanhSachBienLai(false, '');
    }
    // Không tự động search khi searchValue có giá trị - chỉ search khi bấm icon hoặc Enter
  }, [searchValue]);

  // Render item cho danh sách biên lai từ API
  const renderBienLaiItem = ({item}: {item: BienLaiItem}) => {
    const trangThaiDisplay = getTrangThaiDisplay(item.trang_thai_ten);

    // Function xử lý copy với toast thông báo
    const handleCopy = (value: string, title: string) => {
      Clipboard.setString(value);
      toast.info(`Đã sao chép ${title}`, {
        duration: 2000,
        position: 'bottom',
      });
    };

    const renderLabel = (
      title: string,
      value: string | number | React.ReactNode,
      options?: {
        showCopy?: boolean;
        copyValue?: string;
        statusColor?: {
          color: string;
          backgroundColor: string;
        };
      },
    ) => {
      return (
        <View style={styles.contentRow}>
          <Text style={[styles.title, {color: colors.gray[600]}]}>{title}</Text>

          {/* Hiển thị với copy button */}
          {options?.showCopy && value !== '' ? (
            <View style={styles.copyRow}>
              <Text style={styles.value}>{value}</Text>
              <TouchableOpacity activeOpacity={1} onPress={() => handleCopy(options.copyValue || String(value), title)}>
                <Image source={R.icons.ic_copy} style={styles.copyIcon} resizeMode="contain" />
              </TouchableOpacity>
            </View>
          ) : /* Hiển thị trạng thái với màu */
          options?.statusColor ? (
            <View
              style={{
                backgroundColor: options.statusColor.backgroundColor,
                paddingHorizontal: 8,
                paddingVertical: 2,
                borderRadius: 4,
                alignSelf: 'flex-start',
              }}>
              <Text style={[styles.value, {color: options.statusColor.color}]}>{value}</Text>
            </View>
          ) : /* Hiển thị bình thường */
          typeof value === 'string' || typeof value === 'number' ? (
            <Text style={[styles.value, title === 'Họ và tên' ? styles.nameValue : title === 'Biên lai' ? styles.receiptValue : {}]}>{value}</Text>
          ) : (
            <View>{value}</View>
          )}
        </View>
      );
    };

    return (
      <Card style={styles.item}>
        <TouchableOpacity activeOpacity={1} onPress={() => handleNavigateToDetail(item)}>
          <View style={{padding: 12}}>
            {renderLabel('Mã số BHXH', item.ma_so_bhxh || '', {
              showCopy: true,
              copyValue: item.ma_so_bhxh || '',
            })}

            {renderLabel('Họ và tên', item.ten || item.ten_ho_gia_dinh || '')}

            {renderLabel('Số CCCD', item.cccd || item.cmt || '', {
              showCopy: true,
              copyValue: item.cccd || item.cmt || '',
            })}

            {/* {renderLabel('Điện thoại', item.dien_thoai || 'N/A', {
              showCopy: true,
              copyValue: item.dien_thoai || '',
            })} */}

            {renderLabel('Trạng thái', item.trang_thai_ten, {
              statusColor: trangThaiDisplay,
            })}

            {renderLabel('Biên lai', item.so_bien_lai || 'Chưa có biên lai')}
            {renderLabel('Số tiền', item.so_tien_text || (item.so_tien ? `${item.so_tien.toLocaleString()} đ` : ''))}
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  // Hàm xử lý load more
  const handleLoadMore = useCallback(() => {
    if (!loadMoreState.isLoadingMore && loadMoreState.hasMore && !loadMoreState.isLoading) {
      getDanhSachBienLai(true, searchValue);
    }
  }, [loadMoreState.isLoadingMore, loadMoreState.hasMore, loadMoreState.isLoading, searchValue]);

  // Hàm xử lý refresh
  const handleRefresh = useCallback(() => {
    setIsPullToRefresh(true); // Set flag để cho phép layout dãn ra khi pull-to-refresh
    getDanhSachBienLai(false, searchValue).then(() => {
      setIsPullToRefresh(false); // Reset flag sau khi refresh xong
    });
  }, [searchValue]);

  // Handle navigation to search/add screen with refresh flag
  const handleNavigateToSearch = () => {
    setNeedsRefresh(true); // Set flag to refresh when returning
    navigation.navigate(MAIN_SCREENS.SEARCH_INFORMATION);
  };

  // Handle navigation to detail collection with refresh flag
  const handleNavigateToDetail = (item: BienLaiItem) => {
    setNeedsRefresh(true); // Set flag to refresh when returning
    navigation.navigate(MAIN_SCREENS.DETAIL_COLLECTION, {
      ma: item.bt,
    });
  };

  // Render footer cho loadmore
  const renderFooter = () => {
    if (!loadMoreState.isLoadingMore) return null;
    return (
      <View style={{padding: 20, alignItems: 'center'}}>
        <Loading size="small" message={'Đang tải thêm...'} />
      </View>
    );
  };

  // Render empty component
  const renderEmpty = () => {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingTop: 50}}>
        <Text style={styles.description}>{searchValue ? 'Không tìm thấy biên lai phù hợp' : 'Không có dữ liệu'}</Text>
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={loadMoreState.isLoading && !loadMoreState.isLoadingMore && !isPullToRefresh}
      showHeader
      headerTitle="Danh sách biên lai thu tiền"
      showBackButton
      onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        <TextField
          placeholder="Tên hộ gia đình, tên thành viên, điện thoại, số sổ BHXH ..."
          variant="outlined"
          showPlaceholderWhenEmpty={true}
          rightIconType={
            <TouchableOpacity onPress={handleSearch}>
              <Icon name="SearchNormal" color={colors.gray[600]} size={24} variant="Linear" />
            </TouchableOpacity>
          }
          value={searchValue}
          onChangeText={setSearchValue}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
          inputContainerStyle={styles.searchInput}
        />
        <FlatList
          ref={flatListRef}
          data={bienLaiList}
          renderItem={renderBienLaiItem}
          keyExtractor={(_, index) => index.toString()}
          ListEmptyComponent={renderEmpty}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={<RefreshControl refreshing={isPullToRefresh} onRefresh={handleRefresh} colors={[colors.primary]} tintColor={colors.primary} />}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={10}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
            autoscrollToTopThreshold: 10,
          }}
          onLayout={() => {
            // Force layout calculation when FlatList mounts - chỉ khi pull-to-refresh
            if (isPullToRefresh && flatListRef.current) {
              flatListRef.current.scrollToOffset({offset: 0, animated: false});
            }
          }}
        />
        <TouchableOpacity style={[styles.floatButton, {bottom: Platform.OS === 'android' ? insets.bottom + spacing['2xl'] || spacing.xl : spacing['2xl']}]} onPress={handleNavigateToSearch}>
          <Image source={R.icons.ic_add} style={styles.fabIcon} resizeMode="contain" />
        </TouchableOpacity>
      </View>
    </ScreenComponent>
  );
}
