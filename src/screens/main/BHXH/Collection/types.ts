// Types cho màn hình Collection - Danh sách biên lai

import {colors} from '@constants/theme';

export interface BienLaiItem {
  id?: string;
  nsd_lap_bien_lai?: string;
  trang_thai?: string;
  trang_thai_ten?: string;
  so_bhxh?: string;
  ten?: string;
  ten_ho_gia_dinh?: string;
  cmt?: string;
  cccd?: string;
  dien_thoai?: string;
  dia_chi?: string;
  san_pham?: string;
  so_tien?: number;
  so_tien_text?: string;
  ngay_lap?: string;
  ngay_lap_formatted?: string;
  ma_bien_lai?: string;
  ghi_chu?: string;
  [key: string]: any; // Cho phép thêm các field khác từ API
}

export interface DanhSachBienLaiResponse {
  data: BienLaiItem[];
  tong_so_dong: number;
  trang_hien_tai?: number;
  tong_so_trang?: number;
}

export interface DanhSachBienLaiParams {
  nsd_lap_bien_lai: string;
  trang_thai: string;
  so_bhxh: string;
  ten: string;
  cmt: string;
  nd_tim: string;
  trang: number;
  so_dong: number;
  actionCode: string;
}

// Interface cho tham số API chi tiết biên lai
export interface ChiTietBienLaiParams {
  ma: string; // Mã biên lai (b_ma từ item)
  actionCode: string;
}

// Interface cho response chi tiết biên lai
export interface ChiTietBienLaiResponse {
  data?: ChiTietBienLaiData;
  message?: string;
  success?: boolean;
  error?: string;
}

// Interface cho dữ liệu chi tiết biên lai
export interface ChiTietBienLaiData {
  // Thông tin cơ bản
  ma?: string;
  ma_bien_lai?: string;
  so_bien_lai?: string | number | undefined;
  ngay_lap?: string;
  ngay_lap_formatted?: string;

  // Thông tin người nộp
  so_bhxh?: string;
  ten?: string;
  ten_ho_gia_dinh?: string;
  cccd?: string;
  cmt?: string;
  dien_thoai?: string;
  dia_chi?: string;
  email?: string;

  // Thông tin biên lai
  trang_thai?: string;
  trang_thai_ten?: string;
  loai_bien_lai?: string;
  loai_bien_lai_ten?: string;

  // Thông tin tiền
  so_tien?: number;
  so_tien_text?: string;
  so_tien_bang_chu?: string;

  // Thông tin bảo hiểm
  san_pham?: string;
  san_pham_ten?: string;
  thang_dong?: string;
  nam_dong?: string;
  tu_thang?: string;
  den_thang?: string;

  // Thông tin người lập
  nsd_lap?: string;
  nsd_lap_ten?: string;
  ngay_lap_chi_tiet?: string;

  // Thông tin thanh toán
  hinh_thuc_thanh_toan?: string;
  hinh_thuc_thanh_toan_ten?: string;
  ngay_thanh_toan?: string;
  ngay_thanh_toan_formatted?: string;

  // Ghi chú
  ghi_chu?: string;
  ly_do?: string;

  // Thông tin bổ sung
  ma_don_vi?: string;
  ten_don_vi?: string;
  ma_chi_nhanh?: string;
  ten_chi_nhanh?: string;

  // Cho phép thêm các field khác từ API
  [key: string]: any;
}

export interface LoadMoreState {
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

// Enum cho trạng thái biên lai dựa trên trang_thai_ten
export enum TrangThaiTenBienLai {
  CHUA_LAP = 'Chưa lập',
  DA_LAP = 'Đã lập',
  DA_THANH_TOAN = 'Đã thanh toán',
  HUY = 'Hủy',
  CHO_DUYET = 'Chờ duyệt',
  TU_CHOI = 'Từ chối',
  DANG_XU_LY = 'Đang xử lý',
  HOAN_THANH = 'Hoàn thành',
  CHUA_THANH_TOAN = 'Chưa thanh toán',
  DA_GUI = 'Đã gửi',
  DANG_CHO = 'Đang chờ',
  KHONG_HOP_LE = 'Không hợp lệ',
}

// Mapping trạng thái hiển thị dựa trên trang_thai_ten - Background xám, chỉ thay đổi màu text
export const TRANG_THAI_BIEN_LAI_DISPLAY = {
  // Các trạng thái có thể có từ API (dựa trên trang_thai_ten)

  'Đã thu tiền': {
    text: 'Đã thanh toán',
    color: colors.primary, // Xanh lá
    backgroundColor: '#f5f5f5', // Xám
  },
  'Đã hủy': {
    text: 'Đã hủy',
    color: colors.danger, // Đỏ
    backgroundColor: '#f5f5f5', // Xám
  },
  'Đang xử lý': {
    text: 'Đang xử lý',
    color: colors.warning, // Cam đậm
    backgroundColor: '#f5f5f5', // Xám
  },
  'Đã chuyển BHXH': {
    text: 'Đã chuyển BHXH',
    color: colors.success, // Xanh lá
    backgroundColor: '#f5f5f5', // Xám
  },

  // Fallback cho trạng thái không xác định
  default: {
    text: 'Không xác định',
    color: '#666666', // Xám đậm
    backgroundColor: '#f5f5f5', // Xám
  },
};

// Array chứa tất cả các trạng thái có thể có
export const ALL_TRANG_THAI_VALUES = Object.keys(TRANG_THAI_BIEN_LAI_DISPLAY).filter(key => key !== 'default');

// Function để lấy thông tin hiển thị trạng thái dựa trên trang_thai_ten
export const getTrangThaiDisplay = (trangThaiTen?: string) => {
  if (!trangThaiTen) return TRANG_THAI_BIEN_LAI_DISPLAY['default'];

  // Thử tìm exact match trước (dựa trên trang_thai_ten)
  if (TRANG_THAI_BIEN_LAI_DISPLAY[trangThaiTen as keyof typeof TRANG_THAI_BIEN_LAI_DISPLAY]) {
    return TRANG_THAI_BIEN_LAI_DISPLAY[trangThaiTen as keyof typeof TRANG_THAI_BIEN_LAI_DISPLAY];
  }

  // Thử tìm case-insensitive và trim spaces
  const normalizedTrangThai = trangThaiTen.trim();
  const foundKey = ALL_TRANG_THAI_VALUES.find(key => key.toLowerCase().trim() === normalizedTrangThai.toLowerCase().trim());

  if (foundKey) {
    return TRANG_THAI_BIEN_LAI_DISPLAY[foundKey as keyof typeof TRANG_THAI_BIEN_LAI_DISPLAY];
  }

  // Thử tìm partial match (chứa từ khóa)
  const partialMatch = ALL_TRANG_THAI_VALUES.find(key => key.toLowerCase().includes(normalizedTrangThai.toLowerCase()) || normalizedTrangThai.toLowerCase().includes(key.toLowerCase()));

  if (partialMatch) {
    return TRANG_THAI_BIEN_LAI_DISPLAY[partialMatch as keyof typeof TRANG_THAI_BIEN_LAI_DISPLAY];
  }

  // Fallback về default với text từ API
  return {
    text: trangThaiTen,
    color: '#666666',
    backgroundColor: '#f5f5f5',
  };
};
