import { borderRadius, colors, spacing, typography } from "@constants/theme";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  formRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
   input: {
    flex: 1,
    flexGrow: 1,
    flexBasis: '49%',
    height: '100%',
    width: '100%',
    fontSize: typography.fontSize.base,
    paddingHorizontal: spacing.md,
  },
  participantItem: {
    flexDirection: 'row',
    gap: spacing.xl,
  },
  participantContent: {
    gap: spacing.sm,
  },
  participantTitleText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  participantValueText: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  nameValue: {
    color: colors.primary,
    fontWeight: typography.fontWeight.semibold as any,
  },
  selectedParticipant: {
    backgroundColor: colors.green + '20',
    borderWidth: 1,
    borderColor: colors.green,
  },
  buttonContainer: {
    marginTop: spacing.sm,
    marginBottom: spacing.md
  },
});