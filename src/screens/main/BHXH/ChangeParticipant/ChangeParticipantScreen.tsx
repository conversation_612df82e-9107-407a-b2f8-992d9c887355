import {View, Text, FlatList, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import ScreenComponent from '@components/common/ScreenComponent';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {MainNavigationProp, MainStackParamList} from '@navigation/types';
import {styles} from './changeparticipant.style';
import {<PERSON><PERSON>, Card, TextField} from '@components/common';
import {useForm} from 'react-hook-form';

interface ParticipantProps {
  codeBHXH: string;
  cccd: string;
  name: string;
  dateOfBirth: string;
  phone: string;
}

// Tạo dữ liệu mẫu để demo
const generateSampleParticipants = (currentParticipant: ParticipantProps): ParticipantProps[] => {
  const sampleData: ParticipantProps[] = [
    currentParticipant, // Người tham gia hiện tại ở đầu danh sách
    {
      codeBHXH: '12345678900',
      cccd: '1234567890',
      name: '<PERSON>uy<PERSON><PERSON>n <PERSON>',
      dateOfBirth: '01/01/2000',
      phone: '0909090909',
    },
    {
      codeBHXH: '12345678901',
      cccd: '1234567891',
      name: 'Nguyễn Văn B',
      dateOfBirth: '01/01/1990',
      phone: '0909090908',
    },
    {
      codeBHXH: '12345678902',
      cccd: '1234567892',
      name: 'Trần Thị C',
      dateOfBirth: '15/05/1985',
      phone: '0909090907',
    },
  ];

  // Loại bỏ trùng lặp dựa trên mã BHXH
  return sampleData.filter((item, index, self) => index === self.findIndex(p => p.codeBHXH === item.codeBHXH));
};
type ChangeParticipantParams = {
  nameHousehold: string;
  currentParticipant: any;
  participants?: any[];
  onUpdateInfo?: (updatedData: any) => void;
};

export default function ChangeParticipantScreen({route}: {route: RouteProp<MainStackParamList, MAIN_SCREENS.CHANGE_PARTICIPANT>}) {
  const navigation = useNavigation<MainNavigationProp>();
  const params = route.params as ChangeParticipantParams;
  const {nameHousehold = '', currentParticipant = null, participants, onUpdateInfo} = params || {};

  // Sử dụng dữ liệu participants từ params nếu có, nếu không thì tạo dữ liệu mẫu
  const participantData = participants || (currentParticipant ? generateSampleParticipants(currentParticipant) : []);

  // Mặc định chọn người tham gia hiện tại
  const [selectedParticipant, setSelectedParticipant] = useState<ParticipantProps | null>(currentParticipant);
  const {control, handleSubmit} = useForm<ParticipantProps>({
    defaultValues: {
      codeBHXH: '',
      cccd: '',
      name: '',
      dateOfBirth: '',
      phone: '',
    },
  });
  const [searchData, setSearchData] = useState<ParticipantProps[]>(participantData);
  const handleSearch = (data: ParticipantProps) => {
    logger.log(data);
    if (data.codeBHXH === '' && data.cccd === '' && data.name === '' && data.dateOfBirth === '' && data.phone === '') {
      setSearchData(participantData);
    } else {
      setSearchData(
        participantData.filter(
          (item: ParticipantProps) => item.codeBHXH === data.codeBHXH && item.cccd === data.cccd && item.name === data.name && item.dateOfBirth === data.dateOfBirth && item.phone === data.phone,
        ),
      );
    }
  };
  const onSubmit = (data: ParticipantProps) => {
    logger.log(data);
    handleSearch(data);
  };
  const handleApply = () => {
    logger.log(selectedParticipant);
    navigation.goBack();
    onUpdateInfo?.(selectedParticipant);
  };

  const renderParticipant = (item: ParticipantProps) => {
    return (
      <TouchableOpacity onPress={() => setSelectedParticipant(item)}>
        <Card style={StyleSheet.flatten([styles.participantItem, item.codeBHXH === selectedParticipant?.codeBHXH ? styles.selectedParticipant : undefined])}>
          <View style={styles.participantContent}>
            <Text style={styles.participantTitleText}>Mã số BHXH</Text>
            <Text style={styles.participantTitleText}>Họ và tên</Text>
            <Text style={styles.participantTitleText}>Số CCCD</Text>
            <Text style={styles.participantTitleText}>Ngày sinh</Text>
            <Text style={styles.participantTitleText}>Tên chủ hộ</Text>
          </View>
          <View style={styles.participantContent}>
            <Text style={styles.participantValueText}>{item.codeBHXH}</Text>
            <Text style={[styles.participantValueText, styles.nameValue]}>{item.name}</Text>
            <Text style={styles.participantValueText}>{item.cccd}</Text>
            <Text style={styles.participantValueText}>{item.dateOfBirth}</Text>
            <Text style={styles.participantValueText}>{nameHousehold}</Text>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      showHeader
      headerTitle="Thay đổi người tham gia bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title="Áp dụng" onPress={handleApply} />}>
      <View style={styles.container}>
        {/* Thông tin tìm kiếm */}
        <Card title="Thông tin tìm kiếm">
          <View style={styles.formRow}>
            <TextField label="Mã số BHXH" style={styles.input} control={control} name="codeBHXH" />
            <TextField label="Số CCCD" style={styles.input} control={control} name="cccd" />
          </View>
          <TextField label="Họ và tên" style={styles.input} control={control} name="name" />
          <View style={styles.formRow}>
            <TextField label="Ngày sinh" style={styles.input} control={control} name="dateOfBirth" />
            <TextField label="Điện thoại" style={styles.input} control={control} name="phone" />
          </View>
        </Card>

        {/* Button tìm kiếm */}
        <View style={styles.buttonContainer}>
          <Button title="Tìm kiếm" onPress={handleSubmit(onSubmit)} />
        </View>

        {/* Danh sách người tham gia */}
        <FlatList
          data={searchData}
          renderItem={({item}) => renderParticipant(item)}
          keyExtractor={item => item.codeBHXH}
          ListEmptyComponent={<Text style={styles.description}>Không có dữ liệu</Text>}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </ScreenComponent>
  );
}
