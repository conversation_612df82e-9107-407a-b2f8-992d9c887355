import {View, Text, KeyboardAvoidingView, ScrollView, Platform, TouchableOpacity} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {But<PERSON>, Card, ScreenComponent, TextField, SelectableOption, useToast, createToastHelpers, DateTimePickerComponent} from '@components/common';
import ActionSheetModal, {ActionSheetOption} from '@components/common/ActionSheetModal';
import {styles} from './Styles';
import {useForm, useWatch} from 'react-hook-form';
import {spacing, colors} from '@constants/theme';
import {RootState, useAppSelector} from '@store/index';
import {houseInsuranceFormValidation} from '@utils/validationSchemas';
import {getCommonExecute} from '@services/endpoints';

interface FormValues {
  loai_nha: string;
  md_sd: string;
  tinh_thanh: string;
  tinh_thanh_ten: string;
  phuong_xa: string;
  phuong_xa_ten: string;
  dia_chi: string;
  so_tang: string;
  dien_tich: string;
  nam_sd: string;
  hinh_thuc_chu_sh: string;
  so_tien_bh: string;
  thoi_han_bh: string;
  gio_bd: string;
  ngay_hl: Date | string;
  gio_kt: string;
  ngay_kt: Date | string;
}

const houseTypeOptions = [
  {id: 'can_ho_chung_cu', label: 'Căn hộ chung cư', iconName: 'Building' as const},
  {id: 'nha_lien_ke_biet_thu', label: 'Nhà liền kề, biệt thự', iconName: 'Buildings2' as const},
  {id: 'nha_o_loai_khac', label: 'Nhà ở loại khác', iconName: 'Home2' as const},
];

const defaultValues: FormValues = {
  loai_nha: '',
  md_sd: '',
  tinh_thanh: '',
  tinh_thanh_ten: '',
  phuong_xa: '',
  phuong_xa_ten: '',
  dia_chi: '',
  so_tang: '',
  dien_tich: '',
  nam_sd: '',
  hinh_thuc_chu_sh: '',
  so_tien_bh: '',
  thoi_han_bh: '',
  gio_bd: '',
  ngay_hl: new Date(),
  gio_kt: '',
  ngay_kt: '',
};

export default function ThongTinNDBHScreen({navigation, route}: any) {
  // Get mode and data from route params
  const {mode, editData} = route?.params; // 'create' or 'edit'
  const isEditMode = mode === 'edit';
  const isCreateMore = mode === 'create_more';
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // Helper function to convert Date to YYYYMMDD number format
  const toYMD = useCallback((val: any): number => {
    if (!val) return 0;
    if (val instanceof Date) {
      const y = val.getFullYear();
      const m = String(val.getMonth() + 1).padStart(2, '0');
      const d = String(val.getDate()).padStart(2, '0');
      return parseInt(`${y}${m}${d}`);
    }
    if (typeof val === 'string') {
      if (/^\d{8}$/.test(val)) return parseInt(val);
      const [dd, mm, yyyy] = val.split('/');
      if (dd && mm && yyyy) return parseInt(`${yyyy}${mm.padStart(2, '0')}${dd.padStart(2, '0')}`);
    }
    return 0;
  }, []);

  // Prepare form default values based on mode
  const formDefaultValues = useMemo(() => {
    if (isEditMode && editData) {
      return {
        loai_nha: editData.loai_nha || '',
        md_sd: editData.md_sd || '',
        tinh_thanh: editData.tinh_thanh || '',
        tinh_thanh_ten: editData.tinh_thanh_ten || '',
        phuong_xa: editData.phuong_xa || '',
        phuong_xa_ten: editData.phuong_xa_ten || '',
        dia_chi: editData.dia_chi || '',
        so_tang: editData.so_tang || '',
        dien_tich: editData.dien_tich || '',
        nam_sd: editData.nam_sd || '',
        hinh_thuc_chu_sh: editData.hinh_thuc_chu_sh || '',
        so_tien_bh: editData.so_tien_bh || '',
        thoi_han_bh: editData.thoi_han_bh || '',
        gio_bd: editData.gio_bd || '',
        ngay_hl: editData.ngay_hl || new Date(),
        gio_kt: editData.gio_kt || '',
        ngay_kt: editData.ngay_kt || '',
      };
    }
    return defaultValues;
  }, [isEditMode, editData]);

  const {
    control,
    formState: {errors, isSubmitting},
    handleSubmit,
    setValue,
    trigger,
  } = useForm<FormValues>({
    defaultValues: formDefaultValues,
  });

  // Redux state
  const {provinces, wards} = useAppSelector((state: RootState) => state.address);

  // Local state
  const [showProvinceModal, setShowProvinceModal] = useState(false);
  const [showWardModal, setShowWardModal] = useState(false);

  // Watch for changes
  const selectedProvince = useWatch({control, name: 'tinh_thanh'});

  // Handlers
  const handleProvinceSelect = useCallback(
    (provinceCode: string, provinceName: string) => {
      setValue('tinh_thanh', provinceCode);
      setValue('tinh_thanh_ten', provinceName);
      setValue('phuong_xa', '');
      setValue('phuong_xa_ten', '');
      setShowProvinceModal(false);
      trigger('tinh_thanh_ten');
      trigger('phuong_xa_ten');
    },
    [setValue, trigger],
  );

  const handleWardSelect = useCallback(
    (wardCode: string, wardName: string) => {
      setValue('phuong_xa', wardCode);
      setValue('phuong_xa_ten', wardName);
      setShowWardModal(false);
      trigger('phuong_xa_ten');
    },
    [setValue, trigger],
  );

  // Options for ActionSheetModal
  const provinceOptions: ActionSheetOption[] = useMemo(
    () =>
      provinces.map(province => ({
        id: province.ma,
        title: province.ten,
        onPress: () => handleProvinceSelect(province.ma, province.ten),
      })),
    [provinces, handleProvinceSelect],
  );

  const wardOptions: ActionSheetOption[] = useMemo(() => {
    if (!selectedProvince) {
      return [];
    }
    return wards
      .filter((w: any) => w.ma_tinh === selectedProvince)
      .map((w: any) => ({
        id: w.ma,
        title: w.ten,
        onPress: () => handleWardSelect(w.ma, w.ten),
      }));
  }, [selectedProvince, wards, handleWardSelect]);

  const onSubmit = useCallback(
    async (data: FormValues) => {
      console.log('Form data:', data);
      try {
        const params = {
          // Map form data to API params
          loai_nha: data.loai_nha,
          md_sd: data.md_sd,
          tinh_thanh: data.tinh_thanh,
          phuong_xa: data.phuong_xa,
          dia_chi: data.dia_chi,
          so_tang: parseInt(data.so_tang),
          dien_tich: parseFloat(data.dien_tich),
          nam_sd: parseInt(data.nam_sd),
          hinh_thuc_chu_sh: data.hinh_thuc_chu_sh,
          // Remove currency formatting and convert to number
          so_tien_bh: parseInt(data.so_tien_bh),
          thoi_han_bh: data.thoi_han_bh,
          gio_bd: parseInt(data.gio_bd),
          ngay_hl: toYMD(data.ngay_hl),
          gio_kt: parseInt(data.gio_kt),
          ngay_kt: toYMD(data.ngay_kt),
        };
        console.log('🚀 ~ ThongTinNDBHScreen ~ params:', params);
        const response = await getCommonExecute(params);
        console.log('🚀 ~ ThongTinNDBHScreen ~ response:', response);

        if (response?.data) {
          // Navigate back first
          if (isEditMode) navigation.goBack();
          else {
          }

          // Show toast on previous screen after navigation completes
          // Small delay ensures the previous screen is fully mounted
          setTimeout(() => {
            toast.success(isEditMode ? 'Cập nhật thông tin thành công' : 'Thêm mới thông tin thành công');
          }, 100);
        } else {
          toast.error(response?.message || 'Có lỗi xảy ra, vui lòng thử lại');
          console.log('Loi luu thong tin nha duoc bao hiem: ', response?.error);
        }
      } catch (error) {
        toast.error('Có lỗi xảy ra, vui lòng thử lại');
        console.log('🚀 ~ ThongTinNDBHScreen ~ error:', error);
      }
    },
    [isEditMode, navigation, toast],
  );

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin nhà được bảo hiểm"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}
      showFooter
      footer={<Button loading={isSubmitting} title="Tiếp tục" onPress={handleSubmit(onSubmit)} />}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.sm}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          {/* Thông tin ngôi nhà */}
          <Card title="Thông tin ngôi nhà">
            <SelectableOption control={control} name="loai_nha" label="Chọn loại nhà/căn hộ" options={houseTypeOptions} required error={errors?.loai_nha?.message} />

            {/* Hidden field for validation */}
            <View style={{height: 0, overflow: 'hidden'}}>
              <TextField control={control} name="loai_nha" rules={houseInsuranceFormValidation.loai_nha as any} />
            </View>

            <TextField
              control={control}
              name="md_sd"
              label="Mục đích sử dụng"
              placeholder="Chọn mục đích sử dụng"
              required
              editable={false}
              rightIconType="dropdown"
              error={errors?.md_sd?.message}
              rules={houseInsuranceFormValidation.md_sd as any}
              inputContainerStyle={errors?.md_sd ? {borderColor: colors.danger, borderWidth: 1} : undefined}
              labelStyle={errors?.md_sd && {color: colors.danger}}
            />

            <TouchableOpacity onPress={() => setShowProvinceModal(true)} activeOpacity={1}>
              <View pointerEvents="none">
                <TextField
                  control={control}
                  name="tinh_thanh_ten"
                  label="Tỉnh/thành"
                  placeholder="Chọn tỉnh/thành"
                  required
                  editable={false}
                  rightIconType="dropdown"
                  error={errors?.tinh_thanh_ten?.message}
                  rules={houseInsuranceFormValidation.tinh_thanh_ten as any}
                  inputContainerStyle={errors?.tinh_thanh_ten ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                  labelStyle={errors?.tinh_thanh_ten && {color: colors.danger}}
                />
              </View>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => setShowWardModal(true)} activeOpacity={1}>
              <View pointerEvents="none">
                <TextField
                  control={control}
                  name="phuong_xa_ten"
                  label="Phường/xã"
                  placeholder="Chọn phường/xã"
                  required
                  editable={false}
                  rightIconType="dropdown"
                  error={errors?.phuong_xa_ten?.message}
                  rules={houseInsuranceFormValidation.phuong_xa_ten as any}
                  inputContainerStyle={errors?.phuong_xa_ten ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                  labelStyle={errors?.phuong_xa_ten && {color: colors.danger}}
                />
              </View>
            </TouchableOpacity>

            <TextField control={control} name="dia_chi" label="Địa chỉ" placeholder="Nhập địa chỉ" required error={errors?.dia_chi?.message} rules={houseInsuranceFormValidation.dia_chi as any} />

            <TextField
              control={control}
              name="so_tang"
              label="Số tầng"
              placeholder="Nhập số tầng"
              required
              keyboardType="numeric"
              error={errors?.so_tang?.message}
              rules={houseInsuranceFormValidation.so_tang as any}
            />

            <TextField
              control={control}
              name="dien_tich"
              label="Diện tích (m2)"
              placeholder="Nhập diện tích"
              required
              keyboardType="numeric"
              error={errors?.dien_tich?.message}
              rules={houseInsuranceFormValidation.dien_tich as any}
            />

            <TextField
              control={control}
              name="nam_sd"
              label="Năm sử dụng"
              placeholder="Nhập năm sử dụng"
              required
              keyboardType="numeric"
              error={errors?.nam_sd?.message}
              rules={houseInsuranceFormValidation.nam_sd as any}
            />

            <TextField
              control={control}
              name="hinh_thuc_chu_sh"
              label="Hình thức chủ sở hữu"
              placeholder="Chọn hình thức chủ sở hữu"
              required
              editable={false}
              rightIconType="dropdown"
              error={errors?.hinh_thuc_chu_sh?.message}
              rules={houseInsuranceFormValidation.hinh_thuc_chu_sh as any}
              inputContainerStyle={errors?.hinh_thuc_chu_sh ? {borderColor: colors.danger, borderWidth: 1} : undefined}
              labelStyle={errors?.hinh_thuc_chu_sh && {color: colors.danger}}
            />
          </Card>

          {/* Thông tin bảo hiểm */}
          <Card title="Thông tin bảo hiểm">
            <TextField
              control={control}
              name="so_tien_bh"
              label="Số tiền bảo hiểm ngôi nhà/căn hộ"
              placeholder="Nhập số tiền bảo hiểm"
              required
              keyboardType="numeric"
              isCurrency
              error={errors?.so_tien_bh?.message}
              rules={houseInsuranceFormValidation.so_tien_bh as any}
            />

            <TextField
              control={control}
              name="thoi_han_bh"
              label="Thời hạn bảo hiểm"
              placeholder="Chọn thời hạn bảo hiểm"
              required
              editable={false}
              rightIconType="dropdown"
              error={errors?.thoi_han_bh?.message}
              rules={houseInsuranceFormValidation.thoi_han_bh as any}
              inputContainerStyle={errors?.thoi_han_bh ? {borderColor: colors.danger, borderWidth: 1} : undefined}
              labelStyle={errors?.thoi_han_bh && {color: colors.danger}}
            />

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <TextField
                  control={control}
                  name="gio_bd"
                  label="Giờ bắt đầu"
                  placeholder="Chọn giờ"
                  required
                  editable={false}
                  rightIconType="dropdown"
                  error={errors?.gio_bd?.message}
                  rules={houseInsuranceFormValidation.gio_bd as any}
                  inputContainerStyle={errors?.gio_bd ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                  labelStyle={errors?.gio_bd && {color: colors.danger}}
                />
              </View>
              <View style={styles.halfWidth}>
                <DateTimePickerComponent control={control} name="ngay_hl" mode="date" label="Ngày bắt đầu" placeholder="Chọn ngày" required rules={houseInsuranceFormValidation.ngay_bd as any} />
              </View>
            </View>

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <TextField
                  control={control}
                  name="gio_kt"
                  label="Giờ kết thúc"
                  placeholder="Chọn giờ"
                  required
                  editable={false}
                  rightIconType="dropdown"
                  error={errors?.gio_kt?.message}
                  rules={houseInsuranceFormValidation.gio_kt as any}
                  inputContainerStyle={errors?.gio_kt ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                  labelStyle={errors?.gio_kt && {color: colors.danger}}
                />
              </View>
              <View style={styles.halfWidth}>
                <DateTimePickerComponent
                  control={control}
                  name="ngay_kt"
                  label="Ngày kết thúc"
                  placeholder="Chọn ngày"
                  required
                  error={errors?.ngay_kt?.message}
                  rules={houseInsuranceFormValidation.ngay_kt as any}
                  inputContainerStyle={errors?.ngay_kt ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                  labelStyle={errors?.ngay_kt && {color: colors.danger}}
                />
              </View>
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Province Selection Modal */}
      <ActionSheetModal
        isVisible={showProvinceModal}
        onClose={() => setShowProvinceModal(false)}
        title="Chọn tỉnh/thành phố"
        subtitle="Vui lòng chọn tỉnh/thành phố"
        options={provinceOptions}
        searchPlaceholder="Tìm kiếm tỉnh/thành..."
        cancelButtonText="Hủy"
        selectedValue={selectedProvince}
      />

      {/* Ward Selection Modal */}
      <ActionSheetModal
        isVisible={showWardModal}
        onClose={() => setShowWardModal(false)}
        title="Chọn phường/xã"
        subtitle="Vui lòng chọn phường/xã"
        options={wardOptions}
        searchPlaceholder="Tìm kiếm phường/xã..."
        cancelButtonText="Hủy"
        selectedValue={useWatch({control, name: 'phuong_xa'})}
      />
    </ScreenComponent>
  );
}
