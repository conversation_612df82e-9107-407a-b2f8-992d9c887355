import React, {memo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Card, CustomTouchableOpacity} from '@components/common';
import {colors, spacing, typography} from '@constants/theme';
import {formatCurrencyPlain} from '@utils/currencyFormatter';
import {DonBaoHiemNTN} from '../hooks/useDanhSachDon';
import {MAIN_SCREENS} from '@navigation/routes';

interface DonCardProps {
  item: DonBaoHiemNTN;
  navigation: any;
}

const InfoRow = memo(({title, value, valueStyle}: {title: string; value: string; valueStyle?: object}) => {
  const isEmpty = !value || value === '' || value === null || value === undefined;
  return (
    <View style={styles.cardRow}>
      <Text style={styles.label}>{title}</Text>
      <Text style={[styles.value, valueStyle, isEmpty ? {color: colors.warning} : null]}>{isEmpty ? 'Chưa xác định' : value}</Text>
    </View>
  );
});

InfoRow.displayName = 'InfoRow';

export const DonCard = memo<DonCardProps>(({item, navigation}) => {
  const statusColor = item.trang_thai === 'D' ? colors.success : colors.warning;

  const handlePress = () => {
    navigation.navigate(MAIN_SCREENS.THONG_TIN_DON_NTN, {soId: item.so_id});
  };

  return (
    <CustomTouchableOpacity onPress={handlePress} activeOpacity={0.7}>
      <Card style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.soHD}>{item.so_hd}</Text>
        </View>
        <InfoRow title="Công ty bảo hiểm" value={item.ten_doi_tac_ql} />
        <InfoRow title="Chủ sở hữu" value={item.ten_kh} />
        <InfoRow title="Địa chỉ" value={item.dia_chi_kh} />
        <InfoRow title="Hiệu lực hợp đồng" value={item.hieu_luc} />
        <InfoRow title="Phí bảo hiểm" value={`${formatCurrencyPlain(item.tong_phi)}đ`} valueStyle={styles.phiBH} />
        <InfoRow title="Trạng thái" value={item.trang_thai_ten} valueStyle={{...styles.statusText, color: statusColor}} />
      </Card>
    </CustomTouchableOpacity>
  );
});

DonCard.displayName = 'DonCard';

const styles = StyleSheet.create({
  card: {
    padding: spacing.md,
    gap: spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  soHD: {
    fontSize: 16,
    color: colors.green,
    flex: 1,
    fontFamily: typography.fontFamily.semibold,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontWeight: '600',
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  label: {
    fontSize: 15,
    color: colors.gray[600],
    flex: 1,
    fontFamily: typography.fontFamily.regular,
  },
  value: {
    fontSize: 15,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
    flex: 1,
    textAlign: 'right',
  },
  phiBH: {
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
    fontSize: 15,
  },
});
