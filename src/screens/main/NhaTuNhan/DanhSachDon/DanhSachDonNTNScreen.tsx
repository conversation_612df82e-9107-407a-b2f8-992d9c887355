import React, {useCallback, useEffect, useRef, useState} from 'react';
import {FlatList, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {Icon, Loading, ScreenComponent, TextField} from '@components/common';
import {colors} from '@constants/theme';
import {styles} from './Styles';
import {DonBaoHiemNTN, useDanhSachDon} from './hooks';
import {DonCard} from './components';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';

interface DanhSachDonNTNScreenProps {
  navigation: any;
}

export default function DanhSachDonNTNScreen({navigation}: DanhSachDonNTNScreenProps) {
  const {danhSachDon, loadMoreState, fetchDanhSachDon} = useDanhSachDon();

  const [searchValue, setSearchValue] = useState('');
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [needsRefresh, setNeedsRefresh] = useState(false);

  const hasSearchedBefore = useRef(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const handleSearch = useCallback(() => {
    hasSearchedBefore.current = true;
    fetchDanhSachDon(searchValue, false);
  }, [searchValue, fetchDanhSachDon]);

  const handleLoadMore = useCallback(() => {
    if (!loadMoreState.isLoadingMore && loadMoreState.hasMore && !loadMoreState.isLoading) {
      fetchDanhSachDon(searchValue, true);
    }
  }, [loadMoreState, searchValue, fetchDanhSachDon]);

  const handleRefresh = useCallback(() => {
    setIsPullToRefresh(true);
    fetchDanhSachDon(searchValue, false).finally(() => {
      setIsPullToRefresh(false);
    });
  }, [searchValue, fetchDanhSachDon]);

  const handleNavigateToDetail = useCallback((soId: number) => {
    setNeedsRefresh(true);
    logger.log('Navigate to detail:', soId);
  }, []);

  const handleCreateNew = useCallback(() => {
    setNeedsRefresh(true);
    logger.log('Navigate to create new');
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_NDBH_NTN, {
      mode: 'create',
    });
  }, []);

  useEffect(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (searchValue !== '') {
      hasSearchedBefore.current = true;
      return;
    }

    if (searchValue === '' && hasSearchedBefore.current) {
      debounceTimerRef.current = setTimeout(() => {
        fetchDanhSachDon('', false);
        hasSearchedBefore.current = false;
      }, 300);
    }

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [searchValue]);

  useEffect(() => {
    fetchDanhSachDon('', false).finally(() => {
      setTimeout(() => setIsInitialMount(false), 1000);
    });
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (!isInitialMount && needsRefresh) {
        fetchDanhSachDon(searchValue, false);
        setNeedsRefresh(false);
      }
    }, [isInitialMount, needsRefresh, searchValue, fetchDanhSachDon]),
  );

  const renderItem = useCallback(({item}: {item: DonBaoHiemNTN}) => <DonCard item={item} navigation={navigation} />, [navigation]);

  const renderEmpty = useCallback(() => {
    if (loadMoreState.isLoading) return null;

    return (
      <View style={styles.emptyContainer}>
        <Icon name="DocumentText" size={64} color={colors.gray[400]} variant="Bulk" />
        <Text style={styles.emptyText}>{searchValue ? 'Không tìm thấy đơn bảo hiểm' : 'Chưa có đơn bảo hiểm nào'}</Text>
        <Text style={styles.emptySubText}>
          {searchValue ? 'Không có đơn bảo hiểm nào phù hợp với từ khóa tìm kiếm của bạn' : 'Chưa có đơn bảo hiểm nhà tư nhân nào trong hệ thống. Hãy thêm đơn bảo hiểm mới.'}
        </Text>
      </View>
    );
  }, [searchValue, loadMoreState.isLoading]);

  const renderFooter = useCallback(() => {
    if (loadMoreState.isLoadingMore) {
      return (
        <View style={styles.footerLoader}>
          <Loading size="small" message="Đang tải thêm" />
        </View>
      );
    }

    if (!loadMoreState.hasMore && danhSachDon.length > 0) {
      return (
        <View style={styles.endDataContainer}>
          <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
          <Text style={styles.endDataText}>Bạn đã xem tất cả {danhSachDon.length} đơn bảo hiểm</Text>
        </View>
      );
    }

    return null;
  }, [loadMoreState, danhSachDon.length]);

  const keyExtractor = useCallback((item: DonBaoHiemNTN, index: number) => `${item.so_id}-${index}`, []);

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Danh sách đơn bảo hiểm"
      showBackButton
      onPressBack={() => navigation.goBack()}
      dialogLoading={loadMoreState.isLoading && !loadMoreState.isLoadingMore}>
      <View style={styles.searchContainer}>
        <TextField
          placeholder="Nhập thông tin tìm kiếm..."
          value={searchValue}
          onChangeText={setSearchValue}
          showPlaceholderWhenEmpty
          rightIconType="search"
          onRightIconPress={handleSearch}
          onSubmitEditing={handleSearch}
          autoCorrect={false}
          autoCapitalize="none"
        />
      </View>

      <FlatList
        data={danhSachDon}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={danhSachDon.length === 0 ? styles.emptyContainer : styles.listContainer}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={10}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        refreshControl={<RefreshControl refreshing={isPullToRefresh && !loadMoreState.isLoadingMore} onRefresh={handleRefresh} tintColor={colors.primary} colors={[colors.primary]} />}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
      />

      <TouchableOpacity style={styles.fabButton} activeOpacity={0.8} onPress={handleCreateNew}>
        <Icon name="Add" size={32} color={colors.white} variant="Linear" />
      </TouchableOpacity>
    </ScreenComponent>
  );
}
