import {useCallback, useRef, useState} from 'react';
import {createToastHelpers, useToast} from '@components/common';
import {ACTION_CODE} from '@constants/axios';
import {getCommonExecute} from '@services/endpoints';
import {PAGE_SIZE} from '@commons/Constant';

export interface DonBaoHiemNTN {
  so_id: number;
  so_hd: string;
  ma_doi_tac_ql: string;
  ten_doi_tac_ql: string;
  ten_kh: string;
  dia_chi_kh: string;
  hieu_luc: string;
  tong_phi: number;
  trang_thai: string;
  trang_thai_ten: string;
  ngay_hl?: number;
  ngay_kt?: number;
  sl_doi_tuong?: number;
}

interface LoadMoreState {
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

export const useDanhSachDon = () => {
  const [danhSachDon, setDanhSachDon] = useState<DonBaoHiemNTN[]>([]);
  const [loadMoreState, setLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: PAGE_SIZE,
  });

  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastSearchValueRef = useRef<string>('');

  const fetchDanhSachDon = useCallback(
    async (searchValue: string = '', isLoadMore: boolean = false) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      lastSearchValueRef.current = searchValue;

      abortControllerRef.current = new AbortController();

      try {
        let currentPage: number = 1;
        let pageSize: number = PAGE_SIZE;

        setLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize;
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        const params = {
          nd_tim: searchValue || '',
          nv: '',
          trang: currentPage,
          so_dong: pageSize,
          actionCode: ACTION_CODE.GET_DS_DON_BH,
        };

        const response = await getCommonExecute(params);

        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        if (response?.data) {
          const listDon: DonBaoHiemNTN[] = response.data.data || [];
          const totalItems = response.data.tong_so_dong || listDon.length;

          setDanhSachDon(prev => (isLoadMore ? [...prev, ...listDon] : listDon));

          setLoadMoreState(prev => ({
            ...prev,
            isLoading: false,
            isLoadingMore: false,
            currentPage: currentPage,
            totalItems: totalItems,
            hasMore: currentPage * pageSize < totalItems,
          }));
        } else {
          throw new Error(response?.error || 'Lỗi tải danh sách đơn bảo hiểm');
        }
      } catch (error: any) {
        // Don't show error if request was aborted
        if (error?.name === 'AbortError') {
          return;
        }

        console.error('fetchDanhSachDon error:', error);
        toast.error('Lỗi tải danh sách đơn bảo hiểm');

        setLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      }
    },
    [toast],
  );

  const resetData = useCallback(() => {
    setDanhSachDon([]);
    setLoadMoreState({
      isLoading: false,
      isLoadingMore: false,
      hasMore: true,
      currentPage: 1,
      totalItems: 0,
      pageSize: PAGE_SIZE,
    });
  }, []);

  return {
    danhSachDon,
    loadMoreState,
    fetchDanhSachDon,
    resetData,
  };
};
