import { colors, spacing, typography } from '@constants/theme';
import { StyleSheet, Dimensions } from 'react-native';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  searchContainer: {
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
  },
  listContainer: {
    paddingHorizontal: spacing.sm,
    paddingBottom: spacing.xl * 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    minHeight: SCREEN_HEIGHT * 0.6,
  },
  emptyText: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    color: colors.gray[700],
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: colors.gray[500],
    marginTop: spacing.xs,
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: typography.fontFamily.regular,
  },
  footerLoader: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
  endDataContainer: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
  endDataTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[600],
    fontFamily: typography.fontFamily.semibold,
  },
  endDataText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: spacing.xs,
  },
  fabButton: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.green,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.dark,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});
