import { borderRadius, colors, spacing, typography } from "@constants/theme";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.sm,
    backgroundColor: colors.light
  },
  tabRow: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginHorizontal: spacing.sm,
    marginBottom: spacing.sm
  },
  tabContainer: {
    flex: 1,
    paddingVertical: spacing.sm + 5,
    alignItems: 'center',
    borderRadius: borderRadius.xl,
  },
  tabWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  tabSelected: {
    backgroundColor: colors.green,
    borderColor: colors.green,
  },
  title: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
  titleSelected: {
    color: colors.white,
  },
})