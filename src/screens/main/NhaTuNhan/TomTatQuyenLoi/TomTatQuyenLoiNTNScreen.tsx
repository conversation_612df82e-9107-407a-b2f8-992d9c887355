import {View, Text, TouchableOpacity} from 'react-native';
import React, {useState} from 'react';
import {Icon, ScreenComponent} from '@components/common';
import {styles} from './Styles';
import {colors} from '@constants/theme';

type TabProp = 'C' | 'T';

export default function TomTatQuyenLoiNTNScreen({navigation, route}: any) {
  const [tab, setTab] = useState<TabProp>('C');

  const renderTopTab = ({title, selected, onPress}: any) => {
    return (
      <TouchableOpacity onPress={onPress} style={[styles.tabContainer, selected && styles.tabSelected]}>
        <Text style={[styles.title, selected && styles.titleSelected]}>{title}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Tóm tắt chi tiết quyền lợi"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}>
      <View style={styles.tabRow}>
        {renderTopTab({
          title: 'Quyền lợi bảo hiểm',
          selected: tab === 'C',
          onPress: () => setTab('C'),
        })}
        {renderTopTab({
          title: 'Chi tiết chương trình',
          selected: tab === 'T',
          onPress: () => setTab('T'),
        })}
      </View>
      {tab === 'C' ? <Text>Quyền lợi bảo hiểm</Text> : <Text>Chi tiết chương trình</Text>}
    </ScreenComponent>
  );
}
