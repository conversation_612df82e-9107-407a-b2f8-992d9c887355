import { borderRadius, colors, shadows, spacing, typography } from '@constants/theme';
import { Dimensions, StyleSheet } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const CARD_WIDTH = Math.min(SCREEN_WIDTH - spacing.sm) / 2.6;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    paddingHorizontal: spacing.sm,
  },

  // Section Header
  sectionCard: {
    marginBottom: spacing.md,
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },

  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  sectionSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.regular,
  },

  cardTitle: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },

  card: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    backgroundColor: '#E3EAD8',
    borderWidth: 1,
    borderColor: colors.green,
  },

  // Date picker styles
  dateRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },

  dateField: {
    flex: 1,
  },

  // Package Cards Scroll
  packagesScroll: {
    marginHorizontal: -spacing.md,
  },

  packagesScrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.md,
    gap: spacing.sm
  },

  packageCard: {
    width: CARD_WIDTH,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[400],
  },

  selectedPackageCard: {
    borderColor: colors.green,
    borderWidth: 2,
    borderRadius: borderRadius.xl,
  },

  firstPackageCard: {
    // No longer needed since paddingLeft handles it
  },

  gradientWrapper: {
    flex: 1,
    paddingHorizontal: spacing.sm,
    marginHorizontal: -spacing.sm,
    borderRadius: borderRadius.xl - 2,
  },

  packageHeader: {
    alignItems: 'flex-start',
  },

  logoContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginTop: spacing.sm,
    paddingHorizontal: spacing.xs
  },

  logoPlaceholderOther: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[300],
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginVertical: spacing.xs,
  },

  textPlaceholder: {
    color: colors.gray[700],
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
  },

  micLogo: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },

  packageTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
    marginTop: spacing.sm
  },

  packageDescription: {
    fontSize: typography.fontSize.xs,
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
    lineHeight: typography.fontSize.sm * 1.2,
  },

  originalPrice: {
    fontSize: typography.fontSize.sm + 2,
    color: colors.green,
    marginBottom: spacing.xs / 2,
    fontFamily: typography.fontFamily.semibold,
  },

  packagePrice: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginTop: spacing.xs,
    marginBottom: spacing.sm,
  },

  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    marginRight: -4,
  },

  // Selected Package Card
  selectedPackageContainer: {
    borderRadius: borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.green,
  },

  selectedPackageGradient: {
    flex: 1,
  },

  selectedPackageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    padding: spacing.sm,
  },

  selectedLogoContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    shadowColor: colors.white,
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },

  selectedLogo: {
    width: 40,
    height: 40,
  },

  logoPlaceholder: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[300],
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
  },

  selectedPackageInfo: {
    flex: 1,
  },

  selectedPackageName: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
  },

  selectedTotalBenefit: {
    fontSize: typography.fontSize.xs,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
    fontFamily: typography.fontFamily.regular,
  },

  selectedBenefitAmount: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
  },

  selectedPremiumContainer: {
    alignItems: 'flex-end',
  },

  selectedPremiumLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.dark,
    marginBottom: spacing.xs / 2,
    fontFamily: typography.fontFamily.regular,
  },

  selectedPremiumAmount: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  // Benefits Section
  headerColumn1: {
    flex: 3,
    paddingRight: spacing.sm,
  },

  headerColumn2: {
    flex: 2,
    textAlign: 'center',
    paddingRight: spacing.sm,
  },

  headerColumn3: {
    flex: 1,
    textAlign: 'center',
  },

  benefitRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    minHeight: 44,
    paddingHorizontal: 0,
  },

  benefitLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    lineHeight: typography.fontSize.base * 1.4,
    fontFamily: typography.fontFamily.regular,
  },

  benefitPrice: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
    lineHeight: typography.fontSize.base * 1.4,
  },

  benefitAmount: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
  },

  checkboxColumn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Footer
  footerContainer: {},

  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
    marginHorizontal: spacing.sm,
  },

  totalMainRow: {
    marginBottom: spacing.lg,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },

  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  totalSubLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },

  totalPrice: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },

  totalSubPrice: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },

  footerButtonContainer: {
    flexDirection: 'row',
    marginTop: spacing.md,
  },

  secondaryButton: {
    flex: 1,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.green,
    borderRadius: 14,
    paddingVertical: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },

  secondaryButtonText: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },

  primaryButton: {
    flex: 1,
    backgroundColor: colors.green,
  },

  // Main Benefits Chips
  mainBenefitsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    gap: spacing.xs + 2
  },

  mainBenefitChip: {
    width: '49%',
    marginBottom: spacing.xs - 3,
  },

  mainBenefitChipInner: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.light,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.green,
    flex: 1,
  },

  mainBenefitText: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
    flex: 1,
    marginLeft: spacing.xs,
  },
});
