import {View, Text, TouchableOpacity, Image, Pressable} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {Button, Card, createToastHelpers, CustomTouchableOpacity, Icon, ScreenComponent, useToast} from '@components/common';
import {styles} from './Styles';
import {ScrollView} from 'react-native-gesture-handler';
import {colors, spacing} from '@constants/theme';
import R from '@assets/R';
import LinearGradient from 'react-native-linear-gradient';
import Svg, {Path} from 'react-native-svg';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';
import {getCommonExecute} from '@services/endpoints';

interface GoiBaoHiem {
  id: string;
  tenGoi: string;
  quyenLoi: string;
  giaTriQuyenLoi: string;
  phiBaoHiem: string;
  nhanDang?: string;
  mauNen: string;
}

interface QuyenLoiBoSung {
  id: string;
  ten: string;
  gia: string;
  daChon: boolean;
}

const DATA_GOI_BAO_HIEM: GoiBaoHiem[] = [
  {
    id: '1',
    tenGoi: '<PERSON><PERSON><PERSON>',
    quyenLoi: 'Tổng quyền lợi lên tới',
    giaTriQuyenLoi: '270 triệu đồng',
    phiBaoHiem: '1.234.000đ',
    nhanDang: 'Đang chọn',
    mauNen: '#E3EAD8',
  },
  {
    id: '2',
    tenGoi: 'Gói Bạc',
    quyenLoi: 'Tổng quyền lợi lên tới',
    giaTriQuyenLoi: '370 triệu đồng',
    phiBaoHiem: '2.234.000đ',
    nhanDang: 'Mua nhiều',
    mauNen: colors.white,
  },
  {
    id: '3',
    tenGoi: 'Gói Vàng',
    quyenLoi: 'Tổng quyền lợi lên tới',
    giaTriQuyenLoi: '470 triệu đồng',
    phiBaoHiem: '3.234.000đ',
    mauNen: colors.white,
  },
];

const DATA_QUYEN_LOI_CHINH = [
  {id: '1', ten: 'Hỏa hoạn, nổ, sét đánh'},
  {id: '2', ten: 'Giông bão, lũ lụt'},
  {id: '3', ten: 'Vỡ hoặc tràn nước'},
];

const DATA_QUYEN_LOI_BO_SUNG: QuyenLoiBoSung[] = [
  {id: '1', ten: 'Bảo hiểm khung nhà', gia: '1.500.000đ', daChon: false},
  {id: '2', ten: 'Bảo hiểm tài sản trong nhà', gia: '1.500.000đ', daChon: false},
];

// Utility functions
const chuyenGiaThanhSo = (giaString: string): number => {
  return parseFloat(giaString.replace(/\./g, '').replace('đ', ''));
};

const dinhDangGia = (gia: number): string => {
  return gia.toLocaleString('vi-VN') + 'đ';
};

// Pentagon Badge Component (pointed bottom)
const HuyHieuNguGiac = ({text, backgroundColor, textColor}: {text: string; backgroundColor: string; textColor: string}) => {
  return (
    <View style={{position: 'relative', width: 70, height: 26}}>
      <Svg height="26" width="70" style={{position: 'absolute', top: 0, left: 0}}>
        {/* Pentagon shape: top-left corner, top-right corner, bottom-right, bottom-center point, bottom-left */}
        <Path d="M4,0 L66,0 L66,18 L35,26 L4,18 Z" fill={backgroundColor} />
      </Svg>
      <View style={{alignItems: 'center', justifyContent: 'center', flex: 1, paddingBottom: 4}}>
        <Text style={{color: textColor, fontSize: 11}}>{text}</Text>
      </View>
    </View>
  );
};

export default function ChonGoiBaoHiemNTNScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const {thongTinNDBH} = route?.params || {};

  const [goiDangChon, setGoiDangChon] = useState<GoiBaoHiem>(DATA_GOI_BAO_HIEM[0]);
  const [cacQuyenLoiBoSung, setCacQuyenLoiBoSung] = useState<QuyenLoiBoSung[]>(DATA_QUYEN_LOI_BO_SUNG);

  // Handlers
  const handleChonGoi = useCallback((goi: GoiBaoHiem) => {
    setGoiDangChon(goi);
  }, []);

  const handleChonQuyenLoiBoSung = useCallback((id: string) => {
    setCacQuyenLoiBoSung(prev => prev.map(item => (item.id === id ? {...item, daChon: !item.daChon} : item)));
  }, []);

  const handleXemTomTatQuyenLoi = useCallback(() => {
    // TODO: Navigate to summary
    NavigationUtil.push(MAIN_SCREENS.TOM_TAT_QUYEN_LOI_NTN);
  }, []);

  const onSubmit = useCallback(async () => {
    // TODO: Submit form
    try {
      const params = {};
      logger.log('🚀 ~ ChonGoiBaoHiemNTNScreen ~ params:', params);
      const response = await getCommonExecute(params);
      logger.log('🚀 ~ ChonGoiBaoHiemNTNScreen ~ response:', response);
      if (response?.data) {
        NavigationUtil.push(MAIN_SCREENS.THONG_TIN_MUA_NTN);
      } else {
        logger.log('Luu goi bao hiem that bai: ', response?.error);
      }
    } catch (error) {
      logger.log('🚀 ~ ChonGoiBaoHiemNTNScreen ~ error:', error);
    }
  }, []);

  const handleXemTatCaGoi = useCallback(() => {
    NavigationUtil.push(MAIN_SCREENS.DANH_SACH_GOI_BH_NTN);
  }, []);

  // Memoized calculations
  const tongTien = useMemo(() => {
    const phiChinh = chuyenGiaThanhSo(goiDangChon.phiBaoHiem);
    const tongPhiBoSung = cacQuyenLoiBoSung.filter(item => item.daChon).reduce((tong, item) => tong + chuyenGiaThanhSo(item.gia), 0);

    return {
      phiChinh,
      tongPhiBoSung,
      tongPhiBaoHiem: phiChinh + tongPhiBoSung,
    };
  }, [goiDangChon, cacQuyenLoiBoSung]);

  // Render functions
  const renderCardGoiBaoHiem = useCallback(
    (goi: GoiBaoHiem, index: number) => (
      <TouchableOpacity key={goi.id} style={[styles.packageCard, goiDangChon.id === goi.id && styles.selectedPackageCard, index === 0 && styles.firstPackageCard]} onPress={() => handleChonGoi(goi)}>
        {goiDangChon.id === goi.id ? (
          <LinearGradient colors={['#D9EAC5', '#96BF49']} start={{x: 0.5, y: 0}} end={{x: 0.5, y: 1}} style={styles.gradientWrapper}>
            <View style={styles.packageHeader}>
              <View style={styles.logoContainer}>
                <Image source={R.images.img_mic} style={styles.micLogo} resizeMode="contain" />
              </View>

              {goi.nhanDang === 'Mua nhiều' && (
                <View style={{position: 'absolute', top: 0, right: 0, marginRight: -4}}>
                  <HuyHieuNguGiac text="Mua nhiều" backgroundColor={colors.yellow} textColor={colors.danger} />
                </View>
              )}
            </View>
            <Text style={styles.packageTitle}>{goi.tenGoi}</Text>
            <Text style={[styles.packageDescription, {color: colors.danger}]}>{goi.quyenLoi}</Text>
            <Text style={[styles.originalPrice, {color: colors.danger}]}>{goi.giaTriQuyenLoi}</Text>
            <Text style={styles.packagePrice}>{goi.phiBaoHiem}</Text>
          </LinearGradient>
        ) : (
          <View>
            <View style={styles.packageHeader}>
              <View style={styles.logoContainer}>
                <Image source={R.images.img_mic} style={styles.micLogo} resizeMode="contain" />
              </View>

              {goi.nhanDang === 'Mua nhiều' && (
                <View style={{position: 'absolute', top: 0, right: 0, marginRight: -4}}>
                  <HuyHieuNguGiac text="Mua nhiều" backgroundColor={colors.yellow} textColor={colors.danger} />
                </View>
              )}
            </View>
            <Text style={styles.packageTitle}>{goi.tenGoi}</Text>
            <Text style={styles.packageDescription}>{goi.quyenLoi}</Text>
            <Text style={styles.originalPrice}>{goi.giaTriQuyenLoi}</Text>
            <Text style={styles.packagePrice}>{goi.phiBaoHiem}</Text>
          </View>
        )}
      </TouchableOpacity>
    ),
    [goiDangChon.id, handleChonGoi],
  );

  const renderQuyenLoiBoSung = useCallback(
    (quyenLoi: QuyenLoiBoSung, index: number) => (
      <View key={quyenLoi.id} style={styles.benefitRow}>
        <View style={{flex: 3, paddingRight: spacing.sm}}>
          <Text style={styles.benefitLabel}>
            {index + 1}. {quyenLoi.ten}
          </Text>
        </View>
        <View style={{flex: 2, alignItems: 'center', paddingRight: spacing.sm}}>
          <Text style={[styles.benefitPrice, {marginTop: 1}]}>{quyenLoi.gia}</Text>
        </View>
        <View style={{flex: 1, alignItems: 'center', marginRight: -3}}>
          <TouchableOpacity
            onPress={() => handleChonQuyenLoiBoSung(quyenLoi.id)}
            style={{
              width: 22,
              height: 22,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: quyenLoi.daChon ? colors.green : colors.gray[400],
              backgroundColor: quyenLoi.daChon ? colors.green : colors.white,
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: -3,
            }}
            activeOpacity={0.7}>
            {quyenLoi.daChon && (
              <View
                style={{
                  width: 8,
                  height: 4,
                  borderLeftWidth: 2,
                  borderBottomWidth: 2,
                  borderColor: colors.white,
                  transform: [{rotate: '-45deg'}],
                  marginTop: -1,
                }}
              />
            )}
          </TouchableOpacity>
        </View>
      </View>
    ),
    [handleChonQuyenLoiBoSung],
  );

  const renderQuyenLoiChinh = useCallback(
    (quyenLoi: {id: string; ten: string}) => (
      <View key={quyenLoi.id} style={styles.mainBenefitChip}>
        <View style={styles.mainBenefitChipInner}>
          <Icon name="TickCircle" size={16} color={colors.green} />
          <Text style={styles.mainBenefitText}>{quyenLoi.ten}</Text>
        </View>
      </View>
    ),
    [],
  );

  const renderFooter = useMemo(
    () => (
      <View style={styles.footerContainer}>
        <View style={styles.totalRow}>
          <Text style={styles.totalSubLabel}>Phí bảo hiểm chính</Text>
          <Text style={styles.totalSubPrice}>{goiDangChon.phiBaoHiem}</Text>
        </View>
        <View style={styles.totalRow}>
          <Text style={styles.totalSubLabel}>Quyền lợi bổ sung</Text>
          <Text style={styles.totalSubPrice}>{dinhDangGia(tongTien.tongPhiBoSung)}</Text>
        </View>
        <View style={[styles.totalRow, styles.totalMainRow]}>
          <Text style={styles.totalLabel}>Tổng phí bảo hiểm</Text>
          <Text style={styles.totalPrice}>{dinhDangGia(tongTien.tongPhiBaoHiem)}</Text>
        </View>
        <Button title="Chọn gói" onPress={onSubmit} />
      </View>
    ),
    [goiDangChon.phiBaoHiem, tongTien, onSubmit],
  );

  return (
    <ScreenComponent bodyStyle={styles.container} showHeader headerTitle="Chọn gói bảo hiểm" showBackButton onPressBack={navigation.goBack} showFooter footer={renderFooter}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Các gói phù hợp */}
        <Card>
          <View style={styles.sectionHeader}>
            <Text style={styles.cardTitle}>Các gói phù hợp</Text>
            <TouchableOpacity onPress={handleXemTatCaGoi}>
              <Text style={styles.sectionSubtitle}>Xem tất cả</Text>
            </TouchableOpacity>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.packagesScroll} contentContainerStyle={styles.packagesScrollContent}>
            {DATA_GOI_BAO_HIEM.map(renderCardGoiBaoHiem)}
          </ScrollView>
        </Card>

        {/* Gói đang chọn */}
        <Card>
          <View style={styles.sectionHeader}>
            <Text style={styles.cardTitle}>Gói đang chọn</Text>
          </View>
          <View style={styles.selectedPackageContainer}>
            <LinearGradient colors={['#D9EAC5', '#96BF49']} start={{x: 0, y: 0.5}} end={{x: 1, y: 0.5}} style={styles.selectedPackageGradient}>
              <View style={styles.selectedPackageContent}>
                {/* Logo */}
                <View style={styles.selectedLogoContainer}>
                  <Image source={R.images.img_mic} style={styles.selectedLogo} resizeMode="contain" />
                </View>

                {/* Info */}
                <View style={styles.selectedPackageInfo}>
                  <Text style={styles.selectedPackageName}>{goiDangChon.tenGoi}</Text>
                  <Text style={styles.selectedTotalBenefit}>{goiDangChon.quyenLoi}</Text>
                  <Text style={styles.selectedBenefitAmount}>{goiDangChon.giaTriQuyenLoi}</Text>
                </View>

                {/* Premium */}
                <View style={styles.selectedPremiumContainer}>
                  <Text style={styles.selectedPremiumLabel}>Phí bảo hiểm</Text>
                  <Text style={styles.selectedPremiumAmount}>{goiDangChon.phiBaoHiem}</Text>
                </View>
              </View>
            </LinearGradient>
          </View>
        </Card>

        {/* Tóm tắt quyền lợi */}
        <CustomTouchableOpacity onPress={handleXemTomTatQuyenLoi}>
          <Card style={styles.card}>
            <Icon name={'Book'} color={colors.green} size={20} variant={'Bold'} />
            <Text style={styles.cardTitle}>Tóm tắt quyền lợi</Text>
          </Card>
        </CustomTouchableOpacity>

        {/* Quyền lợi bổ sung */}
        <Card>
          <View style={styles.sectionHeader}>
            <Text style={[styles.cardTitle, {flex: 3}]}>Quyền lợi bổ sung</Text>
            <Text style={[styles.cardTitle, {flex: 2, textAlign: 'center'}]}>Phí bảo hiểm</Text>
            <Text style={[styles.cardTitle, {flex: 1, textAlign: 'center'}]}>Chọn</Text>
          </View>
          {cacQuyenLoiBoSung.map(renderQuyenLoiBoSung)}
        </Card>

        {/* Quyền lợi chính */}
        <Card>
          <View style={styles.sectionHeader}>
            <Text style={styles.cardTitle}>Quyền lợi chính</Text>
          </View>
          <View style={styles.mainBenefitsContainer}>{DATA_QUYEN_LOI_CHINH.map(renderQuyenLoiChinh)}</View>
        </Card>

        <View style={{height: 20}} />
      </ScrollView>
    </ScreenComponent>
  );
}
