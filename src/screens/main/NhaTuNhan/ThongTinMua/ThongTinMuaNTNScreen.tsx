import {View, Text, KeyboardAvoidingView, ScrollView, Platform, Image, TouchableOpacity} from 'react-native';
import React, {useCallback, useMemo, useState} from 'react';
import {Button, Card, createToastHelpers, CustomTouchableOpacity, Radio, ScreenComponent, TextField, useToast} from '@components/common';
import {styles} from './Styles';
import {Controller, useForm} from 'react-hook-form';
import {getCommonExecute} from '@services/endpoints';
import {colors, spacing} from '@constants/theme';
import R from '@assets/R';
import {ThongTinMuaFormValidation} from '@utils/validationSchemas';

interface FormValues {
  ten_kh: string;
  gioi_tinh_kh: string;
  ngay_sinh_kh: Date | string;
  so_cmt_kh: string;
  dia_chi_kh: string;
  dthoai_kh: string;
  email_kh?: string;
  mqh_so_huu: string;
  ngdpt_moi_qhe: string;
  ten_sh: string;
  ngan_hang: string;
  stk: string;
}

const defaultValues: FormValues = {
  ten_kh: '',
  gioi_tinh_kh: 'NAM',
  ngay_sinh_kh: new Date() || '',
  so_cmt_kh: '',
  dia_chi_kh: '',
  dthoai_kh: '',
  email_kh: '',
  mqh_so_huu: '',
  ngdpt_moi_qhe: '',
  ten_sh: '',
  ngan_hang: '',
  stk: '',
};

export default function ThongTinMuaNTNScreen({navigation, route}: any) {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const {thongTinGoiBaoHiem, mode, editData} = route?.params || {};
  const isEditMode = mode === 'edit';

  const [isFrontScannerVisible, setFrontScannerVisible] = useState(false);
  const [isBackScannerVisible, setBackScannerVisible] = useState(false);
  const [frontIdImage, setFrontIdImage] = useState<string | null>();
  const [backIdImage, setBackIdImage] = useState<string | null>();

  const formValues = useMemo(() => {
    if (isEditMode && editData) {
      return {
        ten_kh: editData.ten_kh,
        gioi_tinh_kh: editData.gioi_tinh_kh,
        ngay_sinh_kh: editData.ngay_sinh_kh,
        so_cmt_kh: editData.so_cmt_kh,
        dia_chi_kh: editData.dia_chi_kh,
        dthoai_kh: editData.dthoai_kh,
        email_kh: editData.email_kh,
        mqh_so_huu: editData.mqh_so_huu,
        ngdpt_moi_qhe: editData.ngdpt_moi_qhe,
        ten_sh: editData.ten_sh,
        ngan_hang: editData.ngan_hang,
        stk: editData.stk,
      };
    }
    return defaultValues;
  }, [isEditMode, editData]);

  const {
    control,
    watch,
    formState: {errors, isSubmitting},
    handleSubmit,
  } = useForm<FormValues>({
    defaultValues: formValues,
  });

  // Chụp mặt trước
  const handlePickFrontImage = useCallback(() => {
    setFrontScannerVisible(true);
  }, []);

  // Chụp mặt sau
  const handlePickBackImage = useCallback(() => {
    setBackScannerVisible(true);
  }, []);

  // render cmt
  const renderSinglePhotoUpload = useCallback(() => {
    return (
      <TouchableOpacity onPress={handlePickFrontImage} style={{flexDirection: 'row', flex: 1, alignItems: 'center', gap: spacing.sm}} activeOpacity={0.7}>
        <Image source={frontIdImage ? {uri: frontIdImage} : R.images.img_cccd} style={styles.idCardIcon} resizeMode="contain" />
        <Text style={styles.idCardLabel}>Chụp hoặc tải ảnh căn cước công dân (không bắt buộc)</Text>
      </TouchableOpacity>
    );
  }, [frontIdImage, handlePickFrontImage]);

  const onSubmit = useCallback(async (data: FormValues) => {
    try {
      console.log('🚀 ~ ThongTinMuaNTNScreen ~ formData: ', data);
      const params = {};
      console.log('🚀 ~ ThongTinMuaNTNScreen ~ params:', params);
      const response = await getCommonExecute(params);
      console.log('🚀 ~ ThongTinMuaNTNScreen ~ response:', response);
      if (response?.data) {
        if (isEditMode) navigation.goBack();
        else {
        }
        setTimeout(() => {
          toast.success(isEditMode ? 'Cập nhật thông tin thành công' : 'Thêm mới thông tin thành công');
        }, 100);
      } else {
        console.log('Luu thong tin ben mua that bai: ', response?.error);
      }
    } catch (error) {
      console.log('🚀 ~ ThongTinMuaNTNScreen ~ error:', error);
    }
  }, []);

  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin bên mua"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}
      showFooter
      footer={<Button title={isEditMode ? 'Cập nhật' : 'Tiếp tục'} onPress={handleSubmit(onSubmit)} loading={isSubmitting} />}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 70} enabled={true}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.sm}} keyboardShouldPersistTaps="handled" bounces={false} scrollEventThrottle={16}>
          <Card title="Thông tin người mua">
            {renderSinglePhotoUpload()}
            <View style={styles.form}>
              <TextField
                control={control}
                name="ten_kh"
                required
                label="Họ và tên"
                placeholder="Nhập họ tên người mua"
                rules={ThongTinMuaFormValidation.tenKH as any}
                error={errors?.ten_kh?.message}
              />
              <Controller
                control={control}
                name="gioi_tinh_kh"
                rules={ThongTinMuaFormValidation.gioiTinhKH as any}
                render={({field: {value, onChange}}) => (
                  <Radio.Group
                    label="Giới tính"
                    required
                    value={value}
                    onChange={onChange}
                    orientation="horizontal"
                    containerStyle={styles.genderSection}
                    error={(errors as any).gioi_tinh_kh?.message}>
                    <Radio.Button label="Nam" value="NAM" />
                    <Radio.Button label="Nữ" value="NU" />
                  </Radio.Group>
                )}
              />
              <TextField control={control} name="so_cmt_kh" required label="CCCD" placeholder="Nhập cccd người mua" rules={ThongTinMuaFormValidation.cmtKH as any} error={errors?.so_cmt_kh?.message} />
              <TextField
                control={control}
                name="so_cmt_kh"
                required
                label="Địa chỉ"
                placeholder="Nhập địa chỉ người mua"
                rules={ThongTinMuaFormValidation.diaChiKH as any}
                error={errors?.dia_chi_kh?.message}
              />
              <TextField
                control={control}
                name="so_cmt_kh"
                required
                label="Email"
                placeholder="Nhập email người mua"
                rules={ThongTinMuaFormValidation.emailKH as any}
                error={errors?.email_kh?.message}
              />
              <CustomTouchableOpacity>
                <View pointerEvents="none">
                  <TextField
                    control={control}
                    name="mqh_so_huu"
                    required
                    editable={false}
                    label="Mối quan hệ sở hữu"
                    placeholder="Chọn mối quan hệ sở hữu"
                    rightIconType="dropdown"
                    inputContainerStyle={errors.mqh_so_huu ? {borderColor: colors.danger} : undefined}
                    labelStyle={errors.mqh_so_huu ? {color: colors.danger} : undefined}
                    rules={ThongTinMuaFormValidation.mqhSoHuu as any}
                    error={errors?.mqh_so_huu?.message}
                  />
                </View>
              </CustomTouchableOpacity>
            </View>
          </Card>
          <Card title="Thông tin người thụ hưởng">
            <CustomTouchableOpacity>
              <View pointerEvents="none">
                <TextField
                  control={control}
                  name="ngdpt_moi_qhe"
                  required
                  editable={false}
                  label="Mối quan hệ với chủ sở hữu"
                  placeholder="Chọn mối quan hệ với chủ sở hữu"
                  rightIconType="dropdown"
                  inputContainerStyle={errors.ngdpt_moi_qhe ? {borderColor: colors.danger} : undefined}
                  labelStyle={errors.ngdpt_moi_qhe ? {color: colors.danger} : undefined}
                  rules={ThongTinMuaFormValidation.ndbhMoiQuanHe as any}
                  error={errors?.ngdpt_moi_qhe?.message}
                />
              </View>
            </CustomTouchableOpacity>
            <TextField
              control={control}
              name="ten_sh"
              required
              label="Họ và tên"
              placeholder="Nhập họ tên người thụ hưởng"
              rules={ThongTinMuaFormValidation.tenKH as any}
              error={errors?.ten_sh?.message}
            />
            <CustomTouchableOpacity>
              <View pointerEvents="none">
                <TextField
                  control={control}
                  name="ngan_hang"
                  required
                  editable={false}
                  label="Ngân hàng"
                  placeholder="Chọn ngân hàng"
                  rightIconType="dropdown"
                  inputContainerStyle={errors.ngan_hang ? {borderColor: colors.danger} : undefined}
                  labelStyle={errors.ngan_hang ? {color: colors.danger} : undefined}
                  rules={ThongTinMuaFormValidation.nganHang as any}
                  error={errors?.ngan_hang?.message}
                />
              </View>
            </CustomTouchableOpacity>
            <TextField control={control} name="stk" required label="Số tài khoản" placeholder="Nhập số tài khoản" rules={ThongTinMuaFormValidation.stk as any} error={errors?.stk?.message} />
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
