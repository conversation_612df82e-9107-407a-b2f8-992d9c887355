import { colors, spacing, typography } from "@constants/theme";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.sm,
    backgroundColor: colors.light
  },
  idCardIcon: {
    width: 70,
    height: 50,
  },
  idCardLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    marginTop: spacing.sm,
    marginBottom: spacing.sm,
    flex: 1,
    lineHeight: 21,
    fontFamily: typography.fontFamily.regular,
  },
  form: {
    marginTop: spacing.md
  },
  genderSection: {
    marginBottom: spacing.md,
    marginTop: -spacing.xs
  }
})