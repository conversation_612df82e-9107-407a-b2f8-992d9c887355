import {View, Text} from 'react-native';
import React, {useCallback, useState} from 'react';
import {Button, ScreenComponent, TextField} from '@components/common';
import {styles} from './Styles';

export default function DanhSachGoiNTNScreen({navigation, route}: any) {
  const [searchValue, setSearchValue] = useState('');

  const handleTimKiem = useCallback(() => {}, []);

  const handleXemChiTietGoi = useCallback(() => {}, []);

  const handleChonGoi = useCallback(async () => {}, []);
  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Danh sách gói bảo hiểm"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}
      showFooter
      footer={
        <View style={styles.footerContainer}>
          <Button title="Xem chi tiết" onPress={handleXemChiTietGoi} variant="outline" style={styles.secondButton} />
          <Button title="Chọn gói" onPress={handleChonGoi} style={styles.primaryButton} />
        </View>
      }>
      <TextField
        value={searchValue}
        onChangeText={value => setSearchValue(value)}
        showPlaceholderWhenEmpty
        placeholder="Nhập thông tin tìm kiếm..."
        rightIconType="search"
        onRightIconPress={handleTimKiem}
      />
    </ScreenComponent>
  );
}
