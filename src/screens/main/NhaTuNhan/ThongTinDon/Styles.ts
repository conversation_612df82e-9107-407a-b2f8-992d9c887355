import { colors, spacing, typography } from "@constants/theme";
import { Solana } from "iconsax-react-native";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.sm,
    backgroundColor: colors.light
  },
  footerContainer: {
    gap: spacing.sm,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  primaryButton: {
    flex: 1.5,
    marginLeft: 0
  },
  secondButton: {
    flex: 1,
    marginRight: 0
  },
  footerContent: {
    marginHorizontal: spacing.sm
  },
  footerLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
  },
  footerValue: {
    fontSize: typography.fontSize.xl,
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
  },
  scrollContent: {
    paddingBottom: spacing.lg,
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.sm
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginHorizontal: spacing.xs
  },
  cardItem: {
    // marginBottom: spacing.sm
  },
  line: {
    height: 1,
    backgroundColor: colors.gray[300],
    marginTop: spacing.sm,
    marginBottom: spacing.md
  }
})