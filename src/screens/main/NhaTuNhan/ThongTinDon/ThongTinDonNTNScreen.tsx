import {View, Text, ScrollView} from 'react-native';
import React, {useMemo, useState} from 'react';
import {Button, Icon, ScreenComponent} from '@components/common';
import {styles} from './Styles';
import {InfoRow} from '@screens/main/BHSK/ThongTinDonBH/components';
import {ExpandableCard} from '@screens/main/BHSK/ThongTinDonBH/components/ExpandableCard';
import {formatCurrency} from '@utils/currencyFormatter';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';
import {colors} from '@constants/theme';
import {getCommonExecute} from '@services/endpoints';

interface ThongTinDon {
  loai_nha: string;
  loai_nha_ten: string;
  md_sd: string;
  dia_chi: string;
  so_id_goi: number;
  so_nam: number;
  gio_hl: number;
  ngay_hl: string;
  gio_kt: number;
  ngay_kt: string;
  phi_bh: number;
  phi_bh_chua_vat: number;
  phi_vat: number;
  tong_phi: string;
  ten_kh: string;
  mqh_so_huu: string;
  gioi_tinh_kh: string;
  cmt_kh: string;
  ngay_sinh_kh: string;
  dthoai_kh: string;
  email_kh: string;
  dia_chi_kh: string;
  ngdpt_moi_qhe: string;
  ngan_hang: string;
  stk: string;
}

export default function ThongTinDonNTNScreen({navigation}: any) {
  const [thongTinDon, setThongTinDon] = useState<ThongTinDon[]>([]);

  const getThongTinDon = async () => {
    try {
      const params = {};
      logger.log('🚀 ~ getThongTinDon ~ params:', params);
      const response = await getCommonExecute(params);
      logger.log('🚀 ~ getThongTinDon ~ response:', response);
      if (response?.data) {
        setThongTinDon(response?.data);
      } else {
        logger.log('Lay thong tin chi tiet don bao hiem that bai: ', response?.error);
      }
    } catch (error) {
      logger.log('🚀 ~ getThongTinDon ~ error:', error);
    }
  };
  const handleThemNDBH = () => {
    NavigationUtil.push(MAIN_SCREENS.THONG_TIN_NDBH_NTN, {
      mode: 'create_more',
    });
  };

  const handleThanhToan = () => {
    navigation.navigate(MAIN_SCREENS.THANH_TOAN);
  };

  const handleSuaNguoiMua = () => {
    logger.log('Sửa thông tin người mua');
    // TODO: Navigate đến màn hình sửa
  };

  const handleSuaNDBH = () => {
    logger.log('Sửa thông tin nhà được bảo hiểm');
    // TODO: Navigate đến màn hình sửa
  };

  const handleXoaNDBH = () => {
    logger.log('Xóa thông tin nhà được bảo hiểm');
    // TODO: Hiện modal confirm xóa
  };

  // Hàm render từng card item với icon, title và ExpandableCard
  const renderCardItem = ({title, children, showEdit, showDelete, onEdit, onDelete}: any) => {
    return (
      <View style={styles.cardItem}>
        <ExpandableCard title={title} defaultExpanded showEdit={showEdit} showDelete={showDelete} onEdit={onEdit} onDelete={onDelete}>
          {children}
        </ExpandableCard>
      </View>
    );
  };

  const renderFooter = useMemo(
    () => (
      <View style={styles.footerContainer}>
        <InfoRow label="Tổng tiền" value={formatCurrency(0)} style={styles.footerContent} labelStyle={styles.footerLabel} valueStyle={styles.footerValue} />
        <View style={styles.footerButton}>
          <Button title="Thêm NĐBH" onPress={handleThemNDBH} variant="outline" style={styles.secondButton} />
          <Button title="Thanh toán" onPress={handleThanhToan} style={styles.primaryButton} />
        </View>
      </View>
    ),
    [],
  );
  return (
    <ScreenComponent
      bodyStyle={styles.container}
      showHeader
      headerTitle="Thông tin đơn bảo hiểm"
      showBackButton
      onPressBack={() => {
        navigation.goBack();
      }}
      showFooter
      footer={renderFooter}>
      <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Thông tin người mua bảo hiểm */}
        {renderCardItem({
          title: 'Đối tượng được bảo hiểm',
          showEdit: true,
          onEdit: handleSuaNguoiMua,
          children: (
            <View>
              <InfoRow label="Loại nhà" value="Nguyễn Văn A" />
              <InfoRow label="Mục đích sử dụng" value="0123456789" />
              <InfoRow label="Địa chỉ chi tiết" value="<EMAIL>" />
            </View>
          ),
        })}

        {/* Thông tin nhà được bảo hiểm */}
        {renderCardItem({
          title: 'Thông tin gói bảo hiểm',
          children: (
            <View>
              <InfoRow label="Gói bảo hiểm" value="Căn hộ chung cư" />
              <InfoRow label="Thời hạn bảo hiểm" value="123 Nguyễn Văn Linh, Q.7, TP.HCM" />
              <InfoRow label="Ngày bắt đầu bảo hiểm" value="80 m²" />
              <InfoRow label="Ngày kết thúc bảo hiểm" value="10" />
              <InfoRow label="Số tiền bảo hiểm" value="10" />
              <InfoRow label="Phí bảo hiểm (chưa VAT)" value="10" />
              <InfoRow label="Thuế VAT" value="10" />
              <InfoRow label="Tổng phí bảo hiểm" value="10" />
            </View>
          ),
        })}

        {/* Thông tin gói bảo hiểm */}
        {renderCardItem({
          title: 'Người mua bảo hiểm',
          children: (
            <View>
              <View>
                <InfoRow label="Người mua bảo hiểm" value="Căn hộ chung cư" />
                <InfoRow label="Mối quan hệ sở hữu" value="123 Nguyễn Văn Linh, Q.7, TP.HCM" />
                <InfoRow label="Họ và tên" value="80 m²" />
                <InfoRow label="Giới tính" value="10" />
                <InfoRow label="Số giấy tờ tùy thân" value="10" />
                <InfoRow label="Ngày sinh" value="10" />
                <InfoRow label="Số điện thoại" value="10" />
                <InfoRow label="Email" value="10" />
                <InfoRow label="Địa chỉ" value="10" />
                <View style={styles.line}></View>
                <Text style={styles.title}>Người thụ hưởng</Text>
                <InfoRow label="Mối quan hệ với chủ sở hữu" value="80 m²" />
                <InfoRow label="Họ và tên" value="80 m²" />
                <InfoRow label="Ngân hàng" value="80 m²" />
                <InfoRow label="Số tài khoản" value="80 m²" />
              </View>
            </View>
          ),
        })}
      </ScrollView>
    </ScreenComponent>
  );
}
