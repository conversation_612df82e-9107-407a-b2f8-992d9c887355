import {Dimensions, StyleSheet} from 'react-native';
import {colors, shadows, spacing, typography} from '../../../constants/theme';

const {width: SCREEN_W} = Dimensions.get('window');
const H_MARGIN = 16;
const CARD_W = SCREEN_W - H_MARGIN * 2;
const CARD_H = 180;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.md,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingTop: spacing.sm,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  userDetails: {
    flexShrink: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    tintColor: colors.gray[500],
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.gray[500],
    overflow: 'hidden',
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  locationIcon: {
    width: 16,
    height: 16,
  },
  userName: {
    fontSize: typography.fontSize.xl,
    color: colors.dark,
    fontFamily: typography.fontFamily.bold,
  },
  location: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  notificationWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  notificationIcon: {
    width: 28,
    height: 28,
  },
  notificationText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.black,
  },
  banners: {
    marginHorizontal: -16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  shadowWrap: {
    width: CARD_W,
    height: CARD_H,
    marginHorizontal: H_MARGIN,
    borderRadius: 18,
    ...shadows.base,
  },
  card: {
    flex: 1,
    borderRadius: 18,
    overflow: 'hidden',
  },
  img: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.gray[100],
  },
  dotsWrap: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  dot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  dotInactive: {width: 8, backgroundColor: '#E5E7EB'},
  dotActive: {width: 10, backgroundColor: colors.green},
  section: {
    // marginBottom: spacing.xl,
    marginTop: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.xl,
    color: colors.dark,
    fontFamily: typography.fontFamily.bold,
  },
  viewAll: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.green,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    flexShrink: 0,
    alignSelf: 'center',
    resizeMode: 'contain',
  },
  quickActionTitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    color: colors.dark,
    width: '100%',
  },
  cardContainer: {
    paddingHorizontal: 0,
    alignItems: 'center',
  },
  gridContainer: {
    width: '100%',
  },
  lastRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 16,
    width: '100%',
    paddingHorizontal: 16,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    width: '100%',
    paddingHorizontal: 16,
  },
  cardWrapper: {
    width: (SCREEN_W - 64) / 3,
    aspectRatio: 1,
    marginHorizontal: 6,
  },
  quickActionCardFlat: {
    width: '100%',
    height: '95%',
    padding: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    position: 'relative',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[400],
    // ...shadows.sm,
  },
  statusBadge: {
    position: 'absolute',
    top: -6,
    right: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: spacing.xs,
    zIndex: 1,
    minWidth: 60,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 10,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },
  searchIcon: {
    width: 24,
    height: 24,
  },
  modalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    justifyContent: 'space-evenly',
    marginTop: spacing.md,
  },
  contentItem: {
    alignItems: 'center',
    gap: spacing.sm,
  },
  contentIcon: {
    width: 64,
    height: 64,
    alignSelf: 'center',
  },
  contentText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.dark,
  },
  textStyle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Menlo',
    marginBottom: 14,
  },
});
