import {Card, CustomModal, CustomTouchableOpacity, Icon, ScreenComponent} from '@components/common';
import TextField from '@components/common/TextField';
import {createToastHelpers, useToast} from '@components/common/Toast';
import {ACTION_CODE} from '@constants/axios';
import {MAIN_SCREENS} from '@navigation/routes';
import type {MainNavigationProp} from '@navigation/types';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import React, {useEffect, useState} from 'react';
import {Dimensions, FlatList, Image, ImageSourcePropType, Pressable, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import SwiperFlatList from 'react-native-swiper-flatlist';
import R from '../../../assets/R';
import {colors} from '../../../constants/theme';
import {useAppSelector} from '../../../store';
import {styles} from './Styles';
import {useSelector} from 'react-redux';
import {RootState} from '@store/index';

const {width: SCREEN_W} = Dimensions.get('window');

type BannerItem = {
  id: string;
  url?: string; // For remote images
  localImage?: ImageSourcePropType; // For local images
  title?: string;
  description?: string;
};

// Utility function to create banner items easily
const createBanner = {
  fromUrl: (id: string, url: string, title?: string, description?: string): BannerItem => ({
    id,
    url,
    title,
    description,
  }),
  fromLocal: (id: string, localImage: ImageSourcePropType, title?: string, description?: string): BannerItem => ({
    id,
    localImage,
    title,
    description,
  }),
};

const banners: BannerItem[] = [
  createBanner.fromLocal('1', R.images.img_banner, 'Banner chính'),
  // createBanner.fromLocal('2', R.images.img_banner, 'BHXH/BHYT'),
  // createBanner.fromLocal('3', R.images.img_banner, 'Logo công ty'),
  // Có thể thêm nhiều banner khác
  // createBanner.fromLocal('5', R.images.img_private_house),
];

type QuickActionBase = {
  id: number;
  title: string;
  icon: ImageSourcePropType;
  color: string;
  statusColor: string;
};

type QuickActionActive = QuickActionBase & {
  status?: never;
  onPress: () => void;
};

type QuickActionComingSoon = QuickActionBase & {
  status: 'Sắp ra mắt';
  onPress: () => void;
};

type QuickAction = QuickActionActive | QuickActionComingSoon;

type QuickActionPlaceholder = {
  id: number;
  title: '';
  icon: ImageSourcePropType;
  color: '';
  statusColor: '';
  onPress: () => void;
};

// Custom Banner Image component that supports both local and remote images
const BannerImage: React.FC<{item: BannerItem}> = ({item}) => {
  const [imageError, setImageError] = useState(false);

  // If there's an error, show fallback
  if (imageError) {
    return <Image source={R.images.img_logo} style={styles.img} resizeMode="cover" />;
  }

  // If it's a local image, use it directly
  if (item.localImage) {
    return (
      <Image
        source={item.localImage}
        style={styles.img}
        resizeMode="cover"
        onError={error => {
          console.warn('Local banner image failed to load:', error.nativeEvent.error);
          setImageError(true);
        }}
      />
    );
  }

  // If it's a remote URL, handle with error fallback
  if (item.url) {
    return (
      <Image
        source={{uri: item.url}}
        style={styles.img}
        resizeMode="cover"
        defaultSource={R.images.img_logo}
        onError={error => {
          console.warn('Remote banner image failed to load:', error.nativeEvent.error);
          setImageError(true);
        }}
      />
    );
  }

  // Fallback image if no source is provided
  return <Image source={R.images.img_logo} style={styles.img} resizeMode="cover" />;
};

export const HomeScreen = () => {
  const user = useAppSelector(state => state.auth.user);
  const navigation = useNavigation<MainNavigationProp>();
  const [index, setIndex] = useState(0);
  const [isShowBHXHModal, setIsShowBHXHModal] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const {danhSachMoiQuanHeNdbh} = useSelector((state: RootState) => state.commonCategories);

  useEffect(() => {
    getDanhMucTest();
  }, []);

  const getDanhMucTest = async () => {
    try {
      const params = {
        nhom: 'CLIENT',
        actionCode: ACTION_CODE.LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM,
      };
      await getCommonExecute(params);
    } catch (error) {}
  };

  const handleOpenBHXHModal = () => {
    setIsShowBHXHModal(true);
  };

  const handleCloseBHXHModal = () => {
    setIsShowBHXHModal(false);
  };

  const handleNavigateToBHSK = () => {
    navigation.navigate(MAIN_SCREENS.DANH_SACH_BHSK);
  };

  const handleNavigateToNhaTuNhan = () => {
    navigation.navigate(MAIN_SCREENS.DANH_SACH_DON_NTN);
  };

  const handleHouseholdList = () => {
    navigation.navigate(MAIN_SCREENS.HOUSEHOLD_LIST);
  };

  const handleNavigateToCollection = () => {
    setIsShowBHXHModal(false);
    navigation.navigate(MAIN_SCREENS.COLLECTION);
  };

  const handleNavigateBHTNDSXeMay = () => {
    navigation.navigate(MAIN_SCREENS.DANH_SACH_DON);
  };

  const handleNavigateBHTNDSOto = () => {
    navigation.navigate(MAIN_SCREENS.DANH_SACH_DON_O_TO);
  };

  const handleNavigateVCXOto = () => {
    navigation.navigate(MAIN_SCREENS.DANH_SACH_DON_VCX_O_TO);
  };

  const quickActions: QuickAction[] = [
    {
      id: 1,
      title: 'BHXH/BHYT',
      icon: R.images.img_bhxh_green,
      color: colors.primary,
      statusColor: colors.success,
      onPress: handleOpenBHXHModal,
    },
    // {
    //   id: 2,
    //   title: 'Nhà tư nhân',
    //   icon: R.images.img_private_house_green,
    //   color: colors.primary,
    //   statusColor: colors.success,
    //   onPress: handleNavigateToNhaTuNhan,
    // },
    {
      id: 3,
      title: 'Sức khỏe',
      icon: R.images.img_heath_green,
      color: colors.primary,
      statusColor: colors.success,
      onPress: handleNavigateToBHSK,
    },
    {
      id: 4,
      title: 'TNDS\nXe máy',
      icon: R.images.img_motorbike_green,
      color: colors.primary,
      statusColor: colors.success,
      onPress: handleNavigateBHTNDSXeMay,
    },
    {
      id: 5,
      title: 'TNDS\nÔ tô',
      icon: R.images.img_car_green,
      color: colors.primary,
      statusColor: colors.success,
      onPress: handleNavigateBHTNDSOto,
    },
    // {
    //   id: 6,
    //   title: 'Vât chất xe ô tô',
    //   icon: R.images.img_car_green,
    //   color: colors.primary,
    //   statusColor: colors.success,
    //   onPress: handleNavigateVCXOto,
    // },
    // Example: Coming soon items
    // {
    //   id: 3,
    //   title: 'Nhà tư nhân',
    //   icon: R.images.img_private_house,
    //   color: colors.success,
    //   status: 'Sắp ra mắt',
    //   statusColor: colors.warning,
    //   onPress: () => {
    //     toast.info('Tính năng sắp ra mắt');
    //   },
    // },
  ];

  const toolActions = quickActions.filter(action => action.title === 'BHXH/BHYT');
  const productActions = quickActions.filter(action => action.title !== 'BHXH/BHYT');

  // Tự động đóng modal khi màn hình mất focus (navigate sang màn hình khác)
  useFocusEffect(
    React.useCallback(() => {
      return () => {
        setIsShowBHXHModal(false);
      };
    }, []),
  );

  return (
    <ScreenComponent>
      {/* Header */}
      <TouchableOpacity style={styles.header} onPress={() => navigation.navigate(MAIN_SCREENS.PERSONAL_INFO)}>
        <View style={styles.userInfo}>
          <Image source={user?.anh_dai_dien ? {uri: user.anh_dai_dien} : R.icons.ic_user_bold} style={styles.avatar} resizeMode="cover" />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{user?.ten || 'Người dùng'}</Text>
            <View style={styles.locationInfo}>
              <Text style={styles.location}>{user?.email}</Text>
            </View>
          </View>
        </View>
        {/* user info */}
        {/* notification */}
        {/*
          <TouchableOpacity style={styles.notificationWrapper}>
            <Image source={R.icons.ic_fill_notification} resizeMode="contain" style={styles.notificationIcon} />
            <Text style={styles.notificationText}>20</Text>
          </TouchableOpacity>
        */}
      </TouchableOpacity>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* header banner */}
        <View style={styles.banners}>
          <SwiperFlatList
            data={banners}
            autoplay
            autoplayDelay={5} // giây
            autoplayLoop
            showPagination={false} // tự render dots bên dưới
            onChangeIndex={({index}) => setIndex(index)}
            renderItem={({item, index}) => {
              return (
                <View style={{width: SCREEN_W}}>
                  <View style={styles.shadowWrap}>
                    <View style={styles.card}>
                      <BannerImage item={item} />
                    </View>
                  </View>
                </View>
              );
            }}
            keyExtractor={it => it.id}
            initialNumToRender={1}
            maxToRenderPerBatch={2}
            windowSize={3}
          />

          {/* Dots */}
          <View style={styles.dotsWrap}>
            {banners.map((_, i) => (
              <View key={i} style={[styles.dot, i === index ? styles.dotActive : styles.dotInactive, i === index && {width: 14}]} />
            ))}
          </View>
        </View>

        {/* BHXH/BHYT Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Công cụ</Text>
          </View>

          <FlatList
            data={[
              ...toolActions,
              // Placeholder items để căn chỉnh grid
              {id: 998, title: '', icon: R.images.img_bhxh, color: '', statusColor: '', onPress: () => {}} as QuickActionPlaceholder,
              {id: 999, title: '', icon: R.images.img_bhxh, color: '', statusColor: '', onPress: () => {}} as QuickActionPlaceholder,
            ]}
            numColumns={3}
            keyExtractor={it => String(it.id)}
            columnWrapperStyle={styles.cardRow}
            contentContainerStyle={styles.cardContainer}
            renderItem={({item}) => {
              // Empty placeholder for grid alignment
              if (!item.title) {
                return <View style={styles.cardWrapper} />;
              }

              const quickAction = item as QuickAction;

              return (
                <CustomTouchableOpacity onPress={quickAction.onPress} style={styles.cardWrapper}>
                  <Card style={styles.quickActionCardFlat}>
                    {quickAction.status && (
                      <View style={[styles.statusBadge, {backgroundColor: quickAction.statusColor}]}>
                        <Text style={styles.statusText}>{quickAction.status}</Text>
                      </View>
                    )}
                    <Image source={quickAction.icon} style={styles.quickActionIcon} resizeMode="contain" />
                    <Text style={styles.quickActionTitle}>{quickAction.title}</Text>
                  </Card>
                </CustomTouchableOpacity>
              );
            }}
          />
        </View>

        {/* Other Quick Actions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Sản phẩm bảo hiểm</Text>
            {/* <TouchableOpacity>
              <Text style={styles.viewAll}>Xem tất cả </Text>
            </TouchableOpacity> */}
          </View>

          <FlatList
            data={[
              ...productActions,
              // Placeholder items để căn chỉnh grid
              {id: 998, title: '', icon: R.images.img_bhxh, color: '', statusColor: '', onPress: () => {}} as QuickActionPlaceholder,
              {id: 999, title: '', icon: R.images.img_bhxh, color: '', statusColor: '', onPress: () => {}} as QuickActionPlaceholder,
            ]}
            numColumns={3}
            keyExtractor={it => String(it.id)}
            columnWrapperStyle={styles.cardRow}
            contentContainerStyle={styles.cardContainer}
            renderItem={({item}) => {
              // Empty placeholder for grid alignment
              if (!item.title) {
                return <View style={styles.cardWrapper} />;
              }

              const quickAction = item as QuickAction;

              return (
                <CustomTouchableOpacity onPress={quickAction.onPress} style={styles.cardWrapper}>
                  <Card style={styles.quickActionCardFlat}>
                    {quickAction.status && (
                      <View style={[styles.statusBadge, {backgroundColor: quickAction.statusColor}]}>
                        <Text style={styles.statusText}>{quickAction.status}</Text>
                      </View>
                    )}
                    <Image source={quickAction.icon} style={styles.quickActionIcon} resizeMode="contain" />
                    <Text style={styles.quickActionTitle}>{quickAction.title}</Text>
                  </Card>
                </CustomTouchableOpacity>
              );
            }}
          />

          {/* <Button title="QR created´" onPress={() => navigation.navigate('VietQR')} /> */}
        </View>
      </ScrollView>

      {/* Modal chi tiết bảo hiểm */}
      <CustomModal isVisible={isShowBHXHModal} onClose={handleCloseBHXHModal} title="BHXH/BHYT" animationIn="slideInUp" animationOut="slideOutDown">
        <View>
          <TextField
            value={searchValue}
            onChangeText={setSearchValue}
            placeholder="Bạn đang muốn tìm kiếm..."
            variant="outlined"
            rightIconType={<Icon name="SearchNormal" color={colors.dark} size={24} variant="Linear" />}
            showPlaceholderWhenEmpty={true}
          />
          <View style={styles.modalContent}>
            {/* <Pressable style={styles.contentItem} onPress={() => {}}>
              <Image source={R.images.img_lookup} resizeMode="contain" style={styles.contentIcon} />
              <Text style={styles.contentText}>Tra cứu</Text>
            </Pressable> */}
            <Pressable style={styles.contentItem} onPress={handleHouseholdList}>
              <Image source={R.images.img_house_hold_green} resizeMode="contain" style={styles.contentIcon} />
              <Text style={styles.contentText}>Hộ gia đình</Text>
            </Pressable>
            <Pressable style={styles.contentItem} onPress={handleNavigateToCollection}>
              <Image source={R.images.img_collection_green} resizeMode="contain" style={styles.contentIcon} />
              <Text style={styles.contentText}>Thu hộ</Text>
            </Pressable>
          </View>
        </View>
      </CustomModal>
    </ScreenComponent>
  );
};
