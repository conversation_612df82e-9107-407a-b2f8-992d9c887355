import React, {useRef, useState, useEffect} from 'react';
import {View, StyleSheet, Text, TouchableOpacity, Image, Linking, Platform} from 'react-native';
import {Camera, useCameraDevice, useCameraPermission} from 'react-native-vision-camera';
import type {PhotoFile} from 'react-native-vision-camera';

const CameraScreen = () => {
  const {hasPermission, requestPermission} = useCameraPermission();
  console.log('🚀 ~ CameraScreen ~ hasPermission:', hasPermission);
  const [cameraPosition, setCameraPosition] = useState<'front' | 'back'>('back');
  const device = useCameraDevice(cameraPosition);
  console.log('🚀 ~ CameraScreen ~ device:', device);
  const camera = useRef<Camera>(null);

  const [photo, setPhoto] = useState<PhotoFile | null>(null);

  useEffect(() => {
    if (!hasPermission) {
      requestPermission();
    }
  }, [hasPermission, requestPermission]);

  const onTakePicture = async () => {
    if (camera.current) {
      try {
        const photoData = await camera.current.takePhoto({
          flash: 'off',
          enableShutterSound: false,
        });
        setPhoto(photoData);
      } catch (error) {
        console.error('Failed to take picture:', error);
      }
    }
  };

  const onSwitchCamera = () => {
    setCameraPosition(p => (p === 'back' ? 'front' : 'back'));
  };

  if (!hasPermission) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Ứng dụng cần quyền truy cập camera.</Text>
        <TouchableOpacity style={styles.button} onPress={() => Linking.openSettings()}>
          <Text style={styles.buttonText}>Cấp quyền</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (device == null) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Không tìm thấy thiết bị camera.</Text>
      </View>
    );
  }

  if (photo) {
    return (
      <View style={styles.previewContainer}>
        <Image source={{uri: `file://${photo.path}`}} style={StyleSheet.absoluteFill} resizeMode="cover" />
        <TouchableOpacity style={styles.closeButton} onPress={() => setPhoto(null)}>
          <Text style={styles.buttonText}>Chụp lại</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera ref={camera} style={StyleSheet.absoluteFill} device={device} isActive={true} photo={true} />
      <View style={styles.controlsContainer}>
        <TouchableOpacity style={styles.controlButton} onPress={onSwitchCamera}>
          <Text style={styles.buttonText}>Đổi Camera</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.captureButton} onPress={onTakePicture} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  previewContainer: {
    flex: 1,
  },
  text: {
    color: 'white',
    fontSize: 18,
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 50,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'white',
    borderWidth: 5,
    borderColor: '#E0E0E0',
  },
  controlButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 15,
    borderRadius: 10,
  },
  closeButton: {
    position: 'absolute',
    bottom: 50,
    alignSelf: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 15,
    borderRadius: 10,
  },
});

export default CameraScreen;
