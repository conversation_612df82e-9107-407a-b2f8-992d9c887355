import {FlatList, Image, Platform, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {formatGender} from '@utils/formatters';
import {Card, CustomTouchableOpacity, Icon, Loading, ScreenComponent, TextField, createToastHelpers, useToast} from '@components/common';
import {RouteProp, useFocusEffect, useNavigation} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';
import {styles} from './householdmembers.style';
import R from '../../../../assets/R';
import {MAIN_SCREENS} from '@navigation/routes';
import NavigationUtil from '@navigation/NavigationUtil';
import {getCommonExecute} from '@services/endpoints';
import {ACTION_CODE} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import {PAGE_SIZE} from '@commons/Constant';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

interface HouseholdMembersScreenProps {
  route: RouteProp<any, any>;
}

interface MemberItem {
  ma_doi_tac: string;
  ma_chi_nhanh: string;
  nsd: string;
  pas: string;

  ngay_cap_nhat: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  nguoi_tao: string;

  bt: string;
  bt_ho_gia_dinh: string;
  tinh_thanh: string;
  phuong_xa: string;
  loai: string;
  cmt: string;
  dthoai: string;
  so_bhxh: string;
  ten: string;
  ngay_sinh: string;
  gioi_tinh: string;
  dia_chi: string;
  nd_tim: string;
  moi_qhe_ten: string;
}

export default function HouseholdMembersScreen({route}: HouseholdMembersScreenProps) {
  console.log('HouseholdMembersScreen');

  const navigation = useNavigation<MainNavigationProp>();
  const bt_ho_gia_dinh = route?.params?.bt_ho_gia_dinh;
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [danhSachThanhVien, setDanhSachThanhVien] = useState<MemberItem[]>([]);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [currentPageMember, setCurrentPageMember] = useState(1);
  const [hasMoreMember, setHasMoreMember] = useState(true);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [hasLoadedInitialData, setHasLoadedInitialData] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false); // Guard để tránh multiple refresh

  // Debug effect để theo dõi needsRefresh changes
  useEffect(() => {
    console.log('🔍 needsRefresh changed to:', needsRefresh);
  }, [needsRefresh]);

  // Debug effect để theo dõi isInitialMount changes
  useEffect(() => {
    console.log('🔍 isInitialMount changed to:', isInitialMount);
  }, [isInitialMount]);
  const [searchText, setSearchText] = useState('');
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const insets = useSafeAreaInsets();

  // FlatList refs for layout reset
  const membersListRef = useRef<FlatList>(null);
  const householdListRef = useRef<FlatList>(null);

  // Ref để track xem searchText đã từng có giá trị hay chưa
  const hasSearchedBefore = useRef(false);

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (bt_ho_gia_dinh && !hasLoadedInitialData) {
      getDanhSachThanhVien(1).then(() => {
        setHasLoadedInitialData(true);
        // Mark as not initial mount after first load
        setTimeout(() => setIsInitialMount(false), 1000);
      });
    }

    // No cleanup needed for manual search
  }, [bt_ho_gia_dinh, hasLoadedInitialData]);

  // Use useFocusEffect to refresh data when returning from edit screen
  useFocusEffect(
    useCallback(() => {
      console.log('🔍 useFocusEffect triggered - needsRefresh:', needsRefresh, 'isInitialMount:', isInitialMount, 'isRefreshing:', isRefreshing);
      // Only refresh if we need to refresh and not on initial mount and not already refreshing
      if (needsRefresh && !isInitialMount && !isRefreshing) {
        console.log('🔄 Refreshing member list after returning from form with dialog loading');
        console.log('🔄 About to call getDanhSachThanhVien with showDialogLoading=true');
        setIsRefreshing(true); // Set guard
        getDanhSachThanhVien(1, searchText, true)
          .then(() => {
            console.log('🔄 getDanhSachThanhVien completed');
            // Chỉ reset layout khi pull-to-refresh, không reset khi back lại
            if (isPullToRefresh) {
              setTimeout(() => {
                membersListRef.current?.scrollToOffset({offset: 0, animated: false});
              }, 100);
            }
            // Reset flags sau khi API hoàn thành
            setNeedsRefresh(false);
            setIsRefreshing(false);
          })
          .catch(() => {
            // Reset guard nếu có lỗi
            setIsRefreshing(false);
          });
      }
    }, [needsRefresh, isInitialMount, isPullToRefresh, searchText, isRefreshing]),
  );

  // Backup mechanism: Listen to needsRefresh changes (fallback nếu useFocusEffect không trigger)
  useEffect(() => {
    if (needsRefresh && !isInitialMount && hasLoadedInitialData && !isRefreshing) {
      console.log('🔄 Backup refresh mechanism triggered');
      // Delay để đảm bảo navigation đã hoàn thành và useFocusEffect có cơ hội chạy trước
      const timeoutId = setTimeout(() => {
        if (needsRefresh && !isRefreshing) {
          // Double check flags vẫn còn (nếu useFocusEffect đã chạy thì flag này sẽ là false)
          console.log('🔄 Executing backup refresh with dialog loading (useFocusEffect missed)');
          setIsRefreshing(true);
          getDanhSachThanhVien(1, searchText, true)
            .then(() => {
              setNeedsRefresh(false);
              setIsRefreshing(false);
            })
            .catch(() => {
              setIsRefreshing(false);
            });
        } else {
          console.log('🔄 Backup refresh skipped - useFocusEffect already handled or already refreshing');
        }
      }, 800); // Delay lâu hơn để useFocusEffect có cơ hội chạy trước

      return () => clearTimeout(timeoutId);
    }
  }, [needsRefresh, isInitialMount, hasLoadedInitialData, searchText, isRefreshing]);

  // Lấy danh sách thành viên từ API
  const getDanhSachThanhVien = async (page: number, searchValue?: string, showDialogLoading = false) => {
    console.log('🔄 getDanhSachThanhVien called - page:', page, 'searchValue:', searchValue, 'showDialogLoading:', showDialogLoading);
    try {
      if (page === 1) {
        if (showDialogLoading) {
          console.log('🔄 Setting loading=true for dialog');
          setLoading(true);
        } else if (searchValue !== undefined) {
          console.log('🔄 Setting searchLoading=true');
          setSearchLoading(true);
        } else {
          console.log('🔄 Setting loading=true (default)');
          setLoading(true);
        }
        setCurrentPageMember(1);
      } else {
        setLoadingMore(true);
      }

      const params = {
        bt: '',
        bt_ho_gia_dinh: bt_ho_gia_dinh,
        tinh_thanh: '',
        phuong_xa: '',
        loai: '',
        cccd: '',
        dthoai: '',
        so_bhxh: '',
        ten: '',
        ngay_sinh: '',
        gioi_tinh: '',
        nd_tim: searchValue !== undefined ? searchValue : searchText || '', // truyền vào nội dung ô input
        moi_qhe_ten: '',
        trang: page, // xử lý loadmore
        so_dong: PAGE_SIZE, //fix cứng ko cần thay đổi
        actionCode: ACTION_CODE.GET_DS_THANH_VIEN,
      };
      const response = await getCommonExecute(params);
      const listThanhVien = response.data.data;
      console.log('📋 API response received - members count:', listThanhVien?.length || 0);

      if (page === 1) {
        setDanhSachThanhVien(listThanhVien);
        console.log('🔄 Member list updated (page 1) - new count:', listThanhVien?.length || 0);
      } else {
        setDanhSachThanhVien(prev => [...prev, ...listThanhVien]);
        console.log('📄 More members loaded (page', page, ') - added:', listThanhVien?.length || 0);
      }

      setCurrentPageMember(page);
      const requestedPageSize = PAGE_SIZE; // Đồng bộ với so_dong = 4
      setHasMoreMember(listThanhVien.length >= requestedPageSize);
    } catch (error) {
      console.log('getDanhSachThanhVien ~ error:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setSearchLoading(false);
    }
  };

  const onRefreshData = async () => {
    setIsPullToRefresh(true); // Set flag để cho phép layout dãn ra khi pull-to-refresh
    await getDanhSachThanhVien(1, searchText);
    setIsPullToRefresh(false); // Reset flag sau khi refresh xong
  };

  // Hàm xử lý tìm kiếm thủ công khi bấm icon search hoặc Enter
  const handleSearch = useCallback(() => {
    getDanhSachThanhVien(1, searchText);
  }, [searchText]);

  // Lắng nghe thay đổi của searchText - tự động load data khi search text rỗng
  useEffect(() => {
    // Track khi searchText có giá trị
    if (searchText !== '') {
      hasSearchedBefore.current = true;
    }

    // Chỉ tự động load khi:
    // 1. searchText rỗng (người dùng đã xóa hết text)
    // 2. Đã từng search trước đó (hasSearchedBefore = true)
    // 3. Đã load initial data
    if (searchText === '' && hasSearchedBefore.current && hasLoadedInitialData) {
      getDanhSachThanhVien(1, '');
    }
    // Không tự động search khi searchText có giá trị - chỉ search khi bấm icon hoặc Enter
  }, [searchText, hasLoadedInitialData]);

  const handleUpdateMember = (item: MemberItem) => {
    // Set flag to refresh data when returning
    console.log('🔧 Edit member pressed');
    NavigationUtil.push(MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM, {
      memberId: item.bt,
      // Thêm callback để force refresh nếu cần
      onMemberUpdated: () => {
        console.log('🔄 onMemberUpdated callback triggered - setting needsRefresh to true');
        console.log('🔄 Current needsRefresh before set:', needsRefresh);
        setNeedsRefresh(true);
        console.log('🔄 needsRefresh set to true');
        // Toast đã được hiển thị ở HouseholdMemberFormScreen
      },
    });
  };

  const onAddPress = (data: any) => {
    console.log('➕ Add member pressed');
    NavigationUtil.push(MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM, {
      memberId: '',
      bt_ho_gia_dinh,
      // Thêm callback để force refresh nếu cần
      onMemberAdded: () => {
        console.log('🔄 onMemberAdded callback triggered - setting needsRefresh to true');
        console.log('🔄 Current needsRefresh before set:', needsRefresh);
        setNeedsRefresh(true);
        console.log('🔄 needsRefresh set to true');
        // Toast đã được hiển thị ở HouseholdMemberFormScreen
      },
    });
  };

  const renderMemberItem = ({item}: {item: MemberItem}) => (
    <CustomTouchableOpacity activeOpacity={1} onPress={() => handleUpdateMember(item)}>
      <Card style={styles.householdContent}>
        <View style={styles.householdInfo}>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Mã BHXH</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.so_bhxh || '--'}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Họ tên</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.ten || '--'}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>CCCD</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.cmt || '--'}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Ngày sinh</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.ngay_sinh || '--'}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Giới tính</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {formatGender(item.gioi_tinh)}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Số điện thoại</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.dthoai || '--'}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Mối q.hệ với chủ hộ</Text>
            <Text style={[styles.value, styles.relationshipValue, styles.fieldValue]} numberOfLines={0}>
              {item.moi_qhe_ten || '--'}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.arrowButton}>
          <Image source={R.icons.ic_arrow_right} style={styles.arrowIcon} resizeMode="contain" />
        </TouchableOpacity>
      </Card>
    </CustomTouchableOpacity>
  );

  return (
    <ScreenComponent dialogLoading={loading || searchLoading} showHeader headerTitle={'Danh sách người trong hộ'} showBackButton onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <TextField
            placeholder="Tìm theo số CCCD, mã BHXH..."
            value={searchText}
            onChangeText={setSearchText}
            onSubmitEditing={handleSearch}
            showPlaceholderWhenEmpty={true}
            containerStyle={styles.searchInputContainer}
            rightIconType="search"
            onRightIconPress={handleSearch}
            autoCorrect={false}
            autoCapitalize="none"
            blurOnSubmit={false}
            returnKeyType="search"
            enablesReturnKeyAutomatically={false}
          />
        </View>
        <FlatList
          ref={membersListRef}
          key="members-list"
          data={danhSachThanhVien}
          keyExtractor={(_, index) => String(index)}
          renderItem={renderMemberItem}
          showsVerticalScrollIndicator={false}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={10}
          removeClippedSubviews={false}
          contentContainerStyle={danhSachThanhVien.length === 0 ? styles.emptyContentContainer : undefined}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Icon name="Profile2User" size={80} color={colors.gray[500]} variant="Bold" style={styles.emptyIcon} />
              <Text style={styles.emptyTitle}>{searchText ? 'Không tìm thấy thành viên' : 'Chưa có thành viên nào'}</Text>
              <Text style={styles.emptyText}>
                {searchText ? 'Không có thành viên nào phù hợp với từ khóa tìm kiếm của bạn' : 'Hộ gia đình này chưa có thành viên nào. Hãy thêm thành viên mới để quản lý thông tin.'}
              </Text>
            </View>
          }
          refreshControl={<RefreshControl refreshing={loading && isPullToRefresh} onRefresh={onRefreshData} colors={[colors.primary]} tintColor={colors.primary} />}
          onEndReachedThreshold={0.5}
          onEndReached={() => {
            if (hasMoreMember && !loadingMore && !loading) {
              getDanhSachThanhVien(currentPageMember + 1, searchText);
            }
          }}
          ListFooterComponent={() => {
            if (loadingMore) {
              return (
                <View style={{paddingVertical: 20}}>
                  <Loading color={colors.green} />
                </View>
              );
            }
            if (!hasMoreMember && danhSachThanhVien.length > 0) {
              return (
                <View style={styles.endDataContainer}>
                  {/* <Icon name="TickCircle" size={40} color={colors.green} variant="Bold" /> */}
                  <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
                  <Text style={styles.endDataText}>Bạn đã xem tất cả {danhSachThanhVien.length} thành viên</Text>
                </View>
              );
            }
            return null;
          }}
        />
        {/* Floating Action Button */}
        <TouchableOpacity style={[styles.fab, {bottom: Platform.OS === 'android' ? insets.bottom + spacing['2xl'] || spacing.xl : spacing['2xl']}]} activeOpacity={0.8} onPress={onAddPress}>
          <Image source={R.icons.ic_add} style={styles.fabIcon} resizeMode="contain" />
        </TouchableOpacity>
      </View>
    </ScreenComponent>
  );
}
