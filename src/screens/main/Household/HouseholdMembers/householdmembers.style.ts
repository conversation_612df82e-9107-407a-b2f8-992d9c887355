import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';
import {Platform, StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },

  listContainer: {
    paddingBottom: spacing.xl,
  },

  // Copy exact styles từ HouseholdListScreen
  householdContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.lg,
  },

  householdInfo: {
    flex: 1,
    gap: spacing.sm,
  },

  fieldRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  title: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },

  fieldTitle: {
    flex: 0.9,
    paddingRight: spacing.md,
  },

  value: {
    fontSize: typography.fontSize.base,
    color: colors.black,
  },

  fieldValue: {
    flex: 1,
  },

  relationshipValue: {
    color: colors.primary,
    fontWeight: typography.fontWeight.bold as any,
  },

  arrowButton: {
    padding: spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
  },

  arrowIcon: {
    width: 20,
    height: 20,
    tintColor: colors.green,
  },
  fab: {
    position: 'absolute',
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    bottom: spacing.xl,
    right: spacing.xl,
    backgroundColor: colors.green,
    borderRadius: borderRadius.full,
    ...shadows.lg,
    elevation: 6,
  },
  fabIcon: {
    width: 20,
    height: 20,
    tintColor: colors.white,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'stretch',
    gap: spacing.sm,
    paddingHorizontal: spacing.sm,
    marginTop: spacing.md,
    width: '100%',
    alignSelf: 'center',
  },
  searchInputContainer: {
    flex: 1,
  },
  searchLoadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    marginBottom: spacing['2xl'],
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing['2xl'],
  },
  emptyIcon: {
    width: 80,
    height: 80,
    tintColor: colors.gray[400],
    marginBottom: spacing.sm,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
  },
  endDataContainer: {
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: spacing.sm,
    marginTop: spacing.md,
    marginBottom: Platform.OS === 'ios' ? spacing.xl : spacing['2xl'],
  },
  endDataTitle: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.green,
    textAlign: 'center',
  },
  endDataText: {
    marginTop: spacing.xs,
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 20,
  },
});
