import R from '@assets/R';
import {PAGE_SIZE} from '@commons/Constant';
import {Card, CustomTouchableOpacity, Icon, Loading, ScreenComponent, TextField} from '@components/common';
import {ACTION_CODE} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp} from '@navigation/types';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import {formatGender} from '@utils/formatters';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {FlatList, Image, Platform, RefreshControl, Text, TouchableOpacity, View} from 'react-native';
import {styles} from './householdlist.style';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

interface LoadMoreState {
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

interface HouseholdItem {
  bt: string;
  cmt: string;
  dia_chi: string;
  dthoai: string;
  email: string;
  ngay_sinh: string;
  gioi_tinh: 'NAM' | 'NỮ' | string;
  loai: string;
  ma_doi_tac: string;
  ngay_cap_nhat: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  nguoi_tao: string;
  phuong_xa: string | null;
  sl_tvien: number;
  sott: number;
  stt: number;
  ten: string;
  tinh_thanh: string | null;
  trang_thai_ten: string;
}

interface MemberItem {
  ma_doi_tac: string;
  ma_chi_nhanh: string;
  nsd: string;
  pas: string;

  ngay_cap_nhat: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  nguoi_tao: string;

  bt: string;
  bt_ho_gia_dinh: string;
  tinh_thanh: string;
  phuong_xa: string;
  loai: string;
  cmt: string;
  dthoai: string;
  so_bhxh: string;
  ten: string;
  ngay_sinh: string;
  gioi_tinh: string;
  dia_chi: string;
  nd_tim: string;
  moi_qhe_ten: string;
  chu_ho: string;
}

export default function HouseholdListScreen() {
  console.log(' HouseholdList Screen');
  const navigation = useNavigation<MainNavigationProp>();
  const [activeTab, setActiveTab] = useState<'household' | 'members'>('household');
  const [searchText, setSearchText] = useState('');
  const [danhSachHoGiaDinh, setDanhSachHoGiaDinh] = useState<HouseholdItem[]>([]);
  const [danhSachThanhVien, setDanhSachThanhVien] = useState<MemberItem[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const insets = useSafeAreaInsets();

  // LoadMoreState cho household
  const [householdLoadMoreState, setHouseholdLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: PAGE_SIZE,
  });

  // LoadMoreState cho members
  const [memberLoadMoreState, setMemberLoadMoreState] = useState<LoadMoreState>({
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    totalItems: 0,
    pageSize: PAGE_SIZE,
  });

  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);

  // FlatList refs for layout reset
  const membersListRef = useRef<FlatList>(null);
  const householdListRef = useRef<FlatList>(null);

  // Ref để track xem searchText đã từng có giá trị hay chưa
  const hasSearchedBefore = useRef(false);

  useEffect(() => {
    if (activeTab == 'household') {
      getDanhSachHoGiaDinh(false);
    } else {
      getDanhSachThanhVien(false);
    }
    // Mark as not initial mount after first load
    setTimeout(() => setIsInitialMount(false), 1000);
  }, [activeTab]);

  // Use useFocusEffect to refresh data when returning from edit screen
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔍 useFocusEffect triggered - needsRefresh:', needsRefresh, 'isInitialMount:', isInitialMount, 'activeTab:', activeTab);
      // Only refresh if not on initial mount and we need to refresh
      if (!isInitialMount && needsRefresh) {
        if (activeTab === 'members') {
          console.log('🔄 Refreshing members data with dialog loading after returning from edit');
          setMemberLoadMoreState(prev => ({...prev, isLoading: true})); // Hiện dialog loading
          getDanhSachThanhVien(false, searchText).then(() => {
            // Chỉ reset layout khi pull-to-refresh, không reset khi back lại
            if (isPullToRefresh) {
              setTimeout(() => {
                membersListRef.current?.scrollToOffset({offset: 0, animated: false});
              }, 100);
            }
          });
        } else if (activeTab === 'household') {
          console.log('🔄 Refreshing household data with dialog loading after returning from info');
          setHouseholdLoadMoreState(prev => ({...prev, isLoading: true})); // Hiện dialog loading
          getDanhSachHoGiaDinh(false, searchText).then(() => {
            // Chỉ reset layout khi pull-to-refresh, không reset khi back lại
            if (isPullToRefresh) {
              setTimeout(() => {
                householdListRef.current?.scrollToOffset({offset: 0, animated: false});
              }, 100);
            }
          });
        }
        setNeedsRefresh(false); // Reset flag after refreshing
      }
    }, [isInitialMount, activeTab, needsRefresh, isPullToRefresh, searchText]),
  );

  const getDanhSachHoGiaDinh = useCallback(
    async (isLoadMore: boolean = false, customSearchValue?: string) => {
      try {
        // Get current values from state
        let currentPage: number = 1;
        let pageSize: number = PAGE_SIZE;

        setHouseholdLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize; // Lấy pageSize từ state để consistency
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        // Nếu có customSearchValue (tìm kiếm) thì dùng searchLoading để không ảnh hưởng focus
        if (customSearchValue !== undefined && !isLoadMore) {
          setSearchLoading(true);
        }

        const searchTerm = customSearchValue !== undefined ? customSearchValue : searchText;

        const params = {
          bt: 0,
          tinh_thanh: '',
          phuong_xa: '',
          tinh_thanh_ten: '',
          loai: '',
          nd_tim: searchTerm || '', // truyền vào nội dung ô input
          trang: currentPage, // xử lý loadmore
          so_dong: pageSize, //fix cứng ko cần thay đổi
          actionCode: ACTION_CODE.GET_DS_HO_GIA_DINH,
        };

        const response = await getCommonExecute(params);
        const listHoGiaDinh = response.data.data;
        const totalItems = response.data.tong_so_dong || listHoGiaDinh.length;

        setDanhSachHoGiaDinh(prev => {
          let result: HouseholdItem[];
          if (isLoadMore) {
            // Merge data cho loadmore
            result = [...prev, ...listHoGiaDinh];
          } else {
            // Replace data cho search/refresh
            result = listHoGiaDinh;
          }
          return result;
        });

        setHouseholdLoadMoreState(prev => {
          const hasMoreItems = currentPage * pageSize < totalItems;

          console.log('🔄 Household Load More Info:', {
            currentPage,
            pageSize,
            totalItems,
            hasMoreItems,
            itemsReceived: listHoGiaDinh.length,
          });

          return {
            ...prev,
            isLoading: false,
            isLoadingMore: false,
            currentPage: currentPage,
            totalItems: totalItems,
            hasMore: hasMoreItems,
          };
        });
      } catch (error) {
        console.log('getDanhSachHoGiaDinh ~ error:', error);
        setHouseholdLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      } finally {
        setSearchLoading(false);
      }
    },
    [searchText], // Không có dependencies để tránh re-create function
  );

  const getDanhSachThanhVien = useCallback(
    async (isLoadMore: boolean = false, customSearchValue?: string) => {
      try {
        // Get current values from state
        let currentPage: number = 1;
        let pageSize: number = PAGE_SIZE; // Fix cứng giống household

        setMemberLoadMoreState(prev => {
          currentPage = isLoadMore ? prev.currentPage + 1 : 1;
          pageSize = prev.pageSize; // Lấy pageSize từ state để consistency
          return {
            ...prev,
            isLoading: !isLoadMore,
            isLoadingMore: isLoadMore,
          };
        });

        // Nếu có customSearchValue (tìm kiếm) thì dùng searchLoading để không ảnh hưởng focus
        if (customSearchValue !== undefined && !isLoadMore) {
          setSearchLoading(true);
        }

        const searchTerm = customSearchValue !== undefined ? customSearchValue : searchText;

        const params = {
          bt: '',
          bt_ho_gia_dinh: '',
          tinh_thanh: '',
          phuong_xa: '',
          loai: '',
          cccd: '',
          dthoai: '',
          so_bhxh: '',
          ten: '',
          ngay_sinh: '',
          gioi_tinh: '',
          nd_tim: searchTerm || '', // truyền vào nội dung ô input
          moi_qhe_ten: '',
          chu_ho: '',
          trang: currentPage, // xử lý loadmore
          so_dong: pageSize, //sử dụng pageSize thay vì fix cứng
          actionCode: ACTION_CODE.GET_DS_THANH_VIEN,
        };
        const response = await getCommonExecute(params);
        console.log('🚀 ~ HouseholdListScreen ~ getDanhSachThanhVien ~ response:', response);
        const listThanhVien = response.data.data;
        const totalItems = response.data.tong_so_dong || listThanhVien.length;

        setDanhSachThanhVien(prev => {
          let result: MemberItem[];
          if (isLoadMore) {
            // Merge data cho loadmore
            result = [...prev, ...listThanhVien];
          } else {
            // Replace data cho search/refresh
            result = listThanhVien;
          }
          return result;
        });

        setMemberLoadMoreState(prev => {
          const hasMoreItems = currentPage * pageSize < totalItems;

          console.log('🔄 Member Load More Info:', {
            currentPage,
            pageSize,
            totalItems,
            hasMoreItems,
            itemsReceived: listThanhVien.length,
          });

          return {
            ...prev,
            isLoading: false,
            isLoadingMore: false,
            currentPage: currentPage,
            totalItems: totalItems,
            hasMore: hasMoreItems,
          };
        });
      } catch (error) {
        console.log('getDanhSachThanhVien ~ error:', error);
        setMemberLoadMoreState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
        }));
      } finally {
        setSearchLoading(false);
      }
    },
    [searchText], // Không có dependencies để tránh re-create function
  );

  const onRefreshData = () => {
    setIsPullToRefresh(true); // Set flag để cho phép layout dãn ra khi pull-to-refresh
    if (activeTab === 'household') {
      getDanhSachHoGiaDinh(false, searchText).then(() => {
        setIsPullToRefresh(false); // Reset flag sau khi refresh xong
      });
    } else if (activeTab === 'members') {
      getDanhSachThanhVien(false, searchText).then(() => {
        setIsPullToRefresh(false); // Reset flag sau khi refresh xong
      });
    }
  };

  const handleAddHousehold = () => {
    console.log('➕ Add household pressed');
    navigation.navigate(MAIN_SCREENS.HOUSEHOLD_FORM, {
      infoData: null,
      onUpdateInfo: (updatedData: any) => {
        console.log('🔄 onUpdateInfo callback triggered from HouseholdForm:', updatedData);
        // Chỉ set needsRefresh, không gọi API trực tiếp để tránh duplicate loading
        setNeedsRefresh(true);
      },
    });
  };

  // Hàm xử lý load more cho household
  const handleHouseholdLoadMore = useCallback(() => {
    if (!householdLoadMoreState.isLoadingMore && householdLoadMoreState.hasMore && !householdLoadMoreState.isLoading) {
      getDanhSachHoGiaDinh(true, searchText);
    }
  }, [householdLoadMoreState.isLoadingMore, householdLoadMoreState.hasMore, householdLoadMoreState.isLoading, searchText]);

  // Hàm xử lý load more cho members
  const handleMemberLoadMore = useCallback(() => {
    if (!memberLoadMoreState.isLoadingMore && memberLoadMoreState.hasMore && !memberLoadMoreState.isLoading) {
      getDanhSachThanhVien(true, searchText);
    }
  }, [memberLoadMoreState.isLoadingMore, memberLoadMoreState.hasMore, memberLoadMoreState.isLoading, searchText]);

  const handleGetInfoHousehold = (item: HouseholdItem) => {
    // Set flag to refresh data when returning from household info
    console.log('🏠 Household info pressed - setting needsRefresh flag');
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.HOUSEHOLD_INFO, {
      householdId: item.bt,
      onHouseholdUpdated: () => {
        console.log('🔄 onHouseholdUpdated callback triggered');
        setNeedsRefresh(true);
      },
    });
  };

  const handleUpdateMember = (item: MemberItem) => {
    // Set flag to refresh data when returning
    console.log('🔧 Member edit pressed - setting needsRefresh flag');
    setNeedsRefresh(true);
    NavigationUtil.push(MAIN_SCREENS.HOUSEHOLD_MEMBER_FORM, {
      memberId: item.bt,
      onMemberUpdated: () => {
        console.log('🔄 onMemberUpdated callback triggered in HouseholdList');
        setNeedsRefresh(true);
      },
    });
  };

  // Hàm xử lý tìm kiếm thủ công khi bấm icon search hoặc Enter
  const handleSearch = useCallback(() => {
    if (activeTab === 'household') {
      getDanhSachHoGiaDinh(false, searchText);
    } else if (activeTab === 'members') {
      getDanhSachThanhVien(false, searchText);
    }
  }, [activeTab, searchText, getDanhSachHoGiaDinh, getDanhSachThanhVien]);

  // Lắng nghe thay đổi của searchText - tự động load data khi search text rỗng
  useEffect(() => {
    // Track khi searchText có giá trị
    if (searchText !== '') {
      hasSearchedBefore.current = true;
    }

    // Chỉ tự động load khi:
    // 1. searchText rỗng (người dùng đã xóa hết text)
    // 2. Đã từng search trước đó (hasSearchedBefore = true)
    // 3. Không phải lần mount đầu tiên
    if (searchText === '' && hasSearchedBefore.current && !isInitialMount) {
      if (activeTab === 'household') {
        getDanhSachHoGiaDinh(false, '');
      } else if (activeTab === 'members') {
        getDanhSachThanhVien(false, '');
      }
    }
    // Không tự động search khi searchText có giá trị - chỉ search khi bấm icon hoặc Enter
  }, [searchText, activeTab, isInitialMount, getDanhSachHoGiaDinh, getDanhSachThanhVien]);

  const getSearchPlaceholder = () => {
    return activeTab === 'household' ? 'Tìm theo tên chủ hộ, số CCCD, sđt, email...' : 'Tìm theo tên, số CCCD, mã BHXH...';
  };

  const renderHouseholdItem = ({item}: {item: HouseholdItem}) => (
    <CustomTouchableOpacity onPress={() => handleGetInfoHousehold(item)}>
      <Card style={styles.householdContent}>
        <View style={styles.householdInfo}>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Chủ hộ</Text>
            <Text style={[styles.value, styles.nameValue, styles.fieldValue]} numberOfLines={0}>
              {item.ten}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>CCCD</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.cmt}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Số điện thoại</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.dthoai}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Địa chỉ</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.dia_chi}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Loại</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.loai}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Số thành viên</Text>
            <Text style={[styles.value, styles.memberCountValue, styles.fieldValue]} numberOfLines={0}>
              {item.sl_tvien} người
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.arrowButton}>
          <Image source={R.icons.ic_arrow_right} style={styles.arrowIcon} resizeMode="contain" />
        </TouchableOpacity>
      </Card>
    </CustomTouchableOpacity>
  );

  const renderMemberItem = ({item}: {item: MemberItem}) => (
    <CustomTouchableOpacity onPress={() => handleUpdateMember(item)}>
      <Card style={styles.householdContent}>
        <View style={styles.householdInfo}>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Mã BHXH</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.so_bhxh || 'Chưa có'}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Họ tên</Text>
            <Text style={[styles.value, styles.nameValue, styles.fieldValue]} numberOfLines={0}>
              {item.ten}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>CCCD</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.cmt}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Ngày sinh</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.ngay_sinh}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Giới tính</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {formatGender(item.gioi_tinh)}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Điện thoại</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.dthoai}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Địa chỉ</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.dia_chi}
            </Text>
          </View>
          <View style={styles.fieldRow}>
            <Text style={[styles.title, styles.fieldTitle]}>Tên chủ hộ</Text>
            <Text style={[styles.value, styles.fieldValue]} numberOfLines={0}>
              {item.chu_ho}
            </Text>
          </View>
          {item.bt_ho_gia_dinh && (
            <View style={styles.fieldRow}>
              <Text style={[styles.title, styles.fieldTitle]}>Quan hệ</Text>
              <Text style={[styles.value, styles.memberCountValue, styles.fieldValue]} numberOfLines={0}>
                {item.moi_qhe_ten}
              </Text>
            </View>
          )}
        </View>
        <TouchableOpacity style={styles.arrowButton}>
          <Image source={R.icons.ic_arrow_right} style={styles.arrowIcon} resizeMode="contain" />
        </TouchableOpacity>
      </Card>
    </CustomTouchableOpacity>
  );

  return (
    <ScreenComponent
      dialogLoading={activeTab === 'household' ? householdLoadMoreState.isLoading && !householdLoadMoreState.isLoadingMore : memberLoadMoreState.isLoading && !memberLoadMoreState.isLoadingMore}
      showHeader
      headerTitle="Hộ gia đình"
      showBackButton
      onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        {/* Tabs Header */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'household' && styles.activeTab]}
            onPress={() => {
              setActiveTab('household');
              setSearchText('');
            }}>
            <Text style={[styles.tabText, activeTab === 'household' && styles.activeTabText]}>Hộ gia đình</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'members' && styles.activeTab]}
            onPress={() => {
              setActiveTab('members');
              setSearchText('');
            }}>
            <Text style={[styles.tabText, activeTab === 'members' && styles.activeTabText]}>Người trong hộ</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextField
            placeholder={getSearchPlaceholder()}
            value={searchText}
            onChangeText={setSearchText}
            onSubmitEditing={handleSearch}
            showPlaceholderWhenEmpty={true}
            containerStyle={styles.searchInputContainer}
            rightIconType="search"
            onRightIconPress={handleSearch}
            autoCorrect={false}
            autoCapitalize="none"
            blurOnSubmit={false}
            returnKeyType="search"
            enablesReturnKeyAutomatically={false}
          />
        </View>

        {/* Content */}
        <View style={styles.content}>
          {activeTab === 'household' && (
            <View>
              <FlatList
                ref={householdListRef}
                key="household-list"
                data={danhSachHoGiaDinh}
                keyExtractor={(_, index) => String(index)}
                renderItem={renderHouseholdItem}
                showsVerticalScrollIndicator={false}
                initialNumToRender={10}
                maxToRenderPerBatch={5}
                windowSize={10}
                ListEmptyComponent={
                  <View style={styles.emptyState}>
                    <Image source={R.images.img_house_hold} style={styles.emptyIcon} resizeMode="contain" />
                    <Text style={styles.emptyTitle}>{searchText ? 'Không tìm thấy hộ gia đình' : 'Chưa có hộ gia đình nào'}</Text>
                    <Text style={styles.emptyText}>
                      {searchText ? 'Không có hộ gia đình nào phù hợp với từ khóa tìm kiếm của bạn' : 'Chưa có hộ gia đình nào trong hệ thống. Hãy thêm hộ gia đình mới để quản lý thông tin.'}
                    </Text>
                  </View>
                }
                refreshControl={
                  <RefreshControl
                    refreshing={isPullToRefresh && householdLoadMoreState.isLoading && !householdLoadMoreState.isLoadingMore}
                    onRefresh={onRefreshData}
                    colors={[colors.primary]}
                    tintColor={colors.primary}
                  />
                }
                onEndReachedThreshold={0.1}
                onEndReached={handleHouseholdLoadMore}
                ListFooterComponent={() => {
                  if (householdLoadMoreState.isLoadingMore) {
                    return (
                      <View style={styles.loadMoreContainer}>
                        <Loading size="small" message={'Đang tải thêm...'} />
                      </View>
                    );
                  }
                  if (!householdLoadMoreState.hasMore && danhSachHoGiaDinh.length > 0) {
                    return (
                      <View style={styles.endDataContainer}>
                        {/* <Icon name="TickCircle" size={40} color={colors.green} variant="Bold" /> */}
                        <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
                        <Text style={styles.endDataText}>Bạn đã xem tất cả {danhSachHoGiaDinh.length} hộ gia đình</Text>
                      </View>
                    );
                  }
                  return null;
                }}
              />
            </View>
          )}

          {activeTab === 'members' && (
            <FlatList
              ref={membersListRef}
              key="members-list"
              data={danhSachThanhVien}
              keyExtractor={(_, index) => String(index)}
              renderItem={renderMemberItem}
              showsVerticalScrollIndicator={false}
              initialNumToRender={10}
              maxToRenderPerBatch={5}
              windowSize={10}
              removeClippedSubviews={false}
              ListEmptyComponent={
                <View style={styles.emptyState}>
                  <Icon name="Profile2User" size={80} color={colors.gray[500]} variant="Bold" style={styles.emptyIcon} />
                  <Text style={styles.emptyTitle}>{searchText ? 'Không tìm thấy thành viên' : 'Chưa có thành viên nào'}</Text>
                  <Text style={styles.emptyText}>
                    {searchText ? 'Không có thành viên nào phù hợp với từ khóa tìm kiếm của bạn' : 'Chưa có thành viên nào trong hệ thống. Hãy thêm thành viên mới để quản lý thông tin.'}
                  </Text>
                </View>
              }
              refreshControl={
                <RefreshControl
                  refreshing={isPullToRefresh && memberLoadMoreState.isLoading && !memberLoadMoreState.isLoadingMore}
                  onRefresh={onRefreshData}
                  colors={[colors.primary]}
                  tintColor={colors.primary}
                />
              }
              onEndReachedThreshold={0.1}
              onEndReached={handleMemberLoadMore}
              ListFooterComponent={() => {
                if (memberLoadMoreState.isLoadingMore) {
                  return (
                    <View style={styles.loadMoreContainer}>
                      <Loading size="small" message={'Đang tai thêm...'} />
                    </View>
                  );
                }
                if (!memberLoadMoreState.hasMore && danhSachThanhVien.length > 0) {
                  return (
                    <View style={styles.endDataContainer}>
                      {/* <Icon name="TickCircle" size={40} color={colors.green} variant="Bold" /> */}
                      <Text style={styles.endDataTitle}>Đã hiển thị hết</Text>
                      <Text style={styles.endDataText}>Bạn đã xem tất cả {danhSachThanhVien.length} thành viên</Text>
                    </View>
                  );
                }
                return null;
              }}
            />
          )}
        </View>

        {/* Floating Action Button */}
        {activeTab === 'household' && (
          <TouchableOpacity style={[styles.fab, {bottom: Platform.OS === 'android' ? insets.bottom + spacing['2xl'] || spacing.xl : spacing['2xl']}]} activeOpacity={0.8} onPress={handleAddHousehold}>
            <Image source={R.icons.ic_add} style={styles.fabIcon} resizeMode="contain" />
          </TouchableOpacity>
        )}
      </View>
    </ScreenComponent>
  );
}
