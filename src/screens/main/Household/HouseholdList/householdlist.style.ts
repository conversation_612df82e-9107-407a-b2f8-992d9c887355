import {borderRadius, colors, shadows, spacing, typography} from '@constants/theme';
import {Platform, StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },

  // Tabs
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    marginHorizontal: spacing.sm,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.sm + 5,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
  },
  tabText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.green,
  },
  activeTabText: {
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },

  // TabView
  tabView: {
    flex: 1,
  },

  // Search Bar
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'stretch',
    gap: spacing.sm,
    paddingHorizontal: spacing.sm,
    marginTop: spacing.md,
    width: '100%',
    alignSelf: 'center',
  },
  searchInputContainer: {
    flex: 1,
  },
  searchLoadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchIcon: {
    width: 24,
    height: 24,
    tintColor: colors.green,
  },
  clearButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearText: {
    fontSize: 20,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.semibold,
  },
  filterButton: {
    width: 56,
    height: 56,
    backgroundColor: colors.white,
    borderRadius: borderRadius.base,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.gray[500],
    alignSelf: 'center',
    marginBottom: spacing.md,
  },
  filterIcon: {
    width: 20,
    height: 20,
    tintColor: colors.success,
  },

  // Content
  content: {
    flex: 1,
  },
  listContent: {
    paddingBottom: spacing.xl,
    paddingTop: spacing.sm,
  },
  householdContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: spacing.md,
    padding: spacing.md,
  },
  householdRow: {
    gap: spacing.sm,
    flex: 1,
  },
  fieldRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
    gap: spacing.xl,
  },
  fieldTitle: {
    width: 120,
    paddingRight: spacing.xs,
  },
  fieldValue: {
    flex: 1,
  },
  householdInfo: {
    flex: 1,
    gap: spacing.xs,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    lineHeight: 22,
  },
  value: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
    lineHeight: 22,
  },
  nameValue: {
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  },
  memberCountValue: {
    color: colors.green,
    fontFamily: typography.fontFamily.medium,
  },
  arrowButton: {
    padding: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  arrowIcon: {
    width: 20,
    height: 20,
    tintColor: colors.green,
  },

  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing['2xl'],
  },
  emptyIcon: {
    width: 80,
    height: 80,
    tintColor: colors.gray[500],
    marginBottom: spacing.sm,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 22,
    fontFamily: typography.fontFamily.regular,
  },

  // Load More States
  loadMoreContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadMoreText: {
    marginTop: spacing.xs,
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  },

  // End of Data State
  endDataContainer: {
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: spacing.sm,
    marginTop: spacing.md,
    marginBottom: Platform.OS === 'ios' ? spacing.xl : spacing['2xl'],
  },
  endDataTitle: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.green,
    textAlign: 'center',
  },
  endDataText: {
    marginTop: spacing.xs,
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: typography.fontFamily.regular,
  },

  // Floating Action Button
  fab: {
    position: 'absolute',
    bottom: spacing.lg,
    right: spacing.lg,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.green,
    borderRadius: borderRadius.full,
    // ...shadows.lg,
    // elevation: 6,
  },
  fabIcon: {
    width: 20,
    height: 20,
    tintColor: colors.white,
  },
  fabContainer: {},
});
