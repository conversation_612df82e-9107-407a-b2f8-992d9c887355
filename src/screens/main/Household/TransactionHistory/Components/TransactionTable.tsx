import {Card} from '@components/common';
import currencyFormatter from '@utils/currencyFormatter';
import moment from 'moment';
import React from 'react';
import {Text, View} from 'react-native';

interface BienLaiItem {
  so_bien_lai: string;
  ngay_lap_bien_lai: string;
  so_tien: string;
  don_vi_bhxh_ten: string;
  nguoi_tao: string;
  // Status fields
  trang_thai?: string;
  trang_thai_ten?: string;
  status?: 'paid' | 'unpaid';
  // BHXH fields
  bhtn_dong_tu_thang?: string;
  bhtn_dong_den_thang?: string;
  bhtn_so_tien_dong?: string;
  bhtn_so_thang_dong?: number;
  // BHYT fields
  bhyt_dong_tu_thang?: string;
  bhyt_dong_den_thang?: string;
  bhyt_so_tien_dong?: string;
  bhyt_so_thang_dong?: number;
}

interface TransactionTableProps {
  selectedTab: 'BHXH' | 'BHYT';
  selectedPerson: string;
  bienLaiListBHXH: BienLaiItem[];
  bienLaiListBHYT: BienLaiItem[];
  styles: any;
}

const TransactionTable: React.FC<TransactionTableProps> = ({selectedTab, selectedPerson, bienLaiListBHXH, bienLaiListBHYT, styles}) => {
  // Chọn dữ liệu dựa trên tab được chọn
  const currentBienLaiList = selectedTab === 'BHXH' ? bienLaiListBHXH : bienLaiListBHYT;
  const getStatusStyle = (trangThai?: string) => {
    switch (trangThai) {
      case 'DANG_XU_LY':
        return styles.onpaidStatus;
      case 'DA_THU':
        return styles.paidStatus;
      case 'DA_HUY':
        return styles.unpaidStatus;
      case 'DA_CHUYEN':
        return styles.paidDoneStatus;
      default:
        return styles.unpaidStatus;
    }
  };

  // Render row cho BHXH
  const renderBHXHRow = (item: BienLaiItem, index: number) => (
    <View key={index} style={styles.tableRow}>
      <Text style={[styles.tableCellText, styles.monthColumn]}>{item.bhtn_dong_tu_thang ? moment(item.bhtn_dong_tu_thang, 'YYYY-MM').format('MM/YYYY') : '-'}</Text>
      <Text style={[styles.tableCellText, styles.monthColumn]}>{item.bhtn_dong_den_thang ? moment(item.bhtn_dong_den_thang, 'YYYY-MM').format('MM/YYYY') : '-'}</Text>
      <Text style={[styles.tableCellText, styles.amountColumn]}>{currencyFormatter.formatCurrency(item.bhtn_so_tien_dong || item.so_tien)}</Text>
      <Text style={[styles.tableCellText, styles.statusColumn, getStatusStyle(item.trang_thai)]}>{item.trang_thai_ten}</Text>
    </View>
  );

  // Render row cho BHYT
  const renderBHYTRow = (item: BienLaiItem, index: number) => (
    <View key={index} style={styles.tableRow}>
      <Text style={[styles.tableCellText, styles.monthColumn]}>{item.bhyt_dong_tu_thang ? moment(item.bhyt_dong_tu_thang, 'YYYY-MM').format('MM/YYYY') : '-'}</Text>
      <Text style={[styles.tableCellText, styles.monthColumn]}>{item.bhyt_dong_den_thang ? moment(item.bhyt_dong_den_thang, 'YYYY-MM').format('MM/YYYY') : '-'}</Text>
      <Text style={[styles.tableCellText, styles.amountColumn]}>{currencyFormatter.formatCurrency(item.bhyt_so_tien_dong || item.so_tien)}</Text>
      <Text style={[styles.tableCellText, styles.statusColumn, getStatusStyle(item.trang_thai)]}>{item.trang_thai_ten}</Text>
    </View>
  );

  // Tính tổng số tháng
  const getTotalMonths = () => {
    if (selectedTab === 'BHXH') {
      return bienLaiListBHXH.reduce((total, item) => total + (item.bhtn_so_thang_dong || 0), 0);
    } else {
      return bienLaiListBHYT.reduce((total, item) => total + (item.bhyt_so_thang_dong || 0), 0);
    }
  };

  return (
    <Card style={styles.tableCard}>
      <View style={styles.tableTitleContainer}>
        <Text style={styles.tableTitle}>
          Quá trình tham gia {selectedTab} của {selectedPerson}
        </Text>
        <Text style={styles.tableSubTitle}>Tổng thời gian tham gia: {getTotalMonths()} tháng</Text>
      </View>

      {/* Table Header */}
      <View style={styles.tableHeader}>
        <Text style={[styles.tableHeaderText, styles.monthColumn]}>Từ tháng</Text>
        <Text style={[styles.tableHeaderText, styles.monthColumn]}>Đến tháng</Text>
        <Text style={[styles.tableHeaderText, styles.amountColumn]}>Số tiền</Text>
        <Text style={[styles.tableHeaderText, styles.statusColumn]}>Trạng thái</Text>
      </View>

      {/* Table Rows */}
      {currentBienLaiList.length > 0 ? (
        currentBienLaiList.map((item, index) => {
          if (selectedTab === 'BHXH') {
            return renderBHXHRow(item, index);
          } else {
            return renderBHYTRow(item, index);
          }
        })
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>
            Không có dữ liệu {selectedTab} cho {selectedPerson}
          </Text>
        </View>
      )}
    </Card>
  );
};

export default TransactionTable;
