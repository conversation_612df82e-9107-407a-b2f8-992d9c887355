import { colors, spacing, typography, shadows, borderRadius } from "@constants/theme";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  
  // Fixed Header Styles
  fixedHeader: {
    backgroundColor: colors.light,
    paddingHorizontal: spacing.sm,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  
  // Scrollable Content
  scrollableContent: {
    flex: 1,
    paddingHorizontal: spacing.sm,
  },
  
  // Person Selector
  selectorContainer: {
    marginTop: spacing.md,
    padding: spacing.sm
  },
  
  dropdownIcon: {
    width: 14,
    height: 14,
  },
  
  // Tabs theo design ảnh
  tabsContainer: {
    flexDirection: 'row',
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    gap: spacing.lg,
    justifyContent: 'flex-start',
  },
  
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
  },
  
  tabIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  
  activeTabIconContainer: {
    backgroundColor: 'transparent',
    borderColor: colors.green,
  },
  
  inactiveTabIconContainer: {
    backgroundColor: colors.white,
    borderColor: colors.gray[300],
  },
  
  tabIcon: {
    width: 24,
    height: 24,
  },
  
  activeTabIcon: {
    tintColor: colors.green,
  },
  
  inactiveTabIcon: {
    tintColor: colors.gray[600],
  },
  
  tabText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.gray[600],
  },
  
  activeTabText: {
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as any,
  },
  
  // Table
  tableCard: {
    marginBottom: spacing.lg,
    padding: spacing.md,
  },
  
  tableTitle: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontWeight: typography.fontWeight.medium as any,
    textAlign: 'center',
  },
  
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 2,
    borderBottomColor: colors.green,
    paddingBottom: spacing.sm,
    marginBottom: spacing.sm,
  },
  
  tableHeaderText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.green,
    textAlign: 'center',
  },
  
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingVertical: spacing.sm,
  },
  
  tableCellText: {
    fontSize: typography.fontSize.sm,
    color: colors.black,
    textAlign: 'center',
  },
  
  // Column widths
  monthColumn: {
    flex: 1,
  },
  
  amountColumn: {
    flex: 1,
  },
  
  statusColumn: {
    flex: 1,
  },
  
  paidStatus: {
    color: colors.primary,
    fontWeight: typography.fontWeight.semibold as any,
  },
  paidDoneStatus: {
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as any,
  },
  
  unpaidStatus: {
    color: colors.danger,
    fontWeight: typography.fontWeight.semibold as any,
  },
  onpaidStatus: {
    color: colors.warning,
    fontWeight: typography.fontWeight.semibold as any,
  },
  
  // Create Button
  createButton: {
    backgroundColor: colors.green,
    marginHorizontal: spacing.md,
    marginBottom: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    ...shadows.base,
  },
  
  createButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.white,
  },

  // Empty state
  emptyState: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },

  emptyStateText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[500],
    fontStyle: 'italic',
  },
  tableSubTitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    fontStyle: 'italic',
    textAlign: 'center',
  },
  tableTitleContainer: {
    gap: spacing.sm,
    marginBottom: spacing.md
  },

  // Biên lai styles
  paymentHistoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },

  title: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    fontWeight: typography.fontWeight.medium as any,
    flex: 1,
  },

  value: {
    fontSize: typography.fontSize.sm,
    color: colors.black,
    fontWeight: typography.fontWeight.normal as any,
    flex: 2,
    textAlign: 'right',
  },

  description: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    textAlign: 'center',
    padding: spacing.lg,
  },
});