import R from '@assets/R';
import {NGUON_MOBILE, PAGE_SIZE} from '@commons/Constant';
import {Button, Card, ConfirmModal, ScreenComponent, TextField} from '@components/common';
import ActionSheetModal, {ActionSheetOption} from '@components/common/ActionSheetModal';
import {ACTION_CODE} from '@constants/axios';
import {colors} from '@constants/theme';
import {MainNavigationProp} from '@navigation/types';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Image, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import TransactionTable from './Components/TransactionTable';
import {styles} from './transactionhistory.style';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';

interface HouseholdData {
  id: string;
  ownerName: string;
  address: string;
  type: string;
  memberCount: number;
}

interface TransactionHistoryRouteParams {
  bt_ho_gia_dinh: number;
  householdData?: HouseholdData;
}

interface TransactionHistoryScreenProps {
  route: RouteProp<any, any> & {
    params: TransactionHistoryRouteParams;
  };
}

interface TransactionData {
  month: string;
  endMonth: string;
  amount: string;
  status: 'paid' | 'unpaid';
}

interface MemberItem {
  ma_doi_tac: string;
  ma_chi_nhanh: string;
  nsd: string;
  pas: string;

  ngay_cap_nhat: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  nguoi_tao: string;

  bt: string;
  bt_ho_gia_dinh: string;
  tinh_thanh: string;
  phuong_xa: string;
  loai: string;
  cmt: string;
  dthoai: string;
  so_bhxh: string;
  ten: string;
  ngay_sinh: string;
  gioi_tinh: string;
  dia_chi: string;
  nd_tim: string;
  moi_qhe_ten: string;
}

interface BienLaiItem {
  so_bien_lai: string;
  ngay_lap_bien_lai: string;
  so_tien: string;
  don_vi_bhxh_ten: string;
  nguoi_tao: string;
  // BHXH fields
  bhtn_dong_tu_thang?: string;
  bhtn_dong_den_thang?: string;
  bhtn_so_tien_dong?: string;
  bhtn_so_thang_dong?: number;
  // BHYT fields
  bhyt_dong_tu_thang?: string;
  bhyt_dong_den_thang?: string;
  bhyt_so_tien_dong?: string;
  bhyt_so_thang_dong?: number;
  status?: 'paid' | 'unpaid';
  [key: string]: any; // Cho phép các field động
}

interface DanhSachBienLaiResponse {
  data: BienLaiItem[];
  tong_so_dong: number;
}

export default function TransactionHistoryScreen({route}: TransactionHistoryScreenProps) {
  logger.log('TransactionHistoryScreen');

  const navigation = useNavigation<MainNavigationProp>();
  const {bt_ho_gia_dinh, householdData} = route.params;
  const [selectedTab, setSelectedTab] = useState<'BHXH' | 'BHYT'>('BHXH');
  const [isShowConfirmModal, setIsShowConfirmModal] = useState(false);

  // States cho members từ API
  const [danhSachThanhVien, setDanhSachThanhVien] = useState<MemberItem[]>([]);
  const [loadingMembers, setLoadingMembers] = useState(false);
  const [currentPageMember, setCurrentPageMember] = useState(1);
  const [hasMoreMember, setHasMoreMember] = useState(true);

  // States cho biên lai
  const [bienLaiList, setBienLaiList] = useState<BienLaiItem[]>([]);
  const [loadingBienLai, setLoadingBienLai] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string>('');
  const [selectedMember, setSelectedMember] = useState<MemberItem | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Lấy danh sách thành viên từ API
  const getDanhSachThanhVien = useCallback(
    async (page: number, searchValue?: string) => {
      try {
        if (page === 1) {
          setLoadingMembers(true);
          setCurrentPageMember(1);
        }

        const params = {
          bt: '',
          bt_ho_gia_dinh: bt_ho_gia_dinh,
          tinh_thanh: '',
          phuong_xa: '',
          loai: '',
          cccd: '',
          dthoai: '',
          so_bhxh: '',
          ten: '',
          ngay_sinh: '',
          gioi_tinh: '',
          nd_tim: searchValue || '', // truyền vào nội dung tìm kiếm
          moi_qhe_ten: '',
          trang: page, // xử lý loadmore
          so_dong: PAGE_SIZE, //fix cứng ko cần thay đổi
          nguon: NGUON_MOBILE,
          actionCode: ACTION_CODE.GET_DS_THANH_VIEN,
        };
        const response = await getCommonExecute(params);
        const listThanhVien = response.data.data;

        if (page === 1) {
          setDanhSachThanhVien(listThanhVien);

          // Tự động chọn thành viên đầu tiên nếu có
          if (listThanhVien && listThanhVien.length > 0) {
            const firstMember = listThanhVien[0];
            setSelectedPerson(firstMember.ten || 'Không có tên');
            setSelectedMemberId(firstMember.bt);
            setSelectedMember(firstMember);
            setIsInitialLoad(false);

            // Gọi API getDanhSachBienLai với thành viên đầu tiên
            getDanhSachBienLai(firstMember.bt);
          }
        } else {
          setDanhSachThanhVien(prev => [...prev, ...listThanhVien]);
        }

        setCurrentPageMember(page);
        const requestedPageSize = PAGE_SIZE;
        setHasMoreMember(listThanhVien.length >= requestedPageSize);
      } catch (error) {
        logger.log('getDanhSachThanhVien ~ error:', error);
      } finally {
        setLoadingMembers(false);
      }
    },
    [bt_ho_gia_dinh],
  );

  // Lấy danh sách biên lai từ API
  const getDanhSachBienLai = useCallback(
    async (bt_tvien: string) => {
      if (!bt_tvien) return;

      try {
        setLoadingBienLai(true);

        const params = {
          loai: selectedTab,
          nsd_lap_bien_lai: '',
          trang_thai: '',
          so_bhxh: '',
          ten: '',
          cmt: '',
          nd_tim: '',
          bt_tvien: bt_tvien,
          trang: 1,
          so_dong: 1000,
          actionCode: ACTION_CODE.DANH_SACH_BIEN_LAI,
        };
        logger.log('🚀 ~ TransactionHistoryScreen ~ params:', params);

        const response = await getCommonExecute(params);
        logger.log('🚀 ~ TransactionHistoryScreen ~ getDanhSachBienLai ~ response:', response);

        if (response?.data) {
          const responseData: DanhSachBienLaiResponse = response.data;
          const newItems = responseData.data || [];
          setBienLaiList(newItems);
        }
      } catch (error) {
        logger.log('🚀 ~ getDanhSachBienLai ~ error:', error);
        setBienLaiList([]);
      } finally {
        setLoadingBienLai(false);
      }
    },
    [selectedTab],
  );

  // Tạo danh sách members từ household data
  const getHouseholdMembers = () => {
    if (!householdData) return ['Nguyễn Công Kỳ Anh'];

    const members = [householdData.ownerName]; // Chủ hộ
    const memberCount = householdData.memberCount || 1;

    for (let i = 1; i < memberCount; i++) {
      members.push(`Thành viên ${i + 1}`);
    }

    return members;
  };

  const people = getHouseholdMembers();
  const [selectedPerson, setSelectedPerson] = useState(people[0]);
  const [showPersonModal, setShowPersonModal] = useState(false);

  // Tách dữ liệu BHXH và BHYT từ bienLaiList
  const {bienLaiListBHXH, bienLaiListBHYT} = useMemo(() => {
    const bhxhList = bienLaiList.filter(item => {
      // Kiểm tra xem item có field nào bắt đầu với 'bhtn_' không
      return Object.keys(item).some(key => key.startsWith('bhtn_') && item[key] != null);
    });

    const bhytList = bienLaiList.filter(item => {
      // Kiểm tra xem item có field nào bắt đầu với 'bhyt_' không
      return Object.keys(item).some(key => key.startsWith('bhyt_') && item[key] != null);
    });

    return {
      bienLaiListBHXH: bhxhList,
      bienLaiListBHYT: bhytList,
    };
  }, [bienLaiList]);

  // Load danh sách thành viên lần đầu khi vào màn hình
  useEffect(() => {
    if (bt_ho_gia_dinh) {
      getDanhSachThanhVien(1);
    }
  }, [bt_ho_gia_dinh, getDanhSachThanhVien]);

  // Load danh sách thành viên khi mở modal (chỉ khi chưa có dữ liệu)
  useEffect(() => {
    if (showPersonModal && bt_ho_gia_dinh && danhSachThanhVien.length === 0) {
      getDanhSachThanhVien(1);
    }
  }, [showPersonModal, bt_ho_gia_dinh, getDanhSachThanhVien, danhSachThanhVien.length]);

  // Gọi API khi thay đổi tab (không gọi lần đầu khi selectedMemberId được set từ getDanhSachThanhVien)
  useEffect(() => {
    if (selectedMemberId && !isInitialLoad) {
      getDanhSachBienLai(selectedMemberId);
    }
  }, [selectedTab, selectedMemberId, getDanhSachBienLai, isInitialLoad]);

  // Chuyển đổi thành ActionSheetOption (fallback cho old data)
  const handlePersonSelect = (id: string | number, title: string, member?: MemberItem) => {
    setSelectedPerson(title);
    setSelectedMemberId(String(id));
    setSelectedMember(member || null);
    setShowPersonModal(false);
    setIsInitialLoad(false);

    // Gọi API lấy danh sách biên lai cho thành viên được chọn
    if (String(id) !== title) {
      // Chỉ gọi API khi có bt (id thực từ API)
      getDanhSachBienLai(String(id));
    }
  };

  // Transform danhSachThanhVien thành ActionSheetOption
  const memberOptions: ActionSheetOption[] = danhSachThanhVien.map(member => ({
    id: member.bt,
    title: member.ten || 'Không có tên',
    subtitle: `${member.so_bhxh ? `BHXH: ${member.so_bhxh}` : ''} ${member.cmt ? `| CCCD: ${member.cmt}` : ''}`.trim(),
    onPress: () => handlePersonSelect(member.bt, member.ten || 'Không có tên', member),
  }));

  const personOptions: ActionSheetOption[] = people.map(person => ({
    id: person, // Fallback sử dụng tên làm ID
    title: person,
    onPress: () => handlePersonSelect(person, person),
  }));

  const handleConfirm = () => {
    setIsShowConfirmModal(true);
  };

  // Render item biên lai
  const renderBienLai = ({item}: {item: BienLaiItem}) => {
    const renderLabel = (title: string, value: any, style?: any) => {
      return (
        <View style={styles.paymentHistoryRow}>
          <Text style={styles.title}>{title}</Text>
          <Text style={[styles.value, style]}>{value}</Text>
        </View>
      );
    };
    return (
      <Card>
        {renderLabel('Số biên lai', item.so_bien_lai, {fontWeight: 'bold', color: colors.primary})}
        {renderLabel('Ngày thu tiền', item.ngay_lap_bien_lai)}
        {renderLabel('Số tiền', item.so_tien, {fontWeight: 'bold', color: colors.warning})}
        {renderLabel('Đơn vị', item.don_vi_bhxh_ten)}
        {renderLabel('Cán bộ thu', item.nguoi_tao)}
      </Card>
    );
  };

  return (
    <ScreenComponent showHeader headerTitle="Lịch sử giao dịch" showBackButton onPressBack={() => navigation.goBack()} showFooter footer={<Button title="Tạo thu hộ" onPress={handleConfirm} />}>
      <View style={styles.container}>
        {/* Fixed Header - Person Selector */}
        <View style={styles.fixedHeader}>
          <View style={styles.selectorContainer}>
            <TouchableOpacity onPress={() => setShowPersonModal(true)} activeOpacity={1}>
              <View pointerEvents="none">
                <TextField label="Người tham gia" value={selectedPerson} onChangeText={() => {}} editable={false} required rightIconType="dropdown" placeholder="Chọn người tham gia" />
              </View>
            </TouchableOpacity>
          </View>

          {/* Fixed Tabs */}
          <View style={styles.tabsContainer}>
            <TouchableOpacity style={styles.tab} onPress={() => setSelectedTab('BHXH')}>
              <View style={[styles.tabIconContainer, selectedTab === 'BHXH' ? styles.activeTabIconContainer : styles.inactiveTabIconContainer]}>
                <Image source={R.icons.ic_bhxh} style={[styles.tabIcon, selectedTab === 'BHXH' ? styles.activeTabIcon : styles.inactiveTabIcon]} resizeMode="contain" />
              </View>
              <Text style={[styles.tabText, selectedTab === 'BHXH' && styles.activeTabText]}>BHXH</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.tab} onPress={() => setSelectedTab('BHYT')}>
              <View style={[styles.tabIconContainer, selectedTab === 'BHYT' ? styles.activeTabIconContainer : styles.inactiveTabIconContainer]}>
                <Image source={R.icons.ic_heart} style={[styles.tabIcon, selectedTab === 'BHYT' ? styles.activeTabIcon : styles.inactiveTabIcon]} resizeMode="contain" />
              </View>
              <Text style={[styles.tabText, selectedTab === 'BHYT' && styles.activeTabText]}>BHYT</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Scrollable Content - Only Transaction Table */}
        <ScrollView style={styles.scrollableContent} showsVerticalScrollIndicator={false}>
          <TransactionTable selectedTab={selectedTab} selectedPerson={selectedPerson} bienLaiListBHXH={bienLaiListBHXH} bienLaiListBHYT={bienLaiListBHYT} styles={styles} />
        </ScrollView>
      </View>

      {/* Person Selection Modal */}
      <ActionSheetModal
        isVisible={showPersonModal}
        onClose={() => setShowPersonModal(false)}
        title="Chọn thành viên"
        subtitle="Vui lòng chọn thành viên để xem lịch sử giao dịch"
        options={memberOptions.length > 0 ? memberOptions : personOptions}
        selectedValue={memberOptions.length > 0 ? selectedMemberId : selectedPerson}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm thành viên..."
        cancelButtonText="Hủy"
      />

      {/* Confirm Modal */}
      <ConfirmModal
        visible={isShowConfirmModal}
        onClose={() => setIsShowConfirmModal(false)}
        title="Tạo thu hộ"
        message={`Bạn có chắc chắn muốn tạo thu hộ ${selectedTab} cho ${selectedPerson}?`}
        confirmText="Tạo thu hộ"
        cancelText="Hủy"
        onConfirm={() => {
          setIsShowConfirmModal(false);
          NavigationUtil.push(MAIN_SCREENS.INFORMATION_PARTICIPANT, {
            memberData: selectedMember || {ten: selectedPerson, bt_ho_gia_dinh: bt_ho_gia_dinh},
            householdDetailData: householdData,
          });
        }}
        onCancel={() => setIsShowConfirmModal(false)}
        confirmVariant="success"
        type="info"
      />
    </ScreenComponent>
  );
}
