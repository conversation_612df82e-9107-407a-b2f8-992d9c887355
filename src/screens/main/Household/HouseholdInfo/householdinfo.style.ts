import { colors, spacing, typography, shadows } from "@constants/theme";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  
  // Section Header
  sectionHeader: {
    padding: spacing.md,
    backgroundColor: colors.light,
  },
  
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.gray[600],
  },
  
  // Card thông tin
  infoCard: {
    gap: spacing.sm,
  },
  
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    backgroundColor: colors.white,
  },
  
  infoLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontWeight: typography.fontWeight.normal as any,
    flex: 1,
  },
  
  infoValue: {
    fontSize: typography.fontSize.base,
    color: colors.black,
    fontWeight: typography.fontWeight.medium as any,
    flex: 1.2,
    textAlign: 'right',
  },
  
  lastRow: {
    borderBottomWidth: 0,
  },
  
  memberCount: {
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as any,
  },
  
  // Menu container
  cardContainer: {
    backgroundColor: colors.white,
    marginBottom: spacing.sm,
    paddingVertical: spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
  },
  
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingVertical: spacing.sm,
  },
  cardIcon: {
    width: 24,
    height: 24,
  },
  menuTitle: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.semibold as any,
  },
});