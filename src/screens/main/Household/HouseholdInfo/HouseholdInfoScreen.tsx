import {Image, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Card, ScreenComponent} from '@components/common';
import {RouteProp, useFocusEffect, useNavigation} from '@react-navigation/native';
import {MainNavigationProp, MainStackParamList} from '@navigation/types';
import {MAIN_SCREENS} from '@navigation/routes';
import {styles} from './householdinfo.style';

import R from '../../../../assets/R';
import {getCommonExecute} from '@services/endpoints';
import {ACTION_CODE} from '@constants/axios';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '@store/index';
import {fetchLoaiHoGiaDinh} from '@store/slices/commonSlice';

interface HouseholdDetailData {
  bt: number;
  cmt: string;
  dia_chi: string;
  dthoai: string;
  email: string;
  gioi_tinh: string;
  loai: string;
  ma_doi_tac: string;
  ngay_cap_nhat: string;
  ngay_sinh: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  nguoi_tao: string;
  phuong_xa: string;
  phuong_xa_ten: string;
  sl_tvien: number;
  sott: number;
  stt: number;
  ten: string;
  tinh_thanh: string;
  tinh_thanh_ten: string;
  trang_thai: string;
  trang_thai_ten: string;
}

interface HouseholdInfoScreenProps {
  route: RouteProp<MainStackParamList, 'HouseholdInfoScreen'> & {
    params: {
      householdId?: string;
    };
  };
}

export default function HouseholdInfoScreen({route}: HouseholdInfoScreenProps) {
  const navigation = useNavigation<MainNavigationProp>();
  const dispatch = useDispatch<AppDispatch>();
  const householdId = route?.params?.householdId;
  const [loading, setLoading] = useState(false);
  const [householdDetailData, setHouseholdDetailData] = useState<HouseholdDetailData | null>(null);
  const [needsRefresh, setNeedsRefresh] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const [hasLoadedInitialData, setHasLoadedInitialData] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  logger.log('🚀 ~ HouseholdInfoScreen ~ householdDetailData:', householdDetailData);
  const {danhSachLoaiHoGiaDinh} = useSelector((state: RootState) => state.commonCategories);

  const getChiTietHoGiaDinh = async (showDialogLoading = true) => {
    try {
      if (showDialogLoading) {
        setLoading(true);
      }

      const params = {
        ma: householdId, // b_ma - ID hộ gia đình
        actionCode: ACTION_CODE.GET_CHI_TIET_HO_GIA_DINH,
      };

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ getChiTietHoGiaDinh ~ response:', response);
      if (response.data) {
        setHouseholdDetailData(response.data);
      }
    } catch (error) {
      logger.log('getChiTietHoGiaDinh ~ error:', error);
    } finally {
      if (showDialogLoading) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    // Load danh sách loại hộ gia đình nếu chưa có
    if (danhSachLoaiHoGiaDinh.length === 0) {
      dispatch(fetchLoaiHoGiaDinh());
    }

    if (householdId) {
      getChiTietHoGiaDinh().then(() => {
        setHasLoadedInitialData(true);
      });
      setIsInitialMount(false); // Mark initial mount as complete
    }
  }, [householdId, dispatch, danhSachLoaiHoGiaDinh.length]);

  // Use useFocusEffect to refresh data when returning from edit screen
  useFocusEffect(
    useCallback(() => {
      logger.log('🔍 useFocusEffect triggered - needsRefresh:', needsRefresh, 'isInitialMount:', isInitialMount, 'isRefreshing:', isRefreshing);
      // Only refresh if we need to refresh and not on initial mount and not already refreshing
      if (needsRefresh && !isInitialMount && !isRefreshing) {
        logger.log('🔄 Refreshing household info after returning from form with dialog loading');
        setIsRefreshing(true);
        getChiTietHoGiaDinh(true)
          .then(() => {
            logger.log('🔄 getChiTietHoGiaDinh completed');
            // Reset flags sau khi API hoàn thành
            setNeedsRefresh(false);
            setIsRefreshing(false);
          })
          .catch(() => {
            setIsRefreshing(false);
          });
      }
    }, [needsRefresh, isInitialMount, isRefreshing]),
  );

  // Backup mechanism: Listen to needsRefresh changes (fallback nếu useFocusEffect không trigger)
  useEffect(() => {
    if (needsRefresh && !isInitialMount && hasLoadedInitialData && !isRefreshing) {
      logger.log('🔄 Backup refresh mechanism triggered for HouseholdInfo');
      // Delay để đảm bảo navigation đã hoàn thành và useFocusEffect có cơ hội chạy trước
      const timeoutId = setTimeout(() => {
        if (needsRefresh && !isRefreshing) {
          // Double check flags vẫn còn (nếu useFocusEffect đã chạy thì flag này sẽ là false)
          logger.log('🔄 Executing backup refresh with dialog loading (useFocusEffect missed)');
          setIsRefreshing(true);
          getChiTietHoGiaDinh(true)
            .then(() => {
              setNeedsRefresh(false);
              setIsRefreshing(false);
            })
            .catch(() => {
              setIsRefreshing(false);
            });
        } else {
          logger.log('🔄 Backup refresh skipped - useFocusEffect already handled or already refreshing');
        }
      }, 800);

      return () => clearTimeout(timeoutId);
    }
  }, [needsRefresh, isInitialMount, hasLoadedInitialData, isRefreshing]);

  const displayData = householdDetailData;

  const getTenLoaiHoGiaDinh = (loai: string | undefined) => {
    if (!loai) {
      return 'N/A';
    }
    const loaiHoGiaDinh = danhSachLoaiHoGiaDinh.find(item => item.ma === loai);
    const label = loaiHoGiaDinh?.ten || 'N/A';
    return label;
  };

  // Transform HouseholdDetailData to the expected format for TRANSACTION_HISTORY navigation
  const transformToTransactionHistoryData = (data: HouseholdDetailData | null) => {
    if (!data) {
      return undefined;
    }

    return {
      id: data.bt?.toString() || '',
      ownerName: data.ten || '',
      address: data.dia_chi || '',
      type: data.loai || '',
      memberCount: data.sl_tvien || 0,
    };
  };

  return (
    <ScreenComponent dialogLoading={loading} showHeader headerTitle="Thông tin hộ gia đình" showBackButton onPressBack={() => navigation.goBack()}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Section Header */}

        {/* Thông tin chi tiết */}
        <Card title="Thông tin chung" style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Loại hộ gia đình</Text>
            <Text style={styles.infoValue}>{getTenLoaiHoGiaDinh(displayData?.loai)}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Tỉnh/thành</Text>
            <Text style={styles.infoValue}>{displayData?.tinh_thanh_ten || '--'}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Phường/xã</Text>
            <Text style={styles.infoValue}>{displayData?.phuong_xa_ten || '--'}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Địa chỉ</Text>
            <Text style={styles.infoValue}>{displayData?.dia_chi || '--'}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Tên chủ hộ</Text>
            <Text style={styles.infoValue}>{displayData?.ten || '--'}</Text>
          </View>

          <View style={[styles.infoRow, styles.lastRow]}>
            <Text style={styles.infoLabel}>Số thành viên</Text>
            <Text style={[styles.infoValue, styles.memberCount]}>{displayData?.sl_tvien || '0'} người</Text>
          </View>
        </Card>

        {/* Menu Items */}
        <Card style={styles.cardContainer}>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate(MAIN_SCREENS.HOUSEHOLD_MEMBERS, {
                bt_ho_gia_dinh: displayData?.bt,
                householdData: displayData || undefined,
              });
            }}
            style={styles.cardRow}>
            <Image source={R.icons.ic_person} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Danh sách người trong hộ</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate(MAIN_SCREENS.HOUSEHOLD_FORM, {
                infoData: displayData,
                isEditHouseholdOnly: true,
                mode: 'edit',
                onUpdateInfo: (updatedData: any) => {
                  logger.log('🔄 onUpdateInfo callback triggered - setting needsRefresh to true');
                  logger.log('🔄 Current needsRefresh before set:', needsRefresh);
                  logger.log('Updated household data:', updatedData);
                  // Set flag để refresh khi quay lại màn hình
                  setNeedsRefresh(true);
                  logger.log('🔄 needsRefresh set to true');
                },
              });
            }}
            style={styles.cardRow}>
            <Image source={R.icons.ic_security} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Điều chỉnh thông tin hộ</Text>
          </TouchableOpacity>
          {/* <TouchableOpacity onPress={() => {}} style={styles.cardRow}>
            <Image source={R.icons.ic_list} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Quá trình xử lý</Text>
          </TouchableOpacity> */}
          <TouchableOpacity
            onPress={() =>
              navigation.navigate(MAIN_SCREENS.TRANSACTION_HISTORY, {
                bt_ho_gia_dinh: displayData?.bt,
                householdData: displayData || undefined,
              })
            }
            style={[styles.cardRow, {borderBottomWidth: 0}]}>
            <Image source={R.icons.ic_history} style={styles.cardIcon} resizeMode="contain" />
            <Text style={styles.menuTitle}>Lịch sử nộp tiền</Text>
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
