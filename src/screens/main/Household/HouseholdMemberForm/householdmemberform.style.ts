import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';
export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  noteContainer: {
    backgroundColor: '#C8E1DA',
    padding: spacing.sm,
    borderRadius: borderRadius.lg,
    marginVertical: spacing.sm,
  },
  noteText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
  },
  imageGroup: {
    flexDirection: 'row',
    gap: spacing.md,
    marginTop: spacing.md,
  },
  imageContainer: {
    flex: 1,
    gap: spacing.sm,
  },
  image: {
    width: '100%',
    height: 100,
  },
  imageText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    textAlign: 'center',
  },
  formContainer: {
    marginTop: spacing.lg,
  },
  formRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: typography.fontSize.base,
  },
  genderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.base,
    backgroundColor: colors.white,
    height: 56,
    width: '49%',
  },
  genderButton: {
    flexDirection: 'row',
    gap: spacing.sm,
    padding: spacing.sm,
    borderRadius: borderRadius.base,
    height: '100%',
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  genderButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.green,
  },
  genderIcon: {
    width: 18,
    height: 18,
    tintColor: colors.green,
  },
  footer: {
    flex: 1,
    marginBottom: spacing.sm,
    justifyContent: 'flex-end',
  },
  rightIcon: {
    width: 12,
    height: 12,
  },
  buttonGroup: {
    width: '100%',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  button: {
    flex: 1,
  },
  highlightField: {
    borderColor: colors.green,
    borderWidth: 2,
  },
});
