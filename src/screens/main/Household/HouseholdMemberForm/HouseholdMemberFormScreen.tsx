import R from '@assets/R';
import {<PERSON><PERSON>, Card, ConfirmModal, createToastHelpers, DateTimePickerComponent, ScreenComponent, TextField, useToast} from '@components/common';
import ActionSheetModal, {ActionSheetOption} from '../../../../components/common/ActionSheetModal';
import {ACTION_CODE} from '@constants/axios';
import {colors, spacing} from '@constants/theme';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp, MainStackParamList} from '@navigation/types';
import {RouteProp, useFocusEffect, useNavigation} from '@react-navigation/native';
import {getCommonExecute} from '@services/endpoints';
import {RootState, useAppSelector} from '@store/index';
import {formatDateForAPI, isMale, parseDateFromAPI, toApiGender} from '@utils/formatters';
import {householdFormValidation, householdMemberFormValidation, householdMemberValidation} from '@utils/validationSchemas';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Controller, useForm, useWatch} from 'react-hook-form';
import {BackHandler, Image, KeyboardAvoidingView, Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {styles} from './householdmemberform.style';
import {MOI_QUAN_HE_VOI_CHU_HO} from '@commons/Constant';
import NavigationUtil from '@navigation/NavigationUtil';
import {dimensions} from '@constants/dimensions';

interface LoadingState {
  isSubmitting: boolean;
}

interface FormData {
  so_bhxh: string;
  cmt: string;
  ten: string;
  gioi_tinh: boolean;
  ngay_sinh: Date | undefined;
  dthoai: string;
  email: string;
  moi_qhe: string;
}

type HouseHoldFormData = {
  loai: string; // Loại hộ gia đình
  tinh_thanh: string; // Mã tỉnh
  tinh_thanh_ten: string; // Tên tỉnh để hiển thị
  phuong_xa: string; // Mã xã
  phuong_xa_ten: string; // Tên xã để hiển thị
  dia_chi: string;
};

interface MemberData {
  ma_doi_tac: string;
  ma_chi_nhanh: string;
  nsd: string;
  pas: string;
  ma: string;
  bt_ho_gia_dinh: string;
  so_bhxh: string;
  cmt: string;
  ngay_sinh: string;
  gioi_tinh: string;
  moi_qhe: string;
  dthoai: string;
  email: string;
  ten: string;
  tinh_thanh?: string; // Mã tỉnh
  phuong_xa?: string; // Mã phường/xã
  dia_chi?: string; // Địa chỉ
  loai: string; // Loại hộ gia đình
  stt?: number;
  actionCode: string;
}

interface HouseholdMemberFormScreenProps {
  route: RouteProp<MainStackParamList, 'HouseholdMemberFormScreen'> & {
    params: {
      memberId?: string;
      bt_ho_gia_dinh: string | number;
      onMemberAdded?: () => void;
      onMemberUpdated?: () => void;
    };
  };
}

const relationshipOptions: ActionSheetOption[] = MOI_QUAN_HE_VOI_CHU_HO.map(item => ({
  id: item.value,
  title: item.label,
  onPress: () => {},
}));

export default function HouseholdMemberFormScreen({route}: HouseholdMemberFormScreenProps) {
  logger.log('🏠 HouseholdMemberFormScreen rendered');
  const navigation = useNavigation<MainNavigationProp>();
  const [selectedImage, setSelectedImage] = React.useState<'front' | 'back'>('front');
  const [householdDetailData, setHouseholdDetailData] = useState<any>(null);
  const [showRelationshipModal, setShowRelationshipModal] = useState(false);
  const [isShowConfirmModal, setIsShowConfirmModal] = useState(false);
  const [memberData, setMemberData] = useState<MemberData>();
  const [hasSuccessfulUpdate, setHasSuccessfulUpdate] = useState(false); // Track khi có update thành công
  logger.log('🚀 ~ HouseholdMemberFormScreen ~ memberData:', memberData);
  const {provinces, wards, loadingProvinces} = useAppSelector((state: RootState) => state.address);
  const [showHouseholdTypeModal, setShowHouseholdTypeModal] = useState(false);
  const [showProvinceModal, setShowProvinceModal] = useState(false);
  const [showWardModal, setShowWardModal] = useState(false);
  const [householdTypes, setHouseholdTypes] = useState<any[]>([]);

  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // LoadingState management
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isSubmitting: false,
  });

  const {
    control,
    setValue,
    getValues,
    watch,
    clearErrors,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData & HouseHoldFormData>({
    defaultValues: {
      so_bhxh: '',
      cmt: '',
      ten: '',
      gioi_tinh: true,
      ngay_sinh: undefined,
      dthoai: '',
      email: '',
      moi_qhe: '',

      loai: '',
      tinh_thanh: '',
      tinh_thanh_ten: '',
      phuong_xa: '',
      phuong_xa_ten: '',
      dia_chi: '',
    },
    mode: 'onChange',
  });

  const memberId = route?.params?.memberId;
  const {bt_ho_gia_dinh, onMemberAdded, onMemberUpdated} = route?.params;

  // Function để handle back button - gọi callback nếu có update thành công
  const handleBackPress = () => {
    logger.log('🔙 Back button pressed - hasSuccessfulUpdate:', hasSuccessfulUpdate);
    logger.log('🔙 memberId:', memberId, 'onMemberUpdated:', !!onMemberUpdated, 'onMemberAdded:', !!onMemberAdded);

    // Nếu có update thành công, gọi callback để refresh dữ liệu ở màn trước
    if (hasSuccessfulUpdate) {
      if (memberId && onMemberUpdated) {
        logger.log('🔄 Calling onMemberUpdated callback for memberId:', memberId);
        onMemberUpdated();
      } else if (!memberId && onMemberAdded) {
        logger.log('🔄 Calling onMemberAdded callback for new member');
        onMemberAdded();
      } else {
        logger.log('⚠️ No callback to call despite hasSuccessfulUpdate=true');
      }
    } else {
      logger.log('ℹ️ No successful update, not calling callback');
    }

    navigation.goBack();
  };

  const getChiTietHoGiaDinh = async (householdId: string) => {
    try {
      // setLoading(true);

      const params = {
        ma: householdId, // b_ma - ID hộ gia đình
        actionCode: ACTION_CODE.GET_CHI_TIET_HO_GIA_DINH,
      };

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ getChiTietHoGiaDinh ~ response:', response);
      if (response.data) {
        setHouseholdDetailData(response.data);
      }
    } catch (error) {
      logger.log('getChiTietHoGiaDinh ~ error:', error);
    }
  };

  // Lấy thông tin thành viên
  const getMemberData = useCallback(async () => {
    if (!memberId) {
      logger.log('❌ No memberId provided');
      return;
    }

    // Không hiện loading khi load member data
    try {
      const params = {
        ma: memberId,
        actionCode: ACTION_CODE.GET_CHI_TIET_THANH_VIEN,
      };
      const response = await getCommonExecute(params);
      if (response?.data && response.data.thanh_vien.length > 0) {
        setMemberData(response.data.thanh_vien[0]);
        getChiTietHoGiaDinh(response.data.thanh_vien[0].bt_ho_gia_dinh);
      } else {
        toast.error('Không tìm thấy thông tin thành viên', {
          duration: 2000,
          position: 'top',
        });
        setTimeout(() => {
          navigation.goBack();
        }, 2000);
      }
    } catch (error) {
      logger.log('🚀 ~ getMemberData ~ error:', error);
      toast.error('Lỗi khi tải thông tin thành viên', {
        duration: 3000,
        position: 'top',
      });
      navigation.goBack();
    } finally {
      // Không cần reset loading state
    }
  }, [memberId, navigation]);

  const selectedProvince = useWatch({control, name: 'tinh_thanh'});
  const selectedWard = useWatch({control, name: 'phuong_xa'});
  const provinceName = useWatch({control, name: 'tinh_thanh_ten'});
  const wardName = useWatch({control, name: 'phuong_xa_ten'});
  useEffect(() => {
    if (selectedProvince && !provinceName && provinces.length > 0) {
      const provinceData = provinces.find(p => p.ma === selectedProvince);
      if (provinceData) {
        setValue('tinh_thanh_ten', provinceData.ten);
      }
    }
    if (selectedWard && !wardName && wards.length > 0) {
      const wardData = wards.find((w: any) => w.ma === selectedWard);
      if (wardData) {
        setValue('phuong_xa_ten', wardData.ten);
      }
    }
  }, [selectedProvince, provinceName, selectedWard, wardName, provinces, wards, setValue]);

  useEffect(() => {
    if (memberId) {
      getMemberData();
    }
    getHouseholdType();
  }, [memberId]);

  // Handle Android back button
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        logger.log('📱 Android back button pressed');
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [handleBackPress]),
  );

  // Lấy danh sách loại hộ gia đình
  const getHouseholdType = useCallback(async () => {
    // Không hiện loading khi load household types
    try {
      const response = await getCommonExecute({
        actionCode: ACTION_CODE.GET_LOAI_HO_GIA_DINH,
      });
      const listHouseholdType = response.data;
      if (listHouseholdType && Array.isArray(listHouseholdType)) {
        setHouseholdTypes(listHouseholdType);
      } else {
        toast.warning('Không tải được danh sách loại hộ gia đình', {
          duration: 3000,
          position: 'top',
        });
      }
    } catch (error) {
      console.error('❌ ~ Error fetching household types:', error);
      setHouseholdTypes([]);
      toast.error('Lỗi khi tải danh sách loại hộ gia đình', {
        duration: 3000,
        position: 'top',
      });
    } finally {
      // Không cần reset loading state
    }
  }, []);

  const handleProvinceSelect = (value: string, label: string) => {
    setValue('tinh_thanh', value);
    setValue('tinh_thanh_ten', label);
    setValue('phuong_xa', '');
    setValue('phuong_xa_ten', '');
    clearErrors('tinh_thanh_ten'); // Clear error when selected
    setShowProvinceModal(false);
  };

  const handleWardSelect = (value: string, label: string) => {
    setValue('phuong_xa', value);
    setValue('phuong_xa_ten', label);
    clearErrors('phuong_xa_ten'); // Clear error when selected
    setShowWardModal(false);
  };

  const handleHouseholdTypeSelect = (value: string, label: string) => {
    setValue('loai', label); // Lưu tên thay vì mã để hiển thị
    clearErrors('loai'); // Clear error when selected
    setShowHouseholdTypeModal(false);
  };

  const handleRelationshipSelect = (value: string, label: string) => {
    setValue('moi_qhe', value);
    setShowRelationshipModal(false);
  };

  // Watch các giá trị cần thiết
  const watchedGender = watch('gioi_tinh');
  const watchedRelationship = watch('moi_qhe');
  const selectedHouseholdType = useWatch({control, name: 'loai'});
  const selectedRelationshipLabel = relationshipOptions.find(opt => opt.id === watchedRelationship)?.title || '';

  // Sử dụng watched values thay vì memberData trực tiếp
  const selectedGender = watchedGender;
  const selectedRelationship = watchedRelationship;

  // Autofill form khi có memberData và tất cả reference data đã load
  useEffect(() => {
    if (memberData) {
      // Map đúng fields từ API response
      setValue('so_bhxh', memberData?.so_bhxh || '');
      setValue('cmt', memberData?.cmt || '');
      setValue('ten', memberData?.ten || '');
      setValue('gioi_tinh', isMale(memberData?.gioi_tinh));

      // Parse ngày sinh thành Date object
      const parsedDate = parseDateFromAPI(memberData?.ngay_sinh || '');
      setValue('ngay_sinh', parsedDate);

      setValue('dthoai', memberData?.dthoai || '');
      setValue('email', memberData?.email || '');

      // Địa chỉ
      setValue('dia_chi', memberData?.dia_chi || '');

      // Đối với relationship, cần map value từ API sang đúng value trong options
      logger.log('🚀 ~ Mapping relationship:', {
        apiValue: memberData?.moi_qhe,
        availableOptions: relationshipOptions.map(opt => opt.id),
        constants: MOI_QUAN_HE_VOI_CHU_HO.map(item => item.value),
      });

      const relationshipValue = relationshipOptions.find(opt => opt.id === memberData?.moi_qhe)?.id;
      setValue('moi_qhe', relationshipValue || '');

      logger.log('🚀 ~ Final relationship value set:', relationshipValue);
      // Map loại hộ gia đình từ mã sang tên (nếu householdTypes đã load)
      if (memberData?.loai && householdTypes.length > 0) {
        const householdTypeData = householdTypes.find(type => {
          const typeId = type.ma || type.id || type.value;
          return typeId === memberData.loai;
        });

        if (householdTypeData) {
          const typeName = householdTypeData.ten || householdTypeData.name || householdTypeData.label || '';
          setValue('loai', typeName);
        }
      }

      // Map tỉnh/thành phố từ mã sang tên (nếu provinces đã load)
      if (memberData?.tinh_thanh && provinces.length > 0) {
        const provinceData = provinces.find(p => p.ma === memberData.tinh_thanh);
        if (provinceData) {
          setValue('tinh_thanh', memberData.tinh_thanh);
          setValue('tinh_thanh_ten', provinceData.ten);
        }
      }

      // Map phường/xã từ mã sang tên (nếu wards đã load)
      if (memberData?.phuong_xa && wards.length > 0) {
        const wardData = wards.find((w: any) => w.ma === memberData.phuong_xa);
        if (wardData) {
          setValue('phuong_xa', memberData.phuong_xa);
          setValue('phuong_xa_ten', wardData.ten);
        }
      }
    }
  }, [memberData, householdTypes, provinces, wards, setValue]);

  const householdTypeOptions: ActionSheetOption[] = useMemo(
    () =>
      householdTypes.map(type => ({
        id: type.ma || type.id || type.value,
        title: type.ten || type.name || type.label || 'Không xác định',
        onPress: () => handleHouseholdTypeSelect(type.ma || type.id || type.value, type.ten || type.name || type.label || 'Không xác định'),
      })),
    [householdTypes],
  );

  const provinceOptions: ActionSheetOption[] = useMemo(
    () =>
      provinces.map(province => ({
        id: province.ma,
        title: province.ten,
        onPress: () => handleProvinceSelect(province.ma, province.ten),
      })),
    [provinces],
  );

  const wardOptions: ActionSheetOption[] = useMemo(() => {
    if (!selectedProvince) {
      return [];
    }
    // Lọc phường/xã theo mã tỉnh
    return wards
      .filter((w: any) => w.ma_tinh === selectedProvince)
      .map((w: any) => ({
        id: w.ma,
        title: w.ten,
        onPress: () => handleWardSelect(w.ma, w.ten),
      }));
  }, [selectedProvince, wards]);

  // Cập nhật relationshipOptions để có onPress
  const relationshipOptionsWithHandlers: ActionSheetOption[] = useMemo(
    () =>
      relationshipOptions.map(option => ({
        ...option,
        onPress: () => handleRelationshipSelect(option.id as string, option.title),
      })),
    [],
  );

  const createCollection = () => {
    setIsShowConfirmModal(true);
  };

  // Thêm thành viên mới
  const addMemberData = async () => {
    const formData = getValues();

    // Tìm mã loại hộ gia đình từ tên được chọn
    const selectedHouseholdTypeData = householdTypes.find(type => (type.ten || type.name || type.label) === formData.loai);
    const householdTypeCode = selectedHouseholdTypeData?.ma || selectedHouseholdTypeData?.id || selectedHouseholdTypeData?.value || '';

    try {
      const params = {
        bt: '', // Empty for new member
        bt_ho_gia_dinh: bt_ho_gia_dinh,
        so_bhxh: formData.so_bhxh || '',
        ten: formData.ten?.trim() || '',
        cmt: formData.cmt?.trim() || '',
        ngay_sinh: formData.ngay_sinh ? formatDateForAPI(formData.ngay_sinh) : '',
        gioi_tinh: toApiGender(formData.gioi_tinh),
        tinh_thanh: formData.tinh_thanh || '',
        phuong_xa: formData.phuong_xa || '',
        dia_chi: formData.dia_chi?.trim() || '',
        email: formData.email?.trim() || '',
        dthoai: formData.dthoai?.trim() || '',
        moi_qhe: formData.moi_qhe || '',
        loai: householdTypeCode,
        stt: 0,
        actionCode: ACTION_CODE.UPDATE_THANH_VIEN, // Sử dụng cùng action code
      };
      logger.log('🚀 ~ addMemberData ~ params:', params);

      const response = await getCommonExecute(params);
      return response;
    } catch (error) {
      logger.log('🚀 ~ addMemberData ~ error:', error);
      throw error;
    }
  };

  // Lưu thông tin thành viên
  const updateMemberData = async () => {
    // Lấy dữ liệu từ form
    const formData = getValues();

    // Tìm mã loại hộ gia đình từ tên được chọn
    const selectedHouseholdTypeData = householdTypes.find(type => (type.ten || type.name || type.label) === formData.loai);
    const householdTypeCode = selectedHouseholdTypeData?.ma || selectedHouseholdTypeData?.id || selectedHouseholdTypeData?.value || '';

    try {
      const params = {
        // API parameters theo đúng format procedure 2TR5XDS21DJS1TX
        ma_doi_tac_nsd: memberData?.ma_doi_tac || '',
        ma_chi_nhanh_nsd: memberData?.ma_chi_nhanh || '',
        nsd: memberData?.nsd || '',
        pas: memberData?.pas || '',

        bt: memberId ? parseInt(memberId) : '', // Member ID as number
        bt_ho_gia_dinh: memberData?.bt_ho_gia_dinh ? parseInt(memberData.bt_ho_gia_dinh) : 0 || bt_ho_gia_dinh, // Household ID
        so_bhxh: formData.so_bhxh || '',
        ten: formData.ten?.trim() || '',
        cmt: formData.cmt?.trim() || '',
        ngay_sinh: formData.ngay_sinh ? formatDateForAPI(formData.ngay_sinh) : '',
        gioi_tinh: toApiGender(formData.gioi_tinh),
        tinh_thanh: formData.tinh_thanh || '', // Mã tỉnh
        phuong_xa: formData.phuong_xa || '', // Mã phường
        dia_chi: formData.dia_chi?.trim() || '',
        email: formData.email?.trim() || '',
        dthoai: formData.dthoai?.trim() || '',
        moi_qhe: formData.moi_qhe || '',
        stt: memberData?.stt || 0, // STT as number

        actionCode: ACTION_CODE.UPDATE_THANH_VIEN,
      };
      logger.log('🚀 ~ updateMemberData ~ params:', params);

      const response = await getCommonExecute(params);
      logger.log('🚀 ~ updateMemberData ~ response:', response);
      return response;
    } catch (error) {
      logger.log('🚀 ~ updateMemberData ~ error:', error);
      throw error;
    }
  };

  // Function chung để xử lý cả thêm và cập nhật
  const handleSaveMember = async () => {
    // Prevent concurrent submissions
    if (loadingState.isSubmitting) {
      return;
    }

    setLoadingState(prev => ({...prev, isSubmitting: true}));

    try {
      let response;
      if (memberId) {
        // Cập nhật thành viên hiện có
        response = await updateMemberData();
      } else {
        // Thêm thành viên mới
        response = await addMemberData();
      }

      // Kiểm tra response success theo cấu trúc API
      if (response?.success || response?.data) {
        logger.log('✅ Save successful - response:', response?.success || response?.data);

        // Hiển thị toast success trước
        const successMessage = memberId ? 'Cập nhật thành viên thành công' : 'Thêm thành viên thành công';
        logger.log('🎉 Showing success toast:', successMessage);
        toast.success(successMessage, {
          position: 'top',
        });

        // Set flag để biết có update thành công, callback sẽ được gọi khi user ấn back
        setHasSuccessfulUpdate(true);
      } else {
        logger.log('Lỗi', `${memberId ? 'Cập nhật' : 'Thêm thành viên'} thất bại: ${response?.message || 'Lỗi không xác định'}`);
        toast.error(`${memberId ? 'Cập nhật' : 'Thêm thành viên'} thất bại`, {
          duration: 3000,
          position: 'top',
        });
      }
    } catch (error: any) {
      logger.log('🚀 ~ handleSaveMember ~ error:', error);
      toast.error(`Lỗi khi ${memberId ? 'cập nhật' : 'thêm thành viên'}: ${error?.message || error}`, {
        duration: 3000,
        position: 'top',
      });
    } finally {
      setLoadingState(prev => ({...prev, isSubmitting: false}));
    }
  };

  const onUpdateInfo = () => {
    handleSaveMember();
  };

  const renderButtonFooter = () => {
    const buttonTitle = memberId ? 'Cập nhật' : 'Thêm thành viên';
    return (
      <View style={styles.buttonGroup}>
        <Button title={buttonTitle} onPress={handleSubmit(onUpdateInfo)} style={styles.button} loading={loadingState.isSubmitting} disabled={loadingState.isSubmitting} />
        {memberId && <Button title="Tạo thu hộ" onPress={createCollection} style={styles.button} loading={false} />}
      </View>
    );
  };

  const headerTitle = memberId ? 'Cập nhật thông tin thành viên' : 'Thêm thành viên mới';

  return (
    <ScreenComponent showHeader headerTitle={headerTitle} showBackButton onPressBack={handleBackPress} showFooter footer={renderButtonFooter()}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0} enabled={true}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: spacing.lg}}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}>
          <Card title="Thông tin người tham gia">
            {/* <View style={styles.noteContainer}>
            <Text style={styles.noteText}>Chụp/tải ảnh giấy tờ tùy thân để tự động nhập thông tin</Text>
          </View>
          <View style={styles.imageGroup}>
            <TouchableOpacity style={styles.imageContainer} onPress={handleTapImage}>
              <Image source={R.images.img_cccd} style={styles.image} />
              <Text style={styles.imageText}>Mặt trước</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageContainer} onPress={handleTapImage}>
              <Image source={R.images.img_cccd} style={styles.image} />
              <Text style={styles.imageText}>Mặt sau</Text>
            </TouchableOpacity>
          </View> */}
            <View style={styles.formContainer}>
              <View style={styles.formRow}>
                {/* Mã số BHXH */}
                <TextField
                  label="Mã BHXH"
                  placeholder="Nhập mã BHXH"
                  containerStyle={styles.input}
                  control={control}
                  name="so_bhxh"
                  rules={householdMemberFormValidation.codeBHXH}
                  error={errors.so_bhxh?.message}
                />
                {/* Số cccd */}
                <TextField
                  label="Số CCCD"
                  placeholder="Nhập số CCCD"
                  containerStyle={styles.input}
                  required
                  control={control}
                  name="cmt"
                  rules={householdMemberFormValidation.cccd}
                  error={errors.cmt?.message}
                />
              </View>
              <TextField label="Họ và tên" placeholder="Nhập họ và tên" required control={control} name="ten" rules={householdMemberFormValidation.name} error={errors.ten?.message} />
              <View style={styles.formRow}>
                {/* Giới tính */}
                <View style={styles.genderContainer}>
                  <TouchableOpacity style={[styles.genderButton, {backgroundColor: selectedGender ? colors.green : colors.white}]} onPress={() => setValue('gioi_tinh', true)}>
                    <Image source={R.icons.ic_male} style={[styles.genderIcon, {tintColor: selectedGender ? colors.white : colors.green}]} resizeMode="contain" />
                    <Text style={[styles.genderButtonText, {color: selectedGender ? colors.white : colors.green}]}>Nam</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.genderButton, {backgroundColor: selectedGender ? colors.white : colors.green}]} onPress={() => setValue('gioi_tinh', false)}>
                    <Image source={R.icons.ic_female} style={[styles.genderIcon, {tintColor: selectedGender ? colors.green : colors.white}]} resizeMode="contain" />
                    <Text style={[styles.genderButtonText, {color: selectedGender ? colors.green : colors.white}]}>Nữ</Text>
                  </TouchableOpacity>
                </View>
                {/* Ngày sinh */}
                <DateTimePickerComponent
                  label="Ngày sinh"
                  control={control}
                  name="ngay_sinh"
                  required
                  rules={householdMemberFormValidation.dateOfBirth}
                  error={errors?.ngay_sinh?.message}
                  containerStyle={styles.input}
                  maximumDate={new Date()}
                  minimumDate={new Date(1900, 0, 1)}
                />
              </View>
              <View style={styles.formRow}>
                {/* Tỉnh/Thành phố Dropdown */}
                <TouchableOpacity onPress={() => setShowProvinceModal(true)} activeOpacity={1} style={{flex: 1}} disabled={loadingProvinces}>
                  <View pointerEvents="none">
                    <TextField
                      label="Tỉnh/Thành phố"
                      labelStyle={errors.tinh_thanh ? {color: colors.danger} : undefined}
                      inputContainerStyle={errors.tinh_thanh ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                      required
                      control={control}
                      name="tinh_thanh_ten"
                      rightIconType={'dropdown'}
                      placeholder="Chọn tỉnh/thành phố"
                      rules={householdMemberFormValidation.provinceName}
                      error={errors.tinh_thanh?.message}
                    />
                  </View>
                </TouchableOpacity>

                {/* Phường/Xã Dropdown */}
                <TouchableOpacity onPress={() => setShowWardModal(true)} activeOpacity={1} style={{flex: 1}}>
                  <View pointerEvents="none">
                    <TextField
                      label="Phường/Xã"
                      inputContainerStyle={errors.phuong_xa ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                      labelStyle={errors.phuong_xa ? {color: colors.danger} : undefined}
                      required
                      control={control}
                      name="phuong_xa_ten"
                      rightIconType={'dropdown'}
                      placeholder="Chọn phường/xã"
                      rules={householdMemberFormValidation.wardName as any}
                      error={errors.phuong_xa?.message}
                    />
                  </View>
                </TouchableOpacity>
              </View>
              {/* Địa chỉ */}
              <TextField
                label="Địa chỉ"
                placeholder="Nhập địa chỉ"
                style={styles.input}
                required
                control={control}
                name="dia_chi"
                rules={householdMemberFormValidation.address}
                error={errors.dia_chi?.message}
              />
              <View style={styles.formRow}>
                {/* Số điện thoại */}
                <TextField
                  required
                  label="Điện thoại"
                  placeholder="Nhập số điện thoại"
                  containerStyle={styles.input}
                  control={control}
                  name="dthoai"
                  rules={householdFormValidation.householdOwnerPhone as any}
                  error={errors.dthoai?.message}
                />
                {/* Email */}
                <TextField
                  label="Email"
                  keyboardType={'email-address'}
                  placeholder="Nhập email"
                  containerStyle={styles.input}
                  control={control}
                  name="email"
                  rules={householdFormValidation.householdOwnerEmail as any}
                  // error={errors.email?.message}
                />
              </View>
              {/* Mối quan hệ với chủ hộ Dropdown */}
              <Controller
                control={control}
                name="moi_qhe"
                rules={householdMemberFormValidation.relationship as any}
                render={({field}) => (
                  <TouchableOpacity onPress={() => setShowRelationshipModal(true)} activeOpacity={1}>
                    <View pointerEvents="none">
                      <TextField
                        label="Mối q.hệ với chủ hộ"
                        style={styles.input}
                        required
                        inputContainerStyle={errors.moi_qhe ? {borderColor: colors.danger, borderWidth: 1} : undefined}
                        labelStyle={errors.moi_qhe ? {color: colors.danger} : undefined}
                        rightIconType="dropdown"
                        placeholder="Chọn mối quan hệ"
                        value={selectedRelationshipLabel}
                        onChangeText={field.onChange} // Empty function vì editable={false}
                        rules={householdMemberValidation.relationship}
                        error={errors.moi_qhe?.message}
                      />
                    </View>
                  </TouchableOpacity>
                )}
              />
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
      {/* Relationship Selection Modal */}
      <ActionSheetModal
        isVisible={showRelationshipModal}
        onClose={() => setShowRelationshipModal(false)}
        title="Chọn mối quan hệ với chủ hộ"
        subtitle="Vui lòng chọn mối quan hệ với chủ hộ gia đình"
        options={relationshipOptionsWithHandlers}
        selectedValue={selectedRelationship}
        showSearchField={false}
        cancelButtonText="Hủy"
      />

      {/* Confirm Modal */}
      <ConfirmModal
        visible={isShowConfirmModal}
        onClose={() => setIsShowConfirmModal(false)}
        title="Tạo thu hộ"
        message={`Bạn có chắc chắn muốn tạo thu hộ cho ${getValues()?.ten} không?`}
        confirmText="Tạo thu hộ"
        cancelText="Hủy"
        onConfirm={() => {
          setIsShowConfirmModal(false);
          NavigationUtil.push(MAIN_SCREENS.INFORMATION_PARTICIPANT, {
            memberData: memberData,
            householdDetailData: householdDetailData,
          });
        }}
        onCancel={() => setIsShowConfirmModal(false)}
        confirmVariant="success"
        type="info"
      />

      <ActionSheetModal
        isVisible={showProvinceModal}
        onClose={() => setShowProvinceModal(false)}
        title="Chọn tỉnh/thành phố"
        subtitle="Vui lòng chọn tỉnh/thành phố nơi bạn sinh sống"
        options={provinceOptions}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm tỉnh/thành phố..."
        cancelButtonText="Hủy"
        selectedValue={selectedProvince}
        containerStyle={{height: dimensions.height * 0.75}}
      />

      <ActionSheetModal
        isVisible={showWardModal}
        onClose={() => setShowWardModal(false)}
        title="Chọn phường/xã"
        subtitle="Vui lòng chọn phường/xã nơi bạn sinh sống"
        options={wardOptions}
        showSearchField={true}
        searchPlaceholder="Tìm kiếm phường/xã..."
        cancelButtonText="Hủy"
        selectedValue={selectedWard}
      />

      <ActionSheetModal
        isVisible={showHouseholdTypeModal}
        onClose={() => setShowHouseholdTypeModal(false)}
        title="Chọn loại hộ gia đình"
        subtitle="Vui lòng chọn loại hộ gia đình phù hợp"
        options={householdTypeOptions}
        selectedValue={
          householdTypes.find(type => (type.ten || type.name || type.label) === selectedHouseholdType)?.ma ||
          householdTypes.find(type => (type.ten || type.name || type.label) === selectedHouseholdType)?.id
        }
        showSearchField={true}
        searchPlaceholder="Tìm kiếm loại hộ gia đình..."
        cancelButtonText="Hủy"
      />
    </ScreenComponent>
  );
}
