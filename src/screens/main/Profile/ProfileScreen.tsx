import React, {useState} from 'react';
import {ActivityIndicator, Alert, Image, Modal, ScrollView, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {styles} from './Styles';

import R from '@assets/R';
import {Card, Icon, ScreenComponent} from '@components/common';
import {useToast} from '@components/common/Toast';
import {config} from '@constants/config';
import {colors} from '@constants/theme';
import {MAIN_SCREENS} from '@navigation/routes';
import {MainNavigationProp} from '@navigation/types';
import {useNavigation} from '@react-navigation/native';
import {HotUpdateInfo, hotUpdateService} from '@services/hotUpdate';
import {useAppDispatch, useAppSelector} from '@store/index';
import {logout, setDeletionRequest} from '@store/slices/authSlice';

const getSections = ({navigation, handleLogout, handleCheckUpdate, isCheckingUpdate, user, handleRemoveAccount}: any) => [
  // {
  //   id: 1,
  //   icon: R.icons.ic_bold_chart,
  //   title: '<PERSON><PERSON><PERSON> sử bán bảo hiểm',
  //   iconColor: '#FE6132',
  //   onPress: () => {
  //     navigation.navigate(MAIN_SCREENS.INSURANCE_SALES_HISTORY);
  //   },
  // },
  {
    id: 2,
    icon: R.icons.ic_person,
    title: 'Thông tin cá nhân',
    iconColor: '#5EDE99',
    onPress: () => {
      navigation.navigate(MAIN_SCREENS.PERSONAL_INFO);
    },
  },
  // {
  //   id: 3,
  //   icon: R.icons.ic_bold_lock,
  //   title: 'Thay đổi mật khẩu',
  //   iconColor: '#33CBD5',
  //   onPress: () => {
  //     navigation.navigate(MAIN_SCREENS.CHANGE_PASSWORD);
  //   },
  // },
  // {
  //   id: 4,
  //   icon: R.icons.ic_change,
  //   title: 'Cập nhật ứng dụng',
  //   iconColor: '#FF9500',
  //   onPress: handleCheckUpdate,
  //   loading: isCheckingUpdate,
  // },
  // {
  //   id: 4,
  //   icon: R.icons.ic_cards,
  //   title: 'Thông tin tài khoản ngân hàng',
  //   iconColor: '#EFBE24',
  //   onPress: () => NavigationUtil.push(MAIN_SCREENS.BANK_ACCOUNT, {user}),
  // },
  // {
  //   id: 5,
  //   icon: R.icons.ic_share,
  //   title: 'Chia sẻ với bạn bè',
  //   iconColor: '#BD66F2',
  //   onPress: () => {},
  // },

  {
    id: 6,
    icon: R.icons.ic_trash_bold,
    title: 'Xoá tài khoản',
    iconColor: colors.danger,
    onPress: handleRemoveAccount,
  },
  {
    id: 7,
    icon: R.icons.ic_logout,
    title: 'Đăng xuất',
    iconColor: '#F26666',
    onPress: handleLogout,
  },
];

export const ProfileScreen = () => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation<MainNavigationProp>();
  const user = useAppSelector(state => state.auth.user);
  const deletionRequest = useAppSelector(state => state.auth.deletionRequest);
  const {showToast} = useToast();

  // Hot Update states
  const [isCheckingUpdate, setIsCheckingUpdate] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<HotUpdateInfo | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  // Hot Update functions

  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [emailInput, setEmailInput] = useState(user?.email || '');

  const handleCheckUpdate = async () => {
    setIsCheckingUpdate(true);
    try {
      logger.log('Checking for hot updates...');
      const updateInfo = await hotUpdateService.checkForUpdates();

      if (updateInfo) {
        logger.log('Update available:', updateInfo);
        setUpdateInfo(updateInfo);
        setShowUpdateModal(true);
      } else {
        logger.log('No updates available');
        showToast({
          type: 'info',
          message: 'Không có bản cập nhật mới. Ứng dụng của bạn đã là phiên bản mới nhất',
        });
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
      showToast({
        type: 'error',
        message: 'Lỗi kiểm tra cập nhật. Không thể kiểm tra bản cập nhật. Vui lòng thử lại sau.',
      });
    } finally {
      setIsCheckingUpdate(false);
    }
  };

  const handleDownloadUpdate = async () => {
    if (!updateInfo) return;

    setIsDownloading(true);
    setDownloadProgress(0);

    try {
      logger.log('Starting hot update download...');

      await hotUpdateService.downloadAndInstall(updateInfo, {
        progress: (received: number, total: number) => {
          const progressPercent = Math.round((received / total) * 100);
          logger.log('Download progress:', progressPercent + '%');
          setDownloadProgress(progressPercent);
        },
      });

      logger.log('Hot update download completed');
      setShowUpdateModal(false);

      // Show restart confirmation
      Alert.alert(
        'Cập nhật thành công',
        'Ứng dụng sẽ được khởi động lại để áp dụng bản cập nhật mới.',
        [
          {
            text: 'Khởi động lại',
            onPress: () => {
              hotUpdateService.resetApp();
            },
          },
        ],
        {cancelable: false},
      );
    } catch (error) {
      console.error('Error downloading update:', error);
      showToast({
        type: 'error',
        message: 'Lỗi tải cập nhật. Không thể tải bản cập nhật. Vui lòng thử lại sau.',
      });
    } finally {
      setIsDownloading(false);
      setDownloadProgress(0);
    }
  };

  const handleLogout = () => {
    Alert.alert('Đăng xuất', 'Bạn có chắc chắn muốn đăng xuất?', [
      {text: 'Hủy', style: 'cancel'},
      {
        text: 'Đăng xuất',
        style: 'destructive',
        onPress: () => {
          dispatch(logout());
        },
      },
    ]);
  };

  const handleRemoveAccount = () => {
    setIsDeleteModalVisible(true);
  };

  const handleConfirmDelete = () => {
    const currentTime = Date.now();
    if (deletionRequest && deletionRequest.email === emailInput && currentTime - deletionRequest.timestamp < 48 * 60 * 60 * 1000) {
      setIsDeleteModalVisible(false);
      setTimeout(() => {
        showToast({
          type: 'warning',
          message: 'Yêu cầu xoá tài khoản đã được gửi đi và đang chờ phê duyệt. Vui lòng thử lại trong 48 giờ tiếp theo',
        });
      }, 500);
      return;
    }
    if (emailInput !== user?.email) {
      Alert.alert('Thông báo', 'Email không trùng với email đăng nhập');
    } else {
      dispatch(setDeletionRequest({email: emailInput, timestamp: currentTime}));
      setIsSubmitting(true);
      setTimeout(() => {
        Alert.alert('Thông báo', 'Yêu cầu xoá tài khoản ' + emailInput + ' đang được phê duyệt. Chúng tôi sẽ xử lý yêu cầu trong vòng 48 giờ. Vui lòng đợi trong thời gian chúng tôi xử lý', [
          {
            text: 'OK',
            onPress: () => {
              setIsDeleteModalVisible(false);
              setEmailInput('');
              dispatch(logout());
            },
          },
        ]);
        setIsSubmitting(false);
      }, 1000);
    }
  };

  //RENDER
  const renderSectionItem = (item: any, index: number) => {
    const iconColor = item.iconColor || colors.green;
    const iconBgColor = iconColor.length === 7 ? `${iconColor}22` : iconColor;
    return (
      <TouchableOpacity activeOpacity={1} key={index} onPress={item.onPress} disabled={item.loading}>
        <Card key={index} style={styles.sectionItem}>
          <View style={[styles.iconContainer, {backgroundColor: iconBgColor}]}>
            <Image source={item.icon} style={[styles.iconImage, {tintColor: iconColor}]} />
          </View>
          <Text style={styles.settingTitle}>{item.title}</Text>
          {item.loading ? <ActivityIndicator size="small" color={colors.primary} /> : <Icon name="ArrowRight2" size={24} color={colors.green} />}
        </Card>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent>
      {/* User Info Header và LOGO */}
      <View style={styles.headerView}>
        <View style={styles.userHeader}>
          <Image source={user?.anh_dai_dien ? {uri: user.anh_dai_dien} : R.icons.ic_user_bold} style={styles.avatar} resizeMode="contain" />
          <View>
            <Text style={styles.userName}>{user?.ten || 'Người dùng'}</Text>
            <Text style={styles.userEmail}>Cộng tác viên</Text>
          </View>
        </View>
        <Image source={R.images.img_logo_with_name} style={styles.logo} resizeMode="contain" />
      </View>
      {/* Revenue Container  */}
      {/* <View style={{marginHorizontal: spacing.sm}}>
        <Card style={styles.revenueContainer}>
          <View style={styles.totalRevenue}>
            <Text style={styles.revenueLabel}>Tổng doanh thu</Text>
            <Text style={styles.revenueValue}>{(user?.totalRevenue ?? 0).toLocaleString('vi-VN', {style: 'currency', currency: 'VND'})}</Text>
          </View>
          <View style={styles.divider} />
          <View style={styles.monthlyRevenue}>
            <Text style={styles.revenueLabel}>Thu nhập trong tháng</Text>
            <Text style={styles.revenueValue}>{(user?.monthlyRevenue ?? 0).toLocaleString('vi-VN', {style: 'currency', currency: 'VND'})}</Text>
          </View>
        </Card>
      </View> */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: 100}}>
        {/* Sections */}
        {getSections({navigation, handleLogout, handleCheckUpdate, isCheckingUpdate, user, handleRemoveAccount}).map((item: any, index: number) => renderSectionItem(item, index))}

        {/* Version */}
        <Text style={styles.appVersion}>Phiên bản {config.APP_VERSION}</Text>

        {/* Chat bot */}
      </ScrollView>
      {/*<TouchableOpacity style={styles.techSupportButton} onPress={() => {}}>*/}
      {/*  <Image source={R.images.img_tech_support} style={styles.chatBotImage} />*/}
      {/*</TouchableOpacity>*/}

      {/* Update Confirmation Modal */}
      <Modal visible={showUpdateModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Cập nhật ứng dụng</Text>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.modalMessage}>Có bản cập nhật mới cho ứng dụng. Bạn có muốn tải về và cài đặt không?</Text>

              {updateInfo && (
                <View style={styles.updateInfoContainer}>
                  <Text style={styles.updateInfoTitle}>Thông tin cập nhật:</Text>
                  <Text style={styles.updateInfoText}>
                    Phiên bản: {updateInfo.app_version} (Build {updateInfo.codepush_version})
                  </Text>
                  {updateInfo.description && <Text style={styles.updateInfoText}>Mô tả: {updateInfo.description}</Text>}
                  {updateInfo.release_notes && <Text style={styles.updateInfoText}>Ghi chú: {updateInfo.release_notes}</Text>}
                </View>
              )}

              {isDownloading && (
                <View style={styles.progressContainer}>
                  <Text style={styles.progressText}>Đang tải xuống... {downloadProgress}%</Text>
                  <View style={styles.progressBar}>
                    <View style={[styles.progressFill, {width: `${downloadProgress}%`}]} />
                  </View>
                </View>
              )}
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity style={[styles.modalButton, styles.cancelButton]} onPress={() => setShowUpdateModal(false)} disabled={isDownloading}>
                <Text style={styles.cancelButtonText}>Hủy</Text>
              </TouchableOpacity>

              <TouchableOpacity style={[styles.modalButton, styles.confirmButton]} onPress={handleDownloadUpdate} disabled={isDownloading}>
                {isDownloading ? <ActivityIndicator size="small" color="#FFFFFF" /> : <Text style={styles.confirmButtonText}>Cập nhật</Text>}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Delete Account Modal */}
      <Modal visible={isDeleteModalVisible} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Cảnh báo</Text>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.modalMessage}>Sau khi xoá tài khoản, tất cả dữ liệu của tài khoản trong hệ thống sẽ bị xoá cùng tài khoản. Vui lòng nhập EMAIL đăng nhập để xác nhận</Text>
              <TextInput style={styles.input} placeholder="Nhập email đăng ký" value={emailInput} onChangeText={setEmailInput} autoCapitalize="none" keyboardType="email-address" />
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity style={[styles.modalButton, styles.cancelButton]} onPress={() => setIsDeleteModalVisible(false)}>
                <Text style={styles.cancelButtonText}>Hủy</Text>
              </TouchableOpacity>

              <TouchableOpacity disabled={isSubmitting} style={[styles.modalButton, styles.confirmButton, {flexDirection: 'row'}]} onPress={handleConfirmDelete}>
                {isSubmitting ? <ActivityIndicator size="small" color="#FFFFFF" /> : null}
                <Text style={[styles.confirmButtonText, {marginLeft: 8}]}>Xác nhận</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScreenComponent>
  );
};
