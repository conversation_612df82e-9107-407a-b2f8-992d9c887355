import {View, ScrollView, Alert, Platform, KeyboardAvoidingView} from 'react-native';
import React from 'react';
import {ScreenComponent, Card, Button, TextField} from '@components/common';
import {useNavigation} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {styles} from './Style';
import {spacing} from '@constants/theme';
import {changePasswordSchema} from '@utils/validationSchemas';

interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ChangePasswordScreen() {
  const navigation = useNavigation<MainNavigationProp>();

  const {
    control,
    handleSubmit,
    reset,
    formState: {errors, isSubmitting},
  } = useForm<ChangePasswordFormData>({
    resolver: yupResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: ChangePasswordFormData) => {
    try {
      // TODO: Implement API call to change password
      console.log('Change password data:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      Alert.alert('Thành công', 'Đổi mật khẩu thành công!', [
        {
          text: 'OK',
          onPress: () => {
            reset();
            navigation.goBack();
          },
        },
      ]);
    } catch (error) {
      Alert.alert('Lỗi', 'Có lỗi xảy ra khi đổi mật khẩu');
      console.error('Change password error:', error);
    }
  };

  return (
    <ScreenComponent
      showHeader
      headerTitle="Đổi mật khẩu"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title="Đổi mật khẩu" onPress={handleSubmit(onSubmit)} loading={isSubmitting} disabled={isSubmitting} />}>
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0} enabled={true}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: spacing.lg}}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}>
          <Card>
            <View style={styles.formContainer}>
              <Controller
                control={control}
                name="currentPassword"
                render={({field: {onChange, value}}) => (
                  <TextField label="Mật khẩu hiện tại" value={value} onChangeText={onChange} error={errors.currentPassword?.message} placeholder="Nhập mật khẩu hiện tại" secureTextEntry required />
                )}
              />

              <Controller
                control={control}
                name="newPassword"
                render={({field: {onChange, value}}) => (
                  <TextField label="Mật khẩu mới" value={value} onChangeText={onChange} error={errors.newPassword?.message} placeholder="Nhập mật khẩu mới" secureTextEntry required />
                )}
              />

              <Controller
                control={control}
                name="confirmPassword"
                render={({field: {onChange, value}}) => (
                  <TextField
                    label="Xác nhận mật khẩu mới"
                    value={value}
                    onChangeText={onChange}
                    error={errors.confirmPassword?.message}
                    placeholder="Nhập lại mật khẩu mới"
                    secureTextEntry
                    required
                    containerStyle={styles.containerStyle}
                  />
                )}
              />
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </ScreenComponent>
  );
}
