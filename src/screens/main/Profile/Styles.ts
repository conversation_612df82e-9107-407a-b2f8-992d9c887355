import {StyleSheet} from 'react-native';
import {borderRadius, colors, spacing, typography} from '../../../constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  content: {
    flex: 1,
    padding: spacing.sm,
  },
  logo: {
    width: 120,
    height: 40,
    marginVertical: spacing.sm,
    marginHorizontal: spacing.sm,
  },
  userHeader: {
    // paddingVertical: spacing.xl,
    marginBottom: spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    paddingHorizontal: spacing.sm,
    flexShrink: 1,
  },
  revenueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.green,
  },
  totalRevenue: {
    flex: 1,
    alignItems: 'center',
  },
  monthlyRevenue: {
    flex: 1,
    alignItems: 'center',
  },
  revenueLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.sm,
  },
  revenueValue: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.regular,
    color: colors.white,
  },
  divider: {
    width: 1,
    height: '80%',
    backgroundColor: colors.white,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.full,
    // backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[500],
    tintColor: colors.gray[500],
    marginTop: 2,
  },
  avatarText: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold as '700',
    color: colors.white,
  },
  userName: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.xs,
    flexShrink: 1,
  },
  userEmail: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.md,
  },
  iconContainer: {
    padding: spacing.sm,
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },

  iconImage: {
    width: 24,
    height: 24,
  },
  settingTitle: {
    flex: 1,
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.regular,
  },
  appVersion: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    textAlign: 'center',
    marginTop: spacing.sm,
    fontFamily: typography.fontFamily.regular,
  },
  techSupportButton: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.md,
    borderRadius: borderRadius.full,
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 1,
  },
  chatBotImage: {
    width: 64,
    height: 64,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.md,
    marginBottom: spacing.md,
    marginHorizontal: spacing.sm,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    width: '100%',
    maxWidth: 400,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    textAlign: 'center',
  },
  modalContent: {
    padding: spacing.lg,
  },
  modalMessage: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.md,
    lineHeight: 22,
    fontFamily: typography.fontFamily.regular,
  },
  input: {
    height: 48,
    borderColor: colors.gray[300],
    borderWidth: 1,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    marginTop: spacing.md,
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.regular,
  },
  updateInfoContainer: {
    backgroundColor: colors.gray[500],
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.md,
  },
  updateInfoTitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    marginBottom: spacing.sm,
  },
  updateInfoText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  progressContainer: {
    marginTop: spacing.md,
  },
  progressText: {
    fontSize: typography.fontSize.sm,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily.regular,
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: borderRadius.full,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: borderRadius.full,
  },
  modalActions: {
    flexDirection: 'row',
    padding: spacing.lg,
    paddingTop: 0,
    gap: spacing.md,
  },
  modalButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  cancelButton: {
    backgroundColor: colors.gray[100],
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  cancelButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.semibold,
  },
  confirmButton: {
    backgroundColor: colors.danger,
  },
  confirmButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },
});
