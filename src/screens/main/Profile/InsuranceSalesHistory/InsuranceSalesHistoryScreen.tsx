import {View, Text, TouchableOpacity} from 'react-native';
import React, {useState} from 'react';
import {Card, Icon, ScreenComponent, TextField} from '@components/common';
import {useNavigation} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';
import {styles} from './Styles';
import {colors} from '@constants/theme';
import {FlatList} from 'react-native-gesture-handler';
import currencyFormatter from '@utils/currencyFormatter';
import NavigationUtil from '@navigation/NavigationUtil';
import {MAIN_SCREENS} from '@navigation/routes';

type Tab = 'Đang hiệu lực' | 'Hết hiệu lực';

const dataValidity = [
  {
    customer: 'Nguyễn Văn A',
    product: 'BHXH tự nguyện',
    validity: '01/01/2023 - 01/01/2024',
    amount: '66000',
  },
  {
    customer: 'Trần Thị B',
    product: 'BHYT',
    validity: '01/02/2023 - 01/02/2024',
    amount: '120000',
  },
  {
    customer: '<PERSON><PERSON> Văn C',
    product: 'BHXH bắt buộc',
    validity: '15/03/2023 - 15/03/2024',
    amount: '90000',
  },
];

const dataExpired = [
  {
    customer: 'Phạm Thị D',
    product: 'BHXH tự nguyện',
    validity: '01/01/2022 - 01/01/2023',
    amount: '66000',
  },
  {
    customer: 'Hoàng Văn E',
    product: 'BHYT',
    validity: '01/02/2022 - 01/02/2023',
    amount: '120000',
  },
  {
    customer: 'Vũ Thị F',
    product: 'BHXH bắt buộc',
    validity: '15/03/2022 - 15/03/2023',
    amount: '90000',
  },
];

const data = [...dataValidity, ...dataExpired];

export default function InsuranceSalesHistoryScreen() {
  const navigation = useNavigation<MainNavigationProp>();
  const [activeTab, setActiveTab] = useState<Tab>('Đang hiệu lực');
  const [searchValue, setSearchValue] = useState<string>('');

  // Search and filter logic can be added here
  const filteredData = data.filter(
    item =>
      item.customer.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.product.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.validity.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.amount.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const navigateToDetail = (item: (typeof data)[0]) => {
    // Điềuh hướng đến
    NavigationUtil.push(MAIN_SCREENS.INSURANCE_INFO, {
      dataValidity: item,
      dataExpired: item,
    });
  };

  const renderItem = (item: (typeof data)[0], index: number) => {
    return (
      <TouchableOpacity key={index} onPress={() => navigateToDetail(item)}>
        <Card style={styles.itemCard}>
          <View style={styles.itemRow}>
            <Text style={styles.labelText}>Người mua</Text>
            <Text style={styles.labelText}>Sản phẩm</Text>
            <Text style={styles.labelText}>Hiệu lực</Text>
            <Text style={styles.labelText}>Số tiền</Text>
          </View>
          <View style={styles.itemRow}>
            <Text style={[styles.valueText, styles.customerValue]}>{item.customer}</Text>
            <Text style={styles.valueText}>{item.product}</Text>
            <Text style={styles.valueText}>{item.validity}</Text>
            <Text style={[styles.valueText, styles.amountValue]}>{currencyFormatter.formatCurrency(item.amount, currencyFormatter.currencyDisplayOptions.display)}</Text>
          </View>
          <Icon name="ArrowRight2" size={24} color={colors.green} />
        </Card>
      </TouchableOpacity>
    );
  };

  const renderTab = (tab: Tab) => {
    const isActive = tab === activeTab;
    return (
      <TouchableOpacity key={tab} style={[styles.inactiveTab, isActive && styles.activeTab]} onPress={() => setActiveTab(tab)}>
        <Text style={[styles.inactiveTabTitle, isActive && styles.activeTabTitle]}>{tab}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent showHeader headerTitle="Lịch sử bán bảo hiểm" showBackButton onPressBack={() => navigation.goBack()}>
      <View style={styles.container}>
        <View style={styles.tabContainer}>{(['Đang hiệu lực', 'Hết hiệu lực'] as Tab[]).map(renderTab)}</View>
        {/* Search Container */}
        <View style={styles.searchContainer}>
          <TextField
            label="Tìm kiếm"
            value={searchValue}
            onChangeText={setSearchValue}
            containerStyle={{flex: 1}}
            rightIconType={<Icon name="SearchNormal" size={24} color={colors.green} />}
            onRightIconPress={() => {}}
          />
          <View style={styles.filterButton}>
            <Icon name="Filter" size={32} color={colors.green} variant="Bold" />
          </View>
        </View>
        <View style={styles.content}>
          {activeTab === 'Đang hiệu lực' ? (
            <FlatList
              data={filteredData.filter(item => dataValidity.includes(item))}
              keyExtractor={(_, index) => index.toString()}
              renderItem={({item, index}) => renderItem(item, index)}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => <Text>Không có hợp đồng bảo hiểm nào.</Text>}
            />
          ) : (
            <FlatList
              data={filteredData.filter(item => dataExpired.includes(item))}
              keyExtractor={(_, index) => index.toString()}
              renderItem={({item, index}) => renderItem(item, index)}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => <Text>Không có hợp đồng bảo hiểm nào.</Text>}
            />
          )}
        </View>
      </View>
    </ScreenComponent>
  );
}
