import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  tabContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.base,
    marginHorizontal: spacing.sm,
  },
  content: {
    flex: 1,
  },
  inactiveTab: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm + 5,
    borderRadius: borderRadius.base,
  },
  activeTab: {
    flex: 1,
    backgroundColor: colors.green,
  },
  inactiveTabTitle: {
    color: colors.green,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  activeTabTitle: {
    color: colors.white,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    marginHorizontal: spacing.sm,
    gap: spacing.sm,
  },
  filterButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[400],
    padding: spacing.sm,
    borderRadius: borderRadius.base,
    height: 56,
    width: 56,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  itemRow: {
    gap: spacing.sm,
  },
  labelText: {
    flex: 1,
    color: colors.gray[600],
    fontSize: typography.fontSize.base,
  },
  valueText: {
    flex: 1,
    color: colors.dark,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as '500',
  },
  itemCard: {
    padding: spacing.md,
    marginBottom: spacing.sm,
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
  },
  customerValue: {
    color: colors.primary,
  },
  amountValue: {
    color: colors.green,
  },
});
