import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  cardContainer: {
    // marginBottom: spacing.md,
    // backgroundColor: colors.white,
    // borderRadius: borderRadius.lg,
    // padding: spacing.md,
    // ...shadows.base,
  },
  cardHeader: {
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    marginBottom: spacing.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
  },
  infoContainer: {
    gap: spacing.sm,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  infoLabel: {
    flex: 1,
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  valueContainer: {
    flex: 1.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  infoValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    textAlign: 'right',
    flex: 1,
    fontFamily: typography.fontFamily.medium,
  },
  highlightedValue: {
    color: colors.primary,
    fontFamily: typography.fontFamily.semibold,
  },
  revenueValue: {
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
  copyButton: {
    marginLeft: spacing.xs,
    padding: spacing.xs,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.gray[100],
  },
  copyIcon: {
    width: 16,
    height: 16,
    tintColor: colors.gray[600],
  },
});
