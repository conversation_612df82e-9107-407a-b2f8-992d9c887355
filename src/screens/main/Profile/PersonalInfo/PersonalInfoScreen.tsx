import R from '@assets/R';
import {Card, createToastHelpers, ScreenComponent, useToast} from '@components/common';
import {spacing} from '@constants/theme';
import {MainNavigationProp} from '@navigation/types';
import Clipboard from '@react-native-clipboard/clipboard';
import {useNavigation} from '@react-navigation/native';
import {useAppSelector} from '@store/index';
import React from 'react';
import {Image, ScrollView, StyleProp, Text, TextStyle, TouchableOpacity, View} from 'react-native';
import {styles} from './Styles';

export default function PersonalInfoScreen() {
  logger.log('PersonalInfoScreen');
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  const navigation = useNavigation<MainNavigationProp>();
  const user = useAppSelector(state => state.auth.user);

  // Hàm để hiển thị giá trị hoặc '--' nếu không có data
  const getDisplayValue = (value: any) => {
    if (value === null || value === undefined || value === '') {
      return '--';
    }
    return String(value);
  };

  // Chỉ sử dụng data thật từ API, không fake data
  const displayData = user || {};

  const handleCopyField = (value: string, fieldName: string) => {
    // Copy value to clipboard
    Clipboard.setString(value);

    // Show toast notification
    toast.success(`Đã sao chép ${fieldName}: ${value}`, {
      position: 'top',
    });
  };

  const renderInfoRow = (label: string, value: string, style?: StyleProp<TextStyle>, showCopy?: boolean, onCopy?: () => void) => (
    <View style={styles.infoRow}>
      <Text style={styles.infoLabel}>{label}</Text>
      <View style={styles.valueContainer}>
        <Text style={[styles.infoValue, style]}>{value}</Text>
        {showCopy && (
          <TouchableOpacity onPress={onCopy} style={styles.copyButton}>
            <Image source={R.icons.ic_copy} style={styles.copyIcon} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <ScreenComponent
      showHeader
      headerTitle="Thông tin cá nhân"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      // footer={<Button title="Sửa thông tin" onPress={() => navigation.navigate(MAIN_SCREENS.PERSONAL_EDIT, {userData: displayData})} />}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.lg}}>
        {/* Thông tin cá nhân */}
        <Card style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Thông tin cá nhân</Text>
          </View>

          <View style={styles.infoContainer}>
            {renderInfoRow('Họ và tên', getDisplayValue(displayData.ten), styles.highlightedValue)}
            {renderInfoRow('Email', getDisplayValue(displayData.email))}
            {renderInfoRow(
              'Số điện thoại',
              getDisplayValue(displayData.dthoai),
              undefined,
              displayData.phoneNumber ? true : false,
              displayData.phoneNumber ? () => handleCopyField(displayData.phoneNumber, 'Số điện thoại') : undefined,
            )}
            {renderInfoRow('Ngày sinh', getDisplayValue(displayData.dateOfBirth))}
            {renderInfoRow('Giới tính', getDisplayValue(displayData.gender))}
            {/* {renderInfoRow(
              'Số CMND/CCCD',
              getDisplayValue(displayData.idNumber),
              undefined,
              displayData.idNumber ? true : false,
              displayData.idNumber ? () => handleCopyField(displayData.idNumber, 'Số CMND/CCCD') : undefined,
            )} */}
            {renderInfoRow('Địa chỉ', getDisplayValue(displayData.address))}
          </View>
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
