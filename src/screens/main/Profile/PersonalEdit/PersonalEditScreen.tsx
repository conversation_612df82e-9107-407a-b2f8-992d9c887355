import {View, Text, ScrollView, Alert, TouchableOpacity, Dimensions, Image} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ScreenComponent, Card, Button, TextField} from '@components/common';
import {useNavigation, useRoute} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';
import {useAppDispatch, useAppSelector} from '@store/index';
import {loginSuccess} from '@store/slices/authSlice';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {styles} from './Styles';
import {spacing, colors} from '@constants/theme';
import Icon from '@components/common/Icon';
import R from '@assets/R';

interface PersonalEditFormData {
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  gender: string;
  idNumber: string;
  address: string;
}

export default function PersonalEditScreen() {
  console.log('PersonalEditScreen');

  const navigation = useNavigation<MainNavigationProp>();
  const route = useRoute();
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.auth.user);
  const token = useAppSelector(state => state.auth.token);

  const {userData} = (route.params as any) || {};

  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors, isSubmitting},
  } = useForm<PersonalEditFormData>({
    defaultValues: {
      fullName: '',
      email: '',
      phoneNumber: '',
      dateOfBirth: '',
      gender: '',
      idNumber: '',
      address: '',
    },
  });

  useEffect(() => {
    if (userData) {
      setValue('fullName', userData.fullName || '');
      setValue('email', userData.email || '');
      setValue('phoneNumber', userData.phoneNumber || '');
      setValue('dateOfBirth', userData.dateOfBirth || '');
      setValue('gender', userData.gender || '');
      setValue('idNumber', userData.idNumber || '');
      setValue('address', userData.address || '');
    }
  }, [userData, setValue]);

  const onSubmit = async (data: PersonalEditFormData) => {
    try {
      // Cập nhật vào auth store
      const updatedUser = {
        ...user,
        ...data,
      };

      dispatch(
        loginSuccess({
          token: token || '',
          user: updatedUser,
        }),
      );

      Alert.alert('Thành công', 'Cập nhật thông tin cá nhân thành công!', [
        {
          text: 'OK',
          onPress: () => navigation.goBack(),
        },
      ]);
    } catch (error) {
      Alert.alert('Lỗi', 'Có lỗi xảy ra khi cập nhật thông tin');
      console.error('Update personal info error:', error);
    }
  };

  return (
    <ScreenComponent
      showHeader
      headerTitle="Thông tin cá nhân"
      showBackButton
      onPressBack={() => navigation.goBack()}
      showFooter
      footer={<Button title="Lưu thông t" onPress={handleSubmit(onSubmit)} loading={isSubmitting} disabled={isSubmitting} />}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.lg}}>
        {/* Chụp ảnh giấy tờ */}
        <Card style={styles.cardContainer}>
          <View style={styles.photoSectionHeader}>
            <Text style={styles.photoSectionTitle}>Chụp/tải ảnh giấy tờ tùy thân để tự động nhập thông tin</Text>
          </View>

          <View style={styles.photoContainer}>
            <TouchableOpacity style={styles.photoColumn}>
              <View style={styles.photoBox}>
                <Image source={R.images.img_cccd} style={styles.photoIcon} />
              </View>
              <Text style={styles.photoLabel}>Mặt trước</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.photoColumn}>
              <View style={styles.photoBox}>
                <Image source={R.images.img_cccd} style={styles.photoIcon} />
              </View>
              <Text style={styles.photoLabel}>Mặt sau</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.formContainer}>
            {/* Họ và tên - full width */}
            <Controller
              control={control}
              name="fullName"
              render={({field: {onChange, value}}) => <TextField label="Họ và tên" value={value} onChangeText={onChange} error={errors.fullName?.message} placeholder="Nhập họ và tên" required />}
            />

            {/* CCCD và Ngày sinh - cùng hàng */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Controller
                  control={control}
                  name="idNumber"
                  render={({field: {onChange, value}}) => (
                    <TextField label="CCCD/Hộ chiếu" value={value} onChangeText={onChange} error={errors.idNumber?.message} placeholder="Nhập số CCCD" keyboardType="numeric" required />
                  )}
                />
              </View>

              <View style={styles.halfWidth}>
                <Controller
                  control={control}
                  name="dateOfBirth"
                  render={({field: {onChange, value}}) => <TextField label="Ngày sinh" value={value} onChangeText={onChange} error={errors.dateOfBirth?.message} placeholder="DD/MM/YYYY" required />}
                />
              </View>
            </View>

            {/* Giới tính - buttons */}
            <View style={styles.genderContainer}>
              <Text style={styles.genderLabel}>
                Giới tính <Text style={styles.required}>*</Text>
              </Text>
              <Controller
                control={control}
                name="gender"
                render={({field: {onChange, value}}) => (
                  <View style={styles.genderButtons}>
                    <TouchableOpacity style={[styles.genderButton, value === 'Nam' && styles.genderButtonSelected]} onPress={() => onChange('Nam')}>
                      <Icon name="Man" size={20} color={value === 'Nam' ? colors.white : colors.gray[600]} />
                      <Text style={[styles.genderButtonText, value === 'Nam' && styles.genderButtonTextSelected]}>Nam</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={[styles.genderButton, value === 'Nữ' && styles.genderButtonSelected]} onPress={() => onChange('Nữ')}>
                      <Icon name="Woman" size={20} color={value === 'Nữ' ? colors.white : colors.gray[600]} />
                      <Text style={[styles.genderButtonText, value === 'Nữ' && styles.genderButtonTextSelected]}>Nữ</Text>
                    </TouchableOpacity>
                  </View>
                )}
              />
              {errors.gender && <Text style={styles.errorText}>{errors.gender.message}</Text>}
            </View>

            {/* Địa chỉ - full width */}
            <Controller
              control={control}
              name="address"
              render={({field: {onChange, value}}) => (
                <TextField label="Địa chỉ" value={value} onChangeText={onChange} error={errors.address?.message} placeholder="Nhập địa chỉ" multiline numberOfLines={2} required />
              )}
            />

            {/* SĐT và Email - cùng hàng */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Controller
                  control={control}
                  name="phoneNumber"
                  render={({field: {onChange, value}}) => (
                    <TextField label="Điện thoại" value={value} onChangeText={onChange} error={errors.phoneNumber?.message} placeholder="Nhập SĐT" keyboardType="phone-pad" required />
                  )}
                />
              </View>

              <View style={styles.halfWidth}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, value}}) => (
                    <TextField label="Email" value={value} onChangeText={onChange} error={errors.email?.message} placeholder="Nhập email" keyboardType="email-address" required />
                  )}
                />
              </View>
            </View>
          </View>
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
