import {colors, spacing, typography, borderRadius, shadows} from '@constants/theme';
import {StyleSheet, Dimensions} from 'react-native';

const {width} = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[100],
    padding: spacing.sm,
  },
  footerContainer: {
    padding: spacing.md,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  cardContainer: {},
  cardHeader: {
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
    marginBottom: spacing.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.gray[900],
  },
  formContainer: {
    gap: spacing.sm,
  },

  // Photo section styles
  photoSectionHeader: {
    paddingVertical: spacing.sm,
    backgroundColor: '#C8E1DA',
    borderRadius: borderRadius.base,
    marginBottom: spacing.md,
  },
  photoSectionTitle: {
    fontSize: typography.fontSize.sm + 2,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.dark,
    textAlign: 'center',
  },
  photoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: spacing.sm,
    marginBottom: spacing.xl,
  },
  photoBox: {
    aspectRatio: 1.6,
    backgroundColor: colors.gray[100],
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.base,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    textAlign: 'center',
  },

  // Row layout styles
  rowContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  halfWidth: {
    flex: 1,
  },

  // Gender selection styles
  genderContainer: {
    marginBottom: spacing.md,
    marginTop: -spacing.sm,
  },
  genderLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.gray[700],
    marginBottom: spacing.sm,
  },
  required: {
    color: colors.danger,
  },
  genderButtons: {
    flexDirection: 'row',
    borderColor: colors.gray[300],
    borderRadius: borderRadius.base,
    borderWidth: 1,
  },
  genderButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    backgroundColor: colors.white,
    borderRadius: borderRadius.base,
    gap: spacing.xs,
  },
  genderButtonSelected: {
    backgroundColor: colors.green,
    borderColor: colors.green,
  },
  genderButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.gray[700],
  },
  genderButtonTextSelected: {
    color: colors.white,
  },

  // Error text style
  errorText: {
    fontSize: typography.fontSize.xs,
    color: colors.danger,
    marginTop: spacing.xs,
  },
  photoIcon: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  photoColumn: {
    flex: 1,
    gap: spacing.md,
  },
});
