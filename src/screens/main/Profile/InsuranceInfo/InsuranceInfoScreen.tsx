import {View, Text, TouchableOpacity, ScrollView, Image, StyleProp, TextStyle} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {MainNavigationProp} from '@navigation/types';
import {Card, ScreenComponent} from '@components/common';
import {styles} from './Styles';
import R from '@assets/R';
import {spacing} from '@constants/theme';

export default function InsuranceInfoScreen({route}: any) {
  console.log('🚀 ~ InsuranceInfoScreen ~ route.params:', route.params);
  const navigation = useNavigation<MainNavigationProp>();
  const {dataValidity, dataExpired, userData} = route.params || {};
  console.log('🚀 ~ InsuranceInfoScreen ~ route.params:', route.params);

  // Fake data cho demo - sẽ được thay thế bằng data thật từ API
  const mockData = {
    bhxhCode: '1234567890',
    fullName: '<PERSON><PERSON><PERSON><PERSON>',
    dateOfBirth: '15/05/1985',
    gender: 'Nam',
    idNumber: '123456789012',
    phoneNumber: '**********',
    address: '123 Đường ABC, Phường XYZ, Quận 1, TP.HCM',
    householdHead: 'Nguyễn Văn Bình',
    relationship: 'Con',
    householdType: 'Hộ thường trú',
    voluntaryInsurance: {
      months: '12 tháng',
      fromMonth: '01/2024',
      baseAmount: '2.340.000 VNĐ',
      paymentMethod: 'Chuyển khoản',
      totalAmount: '561.600 VNĐ',
    },
    healthInsurance: {
      months: '12 tháng',
      fromMonth: '01/2024',
      hospital: 'Bệnh viện Chợ Rẫy',
      totalAmount: '756.000 VNĐ',
    },
    receipt: {
      receiptNumber: 'BL2024001234',
      content: 'Thu BHXH tự nguyện + BHYT năm 2024',
      note: 'Đã thanh toán đầy đủ',
    },
  };

  // Sử dụng userData từ params nếu có, nếu không thì dùng mockData
  const displayData = userData || mockData;

  const handleCopyBHXH = () => {
    // Logic to copy BHXH code to clipboard
  };

  const renderInfoRow = (label: string, value: string, style?: StyleProp<TextStyle>, isHighlighted?: boolean) => (
    <View style={styles.infoRow}>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={[styles.infoValue, isHighlighted && styles.highlightedValue, style]}>{value}</Text>
    </View>
  );

  return (
    <ScreenComponent showHeader headerTitle="Thông tin bảo hiểm" showBackButton onPressBack={() => navigation.goBack()}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={{paddingBottom: spacing.lg}}>
        {/* Thông tin chung */}
        <Card style={styles.cardContainer} title="Thông tin chung">
          <View style={styles.cardHeader}>
            <Text style={styles.title}>Người tham gia</Text>
          </View>
          <View style={styles.infoContainer}>
            {/* Mã số BHXH with Copy button */}
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Mã số BHXH</Text>
              <View style={styles.copyRow}>
                <Text style={styles.codeBHXHValue}>{displayData.bhxhCode}</Text>
                <TouchableOpacity onPress={handleCopyBHXH} style={styles.copyButton}>
                  <Image source={R.icons.ic_copy} style={styles.copyIcon} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Họ và tên with Đổi người button below */}
            <View style={styles.nameSection}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Họ và tên</Text>
                <Text style={[styles.infoValue, styles.highlightedValue]}>{displayData.fullName}</Text>
              </View>
            </View>

            {renderInfoRow('Ngày sinh', displayData.dateOfBirth)}
            {renderInfoRow('Giới tính', displayData.gender)}
            {renderInfoRow('Số CMND/CCCD', displayData.idNumber)}
            {renderInfoRow('Số điện thoại', displayData.phoneNumber)}
            {renderInfoRow('Địa chỉ', displayData.address)}
          </View>
          <View style={styles.cardHeader}>
            <Text style={styles.title}>Hộ gia đình</Text>
          </View>
          <View style={styles.infoContainer}>
            {renderInfoRow('Tên chủ hộ', displayData.householdHead, styles.highlightedValue)}
            {renderInfoRow('Mối q.hệ với chủ hộ', displayData.relationship)}
            {renderInfoRow('Loại hộ gia đình', displayData.householdType, styles.typeHousehold)}
          </View>
        </Card>

        {/* Thông tin sản phẩm */}
        <Card style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Thông tin sản phẩm</Text>
          </View>

          <View style={styles.cardHeader}>
            <Text style={styles.title}>BHXH tự nguyện</Text>
          </View>
          <View style={styles.infoContainer}>
            {renderInfoRow('Số tháng đóng', displayData.voluntaryInsurance.months, styles.productInfoValue)}
            {renderInfoRow('Từ tháng', displayData.voluntaryInsurance.fromMonth)}
            {renderInfoRow('Mức tiền căn cứ đóng', displayData.voluntaryInsurance.baseAmount, styles.highlightedValue)}
            {renderInfoRow('Phương thức đóng', displayData.voluntaryInsurance.paymentMethod)}
            {renderInfoRow('Số tiền đóng', displayData.voluntaryInsurance.totalAmount, styles.highlightedValue)}
          </View>

          <View style={styles.cardHeader}>
            <Text style={styles.title}>Bảo hiểm y tế</Text>
          </View>
          <View style={styles.infoContainer}>
            {renderInfoRow('Số tháng đóng', displayData.healthInsurance.months, styles.productInfoValue)}
            {renderInfoRow('Từ tháng', displayData.healthInsurance.fromMonth)}
            {renderInfoRow('Nơi đăng ký KCB', displayData.healthInsurance.hospital, styles.typeHousehold)}
            {renderInfoRow('Số tiền đóng', displayData.healthInsurance.totalAmount, styles.highlightedValue)}
          </View>
        </Card>

        {/* Thông tin biên lai */}
        <Card style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Thông tin biên lai</Text>
          </View>

          <View style={styles.receiptContainer}>
            {renderInfoRow('Biên lai', displayData.receipt.receiptNumber, styles.typeHousehold)}
            {renderInfoRow('Nội dung thu', displayData.receipt.content)}
            {renderInfoRow('Ghi chú', displayData.receipt.note)}
          </View>
        </Card>
      </ScrollView>
    </ScreenComponent>
  );
}
