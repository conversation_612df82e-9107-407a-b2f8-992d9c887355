import {colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.light,
    padding: spacing.sm,
  },
  cardContainer: {
    backgroundColor: colors.white,
    marginBottom: spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButtonText: {
    fontSize: typography.fontSize.sm,
    color: '#0033FF',
  },

  // Participant info styles
  participantHeader: {
    marginBottom: spacing.md,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  participantTitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing.xs,
  },
  participantCode: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.green,
    marginBottom: spacing.xs / 2,
  },
  participantName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
  },

  // Info container styles
  infoContainer: {
    marginBottom: spacing.sm,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
    gap: spacing.lg,
  },
  infoLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    width: 140, // Fixed width for labels to align values
  },
  infoValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
    flex: 1,
    lineHeight: 22,
  },
  codeBHXHValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.medium as any,
  },
  highlightedValue: {
    color: colors.green,
    fontWeight: typography.fontWeight.bold as any,
  },

  // Household section (now handled by infoRow)
  // Styles moved to highlightedValue

  // Type household
  typeHousehold: {
    fontSize: typography.fontSize.base,
    color: colors.danger,
    fontWeight: typography.fontWeight.medium as any,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },

  // Product styles
  productContainer: {
    marginBottom: spacing.sm,
  },
  productTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    marginBottom: spacing.sm,
  },
  productInfo: {
    marginTop: spacing.sm,
  },

  // Receipt styles
  receiptContainer: {},

  // Button styles
  buttonContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginTop: spacing.lg,
    paddingVertical: spacing.md,
  },
  button: {
    flex: 1,
  },
  copyRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    flex: 1,
  },
  copyIcon: {
    width: 18,
    height: 18,
    tintColor: colors.green,
  },
  copyButton: {
    padding: spacing.xs,
  },

  // Name section with change person button
  nameSection: {
    marginBottom: spacing.sm,
  },
  changePersonRow: {
    flexDirection: 'row',
    marginTop: spacing.xs,
    marginLeft: 140 + spacing.lg, // Align with value position
  },
  editIcon: {
    width: 16,
    height: 16,
    marginRight: spacing.sm,
  },
  typeHouseholdValue: {
    color: colors.danger,
    fontWeight: typography.fontWeight.medium as any,
  },
  productInfoValue: {
    fontSize: typography.fontSize.base,
    color: '#0033FF',
    fontWeight: typography.fontWeight.medium as any,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingVertical: spacing.sm,
  },
  cardIcon: {
    width: 24,
    height: 24,
  },
  menuTitle: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontWeight: typography.fontWeight.semibold as any,
  },
});
