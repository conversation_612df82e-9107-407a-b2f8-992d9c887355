import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.sm,
    backgroundColor: colors.light,
  },
  formContainer: {
    marginTop: spacing.md,
  },
  inputContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.md,
    marginBottom: -spacing.sm,
  },
  input: {
    flex: 1,
  },
  radio: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  card: {
    paddingBottom: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  headerLabel: {
    flexDirection: 'row',
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
  },
  iconStyle: {
    marginLeft: spacing.md,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  selectedItem: {
    borderColor: colors.green,
    backgroundColor: colors.green + '08',
  },
  logoPlaceholder: {
    width: 48,
    height: 48,
    paddingHorizontal: spacing.xs,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[400],
    padding: spacing.sm,
  },
  logoItem: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  textPlaceholder: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semibold,
    color: colors.gray[600],
    textAlign: 'center',
  },
  itemContent: {
    flex: 1,
    gap: spacing.xs,
  },
  tenNhaBH: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
  },
  textChiTiet: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontFamily: typography.fontFamily.regular,
  },
  nhaBHContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  nhaBHItem: {
    flex: 1,
    maxWidth: '49%',
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
  },
  footerLabel: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.regular,
  },
  footerValue: {
    fontSize: typography.fontSize.base,
    color: colors.dark,
    fontFamily: typography.fontFamily.medium,
  },
  contentFooter: {
    marginBottom: spacing.md,
    gap: spacing.sm + 2,
  },
  tongPhi: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  error: {
    color: colors.danger,
    fontFamily: typography.fontFamily.regular,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    flex: 1.1,
    marginLeft: 4,
  },
  secondButton: {
    flex: 1,
    marginRight: 4,
  },
  secondTextButton: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
  radioGroup: {
    marginBottom: spacing.md,
  },
});
