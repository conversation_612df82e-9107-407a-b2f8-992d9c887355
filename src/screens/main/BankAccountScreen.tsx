import React, {useEffect, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {Button, ScreenComponent, TextField} from '@components/common';

import {BankInfo, POPULAR_BANKS, vietQRService} from '../../services/vietqr';
import {spacing} from '@constants/theme';
import BankModal from '@components/QRPayment/BankModal';

const BankAccountScreen = ({route, navigation}: {route: any; navigation: any}) => {
  const {user} = route.params;

  const [banks, setBanks] = useState<BankInfo[]>([]);
  const [loadingBanks, setLoadingBanks] = useState(true);
  const [selectedBank, setSelectedBank] = useState<BankInfo | null>(null);
  const [accountName, setAccountName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [isBankModalVisible, setBankModalVisible] = useState(false);

  useEffect(() => {
    const loadBanks = async () => {
      try {
        const bankList = await vietQRService.getBanks();
        setBanks(bankList);
      } catch (error) {
        setBanks(
          POPULAR_BANKS.map(bank => ({
            ...bank,
            id: parseInt(bank.code),
            bin: bank.code, // Assuming bin is same as code for popular banks
            logo: '', // No logo available in this list
            transferSupported: 1,
            lookupSupported: 1,
          })),
        );
      } finally {
        setLoadingBanks(false);
      }
    };
    loadBanks();
  }, []);

  const handleSelectBank = (bank: BankInfo) => {
    setSelectedBank(bank);
    setBankModalVisible(false);
  };

  const handleAccountNameChange = (text: string) => {
    const formattedText = text.toUpperCase().replace(/[^A-Z\s]/g, '');
    setAccountName(formattedText);
  };

  const handleAccountNumberChange = (text: string) => {
    // Cho phép nhập số và chữ, loại bỏ ký tự đặc biệt
    const formattedText = text.replace(/[^A-Za-z0-9]/g, '');
    setAccountNumber(formattedText);
  };

  const handleSave = () => {
    logger.log('Selected Bank:', selectedBank);
    logger.log('Account Name:', accountName);
    logger.log('Account Number:', accountNumber);
  };

  return (
    <ScreenComponent showHeader headerTitle="Tài khoản ngân hàng" showBackButton onPressBack={() => navigation.goBack()}>
      <View style={{flex: 1, padding: spacing.md}}>
        <TouchableOpacity onPress={() => setBankModalVisible(true)} disabled={loadingBanks}>
          <View pointerEvents="none">
            <TextField
              required
              label="Ngân hàng"
              value={selectedBank ? selectedBank.shortName : ''}
              onChangeText={() => {}}
              editable={false}
              placeholder={loadingBanks ? 'Đang tải danh sách...' : 'Chọn ngân hàng'}
              rightIconType="dropdown"
              // onRightIconPress={() => setBankModalVisible(true)}
            />
          </View>
        </TouchableOpacity>

        <TextField label="Số tài khoản" value={accountNumber} onChangeText={handleAccountNumberChange} placeholder="Nhập số tài khoản" required />

        <TextField label="Tên tài khoản" value={accountName} onChangeText={handleAccountNameChange} placeholder="Nhập tên tài khoản (Ví dụ: NGUYEN VAN A)" autoCapitalize="characters" />

        <Button title="Lưu thông tin" onPress={handleSave} style={{marginTop: spacing.lg}} disabled={!selectedBank || !accountName || !accountNumber} />

        <BankModal visible={isBankModalVisible} banks={banks} onClose={() => setBankModalVisible(false)} onSelectBank={handleSelectBank} />
      </View>
    </ScreenComponent>
  );
};

export default BankAccountScreen;
