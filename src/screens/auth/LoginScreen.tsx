import R from '@assets/R';
import {Button, createToastHel<PERSON>, TextField, useToast} from '@components/common';
import {ACTION_CODE} from '@constants/axios';
import {borderRadius, colors, spacing, typography} from '@constants/theme';
import {login} from '@services/endpoints';
import {useAppDispatch} from '@store/index';
import {fetchDistricts, fetchProvinces, fetchWards} from '@store/slices/addressSlice';
import {loginFailure, loginStart, loginSuccess} from '@store/slices/authSlice';
import {fetchDanhMucHeThong, fetchDanhMucOTo, fetchDanhMucSucKhoe, fetchDanhMucXe, fetchLoaiHoGiaDinh} from '@store/slices/commonSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {KJUR} from 'jsrsasign';
import React, {useState} from 'react';
import {useForm} from 'react-hook-form';
import {Alert, Dimensions, Image, Keyboard, KeyboardAvoidingView, Platform, Pressable, StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';
import {loginFormValidation} from '@utils/validationSchemas';
import NavigationUtil from '@navigation/NavigationUtil';
import {NGUON_MOBILE} from '@commons/Constant';

type LoginFormData = {
  email: string;
  password: string;
};

// TODO: Remove this after testing

export default function LoginScreen() {
  const dispatch = useAppDispatch();
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);

  // State để track việc đã ấn button Hỗ trợ
  const [hasClickedSupport, setHasClickedSupport] = useState(false);

  // const navigation = useNavigation<StackNavigationProp<AuthStackParamList & MainStackParamList>>();
  // const [errors, setErrors] = useState<{email?: string; password?: string}>({});

  // Initialize React Hook Form
  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors, isSubmitting},
  } = useForm<LoginFormData>({
    // resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '', // Sử dụng 'admin' như trong cURL command
      password: '', // Password sẽ được hash thành mat_khau trong cURL
    },
  });

  React.useEffect(() => {
    const checkLastUserLogin = async () => {
      const lastUserLogin = await AsyncStorage.getItem('LAST_USER_LOGIN');
      if (lastUserLogin) {
        setValue('email', lastUserLogin);
      }
    };

    checkLastUserLogin();
  }, [setValue]);

  const onSubmit = async (data: LoginFormData) => {
    dispatch(loginStart());

    try {
      // Hash password với SHA256 theo tiêu chuẩn mới (username + password)
      const md = new KJUR.crypto.MessageDigest({alg: 'sha256', prov: 'cryptojs'});
      md.updateString(data.email + data.password);
      const hashedPassword = md.digest();

      // Sử dụng format mới theo cURL command - chỉ có tai_khoan và mat_khau
      const paramsLogin = {
        tai_khoan: data.email,
        mat_khau: hashedPassword,
        nguon: NGUON_MOBILE,
        actionCode: ACTION_CODE.DANG_NHAP,
      };

      const response: any = await login(paramsLogin);

      // Xử lý response theo format mới từ server
      if (response.data && response.data.token) {
        await AsyncStorage.setItem('USER_INFO', JSON.stringify(response.data));
        await AsyncStorage.setItem('LAST_USER_LOGIN', data.email);
        const {token, refresh_token, nsd} = response.data;
        dispatch(
          loginSuccess({
            token: token, // Lấy token từ response mới
            refreshToken: refresh_token, // Lấy refresh_token từ response mới
            user: nsd, // Lấy thông tin người dùng (nsd) từ response mới
            pas: hashedPassword,
            nsd: nsd.tai_khoan, // Chỉ lưu tai_khoan cho nsd field
          }),
        );
        dispatch(fetchProvinces());
        dispatch(fetchDistricts());
        dispatch(fetchWards());
        dispatch(fetchLoaiHoGiaDinh());
        dispatch(fetchDanhMucHeThong());
        dispatch(fetchDanhMucXe());
        dispatch(fetchDanhMucSucKhoe());
        dispatch(fetchDanhMucOTo());
        toast.success('Đăng nhập thành công', {duration: 2000, position: 'top'});
      } else {
        // Xử lý lỗi nếu response không có token hoặc có lỗi từ server
        const errorMessage = response?.error_message || response?.output?.message || 'Đăng nhập không thành công.';
        dispatch(loginFailure(errorMessage));
        Alert.alert('Lỗi', errorMessage);
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.error_message || error.message || 'Đăng nhập thất bại. Vui lòng thử lại.';
      dispatch(loginFailure(errorMessage));
      Alert.alert('Lỗi', errorMessage);
    } finally {
    }
  };

  // Function xử lý button Hỗ trợ/Chuyển đến Trang chủ
  const handleSupportPress = () => {
    // Lần đầu ấn → Chuyển đến màn hình Hỗ trợ
    NavigationUtil.navigate('Support', {
      title: 'Hỗ trợ',
      icon: R.icons.ic_support,
      color: '#4CAF50',
      buttonTitle: 'Quay về đăng nhập',
    });
  };
  return (
    <View style={styles.container}>
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{flex: 1}}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{flex: 1}}>
            <View style={styles.header}>
              <Image source={R.images.img_logo_with_name} style={styles.logo} />
              <Text style={styles.subtitle}>Vui lòng nhập thông tin để tiến hành đăng nhập</Text>
            </View>
            <View style={styles.formCard}>
              {/* Email field with Material Design Outlined variant */}
              <TextField
                control={control}
                name="email"
                label="Tài khoản"
                placeholder="Nhập tài khoản"
                keyboardType="email-address"
                autoCapitalize="none"
                error={errors.email}
                rules={loginFormValidation.username as any}
                required
              />

              {/* Password field with Material Design Filled variant */}
              <TextField
                control={control}
                name="password"
                label="Mật khẩu"
                placeholder="Nhập mật khẩu"
                secureTextEntry
                error={errors.password}
                rules={loginFormValidation.password as any}
                returnKeyType="done"
                required
                onSubmitEditing={handleSubmit(onSubmit)}
              />

              {/* <View style={styles.forgotAndRegisterRow}>
                  <Text style={styles.forgotPassword}>Quên mật khẩu?</Text>
                  <Text style={styles.forgotPassword}>Đăng ký tài khoản</Text>
                </View> */}

              <Button title="Đăng nhập" onPress={handleSubmit(onSubmit)} loading={isSubmitting} variant="primary" size="small" style={styles.loginButton} />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
      <View style={styles.footerContainer}>
        <Pressable onPress={handleSupportPress} style={styles.footer}>
          <Image source={R.icons.ic_support} style={styles.iconSupport} />
          <Text style={styles.footerText}>{'Hỗ trợ'}</Text>
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.md,
    marginTop: 100,
  },
  logo: {
    resizeMode: 'contain',
    width: Dimensions.get('window').width * 0.8,
    height: 100, // Thêm chiều cao để tránh lỗi
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[600],
    textAlign: 'center',
  },
  formCard: {
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.md,
    marginTop: 60,
  },
  loginButton: {
    marginTop: spacing.md,
    marginHorizontal: -spacing.xs + 2,
    backgroundColor: colors.green,
  },
  forgotAndRegisterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  forgotPassword: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontWeight: typography.fontWeight.semibold as '600',
  },
  buttonGroupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.md,
  },
  fingerprintButton: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.md,
  },
  fingerprintIcon: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  footer: {
    alignItems: 'center',
  },
  footerContainer: {
    position: 'absolute',
    bottom: spacing['2xl'],
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  footerText: {
    color: colors.green,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold as '600',
  },
  iconSupport: {
    width: 30,
    height: 30,
    tintColor: colors.green,
  },
});
