import {NGUON_MOBILE} from '@commons/Constant';
import {CONFIG_SERVER} from '@constants/axios';
import CryptoJS from 'crypto-js';
import {KJUR, utf8tob64u} from 'jsrsasign';
import React, {useEffect, useState, useRef} from 'react';
import {ActivityIndicator, Alert, View, AppState, AppStateStatus} from 'react-native';
import {WebView} from 'react-native-webview';
import {ERROR_STATUSES, INFO_STATUSES, PaymentStatus, SUCCESS_STATUSES, VNPAY_CRYPTO_CONFIG, VNPAY_STATUS_MESSAGES} from './constants';
import {createToastHelpers, useToast} from '@components/common';

const generateSignature = (payload: any, secretKey: string): string => {
  // 1. Stringify the payload
  //   const payloadString = JSON.stringify(payload);

  // 2. Encode the payload string using Base64URL
  const base64UrlPayload = utf8tob64u(payload);

  // 3. Concatenate with a dot and the secret key
  const signatureInput = base64UrlPayload + '.' + secretKey;

  // 4. Calculate the SHA256 hash using KJUR (same as authEndpoints)
  const md = new KJUR.crypto.MessageDigest({alg: 'sha256', prov: 'cryptojs'});
  md.updateString(signatureInput);
  return md.digest();
};

interface VNPayWebViewProps {
  amount: number;
  orderId: string;
  onClose: () => void;
  onSuccess: () => void;
}
const paymentOptionKey = {
  iv: VNPAY_CRYPTO_CONFIG.options.iv,
  mode: VNPAY_CRYPTO_CONFIG.options.mode,
  padding: VNPAY_CRYPTO_CONFIG.options.padding,
};

const VNPayWebView: React.FC<VNPayWebViewProps> = ({amount, orderId, onClose, onSuccess}) => {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [paymentUrl, setPaymentUrl] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isProcessed, setIsProcessed] = useState<boolean>(false);
  const appStateRef = useRef(AppState.currentState);
  const webViewRef = useRef<WebView>(null);

  useEffect(() => {
    // Generate URL once when component mounts
    generateVNPayURL();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Listen to app state changes (background/foreground)
  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription.remove();
    };
  }, [isProcessed]);

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
      logger.log('App returned from background - checking payment status');
      if (webViewRef.current) {
        // Reload the WebView to ensure payment callback is processed
        webViewRef.current.reload();
      }
    }
    appStateRef.current = nextAppState;
  };

  const generateVNPayURL = async () => {
    try {
      const returnUrl = `${CONFIG_SERVER.PAYMENT_URL}/return-vnpay`;

      // 1) Build payload string
      const formatPayload = `nguon=${NGUON_MOBILE}&ma_gd=${orderId}&so_tien=${amount}&returnUrl=${returnUrl}`;
      // const formatPayload = `nguon=SBH4SALE&ma_gd=d9e36341f45a4f4eb4fcb6ccec4e13f0&so_tien=17750000&returnUrl=https://sanbaohiem-payment.escs.vn/return-vnpay`;

      //   // 2) Base64URL encode
      //   const payloadEncoded = utf8tob64u(formatPayload);
      const signature = generateSignature(formatPayload, CONFIG_SERVER.PAYMENT_KEY);

      // 4) Combined payload per doc
      // const combinedPayload = `nguon=SBH4SALE&ma_gd=d9e36341f45a4f4eb4fcb6ccec4e13f0&so_tien=17750000&returnUrl=https://sanbaohiem-payment.escs.vn/return-vnpay&signature=fd4ba5c3a9c2cf40ee14fa3ba506b61f7660981fa45ee92adac5cdfa876f4c1f`;
      const combinedPayload = `nguon=${NGUON_MOBILE}&ma_gd=${orderId}&so_tien=${amount}&returnUrl=${returnUrl}&signature=${signature}`;

      // 5) AES encrypt combined payload using SECRET_KEY

      const encrypted = CryptoJS.AES.encrypt(combinedPayload, VNPAY_CRYPTO_CONFIG.key, paymentOptionKey).toString();

      // 6) Construct final URL in required format
      const finalUrl = `${CONFIG_SERVER.PAYMENT_URL}/payment/genqr?data=${encodeURIComponent(encrypted)}`;

      // 7) Set paymentUrl for WebView to load
      setPaymentUrl(finalUrl);
      return finalUrl;
    } catch (err) {
      console.error('Error generating payment URL:', err);
      const errorMessage = 'Không thể tạo URL thanh toán';
      setError(errorMessage);
      Alert.alert('Thông báo', errorMessage);
      onClose();
      return null;
    }
  };

  //Hàm lắng nghe sự thay đổi của return URL
  const handleNavigationStateChange = (navState: any) => {
    // Nếu đã xử lý thành công, không xử lý thêm
    if (isProcessed) {
      return;
    }

    const url = navState.url || '';

    // Ignore initial payment URL (genqr request payload)
    if (url === paymentUrl) return logger.log('Ignoring initial payment URL, waiting for return callback');

    // Only process return/callback URLs that contain return-vnpay path
    const isReturnUrl = /return-?vnpay|payment_success|payment_error/i.test(url);
    if (!isReturnUrl) return logger.log('URL does not look like a return callback, ignoring:', url);

    // Try to extract `data` param from the URL (either ?data= or &data=)
    const dataMatch = url.match(/[?&]data=([^&]+)/);
    if (dataMatch && dataMatch[1]) {
      const encryptedParam = dataMatch[1];
      try {
        // URL decode the value
        const decoded = decodeURIComponent(encryptedParam);

        // Decrypt using same options used when encrypting
        const bytes = CryptoJS.AES.decrypt(decoded, VNPAY_CRYPTO_CONFIG.key, paymentOptionKey);

        const decrypted = bytes.toString(CryptoJS.enc.Utf8);

        if (decrypted) {
          // parse key=value pairs robustly (message can contain = so join the rest)
          const params: Record<string, string> = {};
          decrypted.split('&').forEach(pair => {
            const [k, ...rest] = pair.split('=');
            params[k] = rest.join('=');
          });

          const trangThai = (params['trang_thai'] || params['trangthai'] || params['trang-thai']) as PaymentStatus | undefined;

          // Handle status per documentation using switch-case
          if (trangThai) {
            switch (true) {
              case SUCCESS_STATUSES.includes(trangThai):
                logger.log('✅ Payment successful');
                setIsProcessed(true);
                onSuccess();
                return;

              case ERROR_STATUSES.includes(trangThai):
                const errorMessage = VNPAY_STATUS_MESSAGES[trangThai] || `Giao dịch không thành công: ${trangThai}`;
                logger.log('❌ Payment error:', errorMessage);
                setIsProcessed(true);
                toast.error(errorMessage);
                onClose();
                return;

              case INFO_STATUSES.includes(trangThai):
                const infoMessage = VNPAY_STATUS_MESSAGES[trangThai] || `Trạng thái giao dịch: ${trangThai}`;
                logger.log('ℹ️ Payment info:', infoMessage);
                toast.info(infoMessage);
                return;

              default:
                // Fallback: unknown status
                logger.log('Unknown status:', trangThai);
                if (!isProcessed) {
                  setIsProcessed(true);
                  onClose();
                  toast.error('Có lỗi xảy khi tạo mã thanh toán. Vui lòng thử lại sau!', {duration: 3000, position: 'top'});
                }
                return;
            }
          } else {
            // No trang_thai found
            logger.log('Missing trang_thai, falling back to URL heuristics');
            if (!isProcessed) {
              setIsProcessed(true);
              onClose();
              toast.error('Có lỗi xảy khi tạo mã thanh toán. Vui lòng thử lại sau!', {duration: 3000, position: 'top'});
            }
          }

          // We handled the data param, stop further processing
          return;
        }
      } catch (e) {
        console.error('Error decrypting data param', e);
        // continue to fallback heuristics below
      }
    }

    // Fallback heuristics: detect callback or success keywords (rarely reached now due to early return checks)
    if (url.includes('/return-vnpay') || url.includes('payment_success')) {
      logger.log('Payment success detected (fallback) - should have been handled above');
    } else if (url.includes('payment_error')) {
      logger.log('Payment error detected (fallback) - should have been handled above');
      if (!isProcessed) {
        setIsProcessed(true);
        onClose();
      }
    }
  };

  return (
    <View style={{flex: 1}}>
      {paymentUrl ? (
        <WebView
          ref={webViewRef}
          source={{uri: paymentUrl}}
          onNavigationStateChange={handleNavigationStateChange}
          startInLoadingState={true}
          renderLoading={() => (
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
              <ActivityIndicator size="large" color="#00ff00" />
            </View>
          )}
        />
      ) : (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="large" color="#00ff00" />
        </View>
      )}
    </View>
  );
};

export default VNPayWebView;
