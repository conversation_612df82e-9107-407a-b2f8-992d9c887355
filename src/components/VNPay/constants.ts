import CryptoJS from 'crypto-js';

export const secretKey = CryptoJS.enc.Utf8.parse('f3K8vNcRz5LM0bqT7eJxUg9HZPd1WYvA');
export const iv = CryptoJS.enc.Utf8.parse('1qAZ2wSX3eDC4rfV');

/**
 * Các trạng thái giao dịch VNPay
 */
export type PaymentStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'EXPIRED' | 'CANCELLED';

export const VNPAY_PAYMENT_STATUS = {
  PENDING: 'PENDING' as PaymentStatus,
  PROCESSING: 'PROCESSING' as PaymentStatus,
  COMPLETED: 'COMPLETED' as PaymentStatus,
  FAILED: 'FAILED' as PaymentStatus,
  EXPIRED: 'EXPIRED' as PaymentStatus,
  CANCELLED: 'CANCELLED' as PaymentStatus,
};

/**
 * <PERSON><PERSON><PERSON> label thân thiện cho người dùng
 */
export const VNPAY_STATUS_MESSAGES: Record<PaymentStatus, string> = {
  PENDING: 'Giao dịch đang chờ xử lý',
  PROCESSING: 'Giao dịch đang xử lý',
  COMPLETED: 'Giao dịch thành công',
  FAILED: 'Giao dịch thất bại',
  EXPIRED: 'Giao dịch đã hết hạn',
  CANCELLED: 'Giao dịch đã huỷ',
};

/**
 * Cấu hình CryptoJS để sử dụng cho mã hóa và giải mã
 * Đảm bảo consistent giữa encrypt và decrypt
 */
export const VNPAY_CRYPTO_CONFIG = {
  key: secretKey,
  iv: iv,
  options: {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  },
};

/**
 * Trạng thái cần gọi onSuccess
 */
export const SUCCESS_STATUSES: PaymentStatus[] = [VNPAY_PAYMENT_STATUS.COMPLETED];

/**
 * Trạng thái cần gọi onClose
 */
export const ERROR_STATUSES: PaymentStatus[] = [VNPAY_PAYMENT_STATUS.FAILED, VNPAY_PAYMENT_STATUS.EXPIRED, VNPAY_PAYMENT_STATUS.CANCELLED];

/**
 * Trạng thái chỉ cần hiển thị thông báo
 */
export const INFO_STATUSES: PaymentStatus[] = [VNPAY_PAYMENT_STATUS.PENDING, VNPAY_PAYMENT_STATUS.PROCESSING];
