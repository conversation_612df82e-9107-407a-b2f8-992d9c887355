import React, {useState} from 'react';
import {View, Text, StyleSheet, TextInput, TouchableOpacity, Alert} from 'react-native';
import QRCode from 'react-native-qrcode-svg';

interface QRPaymentData {
  bankCode: string;
  accountNumber: string;
  accountName: string;
  amount?: number;
  description?: string;
}

interface QRGeneratorProps {
  onQRGenerated?: (qrData: string) => void;
}

const QRGenerator: React.FC<QRGeneratorProps> = ({onQRGenerated}) => {
  const [paymentData, setPaymentData] = useState<QRPaymentData>({
    bankCode: 'VCB', // Vietcombank
    accountNumber: '',
    accountName: '',
    amount: undefined,
    description: '',
  });
  const [qrValue, setQrValue] = useState<string>('');

  // Tạo QR theo chuẩn VietQR
  const generateVietQR = () => {
    if (!paymentData.accountNumber || !paymentData.accountName) {
      Alert.alert('Lỗi', 'Vui lòng nhập đầy đủ thông tin tài khoản');
      return;
    }

    // Format theo chuẩn VietQR
    const qrData = {
      version: '01',
      method: '12', // Static QR
      merchantInfo: {
        guid: '970436', // NAPAS GUID
        bankCode: paymentData.bankCode,
        accountNumber: paymentData.accountNumber,
      },
      amount: paymentData.amount?.toString() || '',
      currency: '704', // VND
      description: paymentData.description || '',
      beneficiaryName: paymentData.accountName,
    };

    // Tạo chuỗi QR theo format VietQR
    const qrString = createVietQRString(qrData);
    setQrValue(qrString);
    onQRGenerated?.(qrString);
  };

  // Tạo QR đơn giản (không theo chuẩn)
  const generateSimpleQR = () => {
    const simpleData = {
      type: 'payment',
      bank: paymentData.bankCode,
      account: paymentData.accountNumber,
      name: paymentData.accountName,
      amount: paymentData.amount,
      note: paymentData.description,
    };

    const qrString = JSON.stringify(simpleData);
    setQrValue(qrString);
    onQRGenerated?.(qrString);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Tạo mã QR chuyển tiền</Text>

      <View style={styles.form}>
        <TextInput
          style={styles.input}
          placeholder="Mã ngân hàng (VCB, TCB, VTB...)"
          value={paymentData.bankCode}
          onChangeText={text => setPaymentData({...paymentData, bankCode: text.toUpperCase()})}
        />

        <TextInput
          style={styles.input}
          placeholder="Số tài khoản"
          value={paymentData.accountNumber}
          onChangeText={text => setPaymentData({...paymentData, accountNumber: text})}
          keyboardType="numeric"
        />

        <TextInput style={styles.input} placeholder="Tên chủ tài khoản" value={paymentData.accountName} onChangeText={text => setPaymentData({...paymentData, accountName: text})} />

        <TextInput
          style={styles.input}
          placeholder="Số tiền (tùy chọn)"
          value={paymentData.amount?.toString() || ''}
          onChangeText={text => setPaymentData({...paymentData, amount: text ? parseInt(text) : undefined})}
          keyboardType="numeric"
        />

        <TextInput style={styles.input} placeholder="Nội dung chuyển tiền" value={paymentData.description} onChangeText={text => setPaymentData({...paymentData, description: text})} />
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={generateVietQR}>
          <Text style={styles.buttonText}>Tạo QR VietQR</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={generateSimpleQR}>
          <Text style={styles.buttonText}>Tạo QR đơn giản</Text>
        </TouchableOpacity>
      </View>

      {qrValue ? (
        <View style={styles.qrContainer}>
          <QRCode value={qrValue} size={200} backgroundColor="white" color="black" />
          <Text style={styles.qrLabel}>Mã QR chuyển tiền</Text>
        </View>
      ) : null}
    </View>
  );
};

// Hàm tạo chuỗi VietQR (đơn giản hóa)
const createVietQRString = (data: any): string => {
  // Đây là format đơn giản, thực tế cần implement theo đúng chuẩn EMVCo
  return `00020101021238570010A000000727012700069704360114${data.merchantInfo.accountNumber}0208QRIBFTTA53037045802VN5925${data.beneficiaryName}6304`;
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  form: {
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    flex: 0.48,
  },
  secondaryButton: {
    backgroundColor: '#34C759',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  qrContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  qrLabel: {
    marginTop: 10,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default QRGenerator;
