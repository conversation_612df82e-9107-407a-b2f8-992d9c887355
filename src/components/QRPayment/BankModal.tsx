import React from 'react';
import {Modal, View, Text, FlatList, TouchableOpacity, StyleSheet, Image, TextInput, SafeAreaView, Dimensions} from 'react-native';
import {BankInfo} from '../../services/vietqr';

interface BankModalProps {
  visible: boolean;
  banks: BankInfo[];
  onClose: () => void;
  onSelectBank: (bank: BankInfo) => void;
}

const BankModal: React.FC<BankModalProps> = ({visible, banks, onClose, onSelectBank}) => {
  const [searchTerm, setSearchTerm] = React.useState('');

  const filteredBanks = banks.filter(
    bank => bank.name.toLowerCase().includes(searchTerm.toLowerCase()) || bank.shortName.toLowerCase().includes(searchTerm.toLowerCase()) || bank.code.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose} style={{height: 200}}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Chọn ngân hàng</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Đóng</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.searchContainer}>
          <TextInput style={styles.searchInput} placeholder="Tìm theo tên ngân hàng..." value={searchTerm} onChangeText={setSearchTerm} />
        </View>
        <FlatList
          data={filteredBanks}
          keyExtractor={item => item.id.toString()}
          renderItem={({item}) => (
            <TouchableOpacity style={styles.bankItem} onPress={() => onSelectBank(item)}>
              <View style={styles.bankLogoContainer}>
                {item.logo ? (
                  <Image source={{uri: item.logo}} style={styles.bankLogo} />
                ) : (
                  <View style={styles.bankLogoPlaceholder}>
                    <Text style={styles.bankLogoText}>{item.shortName.charAt(0)}</Text>
                  </View>
                )}
              </View>
              <View style={styles.bankInfo}>
                <Text style={styles.bankName}>{`${item.shortName} (${item.code})`}</Text>
                <Text style={styles.bankFullName}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          )}
        />
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#007AFF',
  },
  searchContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f5f5f5',
  },
  bankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  bankLogoContainer: {
    width: 40,
    height: 40,
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bankLogo: {
    width: 80,
    height: 50,
    resizeMode: 'contain',
  },
  bankLogoPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bankLogoText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bankInfo: {
    flex: 1,
  },
  bankName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  bankFullName: {
    fontSize: 14,
    color: '#666',
  },
});

export default BankModal;
