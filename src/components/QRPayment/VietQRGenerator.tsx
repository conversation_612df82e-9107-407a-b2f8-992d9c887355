import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TextInput, TouchableOpacity, Alert, ActivityIndicator, ScrollView, Image, Modal} from 'react-native';
import {vietQRService, BankInfo, POPULAR_BANKS} from '../../services/vietqr';
import BankModal from './BankModal';

interface VietQRGeneratorProps {
  onQRGenerated?: (qrDataURL: string) => void;
  onClose?: () => void;
}

const VietQRGenerator: React.FC<VietQRGeneratorProps> = ({onQRGenerated, onClose}) => {
  const [banks, setBanks] = useState<BankInfo[]>([]);
  const [selectedBank, setSelectedBank] = useState<string>('970436'); // VCB default
  const [selectedBankInfo, setSelectedBankInfo] = useState<BankInfo | null>(null);
  const [showBankModal, setShowBankModal] = useState<boolean>(false);
  const [accountNumber, setAccountNumber] = useState<string>('');
  const [accountName, setAccountName] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [qrDataURL, setQrDataURL] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingBanks, setLoadingBanks] = useState<boolean>(true);

  useEffect(() => {
    loadBanks();
  }, []);

  useEffect(() => {
    // Update selected bank info when banks are loaded or selectedBank changes
    const bankInfo = banks.find(bank => bank.code === selectedBank);
    setSelectedBankInfo(bankInfo || null);
  }, [banks, selectedBank]);

  const loadBanks = async () => {
    try {
      const bankList = await vietQRService.getBanks();
      setBanks(bankList);
    } catch (error) {
      // Fallback to popular banks if API fails
      setBanks(
        POPULAR_BANKS.map(bank => ({
          id: parseInt(bank.code),
          name: bank.name,
          code: bank.code,
          bin: bank.code,
          shortName: bank.shortName,
          logo: '',
          transferSupported: 1,
          lookupSupported: 1,
        })),
      );
    } finally {
      setLoadingBanks(false);
    }
  };

  const validateForm = (): boolean => {
    if (!selectedBank) {
      Alert.alert('Lỗi', 'Vui lòng chọn ngân hàng');
      return false;
    }
    if (!accountNumber.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập số tài khoản');
      return false;
    }
    if (!accountName.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập tên chủ tài khoản');
      return false;
    }
    return true;
  };

  const lookupAccount = async () => {
    if (!selectedBank || !accountNumber.trim()) {
      Alert.alert('Lỗi', 'Vui lòng chọn ngân hàng và nhập số tài khoản');
      return;
    }

    setLoading(true);
    try {
      const result = await vietQRService.lookupAccount(selectedBank, accountNumber);
      if (result.code === '00' && result.data) {
        setAccountName(result.data.accountName);
        Alert.alert('Thành công', 'Đã tìm thấy thông tin tài khoản');
      } else {
        Alert.alert('Lỗi', result.desc || 'Không tìm thấy thông tin tài khoản');
      }
    } catch (error) {
      Alert.alert('Lỗi', 'Không thể kiểm tra thông tin tài khoản');
    } finally {
      setLoading(false);
    }
  };

  const generateQR = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const result = await vietQRService.generateQR({
        accountNo: accountNumber,
        accountName: accountName,
        acqId: selectedBankInfo?.bin || selectedBank,
        amount: amount ? parseInt(amount) : undefined,
        addInfo: description,
        template: 'compact',
      });

      if (result.code === '00' && result.data) {
        setQrDataURL(result.data.qrDataURL);
        onQRGenerated?.(result.data.qrDataURL);
        Alert.alert('Thành công', 'Đã tạo mã QR thành công');
      } else {
        Alert.alert('Lỗi', result.desc || 'Không thể tạo mã QR');
      }
    } catch (error) {
      Alert.alert('Lỗi', 'Không thể tạo mã QR. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (text: string) => {
    const numericValue = text.replace(/[^0-9]/g, '');
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleSelectBank = (bank: BankInfo) => {
    setSelectedBank(bank.code);
    setSelectedBankInfo(bank);
    setShowBankModal(false);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Tạo mã QR VietQR</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.form}>
        {/* Chọn ngân hàng */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Ngân hàng *</Text>
          {loadingBanks ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <TouchableOpacity style={styles.bankSelector} onPress={() => setShowBankModal(true)}>
              <View style={styles.bankSelectorContent}>
                {selectedBankInfo ? (
                  <>
                    <View style={styles.bankLogoContainer}>
                      {selectedBankInfo.logo ? (
                        <Image source={{uri: selectedBankInfo.logo}} style={styles.bankLogo} />
                      ) : (
                        <View style={styles.bankLogoPlaceholder}>
                          <Text style={styles.bankLogoText}>{selectedBankInfo.shortName.charAt(0)}</Text>
                        </View>
                      )}
                    </View>
                    <View style={styles.bankTextContainer}>
                      <Text style={styles.bankName}>
                        {selectedBankInfo.shortName} ({selectedBankInfo.code})
                      </Text>
                      <Text style={styles.bankFullName}>{selectedBankInfo.name}</Text>
                    </View>
                  </>
                ) : (
                  <Text style={styles.placeholderText}>Chọn ngân hàng</Text>
                )}
                <Text style={styles.dropdownIcon}>▼</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>

        {/* Số tài khoản */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Số tài khoản *</Text>
          <View style={styles.inputWithButton}>
            <TextInput style={[styles.input, styles.inputFlex]} placeholder="Nhập số tài khoản" value={accountNumber} onChangeText={setAccountNumber} keyboardType="numeric" />
            <TouchableOpacity style={styles.lookupButton} onPress={lookupAccount} disabled={loading}>
              <Text style={styles.lookupButtonText}>Kiểm tra</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Tên chủ tài khoản */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Tên chủ tài khoản *</Text>
          <TextInput style={styles.input} placeholder="Nhập tên chủ tài khoản" value={accountName} onChangeText={setAccountName} />
        </View>

        {/* Số tiền */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Số tiền (VND)</Text>
          <TextInput style={styles.input} placeholder="Nhập số tiền (tùy chọn)" value={amount} onChangeText={text => setAmount(formatAmount(text))} keyboardType="numeric" />
        </View>

        {/* Nội dung */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Nội dung chuyển tiền</Text>
          <TextInput style={[styles.input, styles.textArea]} placeholder="Nhập nội dung chuyển tiền" value={description} onChangeText={setDescription} multiline numberOfLines={3} />
        </View>
      </View>

      {/* Nút tạo QR */}
      <TouchableOpacity style={[styles.generateButton, loading && styles.disabledButton]} onPress={generateQR} disabled={loading}>
        {loading ? <ActivityIndicator size="small" color="white" /> : <Text style={styles.generateButtonText}>Tạo mã QR</Text>}
      </TouchableOpacity>

      {/* Hiển thị QR */}
      {qrDataURL ? (
        <View style={styles.qrContainer}>
          <Image source={{uri: qrDataURL}} style={styles.qrImage} />
          <Text style={styles.qrLabel}>Mã QR VietQR</Text>
          {selectedBankInfo && (
            <Text style={styles.bankInfo}>
              {selectedBankInfo.name} - {accountNumber}
            </Text>
          )}
          {amount && <Text style={styles.amountInfo}>Số tiền: {amount} VND</Text>}
        </View>
      ) : null}

      <BankModal visible={showBankModal} banks={banks} onClose={() => setShowBankModal(false)} onSelectBank={handleSelectBank} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fafafa',
  },
  inputWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputFlex: {
    flex: 1,
    marginRight: 10,
  },
  lookupButton: {
    backgroundColor: '#34C759',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  lookupButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fafafa',
  },
  picker: {
    height: 50,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  generateButton: {
    backgroundColor: '#007AFF',
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  generateButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  qrContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
    margin: 20,
    borderRadius: 12,
  },
  qrImage: {
    width: 250,
    height: 250,
    marginBottom: 16,
  },
  qrLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  bankInfo: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  amountInfo: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  bankSelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fafafa',
    minHeight: 50,
  },
  bankSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  bankLogoContainer: {
    width: 32,
    height: 32,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bankLogo: {
    width: 32,
    height: 32,
    resizeMode: 'contain',
  },
  bankLogoPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bankLogoText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  bankTextContainer: {
    flex: 1,
  },
  bankName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  bankFullName: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
    flex: 1,
  },
  dropdownIcon: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
});

export default VietQRGenerator;
