import React from 'react';
import {Platform, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {colors, spacing, typography} from '../../constants/theme';
import {useResponsive} from '../../hooks/useResponsive';
import Icon from '../common/Icon';

export type HeaderVariant = 'default' | 'transparent' | 'colored' | 'minimal';

export interface SharedHeaderProps {
  title?: string;
  showBackButton?: boolean;
  onBack?: () => void;
  rightComponent?: React.ReactNode;
  leftComponent?: React.ReactNode;
  variant?: HeaderVariant;
  backgroundColor?: string;
  titleColor?: string;
  elevation?: boolean;
  borderBottom?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
}

const SharedHeader: React.FC<SharedHeaderProps> = ({
  title,
  showBackButton = false,
  onBack,
  rightComponent,
  leftComponent,
  variant = 'default',
  backgroundColor,
  titleColor,
  elevation = true,
  borderBottom = true,
  style,
  titleStyle,
}) => {
  const insets = useSafeAreaInsets();
  const responsive = useResponsive();

  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'transparent':
        return {
          backgroundColor: 'transparent',
          titleColor: colors.white,
          elevation: false,
          borderBottom: false,
        };
      case 'colored':
        return {
          backgroundColor: backgroundColor || colors.green,
          titleColor: titleColor || colors.white,
          elevation: true,
          borderBottom: false,
        };
      case 'minimal':
        return {
          backgroundColor: 'transparent',
          titleColor: titleColor || colors.dark,
          elevation: false,
          borderBottom: false,
        };
      default: // 'default'
        return {
          backgroundColor: backgroundColor || colors.green,
          titleColor: titleColor || colors.white,
          elevation: true,
          borderBottom: true,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const finalBackgroundColor = backgroundColor || variantStyles.backgroundColor;
  const finalTitleColor = titleColor || variantStyles.titleColor;
  const shouldShowElevation = elevation && variantStyles.elevation;
  const shouldShowBorder = borderBottom && variantStyles.borderBottom;

  // Responsive styling
  const headerHeight = responsive.headerHeight;
  const horizontalPadding = responsive.spacing(spacing.sm);
  const verticalPadding = responsive.spacing(spacing.sm);
  const titleFontSize = responsive.fontSize(typography.fontSize.lg);
  const iconSize = responsive.isTablet ? 28 : 24;
  const touchTargetSize = responsive.minTouchTarget;

  const headerStyle = [
    styles.header,
    {
      backgroundColor: finalBackgroundColor,
      minHeight: headerHeight,
      paddingHorizontal: horizontalPadding,
      paddingVertical: verticalPadding,
      paddingTop: Math.max(verticalPadding, insets.top + verticalPadding),
    },
    shouldShowElevation && styles.headerElevation,
    shouldShowBorder && styles.headerBorder,
    style,
  ];

  return (
    <View style={headerStyle}>
      {/* Left Section */}
      <View style={[styles.headerLeft, {minWidth: touchTargetSize}]}>
        {leftComponent ? (
          leftComponent
        ) : showBackButton && onBack ? (
          <TouchableOpacity
            onPress={onBack}
            style={[styles.backButton, {width: touchTargetSize, height: touchTargetSize}]}
            activeOpacity={0.7}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Icon name={'ArrowLeft2'} size={iconSize} color={finalTitleColor} />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Center - Title */}
      <View style={styles.headerCenter}>
        {title && (
          <Text
            style={[
              styles.headerTitle,
              {
                color: finalTitleColor,
                fontSize: titleFontSize,
                fontFamily: responsive.isTablet ? typography.fontFamily.bold : typography.fontFamily.semibold,
              },
              titleStyle,
            ]}
            numberOfLines={2}
            {title}
          </Text>
        )}
      </View>

      {/* Right Section */}
      <View style={[styles.headerRight, {minWidth: touchTargetSize}]}>{rightComponent}</View>
    </View>
  );
};

// Preset Header Variants Factory
export const createHeaderVariant = {
  default: (title: string, onBack?: () => void): SharedHeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    variant: 'default',
  }),

  colored: (title: string, bgColor: string, onBack?: () => void): SharedHeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    variant: 'colored',
    backgroundColor: bgColor,
  }),

  transparent: (title: string, onBack?: () => void): SharedHeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    variant: 'transparent',
  }),

  minimal: (title: string, onBack?: () => void): SharedHeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    variant: 'minimal',
  }),

  withActions: (title: string, actions: React.ReactNode, onBack?: () => void): SharedHeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    rightComponent: actions,
    variant: 'default',
  }),
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerElevation: {
    elevation: 4,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerLeft: {
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xs,
  },
  headerRight: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  headerTitle: {
    textAlign: 'center',
    lineHeight: typography.lineHeight.tight * typography.fontSize.lg,
  },
  backButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
  },
});

export default SharedHeader;
