import React from 'react';
import {Image, Platform, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {colors, typography} from '../../constants/theme';
import {useResponsive} from '../../hooks/useResponsive';

export interface TabConfig {
  name: string;
  label: string;
  icon: any; // ImageSourcePropType
  testID?: string;
}

export interface SharedBottomTabBarProps {
  tabs: TabConfig[];
  activeTab: string;
  onTabPress: (tabName: string) => void;
  style?: any;
}

const SharedBottomTabBar: React.FC<SharedBottomTabBarProps> = ({
  tabs,
  activeTab,
  onTabPress,
  style,
}) => {
  const insets = useSafeAreaInsets();
  const responsive = useResponsive();

  // Responsive calculations
  const tabBarHeight = responsive.tabBarHeight;
  const iconSize = responsive.iconSize;
  const fontSize = responsive.fontSize(typography.fontSize.xs);
  const paddingTop = responsive.spacing(4);
  const touchTargetSize = responsive.minTouchTarget;

  // Calculate total height including safe area
  const totalHeight = Platform.select({
    ios: tabBarHeight + insets.bottom,
    android: tabBarHeight + Math.max(insets.bottom, 12),
  });

  const tabBarStyle = [
    styles.tabBar,
    {
      height: totalHeight,
      paddingTop,
      paddingBottom: Platform.select({
        ios: insets.bottom,
        android: Math.max(insets.bottom, 12),
      }),
    },
    style,
  ];

  const renderTab = (tab: TabConfig) => {
    const isActive = activeTab === tab.name;
    const iconStyle = [
      styles.tabIcon,
      {
        width: iconSize,
        height: iconSize,
        tintColor: isActive ? colors.green : colors.gray[500],
      },
      isActive && styles.tabIconFocus,
    ];

    const textStyle = [
      styles.tabText,
      {
        fontSize,
        color: isActive ? colors.green : colors.gray[500],
        fontFamily: isActive 
          ? typography.fontFamily.semibold 
          : typography.fontFamily.regular,
      },
    ];

    const tabStyle = [
      styles.tab,
      {
        minHeight: touchTargetSize,
        paddingHorizontal: responsive.spacing(4),
      },
    ];

    return (
      <TouchableOpacity
        key={tab.name}
        style={tabStyle}
        onPress={() => onTabPress(tab.name)}
        activeOpacity={0.7}
        testID={tab.testID}
        accessibilityRole="tab"
        accessibilityState={{selected: isActive}}
        accessibilityLabel={tab.label}>
        <Image source={tab.icon} style={iconStyle} resizeMode="contain" />
        <Text style={textStyle}>{tab.label}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={tabBarStyle}>
      <View style={styles.tabContainer}>
        {tabs.map(renderTab)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    elevation: 8,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    marginBottom: 4,
  },
  tabIconFocus: {
    // Slightly larger when focused (handled by iconSize calculation)
  },
  tabText: {
    textAlign: 'center',
    lineHeight: typography.lineHeight.tight * typography.fontSize.xs,
  },
});

export default SharedBottomTabBar;
