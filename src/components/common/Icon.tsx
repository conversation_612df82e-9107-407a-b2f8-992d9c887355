import React from 'react';
import * as Icons from 'iconsax-react-native';
import { ViewStyle } from 'react-native';

export type IconVariant = 'Linear' | 'Outline' | 'Broken' | 'Bold' | 'Bulk' | 'TwoTone';

export interface IconProps {
  /** Tên icon từ thư viện iconsax-react-native */
  name: keyof typeof Icons;
  /** Kích thước icon (default: 24) */
  size?: number;
  /** Màu sắc icon (default: '#000000') */
  color?: string;
  /** Biến thể icon (default: 'Linear') */
  variant?: IconVariant;
  /** Style bổ sung cho container */
  style?: ViewStyle;
  /** Callback khi icon được nhấn */
  onPress?: () => void;
}

/**
 * Component Icon sử dụng thư viện iconsax-react-native
 * 
 * @param name - Tên icon từ thư viện iconsax
 * @param size - Kích thước icon (mặc định: 24)
 * @param color - <PERSON><PERSON><PERSON> sắc icon (mặc định: '#000000')
 * @param variant - <PERSON>i<PERSON>n thể icon (mặc định: 'Linear')
 * @param style - Style bổ sung
 * @param onPress - Callback khi nhấn icon
 */
export const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = '#000000',
  variant = 'Linear',
  style,
  onPress,
}) => {
  // Lấy icon component từ thư viện
  const IconComponent = Icons[name] as any;

  if (!IconComponent) {
    console.warn(`Icon "${name}" không tồn tại trong thư viện iconsax-react-native`);
    return null;
  }

  return (
    <IconComponent
      size={size}
      color={color}
      variant={variant}
      style={style}
      onPress={onPress}
    />
  );
};

export default Icon;