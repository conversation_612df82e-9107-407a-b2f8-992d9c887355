import React from 'react';
import * as IconsaxIcons from 'iconsax-react-native';
import * as LucideIcons from 'lucide-react-native';
import {ViewStyle} from 'react-native';

export type IconVariant = 'Linear' | 'Outline' | 'Broken' | 'Bold' | 'Bulk' | 'TwoTone';
export type IconLibrary = 'iconsax' | 'lucide';

// Lấy tên các icon từ lucide (loại bỏ các export không phải icon)
type LucideIconName = keyof typeof LucideIcons;
type IconsaxIconName = keyof typeof IconsaxIcons;

export interface IconProps {
  /** Tên icon từ thư viện */
  name: IconsaxIconName | LucideIconName;
  /** Kích thước icon (default: 24) */
  size?: number;
  /** Màu sắc icon (default: '#000000') */
  color?: string;
  /** Biến thể icon - chỉ áp dụng cho iconsax (default: 'Linear') */
  variant?: IconVariant;
  /** Style bổ sung cho container */
  style?: ViewStyle;
  /** Callback khi icon được nhấn */
  onPress?: () => void;
  /** Thư viện icon sử dụng (default: 'iconsax') */
  library?: IconLibrary;
  /** Stroke width - chỉ áp dụng cho lucide (default: 2) */
  strokeWidth?: number;
}

/**
 * Component Icon hỗ trợ cả 2 thư viện: iconsax-react-native và lucide-react-native
 *
 * @example
 * // Sử dụng iconsax (mặc định)
 * <Icon name="Home" size={24} color="#000" variant="Bold" />
 *
 * @example
 * // Sử dụng lucide
 * <Icon name="X" size={24} color="#000" library="lucide" strokeWidth={2} />
 */
export const Icon: React.FC<IconProps> = ({name, size = 24, color = '#000000', variant = 'Linear', style, onPress, library = 'iconsax', strokeWidth = 2}) => {
  if (library === 'lucide') {
    // Sử dụng lucide-react-native
    const LucideIcon = LucideIcons[name as LucideIconName] as any;

    if (!LucideIcon) {
      console.warn(`Icon "${name}" không tồn tại trong thư viện lucide-react-native`);
      return null;
    }

    return <LucideIcon size={size} color={color} strokeWidth={strokeWidth} style={style} onPress={onPress} />;
  }

  // Mặc định sử dụng iconsax-react-native
  const IconsaxIcon = IconsaxIcons[name as IconsaxIconName] as any;

  if (!IconsaxIcon) {
    console.warn(`Icon "${name}" không tồn tại trong thư viện iconsax-react-native`);
    return null;
  }

  return <IconsaxIcon size={size} color={color} variant={variant} style={style} onPress={onPress} />;
};

export default Icon;
