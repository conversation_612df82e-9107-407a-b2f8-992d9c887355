import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Control, Controller, FieldValues, Path} from 'react-hook-form';
import {colors, spacing, borderRadius, shadows, typography} from '@constants/theme';
import Icon from './Icon';

interface OptionItem {
  id: string;
  label: string;
  iconName: keyof typeof import('iconsax-react-native');
}

interface SelectableOptionProps<TFieldValues extends FieldValues> {
  control: Control<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  options: OptionItem[];
  required?: boolean;
  error?: string;
}

function SelectableOption<TFieldValues extends FieldValues>({control, name, label, options, required, error}: SelectableOptionProps<TFieldValues>) {
  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}

      <Controller
        control={control}
        name={name}
        render={({field: {onChange, value}}) => (
          <View style={styles.optionsContainer}>
            {options.map(option => {
              const isSelected = value === option.id;
              return (
                <TouchableOpacity key={option.id} style={[styles.optionCard, isSelected && styles.optionCardSelected]} onPress={() => onChange(option.id)} activeOpacity={0.7}>
                  <View style={[styles.iconContainer, isSelected && styles.iconContainerSelected]}>
                    <Icon name={option.iconName} size={32} color={isSelected ? colors.green : colors.gray[600]} variant="Bold" />
                  </View>
                  <Text style={[styles.optionLabel, isSelected && styles.optionLabelSelected]}>{option.label}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
        )}
      />

      {error && <Text style={styles.error}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
    marginBottom: spacing.sm,
  },
  required: {
    color: colors.danger,
  },
  optionsContainer: {
    flexDirection: 'row',
    gap: spacing.xs,
    marginHorizontal: -spacing.xs / 2,
  },
  optionCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.lg,
    padding: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
    marginHorizontal: spacing.xs / 2,
    ...shadows.sm,
  },
  optionCardSelected: {
    borderColor: colors.green,
    borderWidth: 2,
    backgroundColor: '#F0F8E8',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.base,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xs,
  },
  iconContainerSelected: {
    backgroundColor: '#E8F5D9',
  },
  optionLabel: {
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },
  optionLabelSelected: {
    color: colors.green,
    fontWeight: '600',
  },
  error: {
    fontSize: 14,
    color: colors.danger,
    marginTop: spacing.xs,
  },
});

export default SelectableOption;
