import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {colors, typography, spacing, borderRadius} from '@constants/theme';
import {Image} from 'react-native';
import R from '@assets/R';

interface TrendInfo {
  direction: 'up' | 'down' | 'stable';
  percentage: number;
  period?: string;
}

interface StatisticCardProps {
  title: string;
  amount: string;
  subtitle?: string | any;
  text?: string | any;
  icon?: any;
  backgroundColor?: string;
  iconBackgroundColor?: string;
  trend?: TrendInfo;
  onPress?: () => void;
}

export default function StatisticCard({title, amount, subtitle, text, backgroundColor = colors.primary, trend, onPress}: StatisticCardProps) {
  const getTrendIcon = () => {
    if (!trend) return null;
    switch (trend.direction) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      case 'stable':
        return '→';
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    if (!trend) return colors.white;
    switch (trend.direction) {
      case 'up':
        return '#4CAF50';
      case 'down':
        return '#F44336';
      case 'stable':
        return colors.white;
      default:
        return colors.white;
    }
  };
  return (
    <View style={[styles.container, {backgroundColor}]}>
      <View style={styles.content}>
        <View style={styles.leftContent}>
          <View style={[styles.iconContainer, {backgroundColor: trend?.direction === 'up' ? '#E0FFF0' : '#F1E0FF'}]}>
            {trend?.direction === 'up' ? (
              <Image source={R.icons.ic_bold_chart} style={[styles.icon, {tintColor: colors.green}]} />
            ) : (
              <Image source={R.icons.ic_wallet} style={[styles.icon, {tintColor: '#BD66F2'}]} />
            )}
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.amount}>{amount}</Text>
            {trend && (
              <View style={[styles.trendContainer, {backgroundColor: trend?.direction === 'up' ? '#E0FFF0' : '#FFE0E0'}]}>
                <Text style={[styles.trendIcon, {color: getTrendColor()}]}>{getTrendIcon()}</Text>
                <Text style={[styles.trendText, {color: getTrendColor()}]}>
                  {trend.percentage}% {trend.period && `(${trend.period})`}
                </Text>
              </View>
            )}
            <Text>
              {subtitle && <Text style={[styles.subtitle, {color: trend ? getTrendColor() : colors.dark}]}>{subtitle}</Text>} {text && <Text style={styles.text}>{text}</Text>}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    minHeight: 100,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
    padding: spacing.sm + 2,
  },
  icon: {
    width: 28,
    height: 28,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  amount: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.green,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.dark,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xs,
    borderRadius: borderRadius.sm,
    maxWidth: '25%',
    overflow: 'hidden',
  },
  trendIcon: {
    fontSize: 16,
    fontWeight: typography.fontWeight.bold,
    marginRight: spacing.xs / 2,
  },
  trendText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  text: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.dark,
  },
});
