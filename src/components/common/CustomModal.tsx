import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ViewStyle, TextStyle} from 'react-native';
import Modal from 'react-native-modal';
import {colors, typography, spacing, borderRadius, shadows} from '../../constants/theme';
import {dimensions} from '../../constants/dimensions';

export interface CustomModalProps {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  children?: React.ReactNode;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  closeOnBackButtonPress?: boolean;
  animationIn?: 'slideInUp' | 'slideInDown' | 'slideInLeft' | 'slideInRight' | 'fadeIn' | 'zoomIn';
  animationOut?: 'slideOutDown' | 'slideOutUp' | 'slideOutLeft' | 'slideOutRight' | 'fadeOut' | 'zoomOut';
  animationInTiming?: number;
  animationOutTiming?: number;
  backdropOpacity?: number;
  modalStyle?: ViewStyle;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  onModalShow?: () => void;
  onModalHide?: () => void;
}

const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  onClose,
  title,
  children,
  showCloseButton = true,
  closeOnBackdropPress = true,
  closeOnBackButtonPress = true,
  animationIn = 'slideInUp',
  animationOut = 'slideOutDown',
  animationInTiming = 300,
  animationOutTiming = 300,
  backdropOpacity = 0.5,
  modalStyle,
  contentStyle,
  titleStyle,
  onModalShow,
  onModalHide,
}) => {
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={closeOnBackdropPress ? onClose : undefined}
      onBackButtonPress={closeOnBackButtonPress ? onClose : undefined}
      animationIn={animationIn}
      animationOut={animationOut}
      animationInTiming={animationInTiming}
      animationOutTiming={animationOutTiming}
      backdropOpacity={backdropOpacity}
      useNativeDriver={true}
      hideModalContentWhileAnimating={true}
      onModalShow={onModalShow}
      onModalHide={onModalHide}
      style={[styles.modal, modalStyle]}>
      <View style={[styles.container, contentStyle]}>
        {/* Header */}
        {(title || showCloseButton) && (
          <View style={styles.header}>
            {title && (
              <Text style={[styles.title, titleStyle]} numberOfLines={1}>
                {title}
              </Text>
            )}
            {showCloseButton && (
              <TouchableOpacity style={styles.closeButton} onPress={onClose} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Content */}
        <View style={styles.content}>{children}</View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: spacing.md,
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    maxHeight: dimensions.height * 0.8,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: typography.fontSize.lg,
    color: colors.dark,
    flex: 1,
    marginRight: spacing.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.full,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.medium,
  },
  content: {
    padding: spacing.lg,
  },
});

export default CustomModal;
