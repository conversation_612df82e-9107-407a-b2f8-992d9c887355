import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import Modal from 'react-native-modal';
import {dimensions} from '../../constants/dimensions';
import {borderRadius, colors, spacing, typography} from '../../constants/theme';
import Icon from './Icon';

export interface CustomModalProps {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  children?: React.ReactNode;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  closeOnBackButtonPress?: boolean;
  animationIn?: 'slideInUp' | 'slideInDown' | 'slideInLeft' | 'slideInRight' | 'fadeIn' | 'zoomIn';
  animationOut?: 'slideOutDown' | 'slideOutUp' | 'slideOutLeft' | 'slideOutRight' | 'fadeOut' | 'zoomOut';
  animationInTiming?: number;
  animationOutTiming?: number;
  backdropOpacity?: number;
  modalStyle?: ViewStyle;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  onModalShow?: () => void;
  onModalHide?: () => void;
  headerDivider?: boolean;
  enableHaptics?: boolean;
}

const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  onClose,
  title,
  children,
  showCloseButton = true,
  closeOnBackdropPress = true,
  closeOnBackButtonPress = true,
  animationIn = 'zoomIn',
  animationOut = 'zoomOut',
  animationInTiming = 300,
  animationOutTiming = 250,
  backdropOpacity = 0.5,
  modalStyle,
  contentStyle,
  titleStyle,
  onModalShow,
  onModalHide,
  headerDivider = true,
  enableHaptics = true,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    if (isVisible) {
      // Fade in animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 60,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.95);
    }
  }, [isVisible, fadeAnim, scaleAnim]);

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={closeOnBackdropPress ? handleClose : undefined}
      onBackButtonPress={closeOnBackButtonPress ? handleClose : undefined}
      animationIn={animationIn}
      animationOut={animationOut}
      animationInTiming={animationInTiming}
      animationOutTiming={animationOutTiming}
      backdropOpacity={backdropOpacity}
      backdropTransitionInTiming={300}
      backdropTransitionOutTiming={250}
      useNativeDriver={true}
      hideModalContentWhileAnimating={true}
      onModalShow={onModalShow}
      onModalHide={onModalHide}
      style={[styles.modal, modalStyle]}>
      <Animated.View
        style={[
          styles.container,
          contentStyle,
          {
            opacity: fadeAnim,
            transform: [{scale: scaleAnim}],
          },
        ]}>
        {/* Header */}
        {(title || showCloseButton) && (
          <>
            <View style={styles.header}>
              {title && (
                <Text style={[styles.title, titleStyle]} numberOfLines={2}>
                  {title}
                </Text>
              )}
              {showCloseButton && (
                <TouchableOpacity style={styles.closeButton} onPress={handleClose} activeOpacity={0.7} hitSlop={{top: 12, bottom: 12, left: 12, right: 12}}>
                  <View style={styles.closeIconWrapper}>
                    <Icon name="CloseCircle" size={22} color={colors.gray[600]} variant="Bold" />
                  </View>
                </TouchableOpacity>
              )}
            </View>
            {headerDivider && title && <View style={styles.divider} />}
          </>
        )}

        {/* Content */}
        <View style={[styles.content, !title && !showCloseButton && styles.contentNoHeader]}>{children}</View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: 0,
    padding: spacing.lg,
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: 24,
    maxHeight: dimensions.height * 0.85,
    width: '100%',
    maxWidth: 480,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.sm,
    gap: spacing.lg,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.gray[900],
    flex: 1,
    lineHeight: typography.fontSize.xl * 1.4,
    letterSpacing: -0.3,
  },
  closeButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIconWrapper: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginHorizontal: spacing.lg,
  },
  content: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.lg,
  },
  contentNoHeader: {
    paddingTop: spacing.lg,
  },
});

export default CustomModal;
