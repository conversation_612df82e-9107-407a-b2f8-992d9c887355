import React, {useEffect} from 'react';
import {StatusBar, Platform} from 'react-native';

interface StatusBarManagerProps {
  backgroundColor?: string;
  barStyle?: 'light-content' | 'dark-content';
  translucent?: boolean;
}

const StatusBarManager: React.FC<StatusBarManagerProps> = ({backgroundColor = '#96bf49', barStyle = 'dark-content', translucent = Platform.OS === 'android'}) => {
  useEffect(() => {
    // Set status bar background color
    StatusBar.setBackgroundColor(backgroundColor, true);

    // Set status bar style
    StatusBar.setBarStyle(barStyle, true);

    // Set translucent
    StatusBar.setTranslucent(translucent);

    // Cleanup function to reset to default when component unmounts
    return () => {
      StatusBar.setBackgroundColor('#ffffff', true);
      StatusBar.setBarStyle('dark-content', true);
      StatusBar.setTranslucent(false);
    };
  }, [backgroundColor, barStyle, translucent]);

  return null; // This component doesn't render anything
};

export default StatusBarManager;
