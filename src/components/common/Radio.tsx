import React, {useState, useEffect, createContext, useContext, useMemo, useCallback} from 'react';
import {TouchableOpacity, View, Text, StyleSheet, ViewStyle, TextStyle} from 'react-native';
import {colors, spacing, typography, borderRadius} from '../../constants/theme';

// Context for Radio.Group
interface RadioContextValue {
  value?: string | number | null;
  onChange?: (value: string | number) => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  labelPosition?: 'left' | 'right';
}

const RadioContext = createContext<RadioContextValue>({});

// Radio Button Props
interface RadioButtonProps {
  label?: string;
  value: string | number;
  selected?: boolean;
  onPress?: (value: string | number) => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  labelPosition?: 'left' | 'right';
  containerStyle?: ViewStyle;
  radioStyle?: ViewStyle;
  innerCircleStyle?: ViewStyle;
  labelStyle?: TextStyle;
  color?: string;
}

// Radio Group Props
export interface RadioOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface RadioGroupProps {
  children?: React.ReactNode;
  options?: RadioOption[];
  value?: string | number | null;
  onChange?: (value: string | number) => void;
  onValueChange?: (value: string | number) => void; // alias for onChange
  orientation?: 'horizontal' | 'vertical';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  label?: string;
  required?: boolean;
  error?: string;
  helperText?: string;
  containerStyle?: ViewStyle;
  radioContainerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  helperTextStyle?: TextStyle;
  color?: string;
  spacing?: number;
  labelPosition?: 'left' | 'right';
}

// Radio Button Component
const RadioButton = React.memo<RadioButtonProps>(props => {
  const context = useContext(RadioContext);

  const {
    label,
    value,
    selected: propSelected,
    onPress: propOnPress,
    disabled: propDisabled,
    size: propSize,
    labelPosition: propLabelPosition,
    containerStyle,
    radioStyle,
    innerCircleStyle,
    labelStyle,
    color: propColor,
  } = props;

  // Use context values if available, otherwise use props
  const selected = context.value !== undefined ? context.value === value : propSelected;
  const onPress = context.onChange || propOnPress;
  const disabled = propDisabled ?? context.disabled ?? false;
  const size = propSize || context.size || 'medium';
  const labelPosition = propLabelPosition || context.labelPosition || 'right';
  const color = propColor || context.color || colors.green;

  const handlePress = useCallback(() => {
    if (!disabled && onPress) {
      onPress(value);
    }
  }, [disabled, onPress, value]);

  const getSizeStyles = useMemo(() => {
    switch (size) {
      case 'small':
        return {
          radio: styles.radioSmall,
          inner: styles.innerSmall,
          text: styles.textSmall,
        };
      case 'large':
        return {
          radio: styles.radioLarge,
          inner: styles.innerLarge,
          text: styles.textLarge,
        };
      default:
        return {
          radio: styles.radioMedium,
          inner: styles.innerMedium,
          text: styles.textMedium,
        };
    }
  }, [size]);

  const sizeStyles = getSizeStyles;
  const isLeftLabel = labelPosition === 'left';

  return (
    <TouchableOpacity
      style={[styles.radioContainer, isLeftLabel && styles.radioContainerReverse, disabled && styles.radioContainerDisabled, containerStyle]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}>
      {label && isLeftLabel && <Text style={[styles.radioLabel, sizeStyles.text, disabled && styles.radioLabelDisabled, labelStyle]}>{label}</Text>}
      <View style={[styles.radio, sizeStyles.radio, selected && {borderColor: color}, disabled && styles.radioDisabled, radioStyle]}>
        {selected && <View style={[styles.innerCircle, sizeStyles.inner, {backgroundColor: color}, disabled && styles.innerCircleDisabled, innerCircleStyle]} />}
      </View>
      {label && !isLeftLabel && <Text style={[styles.radioLabel, sizeStyles.text, disabled && styles.radioLabelDisabled, labelStyle]}>{label}</Text>}
    </TouchableOpacity>
  );
});

RadioButton.displayName = 'RadioButton';

// Radio Group Component
const RadioGroup: React.FC<RadioGroupProps> = ({
  children,
  options,
  value,
  onChange,
  onValueChange, // alias for onChange
  orientation = 'vertical',
  size = 'medium',
  disabled = false,
  label,
  required = false,
  error,
  helperText,
  containerStyle,
  radioContainerStyle,
  labelStyle,
  errorStyle,
  helperTextStyle,
  color = colors.green,
  spacing: customSpacing,
  labelPosition = 'right',
}) => {
  const [selectedValue, setSelectedValue] = useState<string | number | null>(value || null);
  const handleChange = onChange || onValueChange;

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  const handleValueChange = useCallback(
    (newValue: string | number) => {
      setSelectedValue(newValue);
      if (handleChange) {
        handleChange(newValue);
      }
    },
    [handleChange],
  );

  const radioSpacing = customSpacing || (orientation === 'horizontal' ? spacing.md : spacing.sm);

  const contextValue = useMemo(
    () => ({
      value: selectedValue,
      onChange: handleValueChange,
      disabled,
      size,
      color,
      labelPosition,
    }),
    [selectedValue, handleValueChange, disabled, size, color, labelPosition],
  );

  return (
    <RadioContext.Provider value={contextValue}>
      <View style={[containerStyle]}>
        {label && (
          <View style={styles.labelContainer}>
            <Text style={[styles.label, labelStyle]}>
              {label}
              {required && <Text style={styles.required}> *</Text>}
            </Text>
          </View>
        )}

        <View style={[styles.groupContainer, orientation === 'horizontal' && styles.groupContainerHorizontal, radioContainerStyle]}>
          {children
            ? children
            : options?.map((option, index) => (
                <View
                  key={option.value}
                  style={[
                    orientation === 'horizontal' && index < options.length - 1 && {marginRight: radioSpacing},
                    orientation === 'vertical' && index < options.length - 1 && {marginBottom: radioSpacing},
                  ]}>
                  <RadioButton label={option.label} value={option.value} disabled={option.disabled} />
                </View>
              ))}
        </View>

        {helperText && !error && <Text style={[styles.helperText, helperTextStyle]}>{helperText}</Text>}

        {error && <Text style={[styles.errorText, errorStyle]}>{error}</Text>}
      </View>
    </RadioContext.Provider>
  );
};

// Main Radio Component with sub-components
interface RadioComponent extends React.FC<RadioButtonProps> {
  Group: typeof RadioGroup;
  Button: typeof RadioButton;
}

const Radio = RadioButton as any as RadioComponent;
Radio.Group = RadioGroup;
Radio.Button = RadioButton;

const styles = StyleSheet.create({
  labelContainer: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  label: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[800],
  },
  required: {
    color: colors.danger,
    fontSize: typography.fontSize.base,
  },
  groupContainer: {
    flexDirection: 'column',
  },
  groupContainerHorizontal: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  radioContainerReverse: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-end',
  },
  radioContainerDisabled: {
    opacity: 0.5,
  },
  radio: {
    borderWidth: 2,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioSmall: {
    width: 16,
    height: 16,
  },
  radioMedium: {
    width: 20,
    height: 20,
  },
  radioLarge: {
    width: 24,
    height: 24,
  },
  radioDisabled: {
    borderColor: colors.gray[300],
  },
  innerCircle: {
    borderRadius: borderRadius.full,
  },
  innerSmall: {
    width: 8,
    height: 8,
  },
  innerMedium: {
    width: 10,
    height: 10,
  },
  innerLarge: {
    width: 12,
    height: 12,
  },
  innerCircleDisabled: {
    backgroundColor: colors.gray[400],
  },
  radioLabel: {
    marginHorizontal: spacing.sm,
    color: colors.gray[800],
  },
  textSmall: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
  },
  textMedium: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
  },
  textLarge: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.regular,
  },
  radioLabelDisabled: {
    color: colors.gray[400],
  },
  helperText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    color: colors.danger,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
});

export default Radio;
