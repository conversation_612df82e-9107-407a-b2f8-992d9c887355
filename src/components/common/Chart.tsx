import React from 'react';
import {View, Text, StyleSheet, ViewStyle, TextStyle} from 'react-native';
import Svg, {Circle, G, Path} from 'react-native-svg';
import {colors, typography, spacing} from '../../constants/theme';

export interface ChartDataItem {
  label: string;
  value: number;
  color: string;
}

export interface ChartProps {
  data: ChartDataItem[];
  size?: number;
  strokeWidth?: number;
  centerContent?: React.ReactNode;
  labelPosition?: 'bottom' | 'right';
  showLabels?: boolean;
  showValues?: boolean;
  valueFormatter?: (value: number) => string;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  valueStyle?: TextStyle;
  centerTextStyle?: TextStyle;
  legendSpacing?: number;
}

const Chart: React.FC<ChartProps> = ({
  data,
  size = 200,
  strokeWidth = 30,
  centerContent,
  labelPosition = 'bottom',
  showLabels = true,
  showValues = true,
  valueFormatter = value => `${value.toFixed(1)} Tr`,
  containerStyle,
  labelStyle,
  valueStyle,
  centerTextStyle,
  legendSpacing = spacing.sm,
}) => {
  // Calculate total value
  const total = data.reduce((sum, item) => sum + item.value, 0);

  // Calculate percentages and angles
  const dataWithAngles = data.map(item => ({
    ...item,
    percentage: (item.value / total) * 100,
    angle: (item.value / total) * 360,
  }));

  // SVG properties
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // Create path segments for donut chart
  const createArcPath = (startAngle: number, endAngle: number) => {
    const start = polarToCartesian(center, center, radius, endAngle);
    const end = polarToCartesian(center, center, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';

    return ['M', start.x, start.y, 'A', radius, radius, 0, largeArcFlag, 0, end.x, end.y].join(' ');
  };

  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
    return {
      x: centerX + radius * Math.cos(angleInRadians),
      y: centerY + radius * Math.sin(angleInRadians),
    };
  };

  // Generate chart segments
  let currentAngle = 0;
  const chartSegments = dataWithAngles.map((item, index) => {
    const startAngle = currentAngle;
    const endAngle = currentAngle + item.angle;
    const path = createArcPath(startAngle, endAngle);

    currentAngle = endAngle; // No gap, original style

    return <Path key={index} d={path} stroke={item.color} strokeWidth={strokeWidth} fill="none" strokeLinecap="butt" />;
  });

  // Use original order without complex layering
  const orderedSegments = chartSegments;

  // Legend item component
  const LegendItem: React.FC<{item: ChartDataItem & {percentage: number}}> = ({item}) => {
    return (
      <View style={[styles.legendItem, {marginBottom: legendSpacing}, labelPosition === 'bottom' && {marginBottom: spacing.xs, marginRight: spacing.sm}]}>
        <View style={[styles.legendColor, {backgroundColor: item.color}]} />
        <View style={[labelPosition === 'right' ? styles.legendTextContainerColumn : styles.legendTextContainer]}>
          <Text style={[labelPosition === 'right' ? styles.legendText : styles.legendTextWithMargin, labelStyle]}>{item.label}</Text>
          {showValues && labelPosition === 'right' && <Text style={[styles.legendValue, valueStyle]}>{valueFormatter(item.value)}</Text>}
        </View>
      </View>
    );
  };

  // Legend component
  const Legend = () => (
    <View style={[styles.legend, labelPosition === 'right' ? styles.legendVertical : styles.legendHorizontal]}>
      {dataWithAngles.map((item, index) => (
        <LegendItem key={index} item={item} />
      ))}
    </View>
  );

  return (
    <View style={[styles.container, labelPosition === 'right' && styles.containerRow, containerStyle]}>
      {/* Chart */}
      <View style={styles.chartContainer}>
        <Svg width={size} height={size}>
          <G>{orderedSegments}</G>
        </Svg>

        {/* Center content */}
        {centerContent && (
          <View
            style={[
              styles.centerContent,
              {
                width: radius * 2 - strokeWidth,
                height: radius * 2 - strokeWidth,
              },
            ]}>
            {centerContent}
          </View>
        )}
      </View>

      {/* Legend */}
      {showLabels && <Legend />}
    </View>
  );
};

// Default center content component for revenue display
export const RevenueCenterContent: React.FC<{
  title: string;
  value: string;
  titleStyle?: TextStyle;
  valueStyle?: TextStyle;
}> = ({title, value, titleStyle, valueStyle}) => (
  <View style={styles.revenueCenter}>
    <Text style={[styles.revenueTitle, titleStyle]}>{title}</Text>
    <Text style={[styles.revenueValue, valueStyle]}>{value}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    width: '100%',
  },
  containerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: '100%',
  },
  chartContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerContent: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 999,
  },
  legend: {
    alignItems: 'flex-start',
  },
  legendHorizontal: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: spacing.md,
    maxWidth: 300,
  },
  legendVertical: {
    marginLeft: spacing.lg,
    justifyContent: 'center',
    flex: 1,
    minWidth: 120,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.md,
    marginBottom: spacing.xs,
    minHeight: 24,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.xs,
  },
  legendTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendTextContainerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  legendTextContainerColumn: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  legendText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
    fontWeight: typography.fontWeight.medium,
  },
  legendTextWithMargin: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
    fontWeight: typography.fontWeight.medium,
    marginRight: spacing.xs,
  },
  legendValue: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[900],
    fontWeight: typography.fontWeight.semibold,
  },
  revenueCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  revenueTitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    fontWeight: typography.fontWeight.medium,
    textAlign: 'center',
    marginBottom: 4,
  },
  revenueValue: {
    fontSize: typography.fontSize.lg,
    color: colors.dark,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
});

export default Chart;
