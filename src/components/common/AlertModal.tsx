import {borderRadius, colors, spacing, typography} from '@constants/theme';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Animated, Easing, Image, ImageSourcePropType, Modal, Platform, StyleSheet, Text, TouchableOpacity, Vibration, View} from 'react-native';
import Icon from './Icon';

interface AlertModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  showCancelButton?: boolean;
  /** Custom image source to display instead of default icon */
  image?: ImageSourcePropType;
  /** Image size (width & height), default 120 */
  imageSize?: number;
}

const AlertModal = React.memo<AlertModalProps>(
  ({visible, onClose, title, message, type = 'info', confirmText = 'Đồng ý', cancelText = 'Hủy', onConfirm, onCancel, showCancelButton = false, image, imageSize = 120}) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [slideAnim] = useState(new Animated.Value(600));
    const [backdropAnim] = useState(new Animated.Value(0));
    const [scaleAnim] = useState(new Animated.Value(0));

    const typeConfig = useMemo(() => {
      const baseConfig = {
        success: {
          color: colors.success,
          background: colors.success + '12',
          iconName: 'TickCircle' as const,
        },
        warning: {
          color: colors.warning,
          background: colors.warning + '12',
          iconName: 'Warning2' as const,
        },
        error: {
          color: colors.danger,
          background: colors.danger + '12',
          iconName: 'CloseCircle' as const,
        },
        info: {
          color: colors.green,
          background: colors.green + '12',
          iconName: 'InfoCircle' as const,
        },
      };
      return baseConfig[type] || baseConfig.info;
    }, [type]);

    const showModal = () => {
      setIsModalVisible(true);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]).start();
    };

    const hideModal = (callback?: () => void) => {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 600,
          duration: 300,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setIsModalVisible(false);
        callback?.();
      });
    };

    useEffect(() => {
      if (visible) {
        // Haptic feedback
        if (Platform.OS !== 'web') {
          if (type === 'error') {
            Vibration.vibrate([0, 50, 50, 50]);
          } else if (type === 'success') {
            Vibration.vibrate(50);
          } else {
            Vibration.vibrate(30);
          }
        }
        showModal();
      } else if (isModalVisible) {
        hideModal();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible, type]);

    const handleConfirm = useCallback(() => {
      if (Platform.OS !== 'web') {
        Vibration.vibrate(10);
      }
      hideModal(() => {
        onConfirm?.();
        onClose();
      });
    }, [onConfirm, onClose, hideModal]);

    const handleCancel = useCallback(() => {
      if (Platform.OS !== 'web') {
        Vibration.vibrate(10);
      }
      hideModal(() => {
        onCancel?.();
        onClose();
      });
    }, [onCancel, onClose, hideModal]);

    const handleBackdropPress = useCallback(() => {
      if (!showCancelButton) {
        handleCancel();
      }
    }, [showCancelButton, handleCancel]);

    const renderVisual = () => {
      if (image) {
        return (
          <Animated.View style={[styles.imageContainer, {transform: [{scale: scaleAnim}]}]}>
            <Image source={image} style={{width: imageSize, height: imageSize}} resizeMode="contain" />
          </Animated.View>
        );
      }

      return (
        <Animated.View
          style={[
            styles.iconWrapper,
            {
              backgroundColor: typeConfig.background,
              transform: [{scale: scaleAnim}],
            },
          ]}>
          <Icon name={typeConfig.iconName} size={56} color={typeConfig.color} variant="Bold" />
        </Animated.View>
      );
    };

    return (
      <Modal visible={isModalVisible} transparent animationType="none" onRequestClose={handleBackdropPress}>
        <View style={styles.modalOverlay}>
          {/* Animated Backdrop */}
          <Animated.View style={[styles.modalBackdrop, {opacity: backdropAnim}]}>
            <TouchableOpacity style={styles.backdropTouchable} activeOpacity={1} onPress={handleBackdropPress} />
          </Animated.View>

          {/* Floating Modal Content */}
          <Animated.View style={[styles.modalContent, {transform: [{translateY: slideAnim}]}]}>
            {/* Visual Section */}
            <View style={styles.visualSection}>{renderVisual()}</View>

            {/* Content Section */}
            <View style={styles.content}>
              <Text style={styles.title} numberOfLines={2}>
                {title}
              </Text>
              <Text style={styles.message}>{message}</Text>
            </View>

            {/* Buttons Section */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={[styles.primaryButton, {backgroundColor: typeConfig.color}]} onPress={handleConfirm} activeOpacity={0.85}>
                <Text style={styles.primaryButtonText}>{confirmText}</Text>
              </TouchableOpacity>

              {showCancelButton && (
                <TouchableOpacity style={styles.secondaryButton} onPress={handleCancel} activeOpacity={0.7}>
                  <Text style={styles.secondaryButtonText}>{cancelText}</Text>
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>
        </View>
      </Modal>
    );
  },
);

AlertModal.displayName = 'AlertModal';

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 24,
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.lg,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
  },
  visualSection: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.gray[900],
    textAlign: 'center',
    marginBottom: spacing.sm,
    lineHeight: typography.fontSize.xl * 1.3,
  },
  message: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.fontSize.base * 1.5,
  },
  buttonContainer: {
    width: '100%',
    gap: spacing.sm,
  },
  primaryButton: {
    paddingVertical: spacing.md + 2,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.green,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as any,
    letterSpacing: 0.3,
  },
  secondaryButton: {
    paddingVertical: spacing.md + 2,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray[100],
  },
  secondaryButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    fontWeight: typography.fontWeight.medium as any,
  },
});

export default AlertModal;
