import React, {useMemo, useCallback} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CustomModal from './CustomModal';
import Button from './Button';
import {colors, spacing, typography} from '@constants/theme';

interface AlertModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  confirmText?: string;
  onConfirm?: () => void;
}

const AlertModal = React.memo<AlertModalProps>(({visible, onClose, title, message, type = 'info', confirmText = 'OK', onConfirm}) => {
  const {icon, color} = useMemo(() => {
    switch (type) {
      case 'success':
        return {icon: '✓', color: colors.success};
      case 'warning':
        return {icon: '!', color: colors.warning};
      case 'error':
        return {icon: '✕', color: colors.danger};
      case 'info':
      default:
        return {icon: 'i', color: colors.primary};
    }
  }, [type]);

  return (
    <CustomModal isVisible={visible} onClose={onClose} showCloseButton={false}>
      <View style={styles.container}>
        <View style={[styles.iconContainer, {backgroundColor: color + '20'}]}>
          <Text style={[styles.icon, {color}]}>{icon}</Text>
        </View>

        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>

        <Button
          title={confirmText}
          onPress={useCallback(() => {
            onConfirm?.();
            onClose();
          }, [onConfirm, onClose])}
          variant="primary"
          size="medium"
          style={[styles.button, {backgroundColor: color}]}
        />
      </View>
    </CustomModal>
  );
});

AlertModal.displayName = 'AlertModal';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  icon: {
    fontSize: 28,
    fontWeight: typography.fontWeight.bold as any,
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.black,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  message: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
    lineHeight: 22,
  },
  button: {
    minWidth: 120,
  },
});

export default AlertModal;
