import {borderRadius, colors, spacing, typography} from '@constants/theme';
import React from 'react';
import {StyleSheet, Text, View, ViewStyle} from 'react-native';

interface CardProps {
  title?: string;
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: number;
  margin?: number;
}

const Card = React.memo<CardProps>(({title, children, style, padding = 14, margin = 6}) => {
  return (
    <View style={[styles.card, {padding, margin}, style]}>
      {title && <Text style={styles.title}>{title}</Text>}
      {children}
    </View>
  );
});

Card.displayName = 'Card';

export default Card;

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: borderRadius.xl,
    // ...shadows.lg,
    // elevation: 6,
    borderWidth: 1,
    borderColor: colors.gray[400],
  },
  title: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.md,
  },
});
