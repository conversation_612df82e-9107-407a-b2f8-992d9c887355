import React, {useCallback, useMemo, useState} from 'react';
import {View, Text, StyleSheet, ViewStyle, TextStyle, Platform, TouchableOpacity} from 'react-native';
import {colors, spacing, typography, borderRadius} from '@constants/theme';
import {formatCurrency} from '@utils/formatters';

interface SliderProps {
  min: number;
  max: number;
  step?: number;
  value?: number;
  defaultValue?: number;
  onValueChange?: (value: number) => void;
  formatLabel?: (value: number) => string;
  showLabels?: boolean;
  showTooltip?: boolean;
  containerStyle?: ViewStyle;
  trackStyle?: ViewStyle;
  activeTrackStyle?: ViewStyle;
  thumbStyle?: ViewStyle;
  labelStyle?: TextStyle;
  tooltipStyle?: ViewStyle;
  tooltipTextStyle?: TextStyle;
  disabled?: boolean;
}

const THUMB_SIZE = 24;
const TRACK_HEIGHT = 6;

export default function Slider({
  min = 0,
  max = 100,
  step = 1,
  value: controlledValue,
  defaultValue,
  onValueChange,
  formatLabel = (val: number) => formatCurrency(val),
  showLabels = true,
  showTooltip = true,
  containerStyle,
  trackStyle,
  activeTrackStyle,
  thumbStyle,
  labelStyle,
  tooltipStyle,
  tooltipTextStyle,
  disabled = false,
}: SliderProps) {
  const [sliderWidth, setSliderWidth] = useState(0);
  const [internalValue, setInternalValue] = useState(controlledValue ?? defaultValue ?? min);
  const [isDragging, setIsDragging] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);

  const currentValue = controlledValue ?? internalValue;

  // Force update when sliderWidth changes to recalculate position
  React.useEffect(() => {
    if (sliderWidth > 0) {
      setForceUpdate(prev => prev + 1);
    }
  }, [sliderWidth]);

  // Calculate position percentage from value
  const getPositionPercentage = useCallback(() => {
    if (max === min) return 0;
    // Ensure currentValue is within bounds
    const clampedValue = Math.max(min, Math.min(max, currentValue));
    return ((clampedValue - min) / (max - min)) * 100;
  }, [currentValue, min, max, forceUpdate]); // Add forceUpdate to dependency

  // Calculate value from position
  const getValueFromPosition = useCallback(
    (x: number) => {
      if (sliderWidth === 0) return min;
      const percentage = Math.max(0, Math.min(1, x / sliderWidth));
      const rawValue = min + percentage * (max - min);
      const steppedValue = Math.round(rawValue / step) * step;

      // Snap to min if within 5% of range
      const snapThreshold = (max - min) * 0.05;
      if (Math.abs(steppedValue - min) < snapThreshold) {
        return min;
      }

      // Snap to max if within 5% of range
      if (Math.abs(steppedValue - max) < snapThreshold) {
        return max;
      }

      return Math.max(min, Math.min(max, steppedValue));
    },
    [min, max, step, sliderWidth],
  );

  // Handle value change
  const handleValueChange = useCallback(
    (newValue: number) => {
      if (disabled) return;

      if (controlledValue === undefined) {
        setInternalValue(newValue);
      }

      onValueChange?.(newValue);
    },
    [controlledValue, disabled, onValueChange],
  );

  // Handle track press
  const handleTrackPress = useCallback(
    (event: any) => {
      if (disabled) return;
      const {locationX} = event.nativeEvent;
      const newValue = getValueFromPosition(locationX);
      handleValueChange(newValue);
    },
    [disabled, getValueFromPosition, handleValueChange],
  );

  // Handle thumb drag
  const handleThumbMove = useCallback(
    (event: any) => {
      if (disabled || !isDragging) return;
      const {pageX} = event.nativeEvent;
      // Calculate position relative to track
      // This is simplified - in production you'd need to get track's absolute position
      const newValue = getValueFromPosition(pageX);
      handleValueChange(newValue);
    },
    [disabled, isDragging, getValueFromPosition, handleValueChange],
  );

  // Memoized formatted values
  const formattedMin = useMemo(() => formatLabel(min), [formatLabel, min]);
  const formattedMax = useMemo(() => formatLabel(max), [formatLabel, max]);
  const formattedValue = useMemo(() => formatLabel(currentValue), [formatLabel, currentValue]);

  const positionPercentage = getPositionPercentage();

  // Debug log
  React.useEffect(() => {
    logger.log('Slider values:', {currentValue, min, max, positionPercentage, sliderWidth});
  }, [currentValue, min, max, positionPercentage, sliderWidth]);

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Tooltip */}
      {showTooltip && isDragging && (
        <View
          style={[
            styles.tooltip,
            tooltipStyle,
            {
              left: `${positionPercentage}%`,
              marginLeft: -50, // Half of tooltip width
            },
          ]}>
          <Text style={[styles.tooltipText, tooltipTextStyle]}>{formattedValue}</Text>
          <View style={styles.tooltipArrow} />
        </View>
      )}

      {/* Track Container */}
      <TouchableOpacity
        activeOpacity={1}
        onPress={handleTrackPress}
        disabled={disabled}
        style={styles.trackContainer}
        onLayout={event => {
          setSliderWidth(event.nativeEvent.layout.width);
        }}>
        {/* Inactive Track */}
        <View style={[styles.track, trackStyle]} />

        {/* Active Track */}
        <View
          style={[
            styles.activeTrack,
            activeTrackStyle,
            {
              width: `${positionPercentage}%`,
            },
          ]}
        />

        {/* Thumb */}
        <View
          style={[
            styles.thumb,
            thumbStyle,
            {
              left: `${positionPercentage}%`,
              marginLeft: -THUMB_SIZE / 2,
            },
            disabled && styles.thumbDisabled,
            isDragging && styles.thumbPressed,
          ]}
          onStartShouldSetResponder={() => !disabled}
          onResponderGrant={() => setIsDragging(true)}
          onResponderMove={handleThumbMove}
          onResponderRelease={() => setIsDragging(false)}
          onResponderTerminate={() => setIsDragging(false)}
        />
      </TouchableOpacity>

      {/* Labels */}
      {showLabels && (
        <View style={styles.labelsContainer}>
          <Text style={[styles.label, labelStyle]}>{formattedMin}</Text>
          <Text style={[styles.label, labelStyle]}>{formattedMax}</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: spacing.md,
  },
  trackContainer: {
    height: THUMB_SIZE,
    justifyContent: 'center',
    position: 'relative',
  },
  track: {
    height: TRACK_HEIGHT,
    backgroundColor: colors.gray[300],
    borderRadius: TRACK_HEIGHT / 2,
  },
  activeTrack: {
    position: 'absolute',
    height: TRACK_HEIGHT,
    backgroundColor: colors.green,
    borderRadius: TRACK_HEIGHT / 2,
    left: 0,
  },
  thumb: {
    position: 'absolute',
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    backgroundColor: colors.white,
    borderWidth: 2,
    borderColor: colors.green,
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  thumbDisabled: {
    backgroundColor: colors.gray[300],
    borderColor: colors.gray[400],
  },
  thumbPressed: {
    transform: [{scale: 1.2}],
  },
  tooltip: {
    position: 'absolute',
    top: -20,
    backgroundColor: colors.black,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.base,
    minWidth: 100,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 6,
      },
    }),
  },
  tooltipText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -4,
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: colors.black,
  },
  labelsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.sm,
  },
  label: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[700],
  },
});
