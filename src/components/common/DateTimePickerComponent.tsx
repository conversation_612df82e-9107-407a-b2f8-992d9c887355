import React, {forwardRef, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {Animated, Easing, Modal, Platform, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {Control, Controller, FieldError, FieldValues, Path, RegisterOptions} from 'react-hook-form';
import {borderRadius, colors, spacing, typography} from '../../constants/theme';
import Icon from './Icon';
import moment from 'moment';

export type DateTimePickerMode = 'date' | 'time' | 'datetime';
export type DateTimePickerFormat = 'DD/MM/YYYY' | 'HH:mm' | 'DD/MM/YYYY HH:mm' | 'custom';

interface BaseDateTimePickerProps {
  label?: string;
  placeholder?: string;
  error?: FieldError | string;

  containerStyle?: ViewStyle;
  inputContainerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  required?: boolean;
  disabled?: boolean;
  mode?: DateTimePickerMode;
  format?: DateTimePickerFormat | string;
  minimumDate?: Date;
  maximumDate?: Date;
  minuteInterval?: 1 | 2 | 3 | 4 | 5 | 6 | 10 | 12 | 15 | 20 | 30;
  locale?: string;
  title?: string;
  confirmText?: string;
  cancelText?: string;
  onDateChange?: (date: Date) => void;
  showIcon?: boolean;
  iconName?: keyof typeof import('iconsax-react-native');
  iconColor?: string;
  showTitle?: boolean;
  showPlaceholder?: boolean;
}

interface ControlledDateTimePickerProps<TFieldValues extends FieldValues> extends BaseDateTimePickerProps {
  control: Control<TFieldValues>;
  name: Path<TFieldValues>;
  rules?: Omit<RegisterOptions<TFieldValues, Path<TFieldValues>>, 'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'>;
  defaultValue?: Date;
}

interface UncontrolledDateTimePickerProps extends BaseDateTimePickerProps {
  value?: Date;
  onDateChange: (date: Date) => void;
}

export interface DateTimePickerRef {
  focus: () => void;
  blur: () => void;
  clear: () => void;
}

// Type guard to check if props are for controlled component
function isControlledProps<TFieldValues extends FieldValues>(props: any): props is ControlledDateTimePickerProps<TFieldValues> {
  return 'control' in props && 'name' in props;
}

// Helper function to format date based on mode and format
const formatDate = (date: Date, mode: DateTimePickerMode, format?: DateTimePickerFormat | string): string => {
  if (!date) return '';

  const momentDate = moment(date);

  if (typeof format === 'string' && !['DD/MM/YYYY', 'HH:mm', 'DD/MM/YYYY HH:mm'].includes(format)) {
    return momentDate.format(format);
  }

  switch (mode) {
    case 'date':
      return momentDate.format('DD/MM/YYYY');
    case 'time':
      return momentDate.format('HH:mm');
    case 'datetime':
      return momentDate.format('DD/MM/YYYY HH:mm');
    default:
      return momentDate.format('DD/MM/YYYY');
  }
};

// Helper function to get placeholder text
const getPlaceholder = (mode: DateTimePickerMode): string => {
  switch (mode) {
    case 'date':
      return 'Chọn ngày';
    case 'time':
      return 'Chọn giờ';
    case 'datetime':
      return 'Chọn ngày và giờ';
    default:
      return 'Chọn ngày';
  }
};

// Helper function to get icon name
const getIconName = (mode: DateTimePickerMode): keyof typeof import('iconsax-react-native') => {
  switch (mode) {
    case 'date':
      return 'Calendar';
    case 'time':
      return 'Clock';
    case 'datetime':
      return 'Calendar';
    default:
      return 'Calendar';
  }
};

const DateTimePickerComponent = forwardRef<DateTimePickerRef, ControlledDateTimePickerProps<any> | UncontrolledDateTimePickerProps>((props, ref) => {
  const {
    label,
    placeholder,
    error,
    containerStyle,
    inputContainerStyle,
    labelStyle,
    errorStyle,
    required = false,
    disabled = false,
    mode = 'date',
    format,
    minimumDate,
    maximumDate,
    minuteInterval = 1,
    locale = 'vi',
    title,
    showTitle = true,
    showIcon = true,
    iconName,
    iconColor,
    showPlaceholder = true,
  } = props;

  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());
  const [animatedLabelPosition] = useState(new Animated.Value(0));
  const [animatedLabelSize] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(600));
  const [backdropAnim] = useState(new Animated.Value(0));

  const displayPlaceholder = placeholder || getPlaceholder(mode);
  const displayIconName = iconName || getIconName(mode);
  const displayTitle = title || `Chọn ${mode === 'date' ? 'ngày' : mode === 'time' ? 'giờ' : 'ngày và giờ'}`;
  useImperativeHandle(ref, () => ({
    focus: () => {
      setIsFocused(true);
      setIsPickerOpen(true);
    },
    blur: () => {
      setIsFocused(false);
      setIsPickerOpen(false);
    },
    clear: () => {
      if (isControlledProps(props)) {
        // For controlled component, we can't directly clear, user should handle this
        console.warn('Clear method not available for controlled DateTimePickerComponent');
      } else {
        props.onDateChange?.(new Date());
      }
    },
  }));

  // Animation effect for floating label - handle in useEffect properly
  const animateLabel = (shouldFloat: boolean) => {
    Animated.parallel([
      Animated.timing(animatedLabelPosition, {
        toValue: shouldFloat ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(animatedLabelSize, {
        toValue: shouldFloat ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Label styles for floating animation
  const labelStyles = useMemo(() => {
    if (!label) return {};

    const androidAdjustment = Platform.OS === 'android' ? -2 : 0;
    const topPosition = animatedLabelPosition.interpolate({
      inputRange: [0, 1],
      outputRange: [20 + androidAdjustment, -9], // Center vertically when not focused, float to border when focused
    });

    return {
      position: 'absolute' as const,
      left: 8, // spacing.sm instead of spacing.md
      backgroundColor: disabled ? colors.gray[100] : colors.white,
      paddingHorizontal: 4,
      top: topPosition,
      fontSize: animatedLabelSize.interpolate({
        inputRange: [0, 1],
        outputRange: [16, 12],
      }),
      color: !disabled ? (isFocused ? (error ? colors.danger : colors.gray[600]) : error ? colors.danger : colors.gray[600]) : colors.gray[700],
      zIndex: 1000,
      elevation: 1000,
    };
  }, [label, animatedLabelPosition, animatedLabelSize, isFocused, error, disabled]);

  const showModal = () => {
    setIsModalVisible(true);
    setIsPickerOpen(true);
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(backdropAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = (callback?: () => void) => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 600,
        duration: 300,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(backdropAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsModalVisible(false);
      setIsPickerOpen(false);
      setIsFocused(false);
      callback?.();
    });
  };

  const openPicker = (currentValue?: Date) => {
    if (disabled) return;
    setTempDate(currentValue || new Date());
    setIsFocused(true);
    showModal();
  };

  const handleConfirm = (selectedDate: Date) => {
    hideModal(() => {
      if (!isControlledProps(props)) {
        props.onDateChange?.(selectedDate);
      }
    });
  };

  const handleCancel = () => {
    hideModal();
  };

  const renderInput = (value?: Date, onChange?: (date: Date) => void) => {
    const displayValue = value ? formatDate(value, mode, format) : '';
    const hasError = !!error;
    const isEmpty = !displayValue;
    const hasValue = !!value && !!displayValue;

    return (
      <TouchableOpacity
        style={[
          styles.inputContainer,
          hasError && styles.inputContainerError,
          disabled && styles.inputContainerDisabled,
          hasValue && !hasError && !disabled && styles.inputContainerWithValue,
          inputContainerStyle,
        ]}
        onPress={() => {
          openPicker(value);
        }}
        disabled={disabled}
        activeOpacity={0.7}>
        <Text style={[styles.inputText, isEmpty && styles.placeholderText, disabled && styles.disabledText]}>
          {displayValue || (showPlaceholder && (isFocused || isPickerOpen) ? displayPlaceholder : '')}
        </Text>
        {showIcon && <Icon name={displayIconName} size={20} color={iconColor || (hasError ? colors.danger : hasValue ? colors.gray[500] : colors.gray[400])} />}
      </TouchableOpacity>
    );
  };

  const renderPicker = (value?: Date, onChange?: (date: Date) => void) => {
    if (Platform.OS === 'ios') {
      return (
        <Modal visible={isModalVisible} transparent={true} animationType="none" onRequestClose={handleCancel}>
          <View style={styles.modalOverlay}>
            {/* Animated Backdrop */}
            <Animated.View style={[styles.modalBackdrop, {opacity: backdropAnim}]}>
              <TouchableOpacity style={styles.backdropTouchable} activeOpacity={1} onPress={handleCancel} />
            </Animated.View>

            {/* Animated Floating Modal Content */}
            <Animated.View style={[styles.modalContent, {transform: [{translateY: slideAnim}]}]}>
              {/* Header - Minimal */}
              <View style={styles.modalHeader}>
                {showTitle && <Text style={styles.modalTitle}>{displayTitle}</Text>}
                <TouchableOpacity style={styles.closeButton} onPress={handleCancel}>
                  <Icon name="CloseCircle" size={22} color={colors.gray[600]} strokeWidth={1.5} library="iconsax" variant="Bulk" />
                </TouchableOpacity>
              </View>

              {/* Selected Date Display */}
              <View style={styles.selectedDateContainer}>
                <Text style={styles.selectedDateLabel}>{mode === 'date' ? 'Ngày đã chọn' : mode === 'time' ? 'Giờ đã chọn' : 'Ngày giờ đã chọn'}</Text>
                <Text style={styles.selectedDateValue}>{formatDate(tempDate, mode, format)}</Text>
              </View>

              {/* Date Picker */}
              <View style={styles.pickerContainer}>
                <DateTimePicker
                  value={tempDate}
                  mode={mode}
                  display="spinner"
                  minimumDate={minimumDate}
                  maximumDate={maximumDate}
                  minuteInterval={minuteInterval}
                  locale={locale}
                  style={styles.picker}
                  textColor={colors.dark}
                  onChange={(event, selectedDate) => {
                    if (selectedDate) {
                      setTempDate(selectedDate);
                    }
                  }}
                />
              </View>

              {/* Confirm Button */}
              <TouchableOpacity
                style={styles.confirmButton}
                activeOpacity={0.8}
                onPress={() => {
                  onChange?.(tempDate);
                  handleConfirm(tempDate);
                }}>
                <Icon name="TickCircle" size={20} color={colors.white} />
                <Text style={styles.confirmButtonText}>Xác nhận</Text>
              </TouchableOpacity>
            </Animated.View>
          </View>
        </Modal>
      );
    }

    // Android uses native picker (default display)
    if (isPickerOpen) {
      return (
        <DateTimePicker
          value={tempDate}
          mode={mode}
          display="default"
          minimumDate={minimumDate}
          maximumDate={maximumDate}
          minuteInterval={minuteInterval}
          onChange={(event, selectedDate) => {
            if (event.type === 'set' && selectedDate) {
              handleConfirm(selectedDate);
              onChange?.(selectedDate);
            } else {
              handleCancel();
            }
          }}
        />
      );
    }

    return null;
  };

  const renderError = () => {
    if (!error) return null;

    const errorMessage = typeof error === 'string' ? error : error.message;

    return <Text style={[styles.errorText, errorStyle]}>{errorMessage}</Text>;
  };

  const renderFloatingLabel = (hasValue?: boolean, currentValue?: Date) => {
    if (!label) return null;

    // Trigger animation whenever shouldFloat changes
    const shouldFloat = isFocused || isPickerOpen || !!hasValue;

    // Use useEffect to trigger animation
    useEffect(() => {
      animateLabel(shouldFloat);
    }, [shouldFloat]);

    return (
      <TouchableOpacity
        style={styles.floatingLabelContainer}
        onPress={() => {
          if (!disabled) {
            openPicker(currentValue);
          }
        }}
        disabled={disabled}
        activeOpacity={1}>
        <View style={styles.labelBackground} />
        <Animated.Text style={[styles.floatingLabel, labelStyles, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Animated.Text>
      </TouchableOpacity>
    );
  };

  if (isControlledProps(props)) {
    return (
      <Controller
        control={props.control}
        name={props.name}
        rules={props.rules}
        defaultValue={props.defaultValue}
        render={({field: {value, onChange}, fieldState: {error: fieldError}}) => (
          <View style={[styles.container, containerStyle]}>
            <View style={styles.inputWrapper}>
              {renderFloatingLabel(!!value, value)}
              {renderInput(value, onChange)}
            </View>
            {renderPicker(value, onChange)}
            {renderError()}
          </View>
        )}
      />
    );
  }

  const uncontrolledProps = props as UncontrolledDateTimePickerProps;
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.inputWrapper}>
        {renderFloatingLabel(!!uncontrolledProps.value, uncontrolledProps.value)}
        {renderInput(uncontrolledProps.value, uncontrolledProps.onDateChange)}
      </View>
      {renderPicker(uncontrolledProps.value, uncontrolledProps.onDateChange)}
      {renderError()}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  required: {
    color: colors.danger,
  },
  floatingLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    zIndex: 1,
  },
  inputWrapper: {
    position: 'relative',
    minHeight: Platform.OS === 'android' ? 56 : 48,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.base,
    backgroundColor: colors.white,
    height: 56,
    minHeight: 56,
    paddingHorizontal: spacing.sm,
  },
  inputContainerError: {
    borderColor: colors.danger,
  },
  inputContainerDisabled: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[500],
  },
  inputContainerWithValue: {
    borderColor: colors.gray[500],
  },
  inputContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  inputText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    color: colors.black,
    paddingVertical: Platform.OS === 'android' ? spacing.xs : spacing.sm,
    minHeight: Platform.OS === 'android' ? 48 : 40,
    textAlignVertical: Platform.OS === 'android' ? 'center' : 'auto',
    paddingTop: Platform.OS === 'android' ? 18 : 16,
    paddingBottom: Platform.OS === 'android' ? 14 : 16,
  },
  placeholderText: {
    color: colors.gray[600],
  },
  disabledText: {
    color: colors.dark,
  },
  errorText: {
    fontSize: typography.fontSize.xs,
    color: colors.danger,
    marginTop: spacing.xs,
  },
  floatingLabelContainer: {
    position: 'absolute',
    zIndex: 1001,
    elevation: 1001,
  },
  labelBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: colors.white,
    zIndex: -1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 24,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    paddingTop: 20,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
    flex: 1,
  },
  closeButton: {
    padding: spacing.xs,
  },
  selectedDateContainer: {
    alignItems: 'center',
    paddingVertical: spacing.md,
    backgroundColor: colors.green + '10',
    borderRadius: borderRadius.lg,
    marginBottom: spacing.sm,
  },
  selectedDateLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing.xs,
  },
  selectedDateValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.green,
  },
  pickerContainer: {
    paddingVertical: spacing.xs,
  },
  picker: {
    height: 180,
  },
  confirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.green,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.xl,
    gap: spacing.sm,
    marginTop: spacing.sm,
    shadowColor: colors.green,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  confirmButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
});

DateTimePickerComponent.displayName = 'DateTimePickerComponent';

export default DateTimePickerComponent;
