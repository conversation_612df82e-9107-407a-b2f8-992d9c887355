import React, {forwardRef, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {Animated, Platform, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import Modal from 'react-native-modal';
import DateTimePicker from '@react-native-community/datetimepicker';
import {Control, Controller, FieldError, FieldValues, Path, RegisterOptions} from 'react-hook-form';
import {borderRadius, colors, spacing, typography} from '../../constants/theme';
import Icon from './Icon';
import CustomTouchableOpacity from './CustomTouchableOpacity';
import moment from 'moment';

export type DateTimePickerMode = 'date' | 'time' | 'datetime';
export type DateTimePickerFormat = 'DD/MM/YYYY' | 'HH:mm' | 'DD/MM/YYYY HH:mm' | 'custom';

interface BaseDateTimePickerProps {
  label?: string;
  placeholder?: string;
  error?: FieldError | string;

  containerStyle?: ViewStyle;
  inputContainerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  required?: boolean;
  disabled?: boolean;
  mode?: DateTimePickerMode;
  format?: DateTimePickerFormat | string;
  minimumDate?: Date;
  maximumDate?: Date;
  minuteInterval?: 1 | 2 | 3 | 4 | 5 | 6 | 10 | 12 | 15 | 20 | 30;
  locale?: string;
  title?: string;
  confirmText?: string;
  cancelText?: string;
  onDateChange?: (date: Date) => void;
  showIcon?: boolean;
  iconName?: keyof typeof import('iconsax-react-native');
  iconColor?: string;
  showTitle?: boolean;
  showPlaceholder?: boolean;
}

interface ControlledDateTimePickerProps<TFieldValues extends FieldValues> extends BaseDateTimePickerProps {
  control: Control<TFieldValues>;
  name: Path<TFieldValues>;
  rules?: Omit<RegisterOptions<TFieldValues, Path<TFieldValues>>, 'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'>;
  defaultValue?: Date;
}

interface UncontrolledDateTimePickerProps extends BaseDateTimePickerProps {
  value?: Date;
  onDateChange: (date: Date) => void;
}

export interface DateTimePickerRef {
  focus: () => void;
  blur: () => void;
  clear: () => void;
}

// Type guard to check if props are for controlled component
function isControlledProps<TFieldValues extends FieldValues>(props: any): props is ControlledDateTimePickerProps<TFieldValues> {
  return 'control' in props && 'name' in props;
}

// Helper function to format date based on mode and format
const formatDate = (date: Date, mode: DateTimePickerMode, format?: DateTimePickerFormat | string): string => {
  if (!date) return '';

  const momentDate = moment(date);

  if (typeof format === 'string' && !['DD/MM/YYYY', 'HH:mm', 'DD/MM/YYYY HH:mm'].includes(format)) {
    return momentDate.format(format);
  }

  switch (mode) {
    case 'date':
      return momentDate.format('DD/MM/YYYY');
    case 'time':
      return momentDate.format('HH:mm');
    case 'datetime':
      return momentDate.format('DD/MM/YYYY HH:mm');
    default:
      return momentDate.format('DD/MM/YYYY');
  }
};

// Helper function to get placeholder text
const getPlaceholder = (mode: DateTimePickerMode): string => {
  switch (mode) {
    case 'date':
      return 'Chọn ngày';
    case 'time':
      return 'Chọn giờ';
    case 'datetime':
      return 'Chọn ngày và giờ';
    default:
      return 'Chọn ngày';
  }
};

// Helper function to get icon name
const getIconName = (mode: DateTimePickerMode): keyof typeof import('iconsax-react-native') => {
  switch (mode) {
    case 'date':
      return 'Calendar';
    case 'time':
      return 'Clock';
    case 'datetime':
      return 'Calendar';
    default:
      return 'Calendar';
  }
};

const DateTimePickerComponent = forwardRef<DateTimePickerRef, ControlledDateTimePickerProps<any> | UncontrolledDateTimePickerProps>((props, ref) => {
  const {
    label,
    placeholder,
    error,
    containerStyle,
    inputContainerStyle,
    labelStyle,
    errorStyle,
    required = false,
    disabled = false,
    mode = 'date',
    format,
    minimumDate,
    maximumDate,
    minuteInterval = 1,
    locale = 'vi',
    title,
    showTitle = true,
    confirmText = 'Xác nhận',
    cancelText = 'Hủy',
    showIcon = true,
    iconName,
    iconColor,
    showPlaceholder = true,
  } = props;

  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());
  const [animatedLabelPosition] = useState(new Animated.Value(0));
  const [animatedLabelSize] = useState(new Animated.Value(0));

  const displayPlaceholder = placeholder || getPlaceholder(mode);
  const displayIconName = iconName || getIconName(mode);
  const displayTitle = title || `Chọn ${mode === 'date' ? 'ngày' : mode === 'time' ? 'giờ' : 'ngày và giờ'}`;
  useImperativeHandle(ref, () => ({
    focus: () => {
      setIsFocused(true);
      setIsPickerOpen(true);
    },
    blur: () => {
      setIsFocused(false);
      setIsPickerOpen(false);
    },
    clear: () => {
      if (isControlledProps(props)) {
        // For controlled component, we can't directly clear, user should handle this
        console.warn('Clear method not available for controlled DateTimePickerComponent');
      } else {
        props.onDateChange?.(new Date());
      }
    },
  }));

  // Animation effect for floating label - handle in useEffect properly
  const animateLabel = (shouldFloat: boolean) => {
    Animated.parallel([
      Animated.timing(animatedLabelPosition, {
        toValue: shouldFloat ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(animatedLabelSize, {
        toValue: shouldFloat ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Label styles for floating animation
  const labelStyles = useMemo(() => {
    if (!label) return {};

    const androidAdjustment = Platform.OS === 'android' ? -2 : 0;
    const topPosition = animatedLabelPosition.interpolate({
      inputRange: [0, 1],
      outputRange: [18 + androidAdjustment, -9], // Center vertically when not focused, float to border when focused
    });

    return {
      position: 'absolute' as const,
      left: 8, // spacing.sm instead of spacing.md
      backgroundColor: disabled ? colors.gray[100] : colors.white,
      paddingHorizontal: 4,
      top: topPosition,
      fontSize: animatedLabelSize.interpolate({
        inputRange: [0, 1],
        outputRange: [16, 12],
      }),
      color: !disabled ? (isFocused ? (error ? colors.danger : colors.gray[600]) : error ? colors.danger : colors.gray[600]) : colors.gray[700],
      zIndex: 1000,
      elevation: 1000,
    };
  }, [label, animatedLabelPosition, animatedLabelSize, isFocused, error, disabled]);

  const openPicker = (currentValue?: Date) => {
    if (disabled) return;
    setTempDate(currentValue || new Date());
    setIsFocused(true);
    setIsPickerOpen(true);
  };

  const handleConfirm = (selectedDate: Date) => {
    setIsPickerOpen(false);
    setIsFocused(false);
    if (isControlledProps(props)) {
      // Will be handled by Controller
    } else {
      props.onDateChange?.(selectedDate);
    }
  };

  const handleCancel = () => {
    setIsPickerOpen(false);
    setIsFocused(false);
  };

  const renderInput = (value?: Date, onChange?: (date: Date) => void) => {
    const displayValue = value ? formatDate(value, mode, format) : '';
    const hasError = !!error;
    const isEmpty = !displayValue;
    const hasValue = !!value && !!displayValue;

    return (
      <TouchableOpacity
        style={[
          styles.inputContainer,
          hasError && styles.inputContainerError,
          disabled && styles.inputContainerDisabled,
          hasValue && !hasError && !disabled && styles.inputContainerWithValue,
          inputContainerStyle,
        ]}
        onPress={() => {
          openPicker(value);
        }}
        disabled={disabled}
        activeOpacity={0.7}>
        <Text style={[styles.inputText, isEmpty && styles.placeholderText, disabled && styles.disabledText]}>
          {displayValue || (showPlaceholder && (isFocused || isPickerOpen) ? displayPlaceholder : '')}
        </Text>
        {showIcon && <Icon name={displayIconName} size={20} color={iconColor || (hasError ? colors.danger : hasValue ? colors.gray[500] : colors.gray[400])} />}
      </TouchableOpacity>
    );
  };

  const renderPicker = (value?: Date, onChange?: (date: Date) => void) => {
    if (Platform.OS === 'ios') {
      return (
        <Modal
          isVisible={isPickerOpen}
          onBackdropPress={handleCancel}
          onBackButtonPress={handleCancel}
          animationIn="slideInUp"
          animationOut="slideOutDown"
          animationInTiming={300}
          animationOutTiming={300}
          backdropOpacity={0.5}
          useNativeDriver={true}
          hideModalContentWhileAnimating={true}
          style={styles.modal}>
          <View style={styles.modalContent}>
            {/* Header with Title */}
            {showTitle && (
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{displayTitle}</Text>
              </View>
            )}

            {/* Date/Time Picker */}
            <View style={styles.pickerContainer}>
              <DateTimePicker
                value={tempDate}
                mode={mode}
                display="spinner"
                minimumDate={minimumDate}
                maximumDate={maximumDate}
                minuteInterval={minuteInterval}
                locale={locale}
                onChange={(event, selectedDate) => {
                  if (selectedDate) {
                    setTempDate(selectedDate);
                  }
                }}
              />
            </View>

            {/* Footer */}
            <View style={styles.modalFooter}>
              <View style={styles.footerButtons}>
                <CustomTouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                  <Text style={styles.cancelButtonText}>{cancelText}</Text>
                </CustomTouchableOpacity>
                <CustomTouchableOpacity
                  style={styles.confirmButton}
                  onPress={() => {
                    handleConfirm(tempDate);
                    onChange?.(tempDate);
                  }}>
                  <Text style={styles.confirmButtonText}>{confirmText}</Text>
                </CustomTouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      );
    }

    // Android uses native picker (default display)
    if (isPickerOpen) {
      return (
        <DateTimePicker
          value={tempDate}
          mode={mode}
          display="default"
          minimumDate={minimumDate}
          maximumDate={maximumDate}
          minuteInterval={minuteInterval}
          onChange={(event, selectedDate) => {
            if (event.type === 'set' && selectedDate) {
              handleConfirm(selectedDate);
              onChange?.(selectedDate);
            } else {
              handleCancel();
            }
          }}
        />
      );
    }

    return null;
  };

  const renderError = () => {
    if (!error) return null;

    const errorMessage = typeof error === 'string' ? error : error.message;

    return <Text style={[styles.errorText, errorStyle]}>{errorMessage}</Text>;
  };

  const renderFloatingLabel = (hasValue?: boolean, currentValue?: Date) => {
    if (!label) return null;

    // Trigger animation whenever shouldFloat changes
    const shouldFloat = isFocused || isPickerOpen || !!hasValue;

    // Use useEffect to trigger animation
    useEffect(() => {
      animateLabel(shouldFloat);
    }, [shouldFloat]);

    return (
      <TouchableOpacity
        style={styles.floatingLabelContainer}
        onPress={() => {
          if (!disabled) {
            openPicker(currentValue);
          }
        }}
        disabled={disabled}
        activeOpacity={1}>
        <View style={styles.labelBackground} />
        <Animated.Text style={[styles.floatingLabel, labelStyles, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Animated.Text>
      </TouchableOpacity>
    );
  };

  if (isControlledProps(props)) {
    return (
      <Controller
        control={props.control}
        name={props.name}
        rules={props.rules}
        defaultValue={props.defaultValue}
        render={({field: {value, onChange}, fieldState: {error: fieldError}}) => (
          <View style={[styles.container, containerStyle]}>
            <View style={styles.inputWrapper}>
              {renderFloatingLabel(!!value, value)}
              {renderInput(value, onChange)}
            </View>
            {renderPicker(value, onChange)}
            {renderError()}
          </View>
        )}
      />
    );
  }

  const uncontrolledProps = props as UncontrolledDateTimePickerProps;
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.inputWrapper}>
        {renderFloatingLabel(!!uncontrolledProps.value, uncontrolledProps.value)}
        {renderInput(uncontrolledProps.value, uncontrolledProps.onDateChange)}
      </View>
      {renderPicker(uncontrolledProps.value, uncontrolledProps.onDateChange)}
      {renderError()}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
    marginBottom: spacing.xs,
  },
  required: {
    color: colors.danger,
  },
  floatingLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    zIndex: 1,
  },
  inputWrapper: {
    position: 'relative',
    minHeight: Platform.OS === 'android' ? 54 : 48,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.lg,
    backgroundColor: colors.white,
    height: 54,
    minHeight: 54,
    paddingHorizontal: spacing.sm,
  },
  inputContainerError: {
    borderColor: colors.danger,
  },
  inputContainerDisabled: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[500],
  },
  inputContainerWithValue: {
    borderColor: colors.gray[500],
  },
  inputContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  inputText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    color: colors.black,
    fontFamily: typography.fontFamily.regular,
    paddingVertical: Platform.OS === 'android' ? 0 : spacing.sm,
    minHeight: Platform.OS === 'android' ? 54 : 40,
    textAlignVertical: Platform.OS === 'android' ? 'center' : 'auto',
    paddingTop: Platform.OS === 'android' ? 8 : 16,
    paddingBottom: Platform.OS === 'android' ? 8 : 16,
  },
  placeholderText: {
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  disabledText: {
    color: colors.dark,
  },
  errorText: {
    fontSize: typography.fontSize.xs,
    color: colors.danger,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  floatingLabelContainer: {
    position: 'absolute',
    zIndex: 1001,
    elevation: 1001,
  },
  labelBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: colors.white,
    zIndex: -1,
  },
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: spacing.lg,
  },
  modalContent: {
    backgroundColor: colors.light,
    borderRadius: borderRadius.xl,
    width: '100%',
    maxWidth: 340,
    ...Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  modalHeader: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
  },
  pickerContainer: {
    paddingHorizontal: spacing.sm,
    paddingTop: spacing.xs,
  },
  modalFooter: {
    marginHorizontal: spacing.sm,
  },
  footerButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.sm,
    paddingBottom: spacing.md,
    paddingHorizontal: spacing.sm,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.gray[400],
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  cancelButtonText: {
    color: colors.dark,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: colors.green,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  confirmButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
  },
});

DateTimePickerComponent.displayName = 'DateTimePickerComponent';

export default DateTimePickerComponent;
