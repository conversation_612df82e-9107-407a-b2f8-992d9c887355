import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {colors, spacing, typography, borderRadius} from '../../constants/theme';

/**
 * ErrorBoundaryTest Component
 * Test component để trigger errors và test ErrorBoundary functionality
 * Chỉ sử dụng trong development mode
 */

interface ErrorTestComponentProps {
  shouldThrow: boolean;
}

const ErrorTestComponent: React.FC<ErrorTestComponentProps> = ({shouldThrow}) => {
  if (shouldThrow) {
    throw new Error('Test error for ErrorBoundary - This is intentional for testing!');
  }

  return (
    <View style={styles.successContainer}>
      <Text style={styles.successText}>✅ Component rendered successfully!</Text>
    </View>
  );
};

export const ErrorBoundaryTest: React.FC = () => {
  const [shouldThrowError, setShouldThrowError] = useState(false);

  if (!__DEV__) {
    return (
      <View style={styles.container}>
        <Text style={styles.warningText}>ErrorBoundary Test chỉ khả dụng trong development mode</Text>
      </View>
    );
  }

  const triggerError = () => {
    setShouldThrowError(true);
  };

  const resetError = () => {
    setShouldThrowError(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>ErrorBoundary Test</Text>

      <Text style={styles.description}>Sử dụng component này để test ErrorBoundary functionality. Khi nhấn "Trigger Error", component sẽ throw một error để test ErrorBoundary.</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={[styles.button, styles.errorButton]} onPress={triggerError} disabled={shouldThrowError}>
          <Text style={styles.buttonText}>Trigger Error</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.resetButton]} onPress={resetError}>
          <Text style={styles.buttonText}>Reset</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.testArea}>
        <Text style={styles.testAreaTitle}>Test Area:</Text>
        <ErrorTestComponent shouldThrow={shouldThrowError} />
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Cách test:</Text>
        <Text style={styles.infoText}>
          1. Nhấn "Trigger Error" để component throw error{'\n'}
          2. ErrorBoundary sẽ catch error và hiển thị fallback UI{'\n'}
          3. Nhấn "Thử lại" trong ErrorBoundary để reset{'\n'}
          4. Hoặc nhấn "Reset" ở đây để reset state
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.lg,
    backgroundColor: colors.white,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.dark,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    lineHeight: 22,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  button: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.base,
    alignItems: 'center',
  },
  errorButton: {
    backgroundColor: colors.danger,
  },
  resetButton: {
    backgroundColor: colors.primary,
  },
  buttonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.white,
  },
  testArea: {
    backgroundColor: colors.gray[50],
    padding: spacing.lg,
    borderRadius: borderRadius.base,
    marginBottom: spacing.lg,
    minHeight: 100,
  },
  testAreaTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.dark,
    marginBottom: spacing.md,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  successText: {
    fontSize: typography.fontSize.base,
    color: colors.success,
    fontWeight: typography.fontWeight.medium as any,
  },
  infoContainer: {
    backgroundColor: colors.blue[50],
    padding: spacing.md,
    borderRadius: borderRadius.base,
    borderLeftWidth: 4,
    borderLeftColor: colors.blue[500],
  },
  infoTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.blue[700],
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: typography.fontSize.sm,
    color: colors.blue[600],
    lineHeight: 20,
  },
  warningText: {
    fontSize: typography.fontSize.base,
    color: colors.warning,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
