import React, {forwardRef, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Animated, Platform, StyleSheet, Text, TextInput, TextInputProps, TextStyle, TouchableOpacity, TouchableWithoutFeedback, View, ViewStyle} from 'react-native';
import {Control, Controller, FieldError, FieldValues, Path, RegisterOptions} from 'react-hook-form';
import {borderRadius, colors, spacing, typography} from '../../constants/theme';
import Icon from './Icon';
import {formatCurrencyInput, parseCurrency} from '../../utils/currencyFormatter';

type TextFieldVariant = 'outlined' | 'filled' | 'standard';

interface BaseTextFieldProps extends Omit<TextInputProps, 'value' | 'onChangeText'> {
  label?: string;
  error?: FieldError | string;
  leftIcon?: React.ReactNode;
  rightIconType?: React.ReactNode;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputContainerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  required?: boolean;
  variant?: TextFieldVariant;
  showPlaceholderWhenEmpty?: boolean; // ✅ Prop mới để chọn hiển thị placeholder thay vì label
  isCurrency?: boolean; // ✅ Prop mới để bật tính năng format currency
  currencyValue?: number; // ✅ Giá trị currency thực tế (số)
  onCurrencyChange?: (value: number) => void; // ✅ Callback khi currency value thay đổi
}

interface ControlledTextFieldProps<TFieldValues extends FieldValues> extends BaseTextFieldProps {
  control: Control<TFieldValues>;
  name: Path<TFieldValues>;
  rules?: Omit<RegisterOptions<TFieldValues, Path<TFieldValues>>, 'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'>;
  defaultValue?: string;
}

interface UncontrolledTextFieldProps extends BaseTextFieldProps {
  value: string;
  onChangeText: (text: string) => void;
}

// Type guard to check if props are for controlled component
function isControlledProps<TFieldValues extends FieldValues>(props: any): props is ControlledTextFieldProps<TFieldValues> {
  return 'control' in props && 'name' in props;
}

// Helper function to render icon
const renderIcon = (icon: React.ReactNode, defaultColor?: string): React.ReactNode => {
  if (typeof icon === 'string') {
    // Handle common icon names
    switch (icon) {
      case 'search':
        return <Icon name="SearchNormal" size={24} color={defaultColor || colors.gray[600]} />;
      case 'clear':
        return <Icon name="CloseCircle" size={18} color={defaultColor || colors.gray[600]} variant="Bulk" />;
      case 'eye':
        return <Icon name="Eye" size={24} color={defaultColor || colors.gray[400]} />;
      case 'eye-off':
        return <Icon name="EyeSlash" size={24} color={defaultColor || colors.gray[400]} />;
      case 'calendar':
        return <Icon name="Calendar" size={24} color={defaultColor || colors.gray[600]} />;
      case 'dropdown':
        return <Icon name="ArrowDown2" size={20} variant="Bold" color={defaultColor || colors.gray[600]} />;
      case 'check':
        return <Icon name="TickSquare" size={24} color={defaultColor || colors.gray[600]} />;
      case 'info':
        return <Icon name="InfoCircle" size={24} color={defaultColor || colors.gray[600]} />;
      case 'warning':
        return <Icon name="Danger" size={24} color={defaultColor || colors.gray[600]} />;
      case 'success':
        return <Icon name="TickCircle" size={24} color={defaultColor || colors.gray[600]} />;
      default:
        // Fallback: render as text
        return <Text style={{color: defaultColor || colors.gray[600]}}>{icon}</Text>;
    }
  }
  return icon;
};

// Base TextField component
const BaseTextField = forwardRef<
  TextInput,
  BaseTextFieldProps & {
    value: string;
    onChangeText: (text: string) => void;
  }
>(
  (
    {
      label,
      error,
      leftIcon,
      rightIconType,
      onRightIconPress,
      containerStyle,
      inputContainerStyle,
      labelStyle,
      errorStyle,
      required,
      value,
      onChangeText,
      secureTextEntry,
      editable = true,
      variant = 'outlined',
      showPlaceholderWhenEmpty = false,
      isCurrency = false,
      currencyValue,
      onCurrencyChange,
      ...textInputProps
    },
    ref,
  ) => {
    const [isSecure, setIsSecure] = useState(secureTextEntry);
    const [isFocused, setIsFocused] = useState(false);
    const [inputHeight, setInputHeight] = useState(54);
    const hasError = !!error;
    const errorMessage = typeof error === 'string' ? error : error?.message;

    // Currency formatting state
    const [displayValue, setDisplayValue] = useState(() => {
      if (isCurrency) {
        if (currencyValue !== undefined) {
          return formatCurrencyInput(currencyValue.toString());
        }
        return value ? formatCurrencyInput(value) : '';
      }
      return value;
    });

    // Animation values for floating label
    const animatedLabelPosition = useRef(new Animated.Value((isCurrency ? displayValue : value) ? 1 : 0)).current;
    const animatedLabelSize = useRef(new Animated.Value((isCurrency ? displayValue : value) ? 1 : 0)).current;

    // Handle currency input changes - memoized to prevent recreation
    const handleCurrencyChange = useCallback(
      (text: string) => {
        if (!isCurrency) {
          onChangeText(text);
          return;
        }

        // Format the input for display
        const formatted = formatCurrencyInput(text);
        setDisplayValue(formatted);

        // Parse the numeric value - use the original text, not the formatted one
        const numericValue = parseCurrency(text);

        // Call the currency change handler if provided
        if (onCurrencyChange) {
          onCurrencyChange(numericValue);
        }

        // Also call the regular onChangeText with the formatted string (not numeric)
        // This ensures the form gets the formatted display value
        onChangeText(formatted);
      },
      [isCurrency, onChangeText, onCurrencyChange, displayValue],
    );

    // Update display value when currencyValue prop changes
    useEffect(() => {
      if (isCurrency && currencyValue !== undefined) {
        const newDisplayValue = formatCurrencyInput(currencyValue.toString());
        setDisplayValue(newDisplayValue);
      }
    }, [currencyValue, isCurrency]);

    useEffect(() => {
      const currentValue = isCurrency ? displayValue : value;
      const shouldFloat = isFocused || (currentValue && currentValue.length > 0);

      // Chỉ animate label khi không dùng placeholder mode hoặc khi đã có interaction
      if (!showPlaceholderWhenEmpty || shouldFloat) {
        Animated.parallel([
          Animated.timing(animatedLabelPosition, {
            toValue: shouldFloat ? 1 : 0,
            duration: 200,
            useNativeDriver: false,
          }),
          Animated.timing(animatedLabelSize, {
            toValue: shouldFloat ? 1 : 0,
            duration: 200,
            useNativeDriver: false,
          }),
        ]).start();
      }
    }, [isFocused, value, displayValue, isCurrency, animatedLabelPosition, animatedLabelSize, showPlaceholderWhenEmpty]);

    const handleFocus = useCallback(() => {
      setIsFocused(true);
      textInputProps.onFocus?.({} as any);
    }, [textInputProps.onFocus]);

    const handleBlur = useCallback(() => {
      setIsFocused(false);
      textInputProps.onBlur?.({} as any);
    }, [textInputProps.onBlur]);

    const handleContentSizeChange = useCallback(
      (event: any) => {
        if (textInputProps.multiline) {
          const contentHeight = event.nativeEvent.contentSize.height;
          // Base height is 54px, add padding for floating label
          const minHeight = 54;
          const maxHeight = 120;
          // Only expand if content actually needs more space (roughly > 20px line height)
          const newHeight = contentHeight > 20 ? Math.max(minHeight, Math.min(maxHeight, contentHeight + 36)) : minHeight;
          setInputHeight(newHeight);
        }
        textInputProps.onContentSizeChange?.(event);
      },
      [textInputProps.multiline, textInputProps.onContentSizeChange],
    );

    const handleContainerPress = useCallback(() => {
      if (ref && typeof ref === 'object' && ref.current && editable) {
        // Force focus on Android
        if (Platform.OS === 'android') {
          setTimeout(() => {
            ref.current?.focus();
          }, 100);
        } else {
          ref.current.focus();
        }
      }
    }, [ref, editable, isFocused]);

    // Styles based on variant - memoized to prevent recalculation
    const containerStyles = useMemo(() => {
      const dynamicHeight = textInputProps.multiline ? {height: inputHeight} : null;

      switch (variant) {
        case 'filled':
          return [
            styles.inputContainerFilled,
            dynamicHeight,
            isFocused && styles.inputContainerFilledFocused,
            hasError && styles.inputContainerFilledError,
            !editable && styles.inputContainerDisabled,
          ];
        case 'standard':
          return [
            styles.inputContainerStandard,
            dynamicHeight,
            isFocused && styles.inputContainerStandardFocused,
            hasError && styles.inputContainerStandardError,
            !editable && styles.inputContainerDisabled,
          ];
        case 'outlined':
        default:
          return [styles.inputContainer, dynamicHeight, isFocused && styles.inputContainerFocused, hasError && styles.inputContainerError, !editable && styles.inputContainerDisabled];
      }
    }, [variant, isFocused, hasError, editable, textInputProps.multiline, inputHeight]);

    const labelStyles = useMemo(() => {
      if (variant === 'outlined' || variant === 'filled' || variant === 'standard') {
        const currentValue = isCurrency ? displayValue : value;
        const shouldFloat = isFocused || (currentValue && currentValue.length > 0);

        // Different positioning for each variant with Android adjustments
        let topPosition;
        const androidAdjustment = Platform.OS === 'android' ? -2 : 0;

        if (variant === 'outlined') {
          topPosition = animatedLabelPosition.interpolate({
            inputRange: [0, 1],
            outputRange: [19 + androidAdjustment, -9], // Center vertically when not focused, float to border when focused
          });
        } else if (variant === 'filled') {
          topPosition = animatedLabelPosition.interpolate({
            inputRange: [0, 1],
            outputRange: [19 + androidAdjustment, 4],
          });
        } else {
          topPosition = animatedLabelPosition.interpolate({
            inputRange: [0, 1],
            outputRange: [19 + androidAdjustment, 4],
          });
        }

        return {
          position: 'absolute' as const,
          left: leftIcon ? 32 : 12,
          backgroundColor: variant === 'outlined' ? (!editable ? colors.gray[100] : colors.white) : 'transparent',
          top: topPosition,
          fontSize: animatedLabelSize.interpolate({
            inputRange: [0, 1],
            outputRange: [14, 12],
          }),
          color: !editable ? colors.gray[700] : isFocused ? (hasError ? colors.danger : colors.gray[600]) : hasError ? colors.danger : colors.gray[600],
          zIndex: 10,
        };
      }
      return [styles.label, labelStyle];
    }, [variant, isFocused, hasError, editable, value, displayValue, isCurrency, animatedLabelPosition, animatedLabelSize, leftIcon, labelStyle]);

    const renderFloatingLabel = () => {
      if (!label) return null;

      const currentValue = isCurrency ? displayValue : value;
      // Nếu showPlaceholderWhenEmpty = true và chưa focus + chưa có value → không hiển thị label
      if (showPlaceholderWhenEmpty && !isFocused && !currentValue) {
        return null;
      }

      if (variant === 'outlined' || variant === 'filled' || variant === 'standard') {
        return (
          <Animated.Text style={[styles.floatingLabel, labelStyles, labelStyle]}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Animated.Text>
        );
      }

      return (
        <Text style={[styles.label, labelStyle]} pointerEvents="none">
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      );
    };

    return (
      <View style={[styles.container, containerStyle]}>
        {variant !== 'outlined' && variant !== 'filled' && variant !== 'standard' && renderFloatingLabel()}

        <TouchableWithoutFeedback onPress={handleContainerPress} disabled={!editable}>
          <View
            style={[
              styles.inputWrapper,
              containerStyles,
              inputContainerStyle,
              {minHeight: Platform.OS === 'android' ? 54 : 48}, // Tăng touch target
            ]}>
            {/* Render label với pointerEvents none để không block touch */}
            {(variant === 'outlined' || variant === 'filled' || variant === 'standard') && (
              <View pointerEvents="none" style={StyleSheet.absoluteFill}>
                {renderFloatingLabel()}
              </View>
            )}

            {leftIcon && (
              <View style={styles.leftIcon} pointerEvents="none">
                {renderIcon(leftIcon)}
              </View>
            )}

            <TextInput
              ref={ref}
              value={isCurrency ? displayValue : value}
              onChangeText={handleCurrencyChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onContentSizeChange={handleContentSizeChange}
              keyboardType={isCurrency ? 'numeric' : textInputProps.keyboardType}
              style={[
                styles.input,
                variant === 'filled' ? styles.inputFilled : null,
                variant === 'standard' ? styles.inputStandard : null,
                leftIcon ? styles.inputWithLeftIcon : null,
                rightIconType ? styles.inputWithRightIcon : null,
                !editable ? styles.inputDisabled : null,
                (variant === 'outlined' || variant === 'filled' || variant === 'standard') && label ? styles.inputWithFloatingLabel : null,
                // Platform-specific fixes cho Android
                Platform.OS === 'android'
                  ? {
                      color: colors.black,
                      opacity: 1,
                    }
                  : null,
              ]}
              placeholderTextColor={colors.gray[500]}
              editable={editable}
              secureTextEntry={isSecure}
              pointerEvents="auto"
              underlineColorAndroid="transparent"
              importantForAutofill="auto"
              blurOnSubmit={false}
              selectionColor={colors.primary}
              {...(Platform.OS === 'android'
                ? {
                    textAlignVertical: 'center',
                    color: colors.black, // Force màu đen trên Android
                  }
                : {})}
              {...(() => {
                const currentValue = isCurrency ? displayValue : value;
                if (variant === 'outlined' || variant === 'filled' || variant === 'standard') {
                  // Material Design variants
                  if (showPlaceholderWhenEmpty) {
                    return {
                      ...textInputProps,
                      placeholder: !currentValue || currentValue.length === 0 ? textInputProps.placeholder : undefined,
                    };
                  } else {
                    // Mode Material Design chuẩn
                    return {
                      ...textInputProps,
                      placeholder: isFocused || (currentValue && currentValue.length > 0) ? textInputProps.placeholder : undefined,
                    };
                  }
                } else {
                  // Traditional variant
                  return textInputProps;
                }
              })()}
            />

            {rightIconType && (
              <TouchableOpacity style={styles.rightIcon} onPress={onRightIconPress} disabled={!onRightIconPress}>
                {renderIcon(rightIconType)}
              </TouchableOpacity>
            )}

            {secureTextEntry && (
              <TouchableOpacity style={styles.rightIcon} onPress={() => setIsSecure(!isSecure)}>
                {isSecure ? <Icon name="Eye" size={24} variant="Bulk" color={colors.dark} /> : <Icon name="EyeSlash" size={24} variant="Bulk" color={colors.dark} />}
              </TouchableOpacity>
            )}
          </View>
        </TouchableWithoutFeedback>

        {hasError && errorMessage && <Text style={[styles.error, errorStyle]}>{errorMessage}</Text>}
      </View>
    );
  },
);

// Main TextField component with React Hook Form integration
function TextField<TFieldValues extends FieldValues = FieldValues>(props: ControlledTextFieldProps<TFieldValues> | UncontrolledTextFieldProps) {
  if (isControlledProps<TFieldValues>(props)) {
    // Controlled component with React Hook Form
    const {control, name, rules, defaultValue = '', ...restProps} = props;

    return (
      <Controller
        control={control}
        name={name}
        rules={rules}
        defaultValue={defaultValue as any}
        render={({field: {onChange, onBlur, value}, fieldState: {error}}) => <BaseTextField {...restProps} value={value || ''} onChangeText={onChange} onBlur={onBlur} error={error || props.error} />}
      />
    );
  } else {
    // Uncontrolled component
    return <BaseTextField {...props} />;
  }
}

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
    marginBottom: spacing.xs,
  },
  floatingLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    zIndex: 1,
  },
  required: {
    color: colors.danger,
  },
  inputWrapper: {
    position: 'relative',
    minHeight: Platform.OS === 'android' ? 54 : 48,
  },

  // Outlined variant styles
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: borderRadius.lg + 2,
    backgroundColor: colors.white,
    height: 54,
    minHeight: 54,
    paddingHorizontal: spacing.sm,
  },
  inputContainerFocused: {
    borderColor: colors.gray[500],
  },
  inputContainerError: {
    borderColor: colors.danger,
  },
  inputContainerDisabled: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[400],
  },

  // Filled variant styles
  inputContainerFilled: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[400],
    borderTopLeftRadius: borderRadius.base,
    borderTopRightRadius: borderRadius.base,
    minHeight: 54,
  },
  inputContainerFilledFocused: {
    backgroundColor: colors.gray[200],
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  inputContainerFilledError: {
    borderBottomColor: colors.danger,
  },

  // Standard variant styles
  inputContainerStandard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[400],
    backgroundColor: 'transparent',
    minHeight: 54,
  },
  inputContainerStandardFocused: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  inputContainerStandardError: {
    borderBottomColor: colors.danger,
  },

  // Input styles
  input: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.black,
    paddingVertical: Platform.OS === 'android' ? spacing.xs : spacing.sm,
    minHeight: Platform.OS === 'android' ? 48 : 40,
    textAlignVertical: Platform.OS === 'android' ? 'center' : 'auto',
    includeFontPadding: false, // Android specific - removes extra padding
    ...(Platform.OS === 'android'
      ? {
          // Force màu text đen trên Android
          color: colors.black,
        }
      : {}),
  },
  inputWithFloatingLabel: {
    paddingTop: Platform.OS === 'android' ? 18 : 16,
    paddingBottom: Platform.OS === 'android' ? 14 : 16,
  },
  inputFilled: {
    paddingTop: Platform.OS === 'android' ? 24 : 22,
    paddingBottom: Platform.OS === 'android' ? 6 : 8,
    backgroundColor: 'transparent',
  },
  inputStandard: {
    paddingTop: Platform.OS === 'android' ? 24 : 22,
    paddingBottom: Platform.OS === 'android' ? 6 : 8,
    paddingHorizontal: 0,
  },
  inputWithLeftIcon: {
    paddingLeft: spacing.sm,
  },
  inputWithRightIcon: {
    paddingRight: spacing.sm,
  },
  inputDisabled: {
    color: colors.black + ' !important', // Force màu đen
    opacity: 1,
    ...(Platform.OS === 'android'
      ? {
          textColorPrimary: colors.black,
          textColorSecondary: colors.black,
          textColorTertiary: colors.black,
        }
      : {}),
  },

  // Icon styles
  leftIcon: {
    marginLeft: spacing.sm,
  },
  rightIcon: {},

  // Other styles
  toggleSecureText: {
    fontSize: typography.fontSize.sm,
    color: colors.green,
    fontWeight: typography.fontWeight.medium as any,
  },
  error: {
    fontSize: typography.fontSize.sm,
    color: colors.danger,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },

  // Multiline specific styles
  inputContainerMultiline: {
    height: 'auto',
    minHeight: 80,
    alignItems: 'flex-start',
    paddingTop: spacing.sm,
    paddingBottom: spacing.sm,
  },
});

export default TextField;
