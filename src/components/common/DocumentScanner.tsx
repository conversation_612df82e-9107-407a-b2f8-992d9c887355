import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Alert, Image, Linking, Platform, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import RNDocumentScanner from 'react-native-document-scanner-plugin';
import {Camera} from 'react-native-vision-camera';
import TextRecognition from '@react-native-ml-kit/text-recognition';
import {CccdInfo, parseCccdFrontData} from '@utils/ocrParser';
import {callOcrApi} from '@services/endpoints';
import {colors, spacing, typography} from '@constants/theme';
import R from '@assets/R';
import {createToastHelpers, useToast} from './Toast';

export interface DocumentScannerProps {
  isVisible: boolean;
  onClose: () => void;
  onOcrComplete: (info: CccdInfo, imageUri: string) => void;
  initialImageUri?: string | null;
}

const DocumentScanner: React.FC<DocumentScannerProps> = ({isVisible, onClose, onOcrComplete, initialImageUri}) => {
  const toastContext = useToast();
  const toast = createToastHelpers(toastContext);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  useEffect(() => {
    if (isVisible) {
      setSelectedImage(initialImageUri || null);
    } else {
      // Reset image state when modal is not visible to ensure clean state for next open
      setSelectedImage(null);
    }
  }, [isVisible, initialImageUri]);

  // useEffect(() => {
  //   requestPermistion();
  // }, []);

  // const requestPermistion = async () => {
  //   await requestLibraryPermissions();
  //   requestCameraPermissions();
  // };

  const handleClose = () => {
    if (isProcessing) return;
    setSelectedImage(null); // Reset image on close
    onClose();
  };

  const processImage = async (uri: string) => {
    setIsProcessing(true);
    try {
      // Try API OCR first// Call API OCR CVS
      const cccdInfo = await callOcrApi(uri);

      // Check if API OCR returned meaningful data
      // Consider data valid if we have at least ID or name (most important fields)
      const hasValidData = Boolean(cccdInfo.id || cccdInfo.name);
      const hasAnyData = Boolean(cccdInfo.id || cccdInfo.name || cccdInfo.dob || cccdInfo.gender || cccdInfo.address);

      if (hasValidData) {
        toast.success('Đọc thông tin căn cước công dân thành công!', {duration: 2000, position: 'top'});
        onOcrComplete(cccdInfo, uri);
      } else if (hasAnyData) {
        toast.success('Đọc thông tin căn cước công dân thành công (một phần)!', {duration: 2000, position: 'top'});
        onOcrComplete(cccdInfo, uri);
      } else {
        // Fallback to ML Kit OCR if API doesn't return data //Fall back đọc OCR bằng thư viện
        console.log('⚠️ API OCR returned no meaningful data, falling back to ML Kit');
        const result = await TextRecognition.recognize(uri);
        const allLines = result.blocks.flatMap(block => block.lines);

        // Adapt the result to the structure expected by parseCccdFrontData
        const adaptedResult = {
          status: 'succeeded',
          analyzeResult: {
            readResults: [
              {
                lines: allLines.map(line => ({text: line.text})),
              },
            ],
          },
        };

        const fallbackCccdInfo = parseCccdFrontData(adaptedResult);
        toast.success('Đọc thông tin căn cước công dân thành công!', {duration: 2000, position: 'top'});
        onOcrComplete(fallbackCccdInfo, uri);
      }
    } catch (error) {
      console.error('❌ OCR Processing Error:', error);

      // Try ML Kit as final fallback
      try {
        console.log('🔄 Trying ML Kit as final fallback...');
        const result = await TextRecognition.recognize(uri);
        const allLines = result.blocks.flatMap(block => block.lines);

        const adaptedResult = {
          status: 'succeeded',
          analyzeResult: {
            readResults: [
              {
                lines: allLines.map(line => ({text: line.text})),
              },
            ],
          },
        };

        const fallbackCccdInfo = parseCccdFrontData(adaptedResult);
        toast.success('Đọc thông tin căn cước công dân thành công (fallback)!', {duration: 2000, position: 'top'});
        onOcrComplete(fallbackCccdInfo, uri);
      } catch (fallbackError) {
        console.error('❌ Final fallback also failed:', fallbackError);
        toast.error('Quét OCR thất bại, bạn có thể nhập tay.', {
          position: 'top',
          duration: 3000,
        });
        onOcrComplete({}, uri); // Continue flow even on error
      }
    } finally {
      setIsProcessing(false);
      onClose(); // Close modal after processing is done
    }
  };

  const handleCameraPress = async () => {
    const status = await Camera.requestCameraPermission();
    if (status !== 'granted') {
      Alert.alert('Yêu cầu quyền', 'Vui lòng cấp quyền truy cập camera để chụp ảnh.', [
        {
          text: 'Để sau',
          style: 'cancel',
        },
        {
          text: 'Mở cài đặt',
          onPress: () => Linking.openSettings(),
        },
      ]);
      return;
    }
    try {
      const {scannedImages} = await RNDocumentScanner.scanDocument({maxNumDocuments: 1});
      if (scannedImages && scannedImages.length > 0) {
        const uri = Platform.OS === 'android' && !scannedImages[0].startsWith('file://') ? `file://${scannedImages[0]}` : scannedImages[0];
        processImage(uri); // Xử lý ngay lập tức
      }
    } catch (error) {
      console.error('Error scanning document:', error);
    }
  };

  return (
    <Modal onSwipeComplete={handleClose} swipeDirection={['down']} isVisible={isVisible} onBackButtonPress={handleClose} onBackdropPress={handleClose} style={styles.modal}>
      <View style={styles.modalContent}>
        <Text style={styles.modalTitle}>Tải lên CCCD/CMND</Text>
        <View style={styles.imagePreviewContainer}>
          <Image source={selectedImage ? {uri: selectedImage} : R.images.img_cccd} style={styles.imagePreview} resizeMode="contain" />
          {isProcessing && (
            <View style={styles.processingOverlay}>
              <ActivityIndicator size="large" color={colors.white} />
              <Text style={styles.processingText}>Đang phân tích...</Text>
            </View>
          )}
          {selectedImage && !isProcessing && (
            <TouchableOpacity style={styles.rescanButton} onPress={() => processImage(selectedImage)}>
              <Image source={R.icons.ic_scanner_outline} style={styles.rescanIcon} />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity style={styles.cameraButton} onPress={handleCameraPress}>
            <Text style={styles.cameraButtonText}>Chụp ảnh hoặc tải lên ảnh CCCD</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: spacing.lg,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  imagePreviewContainer: {
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  imagePreview: {
    width: '90%',
    height: 200,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: spacing.lg,
  },
  cameraButton: {
    flex: 1,
    padding: spacing.md,
    backgroundColor: colors.green,
    borderRadius: 14,
    alignItems: 'center',
    marginHorizontal: spacing.sm,
  },
  cameraButtonText: {
    color: 'white',
    fontFamily: typography.fontFamily.semibold,
  },
  processingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  processingText: {
    marginTop: spacing.md,
    fontSize: 16,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },
  rescanButton: {
    position: 'absolute',
    top: spacing.md,
    right: '8%',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: spacing.default,
    borderRadius: 50,
  },
  rescanIcon: {
    width: 24,
    height: 24,
    tintColor: colors.white,
  },
});

export default DocumentScanner;
