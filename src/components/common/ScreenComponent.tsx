import {isIOS} from '@commons/Constant';
import {useFocusEffect} from '@react-navigation/native';
import React, {memo, ReactNode, useEffect} from 'react';
import isEqual from 'react-fast-compare';
import {Image, Platform, SafeAreaView, StatusBar, StyleSheet, Text, TouchableOpacity, View, ViewStyle} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {colors, shadows, spacing, typography} from '../../constants/theme';
import {useDialog} from '../../hooks/useDialog';
import Header from './Header';
import Icon from './Icon';
import Loading from './Loading';

// Types và Interfaces
export interface ScreenComponentProps {
  // Loading states
  isLoading?: boolean;
  isError?: boolean;
  isEmpty?: boolean;
  dialogLoading?: boolean;
  loadingMessage?: string;

  // Content
  children?: ReactNode;
  renderView?: ReactNode;

  // Header configuration
  showHeader?: boolean;
  headerTitle?: string;
  headerSubTitle?: string;
  showBackButton?: boolean;
  onPressBack?: () => void;
  headerRightComponent?: ReactNode;
  headerLeftComponent?: ReactNode;
  headerBackgroundColor?: string;
  headerTitleColor?: string;

  // Footer
  footer?: ReactNode;
  showFooter?: boolean;

  // Styling
  backgroundColor?: string;
  bodyStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  statusBarStyle?: 'light-content' | 'dark-content';
  statusBarBackgroundColor?: string;

  // Error handling
  errorMessage?: string;
  onRetry?: () => void;

  // Empty state
  emptyMessage?: string;
  emptyImage?: any;
  onEmptyAction?: () => void;
  emptyActionText?: string;

  // Safe area
  useSafeArea?: boolean;
  topSafeArea?: boolean;
  bottomSafeArea?: boolean;

  // Additional props
  scrollable?: boolean;
  keyboardAvoidingView?: boolean;
}

// Error Component
const ErrorComponent: React.FC<{
  message?: string;
  onRetry?: () => void;
}> = ({message = 'Đã có lỗi xảy ra', onRetry}) => (
  <View style={styles.centerContent}>
    <Icon name="CloseCircle" size={32} color={colors.danger} />
    <Text style={styles.errorText}>{message}</Text>
    {onRetry && (
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>Thử lại</Text>
      </TouchableOpacity>
    )}
  </View>
);

// Empty Component
const EmptyComponent: React.FC<{
  message?: string;
  image?: any;
  onAction?: () => void;
  actionText?: string;
}> = ({message = 'Không có dữ liệu', image, onAction, actionText = 'Tải lại'}) => (
  <View style={styles.centerContent}>
    {image && <Image source={image} style={styles.emptyIcon} />}
    <Text style={styles.emptyText}>{message}</Text>
    {onAction && (
      <TouchableOpacity style={styles.actionButton} onPress={onAction}>
        <Text style={styles.actionButtonText}>{actionText}</Text>
      </TouchableOpacity>
    )}
  </View>
);

const ScreenComponent: React.FC<ScreenComponentProps> = ({
  isLoading = false,
  isError = false,
  isEmpty = false,
  dialogLoading = false,
  loadingMessage,
  children,
  renderView,
  showHeader = false,
  headerTitle,
  showBackButton = false,
  onPressBack,
  headerRightComponent,
  headerLeftComponent,
  headerBackgroundColor,
  headerTitleColor,
  footer,
  showFooter = false,
  backgroundColor = colors.white,
  bodyStyle,
  containerStyle,
  statusBarStyle = 'dark-content',
  errorMessage,
  onRetry,
  emptyMessage,
  emptyImage,
  onEmptyAction,
  emptyActionText,
  useSafeArea = true,
  topSafeArea = false,
  bottomSafeArea = true,
}) => {
  const insets = useSafeAreaInsets();
  const finalHeaderBackgroundColor = headerBackgroundColor || colors.green;
  const {showDialog, hideDialog} = useDialog();

  useEffect(() => {
    if (dialogLoading) {
      showDialog();
    } else {
      hideDialog();
    }
    return () => {
      hideDialog();
    };
  }, [dialogLoading, showDialog, hideDialog]);

  // Set status bar style immediately on mount and when showHeader changes
  useEffect(() => {
    // Always use dark content for all screens
    StatusBar.setBarStyle('dark-content', false);
    // Set translucent only on Android
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
    }
  }, [showHeader]);

  // Also set status bar style when screen is focused (for navigation)
  useFocusEffect(
    React.useCallback(() => {
      // Always use dark content for all screens
      StatusBar.setBarStyle('dark-content', false);
      // Set translucent only on Android
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(true);
      }
    }, [showHeader]),
  );

  const renderBodyContent = () => {
    if (isLoading) {
      return <Loading message={loadingMessage} />;
    }
    if (isError) {
      return <ErrorComponent message={errorMessage} onRetry={onRetry} />;
    }
    if (isEmpty) {
      return <EmptyComponent message={emptyMessage} image={emptyImage} onAction={onEmptyAction} actionText={emptyActionText} />;
    }
    return children || renderView;
  };

  const Container = useSafeArea && Platform.OS === 'ios' ? SafeAreaView : View;

  return (
    <Container
      style={[
        styles.container,
        {backgroundColor},
        containerStyle,
        useSafeArea && {
          // paddingTop: topSafeArea ? (Platform.OS === 'android' ? Math.max(StatusBar.currentHeight || 0, insets.top) : 0) : insets.top,
          paddingTop: showHeader ? Math.max(0, insets.top - spacing.sm) : insets.top, // Reduce paddingTop by spacing.sm (8px) when header is shown to account for header's paddingTop
          paddingBottom: bottomSafeArea ? 0 : insets.bottom,
        },
      ]}>
      {showHeader && <View style={[styles.statusBarOverlay, {backgroundColor: finalHeaderBackgroundColor}]} />}
      {showHeader && (
        <Header
          title={headerTitle}
          showBackButton={showBackButton}
          onBack={onPressBack}
          rightComponent={headerRightComponent}
          leftComponent={headerLeftComponent}
          backgroundColor={finalHeaderBackgroundColor}
          titleColor={headerTitleColor}
        />
      )}

      <View
        style={[
          styles.body,
          bodyStyle,
          {
            paddingBottom: showFooter ? 0 : bottomSafeArea ? 0 : insets.bottom,
          },
        ]}>
        {renderBodyContent()}
      </View>

      {showFooter && footer && (
        <View
          style={[
            styles.footer,
            {
              paddingBottom: !isIOS ? Math.max(insets.bottom, 12) : 12,
            },
          ]}>
          {footer}
        </View>
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  statusBarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 24,
    zIndex: 1000,
  },
  body: {
    flex: 1,
  },
  footer: {
    backgroundColor: colors.light,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingHorizontal: spacing.sm,
    paddingTop: 12,
    ...shadows.sm,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorIcon: {
    width: 64,
    height: 64,
    marginBottom: spacing.md,
    tintColor: colors.danger,
  },
  errorText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.md,
    lineHeight: typography.lineHeight.normal * typography.fontSize.base,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    marginBottom: spacing.md,
    opacity: 0.6,
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.md,
    lineHeight: typography.lineHeight.normal * typography.fontSize.base,
  },
  actionButton: {
    backgroundColor: colors.light,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  actionButtonText: {
    color: colors.primary,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
  },
});

export default memo(ScreenComponent, isEqual);
