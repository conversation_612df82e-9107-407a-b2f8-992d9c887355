import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';
import {colors, typography, spacing} from '@constants/theme';

interface FilterChipProps {
  label: string;
  selected: boolean;
  onPress: () => void;
}

export default function FilterChip({label, selected, onPress}: FilterChipProps) {
  return (
    <TouchableOpacity style={[styles.container, selected && styles.selectedContainer]} onPress={onPress} activeOpacity={0.7}>
      <Text style={[styles.label, selected && styles.selectedLabel]}>{label}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.light,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    marginRight: spacing.sm,
  },
  selectedContainer: {
    backgroundColor: colors.green,
    borderColor: colors.green,
  },
  label: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
  },
  selectedLabel: {
    color: colors.white,
  },
});
