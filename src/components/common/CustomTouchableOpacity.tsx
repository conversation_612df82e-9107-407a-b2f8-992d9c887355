import React, {useRef} from 'react';
import {Animated, TouchableOpacity, TouchableOpacityProps, Platform} from 'react-native';

interface CustomTouchableOpacityProps extends TouchableOpacityProps {
  scaleValue?: number;
  scaleDuration?: number;
}

const CustomTouchableOpacity: React.FC<CustomTouchableOpacityProps> = ({children, scaleValue = 0.95, scaleDuration = 100, activeOpacity = 0.7, onPressIn, onPressOut, style, ...rest}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = (e: any) => {
    Animated.spring(scaleAnim, {
      toValue: scaleValue,
      speed: 50,
      bounciness: 0,
      useNativeDriver: true,
    }).start();
    onPressIn?.(e);
  };

  const handlePressOut = (e: any) => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      speed: 50,
      bounciness: 0,
      useNativeDriver: true,
    }).start();
    onPressOut?.(e);
  };

  return (
    <TouchableOpacity activeOpacity={Platform.OS === 'android' ? 1 : activeOpacity} onPressIn={handlePressIn} onPressOut={handlePressOut} style={style} {...rest}>
      <Animated.View
        style={{
          transform: [{scale: scaleAnim}],
        }}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};

export default CustomTouchableOpacity;
