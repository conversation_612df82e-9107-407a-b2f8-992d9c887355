import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Animated, Dimensions, Easing, Keyboard, Modal, PanResponder, Platform, StyleSheet, Text, TextStyle, TouchableOpacity, TouchableWithoutFeedback, View, ViewStyle} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {dimensions} from '../../constants/dimensions';
import {borderRadius, colors, spacing, typography} from '../../constants/theme';
import Icon from './Icon';
import TextField from './TextField';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export interface ActionSheetOption {
  id: string | number;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  onPress: () => void;
  disabled?: boolean;
  destructive?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
}

export interface ActionSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  options: ActionSheetOption[];
  selectedValue?: string | number;
  showCancelButton?: boolean;
  cancelButtonText?: string;
  showSearchField?: boolean;
  searchPlaceholder?: string;
  closeOnBackdropPress?: boolean;
  closeOnBackButtonPress?: boolean;
  keyboardBehavior?: 'overlay' | 'push';
  modalStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  headerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  optionStyle?: ViewStyle;
  cancelButtonStyle?: ViewStyle;
  searchFieldStyle?: ViewStyle;
  onModalShow?: () => void;
  onModalHide?: () => void;
}

const ITEM_HEIGHT = 56;

const ActionSheetModal: React.FC<ActionSheetModalProps> = props => {
  const {
    isVisible,
    onClose,
    title,
    subtitle,
    options,
    selectedValue,
    showCancelButton = true,
    cancelButtonText = 'Hủy',
    showSearchField = false,
    searchPlaceholder = 'Tìm kiếm...',
    closeOnBackdropPress = true,
    closeOnBackButtonPress = true,
    keyboardBehavior = 'overlay',
    modalStyle,
    containerStyle,
    headerStyle,
    titleStyle,
    subtitleStyle,
    optionStyle,
    cancelButtonStyle,
    searchFieldStyle,
    onModalShow,
    onModalHide,
  } = props;

  const insets = useSafeAreaInsets();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [slideAnim] = useState(new Animated.Value(dimensions.height));
  const [backdropAnim] = useState(new Animated.Value(0));
  const [searchUIValue, setSearchUIValue] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [keyboardInset, setKeyboardInset] = useState(0);
  const [screenHeight, setScreenHeight] = useState(dimensions.height);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const keyboardPadding = useRef(new Animated.Value(spacing.xl)).current;
  const isDraggingRef = useRef(false);

  // Show/Hide modal animations - Bottom Sheet style
  const showModal = useCallback(() => {
    setIsModalVisible(true);

    // Reset to initial position first
    slideAnim.setValue(dimensions.height);
    backdropAnim.setValue(0);

    // Start animation immediately
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 280,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(backdropAnim, {
        toValue: 1,
        duration: 280,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start(() => {
      onModalShow?.();
    });
  }, [slideAnim, backdropAnim, onModalShow]);

  const hideModal = useCallback(
    (callback?: () => void) => {
      Keyboard.dismiss();

      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: dimensions.height,
          duration: 250,
          easing: Easing.in(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 250,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ]).start(() => {
        setIsModalVisible(false);
        onModalHide?.();
        callback?.();
      });
    },
    [slideAnim, backdropAnim, onModalHide],
  );

  const handleClose = useCallback(() => {
    Keyboard.dismiss();
    hideModal(() => {
      setSearchUIValue('');
      setSearchValue('');
      onClose();
    });
  }, [hideModal, onClose]);

  const animateBackToOpen = useCallback(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 220,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(backdropAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start();
  }, [backdropAnim, slideAnim]);

  // Pan Responder for drag-to-dismiss
  const panResponder = useMemo(
    () =>
      PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: (_, gestureState) => gestureState.dy > 5 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx),
        onPanResponderGrant: () => {
          isDraggingRef.current = true;
          slideAnim.stopAnimation();
        },
        onPanResponderMove: (_, gestureState) => {
          if (!isDraggingRef.current) {
            return;
          }

          const dragDistance = Math.max(0, Math.min(gestureState.dy, dimensions.height));
          if (dragDistance >= 0) {
            slideAnim.setValue(dragDistance);
            const fadeThreshold = Math.max(120, Math.min(dimensions.height, 360));
            const fadeProgress = Math.max(0, Math.min(1, 1 - dragDistance / fadeThreshold));
            backdropAnim.setValue(fadeProgress);
          }
        },
        onPanResponderRelease: (_, gestureState) => {
          isDraggingRef.current = false;
          const dragDistance = gestureState.dy;
          const velocity = gestureState.vy;

          if (dragDistance > 80 || velocity > 0.4) {
            handleClose();
          } else {
            animateBackToOpen();
          }
        },
        onPanResponderTerminate: () => {
          isDraggingRef.current = false;
          animateBackToOpen();
        },
      }),
    [animateBackToOpen, backdropAnim, handleClose, slideAnim],
  );

  useEffect(() => {
    if (isVisible) {
      showModal();
    } else if (isModalVisible) {
      hideModal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible]);

  // Lắng nghe keyboard events để điều chỉnh modal và nhận biết trạng thái bàn phím
  useEffect(() => {
    const animateKeyboardPadding = (padding: number) => {
      Animated.timing(keyboardPadding, {
        toValue: padding,
        duration: 220,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false,
      }).start();
    };

    const handleKeyboardShow = (e: any) => {
      setIsKeyboardVisible(true);
      const insetValue = e.endCoordinates?.height ?? 0;
      const clampedInset = Math.min(insetValue, Math.max(0, dimensions.height - insets.top));
      setKeyboardInset(clampedInset);
      animateKeyboardPadding(spacing.xl + clampedInset);

      if (Platform.OS === 'android' && showSearchField && keyboardBehavior === 'overlay') {
        setKeyboardHeight(e.endCoordinates.height);
        setScreenHeight(Dimensions.get('window').height);
      }
    };

    const handleKeyboardHide = () => {
      setIsKeyboardVisible(false);
      setKeyboardInset(0);
      animateKeyboardPadding(spacing.xl);

      if (Platform.OS === 'android' && showSearchField && keyboardBehavior === 'overlay') {
        setKeyboardHeight(0);
        setScreenHeight(dimensions.height);
      }
    };

    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', handleKeyboardShow);
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', handleKeyboardHide);

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, [showSearchField, keyboardBehavior]);

  const onChangeSearchText = useCallback((text: string) => {
    setSearchUIValue(text);
    if (text.trim() === '') {
      setSearchValue('');
    }
  }, []);

  const handleSearch = useCallback(() => {
    Keyboard.dismiss();
    setSearchValue(searchUIValue.trim());
  }, [searchUIValue]);

  const filteredOptions = useMemo(() => {
    if (!showSearchField) return options;

    const kw = searchValue.trim().toLowerCase();
    if (!kw) {
      return options.slice(0, 150);
    }

    const list = options.filter(o => o.title.toLowerCase().includes(kw) || (o.subtitle && o.subtitle.toLowerCase().includes(kw)));
    return list.slice(0, 500);
  }, [options, showSearchField, searchValue]);

  const hasSearchKeyword = showSearchField && searchValue.trim().length > 0;
  const isEmptySearchResult = hasSearchKeyword && filteredOptions.length === 0;

  const handleBackdropPress = useCallback(() => {
    if (closeOnBackdropPress) {
      handleClose();
    }
  }, [closeOnBackdropPress, handleClose]);

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  const keyExtractor = useCallback((item: ActionSheetOption) => String(item.id), []);

  const emptyComponent = useMemo(
    () =>
      hasSearchKeyword ? (
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.noResultsContainer}>
            <Text style={styles.noResultsText}>Không tìm thấy kết quả nào</Text>
            <Text style={styles.noResultsSubtext}>Thử tìm kiếm với từ khóa khác</Text>
          </View>
        </TouchableWithoutFeedback>
      ) : null,
    [hasSearchKeyword, dismissKeyboard],
  );

  const contentContainerStyle = useMemo(() => ({paddingBottom: spacing.sm}), []);

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    [],
  );

  const renderItem = useCallback(
    ({item, index}: {item: ActionSheetOption; index: number}) => {
      const selected = selectedValue === item.id;
      return (
        <TouchableOpacity
          key={item.id}
          style={[styles.option, optionStyle, item.style, item.disabled && styles.disabledOption, index === filteredOptions.length - 1 && styles.lastOption, selected && styles.selectedOption]}
          onPress={() => {
            if (item.disabled) return;
            item.onPress();
            handleClose();
          }}
          disabled={item.disabled}
          activeOpacity={0.7}>
          <View style={styles.optionContent}>
            {item.icon && <View style={styles.optionIcon}>{item.icon}</View>}
            <View style={styles.optionTextContainer}>
              <Text style={[styles.optionTitle, item.destructive && styles.destructiveText, item.disabled && styles.disabledText, selected && styles.selectedText, item.titleStyle]} numberOfLines={1}>
                {item.title}
              </Text>
              {!!item.subtitle && (
                <Text style={[styles.optionSubtitle, item.disabled && styles.disabledText, selected && styles.selectedSubtext]} numberOfLines={2}>
                  {item.subtitle}
                </Text>
              )}
            </View>
            {selected && (
              <View style={styles.checkmarkContainer}>
                <Icon name="TickCircle" size={22} color={colors.green} variant="Bold" />
              </View>
            )}
          </View>
        </TouchableOpacity>
      );
    },
    [filteredOptions.length, handleClose, optionStyle, selectedValue],
  );

  // Tính toán style cho modal container dựa trên keyboard height
  const containerStyles = useMemo(() => {
    const baseMaxHeight = dimensions.height - insets.top;
    const stylesArray: any[] = [styles.modalContent, {maxHeight: baseMaxHeight}, containerStyle];
    const bottomInset = keyboardInset > 0 ? keyboardInset : 0;

    if (Platform.OS === 'android' && showSearchField && keyboardBehavior === 'overlay' && keyboardHeight > 0) {
      const adjustedHeight = Math.max(240, screenHeight - keyboardHeight - spacing.md);
      stylesArray.push({maxHeight: adjustedHeight});
    }

    if (isKeyboardVisible && isEmptySearchResult) {
      const expandedHeight = Platform.OS === 'android' && keyboardHeight > 0 ? Math.max(260, screenHeight - keyboardHeight * 0.4) : baseMaxHeight;
      stylesArray.push({minHeight: Math.min(baseMaxHeight, expandedHeight)});
    }

    stylesArray.push({paddingBottom: spacing.xl + bottomInset});

    return stylesArray;
  }, [containerStyle, insets.top, isEmptySearchResult, isKeyboardVisible, keyboardBehavior, keyboardHeight, keyboardInset, screenHeight, showSearchField]);

  const translateY = slideAnim;

  return (
    <Modal visible={isModalVisible} transparent animationType="none" onRequestClose={closeOnBackButtonPress ? handleClose : undefined}>
      <View style={[styles.modalOverlay, modalStyle]}>
        {/* Animated Backdrop */}
        <Animated.View style={[styles.modalBackdrop, {opacity: backdropAnim}]}>
          <TouchableOpacity style={styles.backdropTouchable} activeOpacity={1} onPress={handleBackdropPress} />
        </Animated.View>

        {/* Animated Bottom Sheet Content */}
        <Animated.View style={[containerStyles, {transform: [{translateY}]}]}>
          {/* Bottom Sheet Handle & Header - Draggable Area */}
          <View {...panResponder.panHandlers}>
            {/* Bottom Sheet Handle */}
            <View style={styles.handleContainer}>
              <View style={styles.handle} />
            </View>

            {/* Header */}
            <TouchableWithoutFeedback onPress={dismissKeyboard}>
              <View style={[styles.header, headerStyle]}>
                <View style={styles.headerTextContainer}>
                  {title && (
                    <Text style={[styles.title, titleStyle]} numberOfLines={2}>
                      {title}
                    </Text>
                  )}
                  {subtitle && (
                    <Text style={[styles.subtitle, subtitleStyle]} numberOfLines={3}>
                      {subtitle}
                    </Text>
                  )}
                </View>
                {!showCancelButton && (
                  <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
                    <Icon name="CloseCircle" size={22} color={colors.gray[600]} variant="Bulk" />
                  </TouchableOpacity>
                )}
              </View>
            </TouchableWithoutFeedback>
          </View>

          {/* Search Field */}
          {showSearchField && (
            <TextField
              showPlaceholderWhenEmpty
              containerStyle={[styles.searchInput, searchFieldStyle] as ViewStyle}
              placeholder={searchPlaceholder}
              placeholderTextColor={colors.gray[500]}
              value={searchUIValue}
              onChangeText={onChangeSearchText}
              autoCapitalize="none"
              autoCorrect={false}
              rightIconType="search"
              onRightIconPress={handleSearch}
              returnKeyType="search"
              onSubmitEditing={handleSearch}
            />
          )}

          {/* Options List */}
          <FlatList
            data={filteredOptions}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            initialNumToRender={8}
            maxToRenderPerBatch={8}
            windowSize={3}
            removeClippedSubviews={Platform.OS === 'android'}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={contentContainerStyle}
            getItemLayout={getItemLayout}
            ListEmptyComponent={emptyComponent}
            updateCellsBatchingPeriod={100}
          />

          {/* Cancel Button */}
          {showCancelButton && (
            <TouchableOpacity style={[styles.cancelButton, cancelButtonStyle]} onPress={handleClose} activeOpacity={0.7}>
              <Text style={styles.cancelButtonText}>{cancelButtonText}</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.lg,
    width: '100%',
    maxHeight: dimensions.height,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -4},
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 20,
  },
  handleContainer: {
    alignItems: 'center',
    paddingTop: spacing.sm,
    paddingBottom: spacing.md,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: colors.gray[300],
    borderRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  headerTextContainer: {
    flex: 1,
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    color: colors.dark,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginTop: spacing.xs,
    lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
  },
  closeButton: {
    padding: spacing.xs,
  },
  option: {
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  lastOption: {borderBottomWidth: 0},
  disabledOption: {opacity: 0.5},
  optionContent: {flexDirection: 'row', alignItems: 'center'},
  optionIcon: {
    marginRight: spacing.md,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {flex: 1},
  optionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.dark,
  },
  optionSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginTop: spacing.xs,
    lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
  },
  destructiveText: {color: colors.danger},
  disabledText: {color: colors.gray[400]},
  selectedOption: {},
  selectedText: {color: colors.green, fontWeight: typography.fontWeight.semibold},
  selectedSubtext: {color: colors.green + 'CC'},
  checkmarkContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchInput: {
    marginBottom: spacing.md,
  },
  noResultsContainer: {paddingVertical: spacing.xl, alignItems: 'center'},
  noResultsText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  noResultsSubtext: {fontSize: typography.fontSize.sm, color: colors.gray[500], textAlign: 'center'},
  cancelButton: {
    marginTop: spacing.md,
    paddingVertical: spacing.md + 2,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray[300],
    shadowColor: colors.gray[500],
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  cancelButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[700],
  },
});

export default ActionSheetModal;
