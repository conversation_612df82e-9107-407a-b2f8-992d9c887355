import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Dimensions, Keyboard, KeyboardAvoidingView, Platform, StyleSheet, Text, TextStyle, TouchableOpacity, TouchableWithoutFeedback, View, ViewStyle} from 'react-native';
import Modal from 'react-native-modal';
import {borderRadius, colors, shadows, spacing, typography} from '../../constants/theme';
import {dimensions} from '../../constants/dimensions';
import TextField from './TextField';
import {FlatList} from 'react-native-gesture-handler';

export interface ActionSheetOption {
  id: string | number;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  onPress: () => void;
  disabled?: boolean;
  destructive?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
}

export interface ActionSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  options: ActionSheetOption[];
  selectedValue?: string | number;
  showCancelButton?: boolean;
  cancelButtonText?: string;
  showSearchField?: boolean;
  searchPlaceholder?: string;
  closeOnBackdropPress?: boolean;
  closeOnBackButtonPress?: boolean;
  animationInTiming?: number;
  animationOutTiming?: number;
  backdropOpacity?: number;
  keyboardBehavior?: 'overlay' | 'push';
  modalStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  headerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  optionStyle?: ViewStyle;
  cancelButtonStyle?: ViewStyle;
  searchFieldStyle?: ViewStyle;
  onModalShow?: () => void;
  onModalHide?: () => void;
}

const ITEM_HEIGHT = 56;

const ActionSheetModal: React.FC<ActionSheetModalProps> = props => {
  const {
    isVisible,
    onClose,
    title,
    subtitle,
    options,
    selectedValue,
    showCancelButton = true,
    cancelButtonText = 'Hủy',
    showSearchField = false,
    searchPlaceholder = 'Tìm kiếm...',
    closeOnBackdropPress = true,
    closeOnBackButtonPress = true,
    animationInTiming = 300,
    animationOutTiming = 300,
    backdropOpacity = 0.5,
    keyboardBehavior = 'overlay',
    modalStyle,
    containerStyle,
    headerStyle,
    titleStyle,
    subtitleStyle,
    optionStyle,
    cancelButtonStyle,
    searchFieldStyle,
    onModalShow,
    onModalHide,
  } = props;

  const [searchUIValue, setSearchUIValue] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [screenHeight, setScreenHeight] = useState(dimensions.height);

  // Lắng nghe keyboard events để điều chỉnh modal trên Android
  useEffect(() => {
    if (Platform.OS !== 'android' || !showSearchField || keyboardBehavior !== 'overlay') {
      return;
    }

    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', e => {
      setKeyboardHeight(e.endCoordinates.height);
      setScreenHeight(Dimensions.get('window').height);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
      setScreenHeight(dimensions.height);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, [showSearchField, keyboardBehavior]);

  const onChangeSearchText = useCallback((text: string) => {
    setSearchUIValue(text);
    // Nếu xóa hết nội dung, reset về danh sách ban đầu
    if (text.trim() === '') {
      setSearchValue('');
    }
  }, []);

  // Hàm xử lý khi ấn icon search
  const handleSearch = useCallback(() => {
    Keyboard.dismiss();
    setSearchValue(searchUIValue.trim());
  }, [searchUIValue]);

  const filteredOptions = useMemo(() => {
    if (!showSearchField) return options;

    const kw = searchValue.trim().toLowerCase();
    if (!kw) {
      // Nếu chưa tìm kiếm hoặc đã xóa hết, hiển thị danh sách ban đầu (giới hạn để load nhanh)
      return options.slice(0, 150);
    }

    // Chỉ lọc khi có từ khóa tìm kiếm (sau khi ấn search)
    const list = options.filter(o => o.title.toLowerCase().includes(kw) || (o.subtitle && o.subtitle.toLowerCase().includes(kw)));
    return list.slice(0, 500);
  }, [options, showSearchField, searchValue]);

  const handleClose = useCallback(() => {
    setSearchUIValue('');
    setSearchValue('');
    onClose();
  }, [onClose]);

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  const keyExtractor = useCallback((item: ActionSheetOption) => String(item.id), []);

  const emptyComponent = useMemo(
    () =>
      showSearchField && searchValue?.trim() ? (
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.noResultsContainer}>
            <Text style={styles.noResultsText}>Không tìm thấy kết quả nào</Text>
            <Text style={styles.noResultsSubtext}>Thử tìm kiếm với từ khóa khác</Text>
          </View>
        </TouchableWithoutFeedback>
      ) : null,
    [showSearchField, searchValue, dismissKeyboard],
  );

  const contentContainerStyle = useMemo(() => ({paddingBottom: spacing.sm}), []);

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    [],
  );

  const renderItem = useCallback(
    ({item, index}: {item: ActionSheetOption; index: number}) => {
      const selected = selectedValue === item.id;
      return (
        <TouchableOpacity
          key={item.id}
          style={[styles.option, optionStyle, item.style, item.disabled && styles.disabledOption, index === filteredOptions.length - 1 && styles.lastOption, selected && styles.selectedOption]}
          onPress={() => {
            if (item.disabled) return;
            item.onPress();
            handleClose();
          }}
          disabled={item.disabled}
          activeOpacity={0.7}>
          <View style={styles.optionContent}>
            {item.icon && <View style={styles.optionIcon}>{item.icon}</View>}
            <View style={styles.optionTextContainer}>
              <Text style={[styles.optionTitle, item.destructive && styles.destructiveText, item.disabled && styles.disabledText, selected && styles.selectedText, item.titleStyle]} numberOfLines={1}>
                {item.title}
              </Text>
              {!!item.subtitle && (
                <Text style={[styles.optionSubtitle, item.disabled && styles.disabledText, selected && styles.selectedSubtext]} numberOfLines={2}>
                  {item.subtitle}
                </Text>
              )}
            </View>
            {selected && (
              <View style={styles.checkmarkContainer}>
                <Text style={styles.checkmark}>✓</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      );
    },
    [filteredOptions.length, handleClose, optionStyle, selectedValue],
  );

  // Render nội dung modal
  const renderContent = () => (
    <>
      {(title || subtitle) && (
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={[styles.header, headerStyle]}>
            {title && (
              <Text style={[styles.title, titleStyle]} numberOfLines={2}>
                {title}
              </Text>
            )}
            {subtitle && (
              <Text style={[styles.subtitle, subtitleStyle]} numberOfLines={3}>
                {subtitle}
              </Text>
            )}
          </View>
        </TouchableWithoutFeedback>
      )}

      {showSearchField && (
        <TextField
          showPlaceholderWhenEmpty
          containerStyle={[styles.searchInput, searchFieldStyle] as ViewStyle}
          placeholder={searchPlaceholder}
          placeholderTextColor={colors.gray[500]}
          value={searchUIValue}
          onChangeText={onChangeSearchText}
          autoCapitalize="none"
          autoCorrect={false}
          rightIconType="search"
          onRightIconPress={handleSearch}
          returnKeyType="search"
          onSubmitEditing={handleSearch}
        />
      )}

      <FlatList
        data={filteredOptions}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        initialNumToRender={20}
        maxToRenderPerBatch={32}
        windowSize={10}
        removeClippedSubviews
        showsVerticalScrollIndicator={false}
        contentContainerStyle={contentContainerStyle}
        getItemLayout={getItemLayout}
        ListEmptyComponent={emptyComponent}
      />

      {showCancelButton && (
        <TouchableOpacity style={[styles.cancelButton, cancelButtonStyle]} onPress={handleClose} activeOpacity={0.7}>
          <Text style={styles.cancelButtonText}>{cancelButtonText}</Text>
        </TouchableOpacity>
      )}
    </>
  );

  // Tính toán style cho modal container dựa trên keyboard height
  const getContainerStyle = () => {
    const baseStyle = [styles.container, containerStyle];

    if (Platform.OS === 'android' && showSearchField && keyboardBehavior === 'overlay' && keyboardHeight > 0) {
      // Trên Android, khi bàn phím hiện, điều chỉnh maxHeight để modal không bị che
      return [
        ...baseStyle,
        {
          maxHeight: screenHeight - keyboardHeight - 50,
          marginBottom: 0,
        },
      ];
    }

    return baseStyle;
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={closeOnBackdropPress ? handleClose : undefined}
      onBackButtonPress={closeOnBackButtonPress ? handleClose : undefined}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={animationInTiming}
      animationOutTiming={animationOutTiming}
      backdropOpacity={backdropOpacity}
      useNativeDriver
      hideModalContentWhileAnimating
      avoidKeyboard={false}
      onModalShow={onModalShow}
      onModalHide={onModalHide}
      propagateSwipe
      statusBarTranslucent={false}
      deviceHeight={screenHeight}
      style={[styles.modal, modalStyle]}>
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} keyboardVerticalOffset={0} style={styles.container}>
        <View style={getContainerStyle()}>{renderContent()}</View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  container: {
    backgroundColor: colors.white,
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    maxHeight: dimensions.height * 0.8,
    ...shadows.lg,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    alignItems: 'center',
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
  },
  option: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  lastOption: {borderBottomWidth: 0},
  disabledOption: {opacity: 0.5},
  optionContent: {flexDirection: 'row', alignItems: 'center'},
  optionIcon: {
    marginRight: spacing.md,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {flex: 1},
  optionTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.dark,
  },
  optionSubtitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    marginTop: spacing.xs,
    lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
  },
  destructiveText: {color: colors.danger},
  disabledText: {color: colors.gray[400]},
  selectedOption: {
    backgroundColor: colors.green + '10',
    borderLeftWidth: 3,
    borderLeftColor: colors.green,
  },
  selectedText: {color: colors.green, fontFamily: typography.fontFamily.semibold},
  selectedSubtext: {color: colors.green + 'CC'},
  checkmarkContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.green,
    borderRadius: 12,
  },
  checkmark: {color: colors.white, fontSize: 14, fontWeight: typography.fontWeight.bold},
  searchInput: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.sm,
    marginTop: spacing.sm,
  },
  noResultsContainer: {paddingVertical: spacing.xl, paddingHorizontal: spacing.lg, alignItems: 'center'},
  noResultsText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  noResultsSubtext: {fontSize: typography.fontSize.sm, color: colors.gray[500], textAlign: 'center'},
  cancelButton: {
    marginBottom: spacing.xl,
    borderWidth: 1,
    borderColor: colors.gray[400],
    alignItems: 'center',
    paddingVertical: spacing.md - 2,
    marginHorizontal: spacing.md,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.light,
    marginTop: spacing.sm,
  },
  cancelButtonText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },
});

export default ActionSheetModal;
