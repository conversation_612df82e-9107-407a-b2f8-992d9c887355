import React, {useEffect, useRef, useMemo, useCallback} from 'react';
import {View, Text, StyleSheet, Animated, Dimensions, TouchableOpacity} from 'react-native';
import CustomModal from './CustomModal';
import Button from './Button';
import {colors, typography, spacing, borderRadius, shadows} from '../../constants/theme';

interface ConfirmModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel?: () => void;
  confirmVariant?: 'primary' | 'danger' | 'success' | 'warning';
  showIcon?: boolean;
  icon?: React.ReactNode;
  type?: 'info' | 'success' | 'warning' | 'danger';
  destructive?: boolean;
  loading?: boolean;
}

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  visible,
  onClose,
  title,
  message,
  confirmText = 'Xác nhận',
  cancelText = 'Hủy',
  onConfirm,
  onCancel,
  confirmVariant = 'primary',
  showIcon = true,
  icon,
  type = 'info',
  destructive = false,
  loading = false,
}) => {
  const scaleValue = useRef(new Animated.Value(0.7)).current;
  const opacityValue = useRef(new Animated.Value(0)).current;
  const slideValue = useRef(new Animated.Value(50)).current;
  const iconScaleValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(scaleValue, {
          toValue: 1,
          tension: 120,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideValue, {
          toValue: 0,
          duration: 350,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Icon animation after modal appears
        Animated.spring(iconScaleValue, {
          toValue: 1,
          tension: 150,
          friction: 6,
          useNativeDriver: true,
        }).start();
      });
    } else {
      Animated.parallel([
        Animated.spring(scaleValue, {
          toValue: 0.7,
          tension: 120,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(opacityValue, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideValue, {
          toValue: 30,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(iconScaleValue, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, scaleValue, opacityValue, slideValue, iconScaleValue]);

  const typeConfig = useMemo(() => {
    const baseConfig = {
      success: {
        color: colors.success,
        background: colors.success + '12',
        icon: '✓',
        borderColor: colors.success + '25',
        gradient: [colors.success + '08', colors.success + '15'],
      },
      warning: {
        color: colors.warning,
        background: colors.warning + '12',
        icon: '!',
        borderColor: colors.warning + '25',
        gradient: [colors.warning + '08', colors.warning + '15'],
      },
      danger: {
        color: colors.danger,
        background: colors.danger + '12',
        icon: '×',
        borderColor: colors.danger + '25',
        gradient: [colors.danger + '08', colors.danger + '15'],
      },
      info: {
        color: colors.green,
        background: colors.green + '12',
        icon: 'i',
        borderColor: colors.green + '25',
        gradient: [colors.green + '08', colors.green + '15'],
      },
    };

    return baseConfig[type] || baseConfig.info;
  }, [type]);

  const confirmColor = useMemo(() => {
    if (destructive) return colors.danger;

    switch (confirmVariant) {
      case 'danger':
        return colors.danger;
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      default:
        return colors.green;
    }
  }, [destructive, confirmVariant]);

  const handleCancel = useCallback(() => {
    if (loading) return;
    onCancel?.();
    onClose();
  }, [loading, onCancel, onClose]);

  const handleConfirm = useCallback(() => {
    if (loading) return;
    onConfirm();
    onClose();
  }, [loading, onConfirm, onClose]);

  const renderIcon = () => {
    if (icon) {
      return (
        <Animated.View
          style={[
            styles.iconContainer,
            {
              backgroundColor: typeConfig.background,
              borderColor: typeConfig.borderColor,
              transform: [{scale: iconScaleValue}],
            },
          ]}>
          {icon}
        </Animated.View>
      );
    }

    if (!showIcon) return null;

    return (
      <Animated.View
        style={[
          styles.iconContainer,
          {
            backgroundColor: typeConfig.background,
            borderColor: typeConfig.borderColor,
            transform: [{scale: iconScaleValue}],
          },
        ]}>
        <Text style={[styles.iconText, {color: typeConfig.color}]}>{typeConfig.icon}</Text>
      </Animated.View>
    );
  };

  const buttonVariant = useMemo(() => {
    if (destructive) return 'secondary';

    switch (confirmVariant) {
      case 'danger':
        return 'secondary';
      case 'success':
      case 'warning':
      case 'primary':
      default:
        return 'primary';
    }
  }, [destructive, confirmVariant]);

  const renderButtons = () => (
    <View style={styles.buttonContainer}>
      <TouchableOpacity style={styles.cancelButtonStyle} onPress={handleCancel}>
        <Text style={styles.cancelButtonText}>{cancelText}</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.confirmButtonStyle} onPress={handleConfirm}>
        <Text style={styles.confirmButtonText}>{confirmText}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <CustomModal isVisible={visible} onClose={onClose} showCloseButton={false}>
      <Animated.View
        style={[
          styles.container,
          {
            opacity: opacityValue,
            transform: [{scale: scaleValue}, {translateY: slideValue}],
          },
        ]}>
        {/* Header Section */}
        <View style={styles.header}>
          {renderIcon()}
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.message}>{message}</Text>
        </View>

        {/* Divider */}
        <View style={styles.divider} />

        {/* Button Section */}
        {renderButtons()}
      </Animated.View>
    </CustomModal>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    marginHorizontal: spacing.lg,
    maxWidth: 340,
    overflow: 'hidden',
    alignSelf: 'center',
    padding: spacing.sm,
  },

  header: {
    alignItems: 'center',
    backgroundColor: colors.white,
    width: '100%',
    marginBottom: spacing.md,
  },

  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
    borderWidth: 2,
  },

  iconText: {
    fontSize: 36,
    fontWeight: typography.fontWeight.bold as any,
    textAlign: 'center',
  },

  title: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold as any,
    color: colors.gray[800],
    textAlign: 'center',
    marginBottom: spacing.md,
    lineHeight: typography.fontSize['2xl'] * 1.3,
    letterSpacing: -0.5,
  },

  message: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.fontSize.base * 1.6,
    paddingHorizontal: spacing.sm,
    maxWidth: '95%',
  },

  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginHorizontal: 0,
  },

  buttonContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingTop: spacing.md,
    gap: spacing.md,
    width: '100%',
  },

  cancelButtonStyle: {
    flex: 1,
    marginHorizontal: 0,
    borderRadius: borderRadius.lg,
    backgroundColor: 'transparent',
    borderColor: colors.green,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },

  confirmButtonStyle: {
    flex: 1,
    marginHorizontal: 0,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.green,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },

  cancelButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.green,
    fontWeight: typography.fontWeight.medium as any,
  },

  confirmButtonText: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    fontWeight: typography.fontWeight.semibold as any,
  },
});

export default ConfirmModal;
