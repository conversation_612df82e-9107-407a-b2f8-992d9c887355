import Icon from '@components/common/Icon';
import React from 'react';
import {StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import {colors, spacing, typography} from '../../constants/theme';

interface HeaderProps {
  title?: string;
  showBackButton?: boolean;
  onBack?: () => void;
  rightComponent?: React.ReactNode;
  leftComponent?: React.ReactNode;
  backgroundColor?: string;
  titleColor?: string;
  elevation?: boolean;
  borderBottom?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showBackButton = false,
  onBack,
  rightComponent,
  leftComponent,
  backgroundColor = colors.green,
  titleColor = colors.white,
  elevation = true,
  borderBottom = true,
  style,
  titleStyle,
}) => {
  const headerStyle = [styles.header, {backgroundColor}, elevation && styles.headerElevation, borderBottom && styles.headerBorder, style];

  return (
    <View style={headerStyle}>
      {/* Left Section */}
      <View style={styles.headerLeft}>
        {leftComponent ? (
          leftComponent
        ) : showBackButton && onBack ? (
          <TouchableOpacity onPress={onBack} style={styles.backButton} activeOpacity={0.7} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
            <Icon name={'ChevronLeft'} library="lucide" size={24} color={colors.white} />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Center - Title */}
      <View style={styles.headerCenter}>
        {title && (
          <Text style={[styles.headerTitle, {color: titleColor}, titleStyle]} numberOfLines={2}>
            {title}
          </Text>
        )}
      </View>

      {/* Right Section */}
      <View style={styles.headerRight}>{rightComponent}</View>
    </View>
  );
};

// Preset Header Variants
export const HeaderVariants = {
  // Header mặc định
  default: (title: string, onBack?: () => void): HeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    elevation: true,
    borderBottom: true,
  }),

  // Header với background màu
  colored: (title: string, bgColor: string, onBack?: () => void): HeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    backgroundColor: bgColor,
    titleColor: colors.white,
    elevation: true,
    borderBottom: false,
  }),

  // Header trong suốt
  transparent: (title: string, onBack?: () => void): HeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    backgroundColor: 'transparent',
    titleColor: colors.white,
    elevation: false,
    borderBottom: false,
  }),

  // Header với action buttons
  withActions: (title: string, actions: React.ReactNode, onBack?: () => void): HeaderProps => ({
    title,
    showBackButton: !!onBack,
    onBack,
    rightComponent: actions,
    elevation: true,
    borderBottom: true,
  }),

  // Header minimal (chỉ có title)
  minimal: (title: string): HeaderProps => ({
    title,
    showBackButton: false,
    elevation: false,
    borderBottom: false,
    backgroundColor: 'transparent',
  }),
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
    paddingVertical: 8,
  },
  headerElevation: {
    elevation: 4,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerLeft: {
    flex: 0.8,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRight: {
    flex: 0.8,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.black,
    textAlign: 'center',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  backIcon: {
    width: 32,
    height: 32,
    resizeMode: 'contain',
  },
  backText: {
    fontSize: typography.fontSize.base,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
});

export default Header;
