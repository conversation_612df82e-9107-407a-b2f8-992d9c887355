import React from 'react';
import {TextInput, View, Text, StyleSheet} from 'react-native';

import {Image, TouchableOpacity} from 'react-native';

interface InputProps {
  label?: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  error?: string;
  multiline?: boolean;
  numberOfLines?: number;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  editable?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  rightIcon?: string;
  onRightIconPress?: () => void;
}

const Input = React.memo<InputProps>(
  ({
    label,
    value,
    onChangeText,
    placeholder,
    secureTextEntry = false,
    error,
    multiline = false,
    numberOfLines = 1,
    keyboardType = 'default',
    editable = true,
    autoCapitalize = 'none',
    rightIcon,
    onRightIconPress,
  }) => {
    return (
      <View style={styles.container}>
        {label && <Text style={styles.label}>{label}</Text>}
        <View style={[styles.inputContainer, !!error && styles.inputError, !editable && styles.disabledContainer]}>
          <TextInput
            style={[styles.input, multiline && styles.multiline, !editable && styles.disabledInput]}
            value={value}
            onChangeText={onChangeText}
            placeholder={placeholder}
            secureTextEntry={secureTextEntry}
            multiline={multiline}
            numberOfLines={numberOfLines}
            keyboardType={keyboardType}
            placeholderTextColor="#999"
            editable={editable}
            autoCapitalize={autoCapitalize}
          />
          {rightIcon && (
            <TouchableOpacity onPress={onRightIconPress} style={styles.rightIconContainer} disabled={!editable || !onRightIconPress}>
              <Text>▼</Text>
            </TouchableOpacity>
          )}
        </View>
        {error && <Text style={styles.errorText}>{error}</Text>}
      </View>
    );
  },
);

Input.displayName = 'Input';

export default Input;

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  inputContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  input: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  inputError: {
    borderColor: '#FF3B30',
  },
  disabledContainer: {
    backgroundColor: '#f5f5f5',
  },
  disabledInput: {
    color: '#999',
  },
  multiline: {
    textAlignVertical: 'top',
  },
  rightIconContainer: {
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginTop: 4,
  },
});
