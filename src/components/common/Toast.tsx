import {colors, spacing, typography} from '@constants/theme';
import React, {createContext, ReactNode, useContext, useEffect, useRef, useState} from 'react';
import {Animated, Dimensions, Platform, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import Icon from './Icon';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

// Toast Types
export type ToastType = 'success' | 'error' | 'info' | 'warning';
export type ToastPosition = 'top' | 'bottom';

// Toast Item Interface
export interface ToastItem {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  position?: ToastPosition;
  onPress?: () => void;
  icon?: string;
}

// Toast Configuration
export interface ToastConfig {
  position?: ToastPosition;
  duration?: number;
  animationDuration?: number;
  maxToasts?: number;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

// Toast Context Interface
interface ToastContextType {
  showToast: (item: Omit<ToastItem, 'id'>) => void;
  hideToast: (id: string) => void;
  hideAllToasts: () => void;
}

// Create Toast Context
const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Toast Colors - Clean and modern (inspired by HeroUI)
const TOAST_COLORS = {
  success: {
    primary: '#10B981',
    background: 'rgba(255, 255, 255, 0.98)',
    text: '#1F2937',
    subtext: '#6B7280',
  },
  error: {
    primary: '#EF4444',
    background: 'rgba(255, 255, 255, 0.98)',
    text: '#1F2937',
    subtext: '#6B7280',
  },
  warning: {
    primary: '#F59E0B',
    background: 'rgba(255, 255, 255, 0.98)',
    text: '#1F2937',
    subtext: '#6B7280',
  },
  info: {
    primary: '#3B82F6',
    background: 'rgba(255, 255, 255, 0.98)',
    text: '#1F2937',
    subtext: '#6B7280',
  },
};

// Toast Icons - Clean and modern
const TOAST_ICONS = {
  success: <Icon name="TickCircle" size={22} color={TOAST_COLORS.success.primary} variant="Bold" />,
  error: <Icon name="Danger" size={22} color={TOAST_COLORS.error.primary} variant="Bold" />,
  warning: <Icon name="Warning2" size={22} color={TOAST_COLORS.warning.primary} variant="Bold" />,
  info: <Icon name="InfoCircle" size={22} color={TOAST_COLORS.info.primary} variant="Bold" />,
};

// Individual Toast Component
interface ToastComponentProps {
  item: ToastItem;
  onHide: (id: string) => void;
  config: ToastConfig;
}

const ToastComponent: React.FC<ToastComponentProps> = ({item, onHide, config}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(item.position === 'top' ? -100 : 100)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const duration = item.duration || config.duration || 3000;

    // Show animation with spring effect
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: config.animationDuration || 300,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 65,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 65,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto hide
    const timer = setTimeout(() => {
      hideToast();
    }, duration);

    return () => clearTimeout(timer);
  }, []);

  const hideToast = () => {
    if (!isVisible) return;
    setIsVisible(false);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: config.animationDuration || 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: item.position === 'top' ? -100 : 100,
        duration: config.animationDuration || 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onHide(item.id);
    });
  };

  const handlePress = () => {
    if (item.onPress) {
      item.onPress();
    }
    hideToast();
  };

  const toastColor = TOAST_COLORS[item.type];
  const toastIcon = item.icon || TOAST_ICONS[item.type];

  const ToastContent = () => (
    <View style={styles.toastContent}>
      {toastIcon}
      <View style={styles.contentContainer}>
        {item.title && (
          <Text style={[styles.title, {color: toastColor.text}, config.textStyle]} numberOfLines={2}>
            {item.title}
          </Text>
        )}
        <Text style={[styles.message, {color: toastColor.subtext}, config.textStyle]}>
          {item.message}
        </Text>
      </View>
      <TouchableOpacity style={styles.closeButton} onPress={hideToast} hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
        <Icon name="CloseCircle" size={18} color={toastColor.subtext} variant="Bulk" />
      </TouchableOpacity>
    </View>
  );

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        {
          opacity: fadeAnim,
          transform: [{translateY: slideAnim}, {scale: scaleAnim}],
        },
        item.position === 'top' ? styles.toastTop : styles.toastBottom,
        config.style,
      ]}>
      <TouchableOpacity
        style={[
          styles.toast,
          {
            backgroundColor: toastColor.background,
          },
        ]}
        onPress={handlePress}
        activeOpacity={0.95}>
        <ToastContent />
      </TouchableOpacity>
    </Animated.View>
  );
};

// Toast Container Component
interface ToastContainerProps {
  toasts: ToastItem[];
  onHide: (id: string) => void;
  config: ToastConfig;
}

const ToastContainer: React.FC<ToastContainerProps> = ({toasts, onHide, config}) => {
  const topToasts = toasts.filter(toast => (toast.position || config.position || 'top') === 'top');
  const bottomToasts = toasts.filter(toast => (toast.position || config.position || 'top') === 'bottom');

  return (
    <>
      {/* Top Toasts */}
      <View style={[styles.container, styles.topContainer]} pointerEvents="box-none">
        {topToasts.map(toast => (
          <ToastComponent key={toast.id} item={{...toast, position: 'top'}} onHide={onHide} config={config} />
        ))}
      </View>

      {/* Bottom Toasts */}
      <View style={[styles.container, styles.bottomContainer]} pointerEvents="box-none">
        {bottomToasts.map(toast => (
          <ToastComponent key={toast.id} item={{...toast, position: 'bottom'}} onHide={onHide} config={config} />
        ))}
      </View>
    </>
  );
};

// Toast Provider Component
interface ToastProviderProps {
  children: ReactNode;
  config?: ToastConfig;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({children, config = {}}) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const showToast = (item: Omit<ToastItem, 'id'>) => {
    const id = `toast_${Date.now()}_${Math.random()}`;
    const newToast: ToastItem = {
      ...item,
      id,
      position: item.position || config.position || 'top',
    };

    setToasts(prev => {
      const maxToasts = config.maxToasts || 3;
      const updated = [newToast, ...prev];
      return updated.slice(0, maxToasts);
    });
  };

  const hideToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const hideAllToasts = () => {
    setToasts([]);
  };

  const contextValue: ToastContextType = {
    showToast,
    hideToast,
    hideAllToasts,
  };

  // Set ref for global toast access
  useEffect(() => {
    // Dynamically import to avoid circular dependency
    import('@utils/ToastHelper').then(({ToastHelper}) => {
      ToastHelper.setRef({showToast, hideToast, hideAllToasts});
    });
  }, []);

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastContainer toasts={toasts} onHide={hideToast} config={config} />
    </ToastContext.Provider>
  );
};

// useToast Hook
export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Helper Functions for Quick Toast Display
export const createToastHelpers = (toastContext: ToastContextType) => ({
  success: (message: string, options?: Partial<ToastItem>) => {
    toastContext.showToast({
      type: 'success',
      message,
      ...options,
    });
  },
  error: (message: string, options?: Partial<ToastItem>) => {
    toastContext.showToast({
      type: 'error',
      message,
      ...options,
    });
  },
  info: (message: string, options?: Partial<ToastItem>) => {
    toastContext.showToast({
      type: 'info',
      message,
      ...options,
    });
  },
  warning: (message: string, options?: Partial<ToastItem>) => {
    toastContext.showToast({
      type: 'warning',
      message,
      ...options,
    });
  },
});

// Styles - iOS HeroUI inspired
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 9999,
    paddingHorizontal: spacing.md,
  },
  topContainer: {
    top: Platform.OS === 'ios' ? 60 : 10,
  },
  bottomContainer: {
    bottom: Platform.OS === 'ios' ? 60 : 40,
  },
  toastContainer: {
    marginVertical: 6,
  },
  toastTop: {
    // Additional styles for top positioned toasts
  },
  toastBottom: {
    // Additional styles for bottom positioned toasts
  },
  toast: {
    borderRadius: 16,
    overflow: 'hidden',
    borderColor: colors.gray[400],
    borderWidth: 1,
  },
  blurContainer: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  blurOverlay: {
    borderRadius: 16,
  },
  toastContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    minHeight: 64,
    gap: 8,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 2,
    lineHeight: 20,
    letterSpacing: -0.3,
  },
  message: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
    letterSpacing: -0.2,
  },
  closeButton: {
    width: 26,
    height: 26,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
});

export default {
  ToastProvider,
  useToast,
  createToastHelpers,
};
