import {useState, useCallback} from 'react';
import {IOSAlertButton} from './IOSAlert';

interface AlertOptions {
  title?: string;
  message?: string;
  buttons?: IOSAlertButton[];
}

interface UseIOSAlertReturn {
  visible: boolean;
  title?: string;
  message?: string;
  buttons: IOSAlertButton[];
  show: (options: AlertOptions) => void;
  hide: () => void;
}

export const useIOSAlert = (): UseIOSAlertReturn => {
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState<string | undefined>();
  const [message, setMessage] = useState<string | undefined>();
  const [buttons, setButtons] = useState<IOSAlertButton[]>([]);

  const show = useCallback((options: AlertOptions) => {
    setTitle(options.title);
    setMessage(options.message);
    setButtons(options.buttons || []);
    setVisible(true);
  }, []);

  const hide = useCallback(() => {
    setVisible(false);
  }, []);

  return {
    visible,
    title,
    message,
    buttons,
    show,
    hide,
  };
};
