import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Animated, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle} from 'react-native';
import {Control, Controller, FieldError, FieldValues, Path, RegisterOptions} from 'react-hook-form';
import {borderRadius, colors, spacing, typography} from '../../constants/theme';

type CheckboxSize = 'small' | 'medium' | 'large';
type CheckboxVariant = 'default' | 'outlined' | 'filled';

interface BaseCheckboxProps {
  /** Nhãn hiển thị bên cạnh checkbox */
  label?: string;
  /** Mô tả bổ sung */
  description?: string;
  /** Lỗi hiển thị */
  error?: FieldError | string;
  /** Vô hiệu hóa checkbox */
  disabled?: boolean;
  /** Kích thước checkbox */
  size?: CheckboxSize;
  /** Biến thể hiển thị */
  variant?: CheckboxVariant;
  /** Style container */
  containerStyle?: ViewStyle;
  /** Style cho checkbox */
  checkboxStyle?: ViewStyle;
  /** Style cho label */
  labelStyle?: TextStyle;
  /** Style cho description */
  descriptionStyle?: TextStyle;
  /** Style cho error */
  errorStyle?: TextStyle;
  /** Vị trí checkbox (left/right) */
  checkboxPosition?: 'left' | 'right';
  /** Icon tùy chỉnh khi checked */
  checkedIcon?: React.ReactNode;
  /** Icon tùy chỉnh khi unchecked */
  uncheckedIcon?: React.ReactNode;
  /** Callback khi giá trị thay đổi */
  onValueChange?: (value: boolean) => void;
  /** Bắt buộc */
  required?: boolean;
  /** Hiệu ứng animation */
  animated?: boolean;
  /** Test ID cho testing */
  testID?: string;
}

interface ControlledCheckboxProps<TFieldValues extends FieldValues> extends BaseCheckboxProps {
  /** React Hook Form control */
  control: Control<TFieldValues>;
  /** Tên field */
  name: Path<TFieldValues>;
  /** Validation rules */
  rules?: Omit<RegisterOptions<TFieldValues, Path<TFieldValues>>, 'valueAsNumber' | 'valueAsDate' | 'setValueAs' | 'disabled'>;
  /** Giá trị mặc định */
  defaultValue?: boolean;
}

interface UncontrolledCheckboxProps extends BaseCheckboxProps {
  /** Giá trị hiện tại */
  value: boolean;
  /** Callback khi thay đổi */
  onValueChange: (value: boolean) => void;
}

export interface CheckboxRef {
  /** Focus vào checkbox */
  focus: () => void;
  /** Blur khỏi checkbox */
  blur: () => void;
  /** Toggle giá trị */
  toggle: () => void;
  /** Set giá trị */
  setValue: (value: boolean) => void;
}

// Type guard để kiểm tra controlled props
function isControlledProps<TFieldValues extends FieldValues>(props: any): props is ControlledCheckboxProps<TFieldValues> {
  return 'control' in props && 'name' in props;
}

// Size configurations
const sizeConfig = {
  small: {
    checkboxSize: 18, // Tăng size
    iconSize: 12,
    fontSize: typography.fontSize.sm,
    spacing: spacing.xs,
  },
  medium: {
    checkboxSize: 22, // Tăng size
    iconSize: 14,
    fontSize: typography.fontSize.base,
    spacing: spacing.sm,
  },
  large: {
    checkboxSize: 26, // Tăng size
    iconSize: 16,
    fontSize: typography.fontSize.lg,
    spacing: spacing.md,
  },
};

/**
 * Component Checkbox đẹp, đồng bộ và tối ưu
 * Hỗ trợ cả controlled (react-hook-form) và uncontrolled mode
 */
const Checkbox = forwardRef<CheckboxRef, ControlledCheckboxProps<any> | UncontrolledCheckboxProps>((props, ref) => {
  const {
    label,
    description,
    error,
    disabled = false,
    size = 'medium',
    variant = 'filled',
    containerStyle,
    checkboxStyle,
    labelStyle,
    descriptionStyle,
    errorStyle,
    checkboxPosition = 'left',
    checkedIcon,
    uncheckedIcon,
    required = false,
    animated = true,
    testID,
    onValueChange,
  } = props;

  // Animation values - tách riêng native và non-native
  const scaleAnim = useRef(new Animated.Value(1)).current; // native
  const colorAnim = useRef(new Animated.Value(0)).current; // non-native (for backgroundColor)
  const checkScaleAnim = useRef(new Animated.Value(0)).current; // native
  const checkRotateAnim = useRef(new Animated.Value(0)).current; // native
  const checkOpacityAnim = useRef(new Animated.Value(0)).current; // native (for checkmark opacity)

  // Internal state for uncontrolled mode
  const [internalValue, setInternalValue] = useState(false);
  const touchableRef = useRef<TouchableOpacity>(null);

  // Get size configuration
  const config = sizeConfig[size];

  // Animation functions
  const animatePress = useCallback(() => {
    if (!animated) return;

    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  }, [scaleAnim, animated]);

  const animateCheck = useCallback(
    (checked: boolean) => {
      if (!animated) {
        checkOpacityAnim.setValue(checked ? 1 : 0);
        colorAnim.setValue(checked ? 1 : 0);
        checkScaleAnim.setValue(checked ? 1 : 0);
        checkRotateAnim.setValue(checked ? 1 : 0);
        return;
      }

      if (checked) {
        // Animation khi tích - bounce effect
        Animated.parallel([
          Animated.timing(checkOpacityAnim, {
            toValue: 1,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(colorAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: false,
          }),
          Animated.sequence([
            Animated.timing(checkScaleAnim, {
              toValue: 1.2,
              duration: 150,
              useNativeDriver: true,
            }),
            Animated.spring(checkScaleAnim, {
              toValue: 1,
              friction: 4,
              tension: 100,
              useNativeDriver: true,
            }),
          ]),
          Animated.timing(checkRotateAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Animation khi bỏ tích - fade out nhanh
        Animated.parallel([
          Animated.timing(checkOpacityAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(colorAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: false,
          }),
          Animated.timing(checkScaleAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(checkRotateAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
        ]).start();
      }
    },
    [checkOpacityAnim, colorAnim, checkScaleAnim, checkRotateAnim, animated],
  );

  // Handle value change
  const handleValueChange = useCallback(
    (newValue: boolean) => {
      animatePress();
      animateCheck(newValue);

      if (!isControlledProps(props)) {
        setInternalValue(newValue);
      }

      onValueChange?.(newValue);
    },
    [animatePress, animateCheck, onValueChange, props],
  );

  // Ref methods
  useImperativeHandle(ref, () => ({
    focus: () => {
      touchableRef.current?.focus();
    },
    blur: () => {
      touchableRef.current?.blur();
    },
    toggle: () => {
      if (isControlledProps(props)) {
        // For controlled mode, we can't directly toggle without access to form methods
        console.warn('Toggle method not available in controlled mode. Use form.setValue instead.');
        return;
      }
      handleValueChange(!internalValue);
    },
    setValue: (value: boolean) => {
      if (isControlledProps(props)) {
        console.warn('SetValue method not available in controlled mode. Use form.setValue instead.');
        return;
      }
      handleValueChange(value);
    },
  }));

  // Initialize animations on mount
  useEffect(() => {
    const initialValue = isControlledProps(props) ? (props as ControlledCheckboxProps<any>).defaultValue || false : (props as UncontrolledCheckboxProps).value;

    // Set initial animation state without animation
    checkOpacityAnim.setValue(initialValue ? 1 : 0);
    colorAnim.setValue(initialValue ? 1 : 0);
    checkScaleAnim.setValue(initialValue ? 1 : 0);
    checkRotateAnim.setValue(initialValue ? 1 : 0);
  }, []); // Only run on mount

  // Get current value for uncontrolled mode
  const uncontrolledCurrentValue = !isControlledProps(props) ? (props as UncontrolledCheckboxProps).value : false;

  // Update animations when value changes (for uncontrolled mode)
  useEffect(() => {
    if (!isControlledProps(props)) {
      animateCheck(uncontrolledCurrentValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uncontrolledCurrentValue]);

  // Animated background color
  const backgroundColor = colorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [disabled ? colors.gray[200] : colors.white, disabled ? colors.gray[400] : colors.green],
  });

  // Animated border color
  const borderColor = colorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [disabled ? colors.gray[300] : colors.gray[400], disabled ? colors.gray[400] : colors.green],
  });

  // Render checkbox content
  const renderCheckbox = useCallback(
    (value: boolean, onChange: (value: boolean) => void) => {
      const handlePress = () => {
        if (disabled) return;
        const newValue = !value;
        // Trigger animation first, then update value
        animateCheck(newValue);
        onChange(newValue);
      };

      const getCheckboxStyle = () => {
        const baseStyle = {
          width: config.checkboxSize,
          height: config.checkboxSize,
          borderRadius: borderRadius.base, // Tăng border radius
          borderWidth: 2,
          justifyContent: 'center' as const,
          alignItems: 'center' as const,
        };

        if (variant === 'filled') {
          return {
            ...baseStyle,
            backgroundColor: value ? (disabled ? colors.gray[400] : colors.green) : disabled ? colors.gray[100] : '#F8F9FA', // Màu background nhẹ nhàng
            borderColor: value ? (disabled ? colors.gray[400] : colors.green) : disabled ? colors.gray[300] : '#E0E0E0', // Border nhẹ nhàng hơn
          };
        }

        return {
          ...baseStyle,
          backgroundColor: colors.white, // Background luôn trắng cho variant default
          borderColor: value ? (disabled ? colors.gray[400] : colors.green) : disabled ? colors.gray[300] : colors.gray[400],
        };
      };

      // Interpolate rotation for checkmark
      const checkRotation = checkRotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['-90deg', '0deg'],
      });

      const renderCheckIcon = () => {
        if (checkedIcon && value) {
          return checkedIcon;
        }

        if (uncheckedIcon && !value) {
          return uncheckedIcon;
        }

        // Render animated checkmark
        return (
          <Animated.View
            style={{
              width: config.iconSize,
              height: config.iconSize,
              justifyContent: 'center',
              alignItems: 'center',
              opacity: checkOpacityAnim,
              transform: [{scale: checkScaleAnim}, {rotate: checkRotation}],
            }}>
            {/* Simple checkmark */}
            <View
              style={{
                width: config.iconSize * 0.6,
                height: config.iconSize * 0.3,
                borderLeftWidth: 2,
                borderBottomWidth: 2,
                borderColor: variant === 'filled' ? (disabled ? colors.gray[600] : colors.white) : disabled ? colors.gray[600] : colors.green,
                transform: [{rotate: '-45deg'}],
                marginTop: -1,
              }}
            />
          </Animated.View>
        );
      };

      return (
        <TouchableOpacity
          ref={touchableRef}
          onPress={handlePress}
          disabled={disabled}
          activeOpacity={0.8}
          accessibilityRole="checkbox"
          accessibilityState={{
            checked: value,
            disabled: disabled,
          }}
          accessibilityLabel={label}
          accessibilityHint={description}
          testID={testID}
          style={[styles.touchable, disabled && styles.disabled]}>
          <Animated.View
            style={[
              getCheckboxStyle(),
              animated &&
                variant !== 'filled' && {
                  backgroundColor,
                  borderColor,
                },
              animated && {transform: [{scale: scaleAnim}]},
              checkboxStyle,
            ]}>
            {renderCheckIcon()}
          </Animated.View>
        </TouchableOpacity>
      );
    },
    [
      disabled,
      config,
      variant,
      checkedIcon,
      uncheckedIcon,
      checkOpacityAnim,
      scaleAnim,
      animated,
      checkboxStyle,
      label,
      description,
      testID,
      animateCheck,
      backgroundColor,
      borderColor,
      checkScaleAnim,
      checkRotateAnim,
    ],
  );

  // Render label and description
  const renderText = () => {
    if (!label && !description) return null;

    return (
      <View style={styles.textContainer}>
        {label && (
          <Text style={[styles.label, {fontSize: config.fontSize}, disabled && styles.disabledText, required && styles.requiredLabel, labelStyle]}>
            {label}
            {required && <Text style={styles.requiredMark}> *</Text>}
          </Text>
        )}
        {description && <Text style={[styles.description, {fontSize: config.fontSize - 2}, disabled && styles.disabledText, descriptionStyle]}>{description}</Text>}
      </View>
    );
  };

  // Render error
  const renderError = () => {
    const errorMessage = typeof error === 'string' ? error : error?.message;
    if (!errorMessage) return null;

    return <Text style={[styles.error, errorStyle]}>{errorMessage}</Text>;
  };

  // Main render
  if (isControlledProps(props)) {
    const {control, name, rules, defaultValue} = props;

    return (
      <Controller
        control={control}
        name={name}
        rules={rules}
        defaultValue={defaultValue || false}
        render={({field: {value, onChange}, fieldState: {error: fieldError}}) => (
          <View style={[styles.container, containerStyle]}>
            <View style={[styles.checkboxContainer, checkboxPosition === 'right' && styles.checkboxContainerReverse]}>
              {checkboxPosition === 'left' && renderCheckbox(value, onChange)}
              {renderText()}
              {checkboxPosition === 'right' && renderCheckbox(value, onChange)}
            </View>
            {renderError() || (fieldError && <Text style={[styles.error, errorStyle]}>{fieldError.message}</Text>)}
          </View>
        )}
      />
    );
  }

  // Uncontrolled mode
  const {value, onValueChange: onValueChangeProp} = props as UncontrolledCheckboxProps;
  const uncontrolledValue = value;

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={[styles.checkboxContainer, checkboxPosition === 'right' && styles.checkboxContainerReverse]}>
        {checkboxPosition === 'left' && renderCheckbox(uncontrolledValue, onValueChangeProp)}
        {renderText()}
        {checkboxPosition === 'right' && renderCheckbox(uncontrolledValue, onValueChangeProp)}
      </View>
      {renderError()}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md, // Tăng khoảng cách
    marginRight: spacing.md,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    minHeight: 28, // Tăng height
  },
  checkboxContainerReverse: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
  },
  touchable: {
    padding: spacing.sm / 2, // Tăng touch area
    // marginRight: spacing.md, // Tăng khoảng cách với text
    borderRadius: borderRadius.base,
  },
  textContainer: {
    flex: 1,
    paddingTop: 1, // Fine-tune alignment
  },
  label: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.dark,
    lineHeight: typography.fontSize.base * 1.5, // Tăng line height
  },
  requiredLabel: {
    fontWeight: typography.fontWeight.semibold,
  },
  requiredMark: {
    color: colors.danger,
    fontWeight: typography.fontWeight.bold,
  },
  description: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginTop: spacing.xs,
    lineHeight: typography.fontSize.sm * 1.5, // Tăng line height
  },
  error: {
    fontSize: typography.fontSize.sm,
    color: colors.danger,
    marginTop: spacing.xs,
    lineHeight: typography.fontSize.sm * 1.3,
    marginLeft: spacing.lg, // Indent error message
  },
  disabled: {
    opacity: 0.6, // Tăng opacity một chút
  },
  disabledText: {
    color: colors.gray[500],
  },
});

Checkbox.displayName = 'Checkbox';

export default Checkbox;
export type {CheckboxSize, CheckboxVariant, ControlledCheckboxProps, UncontrolledCheckboxProps};
