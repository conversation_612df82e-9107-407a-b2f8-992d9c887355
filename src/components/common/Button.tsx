import React from 'react';
import {ActivityIndicator, StyleSheet, Text, ViewStyle} from 'react-native';
import {colors, typography} from '../../constants/theme';
import CustomTouchableOpacity from './CustomTouchableOpacity';

interface ButtonProps {
  title: string;
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle | ViewStyle[];
}

const Button = React.memo<ButtonProps>(({title, onPress, loading = false, disabled = false, variant = 'primary', size = 'medium', style}) => {
  return (
    <CustomTouchableOpacity style={[styles.button, styles[variant], styles[size], (disabled || loading) && styles.disabled, style]} onPress={onPress} disabled={disabled || loading}>
      {loading ? <ActivityIndicator color="#fff" /> : <Text style={[styles.text, styles[`${variant}Text`]]}>{title}</Text>}
    </CustomTouchableOpacity>
  );
});

Button.displayName = 'Button';

export default Button;

const styles = StyleSheet.create({
  button: {
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
    marginHorizontal: 8,
    // ...shadows.base,
  },
  primary: {
    backgroundColor: colors.green,
  },
  secondary: {
    backgroundColor: '#6C757D',
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.green,
  },
  small: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  medium: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  large: {
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  disabled: {
    opacity: 0.6,
  },
  text: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.fontSize.base + 1,
  },
  primaryText: {
    color: '#fff',
  },
  secondaryText: {
    color: '#fff',
  },
  outlineText: {
    color: colors.green,
  },
});
