import React, {useEffect, useRef} from 'react';
import {Animated, Dimensions, Modal, Platform, StyleSheet, Text, View} from 'react-native';
import {borderRadius, colors, typography} from '@constants/theme';
import {CustomTouchableOpacity} from '@components/common/index';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

export interface IOSAlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

export interface IOSAlertProps {
  visible: boolean;
  title?: string;
  message?: string;
  buttons?: IOSAlertButton[];
  onClose?: () => void;
}

const IOSAlert: React.FC<IOSAlertProps> = ({visible, title, message, buttons = [], onClose}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 10,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  // Default buttons n�u kh�ng c� buttons ��c truy�n v�o
  const alertButtons: IOSAlertButton[] =
    buttons.length > 0
      ? buttons
      : [
          {
            text: 'OK',
            onPress: onClose,
            style: 'default',
          },
        ];

  const handleButtonPress = (button: IOSAlertButton) => {
    if (button.onPress) {
      button.onPress();
    }
    if (onClose) {
      onClose();
    }
  };

  const getButtonTextStyle = (style?: 'default' | 'cancel' | 'destructive') => {
    switch (style) {
      case 'cancel':
        return [styles.buttonText, styles.cancelButtonText];
      case 'destructive':
        return [styles.buttonText, styles.destructiveButtonText];
      default:
        return [styles.buttonText, styles.defaultButtonText];
    }
  };

  const renderButtons = () => {
    const buttonCount = alertButtons.length;

    if (buttonCount === 2) {
      // 2 buttons: horizontal layout
      return (
        <View style={styles.buttonsContainerHorizontal}>
          {alertButtons.map((button, index) => (
            <React.Fragment key={index}>
              {index > 0 && <View style={styles.buttonDividerVertical} />}
              <CustomTouchableOpacity style={[styles.button, styles.buttonHorizontal]} onPress={() => handleButtonPress(button)} activeOpacity={0.6}>
                <Text style={getButtonTextStyle(button.style)}>{button.text}</Text>
              </CustomTouchableOpacity>
            </React.Fragment>
          ))}
        </View>
      );
    }

    // 1 button or 3+ buttons: vertical layout
    return (
      <View style={styles.buttonsContainerVertical}>
        {alertButtons.map((button, index) => (
          <React.Fragment key={index}>
            {index > 0 && <View style={styles.buttonDividerHorizontal} />}
            <CustomTouchableOpacity style={[styles.button, styles.buttonVertical, button.style === 'cancel' && styles.buttonCancel]} onPress={() => handleButtonPress(button)} activeOpacity={0.6}>
              <Text style={getButtonTextStyle(button.style)}>{button.text}</Text>
            </CustomTouchableOpacity>
          </React.Fragment>
        ))}
      </View>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="none" statusBarTranslucent onRequestClose={onClose}>
      <Animated.View style={[styles.overlay, {opacity: opacityAnim}]}>
        <CustomTouchableOpacity style={styles.overlayTouchable} activeOpacity={1} onPress={() => {}}>
          <Animated.View
            style={[
              styles.alertContainer,
              {
                transform: [{scale: scaleAnim}],
                opacity: opacityAnim,
              },
            ]}>
            {/* Content */}
            <View style={styles.contentContainer}>
              {title && <Text style={styles.title}>{title}</Text>}
              {message && <Text style={styles.message}>{message}</Text>}
            </View>

            {/* Divider */}
            <View style={styles.divider} />

            {/* Buttons */}
            {renderButtons()}
          </Animated.View>
        </CustomTouchableOpacity>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  absolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  alertContainer: {
    width: SCREEN_WIDTH - 56,
    maxWidth: 270,
    backgroundColor: Platform.OS === 'ios' ? 'rgba(255, 255, 255, 0.95)' : colors.white,
    borderRadius: borderRadius.xl,
    overflow: 'hidden',
    // ...Platform.select({
    //   ios: {
    //     shadowColor: '#000',
    //     shadowOffset: {width: 0, height: 2},
    //     shadowOpacity: 0.3,
    //     shadowRadius: 10,
    //   },
    //   android: {
    //     elevation: 8,
    //   },
    // }),
  },
  contentContainer: {
    paddingTop: 19,
    paddingBottom: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: typography.fontSize.base + 1,
    fontFamily: typography.fontFamily.semibold,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: 2,
    letterSpacing: -0.41,
  },
  message: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.dark,
    textAlign: 'center',
    marginTop: 3,
    lineHeight: 22,
  },
  divider: {
    height: 0.5,
    backgroundColor: 'rgba(60, 60, 67, 0.29)',
  },
  buttonsContainerHorizontal: {
    flexDirection: 'row',
  },
  buttonsContainerVertical: {
    flexDirection: 'column',
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  buttonHorizontal: {
    flex: 1,
    height: 44,
  },
  buttonVertical: {
    width: '100%',
    height: 44,
  },
  buttonCancel: {
    // Cancel button in vertical layout can have different style if needed
  },
  buttonDividerVertical: {
    width: 0.5,
    backgroundColor: 'rgba(60, 60, 67, 0.29)',
  },
  buttonDividerHorizontal: {
    height: 0.5,
    backgroundColor: 'rgba(60, 60, 67, 0.29)',
  },
  buttonText: {
    fontSize: 17,
    textAlign: 'center',
    letterSpacing: -0.41,
  },
  defaultButtonText: {
    color: colors.green,
    fontFamily: typography.fontFamily.regular,
  },
  cancelButtonText: {
    color: colors.green,
    fontFamily: typography.fontFamily.semibold,
  },
  destructiveButtonText: {
    color: colors.danger,
    fontFamily: typography.fontFamily.regular,
  },
});

export default IOSAlert;
