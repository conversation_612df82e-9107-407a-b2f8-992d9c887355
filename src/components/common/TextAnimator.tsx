import logger from '@utils/logger';
import * as React from 'react';
import {Animated, StyleSheet, View, ViewStyle, TextStyle} from 'react-native';

interface TextAnimatorProps {
  timing?: number;
  content?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  onFinish?: (finished: boolean) => void;
}

const TextAnimator: React.FC<TextAnimatorProps> = ({timing = 10000, content = '', style, textStyle, onFinish}) => {
  logger.log('🚀 ~ TextAnimator ~ props:', {timing, content, style, textStyle, onFinish});
  let animatedValues: Animated.Value[] = [];
  let animations: Animated.CompositeAnimation[] = [];
  let textArr = content.trim().split(' ');
  textArr.forEach((_, i) => {
    animatedValues[i] = new Animated.Value(0);
  });
  textArr = textArr;

  React.useEffect(() => {
    animate(1);
    return () => {
      animate(0);
    };
  }, []);

  const animate = (toValue = 1) => {
    toValue = toValue;
    animations = textArr.map((_, i) => {
      return Animated.timing(animatedValues[i], {
        toValue,
        duration: timing,
        useNativeDriver: true, // Add This line fixx lỗi warning
      });
    });
    Animated.stagger(timing / 5, toValue === 0 ? animations.reverse() : animations).start(() => {
      setTimeout(() => animate(toValue === 0 ? 1 : 0), 1000);
      if (onFinish) {
        onFinish(toValue === 1);
      }
    });
  };

  return (
    <View style={[style, styles.textWrapper]}>
      {textArr.map((v, i) => {
        return (
          <Animated.Text
            onPress={() => animate()}
            key={`${v}-${i}`}
            style={[
              textStyle,
              {
                opacity: animatedValues[i],
                transform: [
                  {
                    translateY: Animated.multiply(animatedValues[i], new Animated.Value(-5)),
                  },
                ],
              },
            ]}>
            {v}
            {`${i < textArr.length ? ' ' : ''}`}
          </Animated.Text>
        );
      })}
    </View>
  );
};

export default TextAnimator;

const styles = StyleSheet.create({
  textWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
});
