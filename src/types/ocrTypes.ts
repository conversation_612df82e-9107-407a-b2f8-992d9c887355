export interface OcrApiResponse {
  data?: {
    gioi_tinh?: string; // Gender
    han_sd?: string; // Expiry date
    loai?: string; // Type (cccd_mat_truoc)
    ngay_cap?: string | null; // Issue date
    ngay_sinh?: string; // Date of birth
    noi_cap?: string | null; // Place of issue
    noi_thuong_tru?: string; // Permanent address
    noi_thuong_tru_phuong_xa?: string; // Ward/Commune
    noi_thuong_tru_quan_huyen?: string; // District
    noi_thuong_tru_tinh_thanh?: string; // Province/City
    que_quan?: string; // Place of origin
    que_quan_phuong_xa?: string; // Origin ward/commune
    que_quan_quan_huyen?: string; // Origin district
    que_quan_tinh_thanh?: string; // Origin province/city
    quoc_gia?: string; // Nationality
    so_cmnd?: string; // ID number
    ten?: string; // Full name
    [key: string]: any; // Allow for additional fields
  };
  output?: any;
  message?: string;
  error?: string;
}

export interface OcrApiRequest {
  file: File | Blob | string; // Can be File object, Blob, or URI string
}

// Response structure that matches the existing parseCccdFrontData function
export interface OcrParseableResponse {
  status: 'succeeded' | 'failed';
  analyzeResult: {
    readResults: Array<{
      lines: Array<{
        text: string;
      }>;
    }>;
  };
}
